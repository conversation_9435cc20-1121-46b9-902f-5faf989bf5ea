# 异步支付服务（AsyncDoPaymentService）校验项脑图

## 接口概述
- **接口名称**：异步支付服务
- **处理方法**：com.howbuy.dtms.order.service.business.payment.AsyncDoPaymentService.process(pmtDealNo)
- **主要功能**：根据支付交易号处理异步支付逻辑

## 校验项脑图

```mermaid
graph LR
    A[异步支付服务 AsyncDoPaymentService] --> B[支付订单存在性校验]
    A --> C[交易支付标记校验]
    A --> D[交易订单校验]
    A --> E[支付发起前状态更新]
    A --> F[支付发起操作]
    A --> G[支付发起后状态更新]

    B --> B1[✅ 正例-通过: 支付订单存在]
    B --> B2[❌ 反例-不通过: 支付订单不存在<br/>异常日志+监控预警]

    C --> C1[✅ 正例-通过: txPmtFlag=1未付款]
    C --> C2[❌ 反例-不通过: txPmtFlag≠1<br/>异常日志+监控预警]

    D --> D1[交易订单存在性校验]
    D --> D2[交易订单状态校验]

    D1 --> D11[✅ 正例-通过: 交易订单存在]
    D1 --> D12[❌ 反例-不通过: 交易订单不存在<br/>异常日志+监控预警]

    D2 --> D21[✅ 正例-通过: payStatus=1未付款 且 orderStatus=1申请成功]
    D2 --> D22[❌ 反例-不通过: 状态不符合要求<br/>异常日志+监控预警]

    E --> E1[支付订单状态更新]
    E --> E2[交易订单状态更新]

    E1 --> E11[✅ 正例-通过: 乐观锁更新成功<br/>txPmtFlag=4付款中, pmtCompFlag=1未对账]
    E1 --> E12[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1, 异常日志+监控预警+抛异常]

    E2 --> E21[✅ 正例-通过: 乐观锁更新成功<br/>payStatus=2付款中]
    E2 --> E22[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1, 异常日志+监控预警+抛异常]

    F --> F1[✅ 正例-通过: 支付外部服务调用成功]
    F --> F2[❌ 反例-不通过: 支付外部服务调用失败<br/>返回错误码和错误描述]

    G --> G1[✅ 正例-通过: 乐观锁更新成功<br/>更新retCode, retDesc, outPmtDealNo]
    G --> G2[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1, 异常日志+监控预警]
```

### 层级结构脑图

```mermaid
mindmap
  root((异步支付服务))
    支付订单存在性校验
      ✅ 支付订单存在
      ❌ 支付订单不存在
    交易支付标记校验
      ✅ txPmtFlag=1未付款
      ❌ txPmtFlag≠1
    交易订单校验
      存在性校验
        ✅ 交易订单存在
        ❌ 交易订单不存在
      状态校验
        ✅ 状态正常
        ❌ 状态异常
    支付发起前状态更新
      支付订单更新
        ✅ 乐观锁成功
        ❌ 乐观锁冲突
      交易订单更新
        ✅ 乐观锁成功
        ❌ 乐观锁冲突
    支付发起操作
      ✅ 调用成功
      ❌ 调用失败
    支付发起后状态更新
      ✅ 更新成功
      ❌ 更新失败
```

## 详细校验项说明

### 1. 支付订单存在性校验
- **✅ 正例-通过**：支付订单存在
- **❌ 反例-不通过**：
  - 支付订单不存在，处理：记录异常日志并发送异常监控预警

### 2. 交易支付标记校验
- **✅ 正例-通过**：txPmtFlag=1-未付款
- **❌ 反例-不通过**：
  - txPmtFlag≠1（非未付款状态），处理：记录异常日志并发送异常监控预警

### 3. 交易订单校验（当orderType=1-交易时）
#### 3.1 交易订单存在性校验
- **✅ 正例-通过**：交易订单存在
- **❌ 反例-不通过**：
  - 交易订单不存在，处理：记录异常日志并发送异常监控预警

#### 3.2 交易订单状态校验
- **✅ 正例-通过**：payStatus=1-未付款 且 orderStatus=1-申请成功
- **❌ 反例-不通过**：
  - payStatus≠1或orderStatus≠1，处理：记录异常日志并发送异常监控预警

### 4. 支付发起前状态更新（事务一致处理）
#### 4.1 支付订单状态更新
- **✅ 正例-通过**：乐观锁更新成功
  - 设置txPmtFlag=4-付款中
  - 设置pmtCompFlag=1-未对账
  - 更新appDtm为服务器时间
- **❌ 反例-不通过**：
  - 乐观锁冲突（UPDATE影响行数≠1），处理：记录异常日志、发送异常监控预警、抛出异常
  - 乐观锁条件：WHERE条件包含txPmtFlag='1'和查询时的updateTimestamp值

#### 4.2 交易订单状态更新（当orderType=1-交易时）
- **✅ 正例-通过**：乐观锁更新成功
  - 设置payStatus=2-付款中
  - 更新updateTimestamp为服务器时间
- **❌ 反例-不通过**：
  - 乐观锁冲突（UPDATE影响行数≠1），处理：记录异常日志、发送异常监控预警、抛出异常
  - 乐观锁条件：WHERE条件包含orderStatus='1'、payStatus='1'和查询时的updateTimestamp值

### 5. 支付发起操作
- **✅ 正例-通过**：支付外部服务调用成功
  - 调用PayOuterService.callPayment成功
  - 返回正常的支付结果
- **❌ 反例-不通过**：
  - 支付外部服务调用失败，处理：返回错误码和错误描述
  - 网络异常、超时等情况

### 6. 支付发起后状态更新
- **✅ 正例-通过**：乐观锁更新成功
  - 更新retCode（返回码）
  - 更新retDesc（返回描述）
  - 更新outPmtDealNo（外部支付交易号）
  - 更新updateTimestamp为服务器时间
- **❌ 反例-不通过**：
  - 乐观锁冲突（UPDATE影响行数≠1），处理：记录异常日志、发送异常监控预警
  - 乐观锁条件：WHERE条件包含txPmtFlag='4'和发起支付前查询时的updateTimestamp值

## 关键技术点

### 乐观锁实现机制
1. **支付订单乐观锁**：UPDATE语句WHERE条件必须包含前置状态和updateTimestamp
2. **交易订单乐观锁**：UPDATE语句WHERE条件必须包含前置状态和updateTimestamp
3. **冲突处理**：UPDATE影响行数不为1时，记录异常日志、发送监控预警、抛出异常

### 异常处理机制
1. **异常日志记录**：所有校验失败场景都需要记录异常日志
2. **监控预警**：关键异常需要发送异常监控预警
3. **异常抛出**：乐观锁冲突等严重异常需要抛出异常中断流程

### 状态流转规则
1. **支付订单状态**：1-未付款 → 4-付款中 → 最终状态
2. **交易订单状态**：payStatus 1-未付款 → 2-付款中 → 最终状态
3. **对账标记**：pmtCompFlag 1-未对账 → 后续对账处理

## 测试建议

### 正常流程测试
1. 准备正常的支付订单和交易订单数据
2. 验证各个校验点的正例场景
3. 确认状态更新的正确性

### 异常场景测试
1. 测试各种数据不存在的情况
2. 测试状态不符合要求的情况
3. 测试乐观锁冲突的情况
4. 验证异常处理机制的有效性

### 并发测试
1. 模拟多线程同时处理同一支付订单
2. 验证乐观锁机制的有效性
3. 确认并发冲突时的异常处理

---
*生成时间：2025-01-24*
*基于文档：.知识库/1.md - 异步支付服务处理逻辑*
