/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.order;

import com.howbuy.dtms.order.client.domain.request.order.QueryDealOrderAndDtlRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.order.QueryDealOrderAndDtlResponse;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/13 14:30
 * @since JDK 1.8
 */
public interface QueryDealOrderFacade {


    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.order.QueryDealOrderFacade.queryDealOrderAndDtl()
     * @apiVersion 1.0.0
     * @apiGroup QueryDealOrderFacadeImpl
     * @apiName queryDealOrderAndDtl()
     * @apiDescription 查询订单及订单明细
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} dealNo 订单号
     * @apiParam (请求体) {String} prebookDealNo 预约单号
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"4","hkCustNo":"z9XBFvuGk","ipAddress":"PH","dealNo":"Hfa4eper","deviceName":"866eYcNvOb","systemVersion":"KlOAvTX","prebookDealNo":"dJ","appTm":"NgCUqWC","macAddress":"jSCHZJi","deviceSerialNo":"607TwOmR","appDt":"c0ktVz0Gl","deviceModel":"sV7RTWmEI","txCode":"l2a","outletCode":"Yn","tradeChannel":"yMUq"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Object} data.hwDealOrderVO 海外订单VO
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.id id
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.custChineseName 客户中文姓名
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.idType 证件类型
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.idNoCipher 证件号码密文
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.idNoDigest 证件号码摘要
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.idNoMask 证件号码掩码
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.invstType 投资者类型 0-机构；1-个人
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.qualificationType 投资者资质 PRO-投资者资质专业;NORMAL-投资者资质普通
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.custRiskLevel 客户风险等级
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.bankAcctCipher 银行账号密文
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.bankAcctDigest 银行账号摘要
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.bankAcctMask 银行账号掩码
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.swiftCode swift编码
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.bankCode 银行代码
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.bankName 银行名称
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.bankChineseName 银行中文名称
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.txCode 交易代码:HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.middleBusiCode 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.productName 产品名称
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.productAbbr 产品简称
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.productCode 产品代码
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.appAmt 申请金额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.appVol 申请份额
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.currency 币种
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.openDt 开放日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.taTradeDt TA交易日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.appDt 申请日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.appTm 申请时间
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.paymentTypeList 支付方式列表 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.payStatus 支付状态 0-无需付款 1-未付款 2-付款中 3-部分成功 4-成功 5-失败 6-退款
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.payVoucherStatus 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.actualPayAmt 实际打款金额
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.actualPayDt 实际打款日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.actualPayTm 实际打款时间
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.prebookDealNo 预约单号
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.externalDealNo 外部单号
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.firstBuyFlag 首次购买标识，1-首次购买；2-追加购买
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.isAgreeCurrencyExchange 是否同意换汇 0-否 1-是
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.orderFormType 成单方式 1：纸质成单；2：电子成单；
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.supportPrebookFlag 支持预约交易标识，0-不支持；1-支持
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.memo 备注
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.ipAddress IP地址
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.outletCode 网点号
     * @apiSuccess (响应结果) {String} data.hwDealOrderVO.recStat 记录状态 0-正常；1-已删除
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.createTimestamp 创建时间戳
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.updateTimestamp 更新时间戳
     * @apiSuccess (响应结果) {Number} data.hwDealOrderVO.relationalDealNo 关联订单号
     * @apiSuccess (响应结果) {Array} data.hwDealOrderDtlVO 海外订单明细VO
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.id ID
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.dealDtlNo 订单明细号
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.cpAcctNo 资金账号
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundName 基金名称
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundAbbr 基金简称
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.mainFundCode 主基金代码
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundCategory 基金类别 1-公募、2-私募、9-其他
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundRiskLevel 基金风险等级
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.redeemType 赎回方式 1-按份额、2-按金额
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.redeemDirectionList 赎回方向列表 1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：支票
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.middleBusiCode 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.appAmt 申请金额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.netAppAmt 净申请金额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.appVol 申请份额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.estimateFee 预估手续费
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.prebookDiscount 预约折扣
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.discountRate 折扣率
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.discountType 折扣类型 1-折扣率 2-折扣金额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.discountAmt 折扣金额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.feeRate 手续费率
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.feeCalMode 费用计算方式 0-外扣法、1-内扣法
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.fee 手续费
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.ackAmt 确认金额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.ackVol 确认份额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.ackNav 确认净值
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.ackNavDt 确认净值日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.currency 币种
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.appStatus 申请状态 0-申请成功；1-申请失败；2-自行撤销；3-强制取消
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.ackStatus 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.transferPrice 转让价格
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundDivMode 分红方式 0-红利再投 1-现金分红 2-N/A不适用
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.openDt 开放日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.payEndDt 打款截止日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.payEndTm 打款截止时间
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.taTradeDt TA交易日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.ackDt 确认日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.taAckNo TA确认流水号
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.submitStatus 上报状态 0-未上报、1-上报中、2-上报成功、3-需重新上报、4-撤回上报、5-无需上报
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.preSubmitTaDt 预计上报日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.preSubmitTaTm 预计上报时间
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundManCode 管理人代码
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.extOption 展期选项 1-本金展期,2-本金+收益展期,3-收益展期,4-到期赎回
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.extControlType 展期控制类型:1-月
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.extControlNum 展期控制数
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.cancelDate 撤单时间 yyyyMMDDhhmmss
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.cancelCause 撤单原因
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.cancelCpAcctNo 撤单资金账号
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.intoFundCode 转入基金代码
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.intoMainFundCode 转入基金母基金
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.intoFundAbbr 转入基金简称
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.intoCurrency 转入币种
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.intoAckAmt 转入确认金额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.intoAckVol 转入确认份额
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.intoAckNav 转入确认净值
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.intoAckNavDt 转入确认净值日期
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.intoFundTxAcctNo 转入基金交易账号
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.relationalDealDtlNo 关联订单明细号
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.volDtlNo 份额明细流水号
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.subAmt 认缴金额
     * @apiSuccess (响应结果) {String} data.hwDealOrderDtlVO.recStat 记录状态 0-正常；1-已删除
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.createTimestamp 创建时间戳
     * @apiSuccess (响应结果) {Number} data.hwDealOrderDtlVO.updateTimestamp 更新时间戳
     * @apiSuccessExample 响应结果示例
     * {"code":"bbp","data":{"hwDealOrderDtlVO":[{"discountRate":2032.161538644267,"intoMainFundCode":"4WgZluQhx","appAmt":1126.2108360069433,"fee":7559.205252247988,"taTradeDt":"V4fQL9q","estimateFee":54.70874375442247,"extControlNum":"R","intoAckAmt":6356.019240050691,"createTimestamp":2698359400186,"intoFundTxAcctNo":"pQ","fundCode":"zbdn","appVol":1157.678514177163,"fundCategory":"Z","id":6072,"ackVol":2931.2209972899905,"intoFundAbbr":"wlcj","cancelCpAcctNo":"ynlW","taAckNo":"mZrlGcH","preSubmitTaDt":"YLsIRL","intoAckNav":1675.4872735705994,"intoCurrency":"DDvTuCI","dealNo":6718,"appStatus":"4VF1FC4xUK","cancelCause":"lR0sMvObD7","preSubmitTaTm":"rvstF","fundRiskLevel":"jy9ht","subAmt":8796.006302030535,"ackStatus":"2YxnwfoHR","openDt":"3GSjMJQ","payEndDt":"SJ2TrG","fundName":"9PbD","netAppAmt":4560.199192933347,"dealDtlNo":9685,"transferPrice":3374.055432491566,"extControlType":"70dYkr","discountAmt":6383.811536105529,"cancelDate":"m9Jx7eMM","relationalDealDtlNo":3991,"fundDivMode":"y0QmxZRqbl","feeRate":5168.060979249698,"fundTxAcctNo":"karuTgM","fundAbbr":"glKqRDhn","intoAckNavDt":"0OpZK","ackAmt":9518.927045782442,"payEndTm":"PIxM89g","mainFundCode":"lxk1bY","cpAcctNo":"AxcdhL4w2","discountType":"N9eCCQd1iB","currency":"uYFHiW","redeemDirectionList":"i","feeCalMode":"oouxh","prebookDiscount":2603.*************,"submitStatus":"TLC","ackNavDt":"pjddjoG","fundManCode":"Wue1bj1Ig","intoFundCode":"iccb","middleBusiCode":"LJYyRKhu6","hkCustNo":"rTS","updateTimestamp":***********,"ackNav":5490.************,"volDtlNo":"F2wAd","redeemType":"DJQZY1g","extOption":"5FpeuzQ","intoAckVol":6438.***********,"ackDt":"2jXcB","recStat":"GXCMU"}],"hwDealOrderVO":{"externalDealNo":"uHVUSJnNok","appAmt":5286.************,"bankAcctMask":"9F53B","taTradeDt":"5yHni","memo":"fPA6An","productName":"Xi","createTimestamp":*************,"appVol":9774.************,"bankAcctCipher":"G","custChineseName":"e","appDt":"w2Rw2FIq2","id":5398,"firstBuyFlag":"8VdlwTdf","idType":"EJRTV31GI","ipAddress":"xIgUGUo","dealNo":1837,"openDt":"bY0o","txCode":"zO","outletCode":"i","actualPayTm":"J5Y9tQ42v0","orderFormType":"BOOv","swiftCode":"kFBny5gWcC","orderStatus":"OAe4","bankName":"cvH0q7X","productAbbr":"ea3WdpoH","qualificationType":"t","relationalDealNo":3478,"invstType":"sHYZH8V4","supportPrebookFlag":"G8h9j1gOlf","cpAcctNo":"Byn6XxR","currency":"N0IY","bankCode":"c26M4UkGL","bankChineseName":"zeQ7Q","payVoucherStatus":"Me2E5P","middleBusiCode":"INS8","isAgreeCurrencyExchange":"yyFTC95O","hkCustNo":"pXCBqbpx","custRiskLevel":"dy6t","idNoDigest":"qzq9","idNoMask":"P7CNMK","bankAcctDigest":"EOieuxAw4J","updateTimestamp":*************,"prebookDealNo":"gH","appTm":"Xi","productCode":"mjm24NbECA","paymentTypeList":"brC5YE8Z","actualPayAmt":7695.************,"businessType":"uPNtSR6Oxv","idNoCipher":"FEqLi","payStatus":"Z","actualPayDt":"t4b","recStat":"WWl8F","tradeChannel":"nwVBKw"}},"description":"ugdW"}
     */
    Response<QueryDealOrderAndDtlResponse> queryDealOrderAndDtl(QueryDealOrderAndDtlRequest request);


}
