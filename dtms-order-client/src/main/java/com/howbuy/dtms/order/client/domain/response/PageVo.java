/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/6/27 17:36
 * @since JDK 1.8
 */
@Data
public class PageVo<T> {

    /**
     * 总记录数
     */

    private long total;

    /**
     * 总页数
     */
    private int pages;

    /**
     * 结果
     */
    private List<T> list;

}
