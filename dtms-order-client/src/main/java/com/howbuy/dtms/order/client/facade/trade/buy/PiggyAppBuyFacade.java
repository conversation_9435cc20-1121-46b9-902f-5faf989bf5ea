package com.howbuy.dtms.order.client.facade.trade.buy;

import com.howbuy.dtms.order.client.facade.BaseFacade;
import com.howbuy.dtms.order.client.domain.request.buy.PiggyAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.buy.PiggyAppBuyResponse;

/**
 * <AUTHOR>
 * @description 储蓄罐申请购买
 * @date 2024/8/13 19:56
 * @since JDK 1.8
 */
public interface PiggyAppBuyFacade extends BaseFacade<PiggyAppBuyRequest, PiggyAppBuyResponse> {
}
