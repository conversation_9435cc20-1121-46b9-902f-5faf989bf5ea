package com.howbuy.dtms.order.client.domain.request.fundtxacct;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 柜台基金交易账号校验请求参数
 * <AUTHOR>
 * @date 2025/4/17 14:20
 * @since JDK 1.8
 */
@Getter
@Setter
public class CounterFundTxAcctOpenRequest extends BaseRequest {
    private static final long serialVersionUID = 5785828536348066990L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号产品信息
     */
    private List<AddFundTxAcctRequest> counterValidateFundTxAcctInfoList;

} 