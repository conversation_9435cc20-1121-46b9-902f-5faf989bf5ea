package com.howbuy.dtms.order.client.facade.query.agreement;

import com.howbuy.dtms.order.client.domain.request.agreement.QuerySupplementalAgreementRequest;
import com.howbuy.dtms.order.client.domain.response.agreement.QuerySupplementalAgreementResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.agreement.QuerySupplementalAgreementFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QuerySupplementalAgreementFacade
 * @apiName execute()
 * @apiDescription 查询用户补签协议接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金编码
 * @apiParam (请求参数) {String} signStatus 签署状态
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=1HyrkTJOI&hkCustNo=LwZa3yH5&signStatus=VLI&ipAddress=1FBOfV77qn&deviceName=1D&systemVersion=lM&appTm=yDwam5&macAddress=rkFu3Vc&deviceSerialNo=AVT8ep&fundCode=gpFEx&appDt=JknIlz&deviceModel=mchKo2R&txCode=snu&outletCode=mDoyQ0mC&tradeChannel=F
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.agreementList 补充协议列表
 * @apiSuccess (响应结果) {String} data.agreementList.agreementId 补签协议ID
 * @apiSuccess (响应结果) {String} data.agreementList.agreementSignEndDt 协议签署截止时间          格式：YYYY-MM-DD HH:MM
 * @apiSuccess (响应结果) {String} data.agreementList.agreementName 协议名称
 * @apiSuccess (响应结果) {String} data.agreementList.fundCode 基金Code
 * @apiSuccess (响应结果) {String} data.agreementList.fundAbbr 基金简称
 * @apiSuccess (响应结果) {String} data.agreementList.agreementDescription 协议说明
 * @apiSuccess (响应结果) {String} data.agreementList.agreementUrl 协议地址          同一个产品，是一样的，所以通过静态链接地址访问
 * @apiSuccess (响应结果) {String} data.agreementList.signStatus 签署状态
 * @apiSuccess (响应结果) {String} data.agreementList.signDate 签署时间
 * @apiSuccessExample 响应结果示例
 * {"code":"kiS","data":{"agreementList":[{"fundAbbr":"9kTTcaNEAJ","fundCode":"Q0a1jG","agreementUrl":"IcRvVJgD","agreementSignEndDt":"ZR06","agreementName":"i0","signStatus":"YKQ","agreementId":"OOCObs5N","signDate":"eCBZQU","agreementDescription":"ahRm"}]},"description":"4SEj3paZ2k"}
 */
public interface QuerySupplementalAgreementFacade extends BaseFacade<QuerySupplementalAgreementRequest, QuerySupplementalAgreementResponse> {

}