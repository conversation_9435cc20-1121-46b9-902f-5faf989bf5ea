/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/13 15:08
 * @since JDK 1.8
 */
public class HwDealOrderVO implements Serializable {

    private static final long serialVersionUID = -6315546981246677341L;

    /**
     * id
     */
    private Long id;

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户中文姓名
     */
    private String custChineseName;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号码密文
     */
    private String idNoCipher;

    /**
     * 证件号码摘要
     */
    private String idNoDigest;

    /**
     * 证件号码掩码
     */
    private String idNoMask;

    /**
     * 投资者类型 0-机构；1-个人
     */
    private String invstType;

    /**
     * 投资者资质 PRO-投资者资质专业;NORMAL-投资者资质普通
     */
    private String qualificationType;

    /**
     * 客户风险等级
     */
    private String custRiskLevel;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行账号密文
     */
    private String bankAcctCipher;

    /**
     * 银行账号摘要
     */
    private String bankAcctDigest;

    /**
     * 银行账号掩码
     */
    private String bankAcctMask;

    /**
     * swift编码
     */
    private String swiftCode;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行中文名称
     */
    private String bankChineseName;

    /**
     * 交易代码:HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验
     */
    private String txCode;

    /**
     * 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
     */
    private String businessType;

    /**
     * 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘
     */
    private String middleBusiCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品简称
     */
    private String productAbbr;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 币种
     */
    private String currency;

    /**
     * 开放日期
     */
    private String openDt;

    /**
     * TA交易日期
     */
    private String taTradeDt;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private String orderStatus;

    /**
     * 支付方式列表 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
     */
    private String paymentTypeList;

    /**
     * 支付状态 0-无需付款 1-未付款 2-付款中 3-部分成功 4-成功 5-失败 6-退款
     */
    private String payStatus;

    /**
     * 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     */
    private String payVoucherStatus;

    /**
     * 实际打款金额
     */
    private BigDecimal actualPayAmt;

    /**
     * 实际打款日期
     */
    private String actualPayDt;

    /**
     * 实际打款时间
     */
    private String actualPayTm;

    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 外部单号
     */
    private String externalDealNo;

    /**
     * 关联订单号(订单表的订单号)
     */
    private Long relationalDealNo;

    /**
     * 批次号
     */
    private Long batchNo;

    /**
     * 首次购买标识，1-首次购买；2-追加购买
     */
    private String firstBuyFlag;

    /**
     * 是否同意换汇 0-否 1-是
     */
    private String isAgreeCurrencyExchange;

    /**
     * 成单方式 1：纸质成单；2：电子成单；
     */
    private String orderFormType;

    /**
     * 支持预约交易标识，0-不支持；1-支持
     */
    private String supportPrebookFlag;

    /**
     * 备注
     */
    private String memo;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     */
    private String tradeChannel;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * 记录状态 0-正常；1-已删除
     */
    private String recStat;

    /**
     * 创建时间戳
     */
    private Date createTimestamp;

    /**
     * 更新时间戳
     */
    private Date updateTimestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDealNo() {
        return dealNo;
    }

    public void setDealNo(Long dealNo) {
        this.dealNo = dealNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCustChineseName() {
        return custChineseName;
    }

    public void setCustChineseName(String custChineseName) {
        this.custChineseName = custChineseName;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getIdNoCipher() {
        return idNoCipher;
    }

    public void setIdNoCipher(String idNoCipher) {
        this.idNoCipher = idNoCipher;
    }

    public String getIdNoDigest() {
        return idNoDigest;
    }

    public void setIdNoDigest(String idNoDigest) {
        this.idNoDigest = idNoDigest;
    }

    public String getIdNoMask() {
        return idNoMask;
    }

    public void setIdNoMask(String idNoMask) {
        this.idNoMask = idNoMask;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getQualificationType() {
        return qualificationType;
    }

    public void setQualificationType(String qualificationType) {
        this.qualificationType = qualificationType;
    }

    public String getCustRiskLevel() {
        return custRiskLevel;
    }

    public void setCustRiskLevel(String custRiskLevel) {
        this.custRiskLevel = custRiskLevel;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getBankAcctCipher() {
        return bankAcctCipher;
    }

    public void setBankAcctCipher(String bankAcctCipher) {
        this.bankAcctCipher = bankAcctCipher;
    }

    public String getBankAcctDigest() {
        return bankAcctDigest;
    }

    public void setBankAcctDigest(String bankAcctDigest) {
        this.bankAcctDigest = bankAcctDigest;
    }

    public String getBankAcctMask() {
        return bankAcctMask;
    }

    public void setBankAcctMask(String bankAcctMask) {
        this.bankAcctMask = bankAcctMask;
    }

    public String getSwiftCode() {
        return swiftCode;
    }

    public void setSwiftCode(String swiftCode) {
        this.swiftCode = swiftCode;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankChineseName() {
        return bankChineseName;
    }

    public void setBankChineseName(String bankChineseName) {
        this.bankChineseName = bankChineseName;
    }

    public String getTxCode() {
        return txCode;
    }

    public void setTxCode(String txCode) {
        this.txCode = txCode;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getMiddleBusiCode() {
        return middleBusiCode;
    }

    public void setMiddleBusiCode(String middleBusiCode) {
        this.middleBusiCode = middleBusiCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductAbbr() {
        return productAbbr;
    }

    public void setProductAbbr(String productAbbr) {
        this.productAbbr = productAbbr;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getOpenDt() {
        return openDt;
    }

    public void setOpenDt(String openDt) {
        this.openDt = openDt;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getPaymentTypeList() {
        return paymentTypeList;
    }

    public void setPaymentTypeList(String paymentTypeList) {
        this.paymentTypeList = paymentTypeList;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayVoucherStatus() {
        return payVoucherStatus;
    }

    public void setPayVoucherStatus(String payVoucherStatus) {
        this.payVoucherStatus = payVoucherStatus;
    }

    public BigDecimal getActualPayAmt() {
        return actualPayAmt;
    }

    public void setActualPayAmt(BigDecimal actualPayAmt) {
        this.actualPayAmt = actualPayAmt;
    }

    public String getActualPayDt() {
        return actualPayDt;
    }

    public void setActualPayDt(String actualPayDt) {
        this.actualPayDt = actualPayDt;
    }

    public String getActualPayTm() {
        return actualPayTm;
    }

    public void setActualPayTm(String actualPayTm) {
        this.actualPayTm = actualPayTm;
    }

    public String getPrebookDealNo() {
        return prebookDealNo;
    }

    public void setPrebookDealNo(String prebookDealNo) {
        this.prebookDealNo = prebookDealNo;
    }

    public String getExternalDealNo() {
        return externalDealNo;
    }

    public void setExternalDealNo(String externalDealNo) {
        this.externalDealNo = externalDealNo;
    }

    public Long getRelationalDealNo() {
        return relationalDealNo;
    }

    public void setRelationalDealNo(Long relationalDealNo) {
        this.relationalDealNo = relationalDealNo;
    }

    public Long getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(Long batchNo) {
        this.batchNo = batchNo;
    }

    public String getFirstBuyFlag() {
        return firstBuyFlag;
    }

    public void setFirstBuyFlag(String firstBuyFlag) {
        this.firstBuyFlag = firstBuyFlag;
    }

    public String getIsAgreeCurrencyExchange() {
        return isAgreeCurrencyExchange;
    }

    public void setIsAgreeCurrencyExchange(String isAgreeCurrencyExchange) {
        this.isAgreeCurrencyExchange = isAgreeCurrencyExchange;
    }

    public String getOrderFormType() {
        return orderFormType;
    }

    public void setOrderFormType(String orderFormType) {
        this.orderFormType = orderFormType;
    }

    public String getSupportPrebookFlag() {
        return supportPrebookFlag;
    }

    public void setSupportPrebookFlag(String supportPrebookFlag) {
        this.supportPrebookFlag = supportPrebookFlag;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getTradeChannel() {
        return tradeChannel;
    }

    public void setTradeChannel(String tradeChannel) {
        this.tradeChannel = tradeChannel;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }
}
