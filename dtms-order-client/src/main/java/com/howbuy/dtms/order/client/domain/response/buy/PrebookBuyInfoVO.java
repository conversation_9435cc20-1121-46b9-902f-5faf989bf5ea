/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 预约买入信息
 * @date 2024/4/10 10:13
 * @since JDK 1.8
 */
@Data
public class PrebookBuyInfoVO implements Serializable {

    private static final long serialVersionUID = 687570226060175926L;
    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 预约支付方式 1-电汇、2-支票、3-海外储蓄罐
     */
    private String prebookPayMethod;

    /**
     * 预约申请金额
     */
    private BigDecimal prebookAppAmt;

    /**
     * 分次call 认缴金额     *
     */
    private BigDecimal subscribeAmt;

    /**
     * 预约折扣率
     */
    private BigDecimal prebookDiscountRate;


}
