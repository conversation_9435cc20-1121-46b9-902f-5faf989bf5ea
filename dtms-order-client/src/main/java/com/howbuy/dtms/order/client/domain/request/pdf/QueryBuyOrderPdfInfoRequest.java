package com.howbuy.dtms.order.client.domain.request.pdf;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 请求类 - 查询购买订单PDF信息
 * <AUTHOR>
 * @date 2024/5/9 16:17
 * @since JDK 1.8
 */

public class QueryBuyOrderPdfInfoRequest extends BaseRequest {

    private static final long serialVersionUID = -5758897558508405016L;

    public QueryBuyOrderPdfInfoRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_QUERY_BUY_ORDER_PDF);
    }

    private String dealNo;

    private String hkCustNo;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }
}