/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/7/23 14:14
 * @since JDK 1.8
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class WebPayVoucherListRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 3110727703160074473L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     */
    private List<String> voucherTypeList;

    /**
     * 审核状态 2-等待复核、3-审核通过、4-审核不通过、6-驳回至客户、7-作废 不传即所有
     */
    private List<String> auditStatusList;

    /**
     * 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP；12-H5    不传即所有
     */
    private List<String> tradeChannelList;

    /**
     * 申请开始时间
     */
    private String appStartDt;

    /**
     * 申请结束时间
     */
    private String appEndDt;

    /**
     * 是否需要审核信息 1 需要，0 不需要
     */
    private String needAuditInfoFlag;


}