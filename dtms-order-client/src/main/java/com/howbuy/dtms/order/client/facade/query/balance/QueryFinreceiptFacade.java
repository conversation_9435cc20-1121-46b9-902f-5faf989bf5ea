package com.howbuy.dtms.order.client.facade.query.balance;

import com.howbuy.dtms.order.client.domain.request.finrecept.QueryFinReciptRequest;
import com.howbuy.dtms.order.client.domain.response.finreceipt.QueryFinReceiptResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;


/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.balance.QueryFinreceiptFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFinreceiptFacade
 * @apiDescription 查询在途数据
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} hbOneNo 一账通号
 * @apiParamExample 请求体示例
 * {"hkCustNo":"3OgWMt9feR"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.hbOneNo 一账通账号
 * @apiSuccess (响应结果) {Number} data.buyUnrefundedPiece 购买待退款订单数
 * @apiSuccess (响应结果) {Number} data.redeemUnrefundedPiece 赎回待回款订单数
 * @apiSuccess (响应结果) {Array} data.unpaidList 待付款订单
 * @apiSuccess (响应结果) {Array} data.unconfirmedList 待确认订单
 * @apiSuccessExample 响应结果示例
 * {"code":"hW8BYE","description":"Anjff"}
 */
public interface QueryFinreceiptFacade extends BaseFacade<QueryFinReciptRequest, QueryFinReceiptResponse> {

}
