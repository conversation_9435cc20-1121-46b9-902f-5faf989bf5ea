/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.counterparam.agreement;

import com.howbuy.dtms.order.client.domain.request.agreement.CounterParamAgreementModifyRequest;
import com.howbuy.dtms.order.client.domain.response.agreement.CounterParamAgreementModifyResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.counterparam.agreement.CounterParamAgreementModifyFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup CounterParamAgreementModifyFacadeImpl
 * @apiName execute()
 * @apiDescription 柜台参数协议修改
 * @apiParam (请求体) {String} agreementId 协议ID
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} fundCode 基金编码
 * @apiParam (请求体) {String} modifyReason 修改原因
 * @apiParamExample 请求体示例
 * {
 *   "agreementId":"AGR202503200001",
 *   "hkCustNo":"HK10086",
 *   "fundCode":"000001",
 *   "modifyReason":"客户要求修改协议参数"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {
 *   "code":"0000",
 *   "data":{},
 *   "description":"协议参数修改成功"
 * }
 */
public interface CounterParamAgreementModifyFacade extends BaseFacade<CounterParamAgreementModifyRequest, CounterParamAgreementModifyResponse> {

}
