package com.howbuy.dtms.order.client.facade.query.payment;

import com.howbuy.dtms.order.client.domain.request.payment.QueryPaymentCheckResultRequest;
import com.howbuy.dtms.order.client.domain.response.payment.QueryPaymentCheckResultResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payment.QueryPaymentCheckResultFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryPaymentCheckResultFacade
 * @apiName execute()
 * @apiDescription 支付对账结果查询接口，支持多条件分页查询支付对账结果
 * @apiParam (请求体) {String} pmtDealNo 支付订单号
 * @apiParam (请求体) {String} dealNo 订单号
 * @apiParam (请求体) {String} outPmtDealNo 外部支付订单号
 * @apiParam (请求体) {Array} fundCodes 基金代码列表
 * @apiParam (请求体) {String} pmtCheckDt 支付对账日期（必填，格式：yyyyMMdd）
 * @apiParam (请求体) {Array} pmtCompFlags 支付对账标记列表      0-无需对账；1-未对账；2-对账完成；3-对账不平
 * @apiParam (请求体) {String} orderType 订单类型 订单类型 1-交易 2-edda入金
 * @apiParam (请求体) {Number} page
 * @apiParam (请求体) {Number} size
 * @apiParam (请求体) {String} txCode 交易码
 * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求体) {String} appTm 申请时间 HHmmss
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress ipAddress
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {String} macAddress MAC地址
 * @apiParam (请求体) {String} deviceSerialNo 设备序列号
 * @apiParam (请求体) {String} deviceModel 设备型号
 * @apiParam (请求体) {String} deviceName 设备名称
 * @apiParam (请求体) {String} systemVersion 系统版本号
 * @apiParamExample 请求体示例
 * {"fundCodes":["DhN932Zh4"],"externalDealNo":"Lg","pmtCheckDt":"Mv4FeI","ipAddress":"z51cntxk","dealNo":"K9CELv","deviceName":"D25","systemVersion":"Macoa2g","pmtDealNo":"N2WxUN","appTm":"RrvGS","macAddress":"6Junu1O2","size":1381,"deviceSerialNo":"3isz8o","outPmtDealNo":"H","appDt":"hy","deviceModel":"Sv","page":2065,"txCode":"a1hIpR","outletCode":"b68k1","pmtCompFlags":["xoQ"],"tradeChannel":"65Wy"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Number} data.total 总记录数
 * @apiSuccess (响应结果) {Number} data.pages 总页数
 * @apiSuccess (响应结果) {Array} data.list 结果
 * @apiSuccess (响应结果) {Number} data.list.pmtDealNo 支付订单号
 * @apiSuccess (响应结果) {Number} data.list.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.list.middleBusiCode 中台业务代码
 * @apiSuccess (响应结果) {String} data.list.paymentTypeList 支付方式列表          111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
 * @apiSuccess (响应结果) {String} data.list.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.list.custName 客户姓名
 * @apiSuccess (响应结果) {String} data.list.cpAcctNo 资金账号
 * @apiSuccess (响应结果) {String} data.list.fundTxAcctNo 基金交易账号
 * @apiSuccess (响应结果) {String} data.list.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.list.fundAddr 基金简称
 * @apiSuccess (响应结果) {String} data.list.currency 币种
 * @apiSuccess (响应结果) {String} data.list.appDtm 申请时间
 * @apiSuccess (响应结果) {String} data.list.pmtAmt 支付金额
 * @apiSuccess (响应结果) {String} data.list.pmtCheckDt 支付对账日期
 * @apiSuccess (响应结果) {String} data.list.outPmtDealNo 外部支付订单号
 * @apiSuccess (响应结果) {String} data.list.outPmtAmt 外部支付金额
 * @apiSuccess (响应结果) {String} data.list.outCurrency 外部币种
 * @apiSuccess (响应结果) {String} data.list.outPmtFlag 外部支付标识          1-待支付 2-支付成功 3-支付失败 4-支付中
 * @apiSuccess (响应结果) {String} data.list.txPmtFlag 交易支付标识          0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；5-已退款;6-等待付款；7-撤单成功；
 * @apiSuccess (响应结果) {String} data.list.pmtCompFlag 支付对账标记          0-无需对账；1-未对账；2-对账完成；3-对账不平;
 * @apiSuccess (响应结果) {String} data.list.memo 备注
 * @apiSuccessExample 响应结果示例
 * {"code":"2gXe5TZC1","data":{"total":4608,"pages":1983,"list":[{"middleBusiCode":"FVht","pmtAmt":"Ep","pmtCheckDt":"Rm3g","hkCustNo":"wL8","outPmtFlag":"CNOa0kDO","memo":"Hl8","dealNo":7672,"custName":"sp668","pmtDealNo":7469,"fundTxAcctNo":"IV3","fundAddr":"HzVv","paymentTypeList":"7QnaHijGzl","fundCode":"OkaTx8BO","pmtCompFlag":"RTGw","appDtm":"5EIuG57h","outPmtDealNo":"moagW","txPmtFlag":"FWmJ","cpAcctNo":"ktX5H","currency":"4HYW8p8BP","outCurrency":"t","outPmtAmt":"F"}]},"description":"E358pRrjNG"}
 */
public interface QueryPaymentCheckResultFacade extends BaseFacade<QueryPaymentCheckResultRequest, QueryPaymentCheckResultResponse> {

}
