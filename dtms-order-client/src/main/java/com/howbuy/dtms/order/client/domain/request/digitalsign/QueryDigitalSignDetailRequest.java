package com.howbuy.dtms.order.client.domain.request.digitalsign;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (查询海外产品电子签约详情接口)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryDigitalSignDetailRequest extends BaseRequest {
    /**
     * 签约订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "签约订单号", isRequired = true)
    private String signDealNo;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
}