/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/26 18:10
 * @since JDK 1.8
 */

@Setter
@Getter
public class HwPayVoucherAuditDetailDTO {

    /**
     * 字段key
     */
    private String fieldName;

    /**
     * 字段描述
     */
    private String fieldDesc;

    /**
     * 是否通过 0-不通过 1-通过
     */
    private boolean passFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 字段类型 0-打款凭证属性字段 1-打款凭证文件字段
     */
    private String fieldType;
}