package com.howbuy.dtms.order.client.facade.trade.sell;

import com.howbuy.dtms.order.client.facade.BaseFacade;
import com.howbuy.dtms.order.client.domain.request.sell.PiggyAppSellRequest;
import com.howbuy.dtms.order.client.domain.response.sell.PiggyAppSellResponse;

/**
 * <AUTHOR>
 * @description 储蓄罐申请卖出
 * @date 2024/8/13 19:56
 * @since JDK 1.8
 */
public interface PiggyAppSellFacade extends BaseFacade<PiggyAppSellRequest, PiggyAppSellResponse> {
}
