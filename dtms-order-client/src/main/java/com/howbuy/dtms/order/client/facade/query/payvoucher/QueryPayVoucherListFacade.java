/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.payvoucher;

import com.howbuy.dtms.order.client.domain.request.payvoucher.PayVoucherListPageRequest;
import com.howbuy.dtms.order.client.domain.response.payvoucher.PayVoucherListPageResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;


/**
 * <AUTHOR>
 * @description: App查询打款凭证列表接口
 * @date 2024/7/23 10:00
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payvoucher.QueryPayVoucherListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryPayVoucherListFacade
 * @apiName execute()
 * @apiDescription App查询打款凭证列表接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {Array} voucherTypeList 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
 * @apiParam (请求参数) {Array} auditStatusList 审核状态 2-等待复核、3-审核通过、4-审核不通过、6-驳回至客户、7-作废 不传即所有
 * @apiParam (请求参数) {Array} tradeChannelList 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP；12-H5    不传即所有
 * @apiParam (请求参数) {Number} pageNo 当前页码
 * @apiParam (请求参数) {Number} pageSize 每页条数
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * voucherTypeList=Mi5kbOAZw&externalDealNo=A&tradeChannelList=XKs49RuvS&hkCustNo=OUxVECOPU&auditStatusList=GyvyejK2&ipAddress=e3215&pageSize=2344&deviceName=SFg&systemVersion=UfW112&appTm=Tr&macAddress=djHsxaT35o&deviceSerialNo=AQ&pageNo=8879&appDt=xu&deviceModel=xPsNIr&txCode=3PVrk&outletCode=uLoqTYHQm&tradeChannel=vOrmywh
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.total 总条数
 * @apiSuccess (响应结果) {Array} data.payVoucherListResponses 打款凭证列表查询
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.voucherNo 打款凭证订单号
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.appDate 申请日期
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.appTime 申请时间
 * @apiSuccess (响应结果) {Number} data.payVoucherListResponses.remitAmt 汇款金额
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.voucherType 打款凭证类型: 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.remitCurrency 汇款币种
 * @apiSuccess (响应结果) {Number} data.payVoucherListResponses.actualPayAmt 实际到账金额
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.actualPayCurrency 实际到账币种
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.auditStatus 审核状态
 * @apiSuccess (响应结果) {String} data.payVoucherListResponses.auditReason 审核原因
 * @apiSuccessExample 响应结果示例
 * {"code":"nOn","data":{"total":"ZPEoTssNhj","payVoucherListResponses":[{"voucherNo":"qvPImhL6tW","remitCurrency":"9k","appTime":"0","auditReason":"d","voucherType":"q4VWZKwVI","auditStatus":"X3K0pGoS","appDate":"bUQb","actualPayAmt":7690.881939258001,"remitAmt":6554.732136810037,"actualPayCurrency":"PfuFBegb"}]},"description":"V6cHQASgSy"}
 */
public interface QueryPayVoucherListFacade extends BaseFacade<PayVoucherListPageRequest, PayVoucherListPageResponse> {

}
