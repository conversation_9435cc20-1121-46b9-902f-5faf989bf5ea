/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.payvoucher;

import com.howbuy.dtms.order.client.domain.request.payvoucher.WebPayVoucherListRequest;
import com.howbuy.dtms.order.client.domain.response.payvoucher.WebPayVoucherListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/8/29 9:40
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payvoucher.WebQueryPayVoucherListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup WebQueryPayVoucherListFacade
 * @apiName execute()
 * @apiDescription 查询打款凭证列表，使用方：CRM
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {Array} voucherTypeList 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
 * @apiParam (请求体) {Array} auditStatusList 审核状态 2-等待复核、3-审核通过、4-审核不通过、6-驳回至客户、7-作废 不传即所有
 * @apiParam (请求体) {Array} tradeChannelList 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP；12-H5    不传即所有
 * @apiParam (请求体) {String} txCode 交易码
 * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求体) {String} appTm 申请时间 HHmmss
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress ipAddress
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {String} macAddress MAC地址
 * @apiParam (请求体) {String} deviceSerialNo 设备序列号
 * @apiParam (请求体) {String} deviceModel 设备型号
 * @apiParam (请求体) {String} deviceName 设备名称
 * @apiParam (请求体) {String} systemVersion 系统版本号
 * @apiParamExample 请求体示例
 * {"voucherTypeList":["kxU"],"externalDealNo":"8zm7KP5M","tradeChannelList":["GGM0"],"hkCustNo":"wzvCaIg14C","auditStatusList":["0kBYQtQS4I"],"ipAddress":"kqjNz5vI9V","deviceName":"2","systemVersion":"N","appTm":"Gy6","macAddress":"WUYyVIwr8J","deviceSerialNo":"ty0loOMEI8","appDt":"ww","deviceModel":"kq4","txCode":"OZIl6","outletCode":"1","tradeChannel":"kms"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.payVoucherList 打款凭证列表查询
 * @apiSuccess (响应结果) {String} data.payVoucherList.voucherNo 打款凭证订单号
 * @apiSuccess (响应结果) {String} data.payVoucherList.bankAcctMask 银行卡号 掩码
 * @apiSuccess (响应结果) {String} data.payVoucherList.appDate 申请日期
 * @apiSuccess (响应结果) {String} data.payVoucherList.appTime 申请时间
 * @apiSuccess (响应结果) {Number} data.payVoucherList.remitAmt 汇款金额
 * @apiSuccess (响应结果) {String} data.payVoucherList.remitCurrency 汇款币种
 * @apiSuccess (响应结果) {String} data.payVoucherList.receiptSerialNo 入账流水号
 * @apiSuccess (响应结果) {String} data.payVoucherList.auditStatus 审核状态
 * @apiSuccess (响应结果) {String} data.payVoucherList.tradeChannel 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP
 * @apiSuccess (响应结果) {String} data.payVoucherList.remark 备注
 * @apiSuccessExample 响应结果示例
 * {"code":"7fGef","data":{"payVoucherList":[{"voucherNo":"oYofBUV","receiptSerialNo":"E9HJexn","remitCurrency":"ZTjH3ICZ9p","appTime":"wbTIFe","bankAcctMask":"oWz","auditStatus":"mru4Bp","appDate":"X","remark":"x3rSfla","remitAmt":215.*************,"tradeChannel":"6bgDLUa63"}]},"description":"T9q6"}
 */
public interface WebQueryPayVoucherListFacade extends BaseFacade<WebPayVoucherListRequest, WebPayVoucherListResponse> {

}
