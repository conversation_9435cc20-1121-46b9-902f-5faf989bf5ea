package com.howbuy.dtms.order.client.facade.query.traderecord;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryDealOrderListForTradeListRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.QueryDealOrderListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.traderecord.QueryDealOrderListForAppTradeListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryDealOrderListForAppTradeListFacade
 * @apiName execute()
 * @apiDescription APP交易记录列表查询接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} orderStatus 订单状态
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {Array} fundCategoryList 基金类别列表      1-公募、2-私募、9-其他
 * @apiParam (请求参数) {Array} busiTypeList 业务类型列表      1-买入、2-卖出、9-其他
 * @apiParam (请求参数) {Array} tradeStatusList 交易状态列表      1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单、9-其他
 * @apiParam (请求参数) {Array} holdStatus 持仓状态      1-持有、2-已清仓
 * @apiParam (请求参数) {String} orderStartDt 订单开始时间      格式：YYYYMMdd
 * @apiParam (请求参数) {String} orderEndDt 订单结束时间      格式：YYYYMMdd
 * @apiParam (请求参数) {String} fundTxCode 基金交易账号
 * @apiParam (请求参数) {Number} page
 * @apiParam (请求参数) {Number} size
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=R&hkCustNo=v2Jl3&ipAddress=iSeqq&orderStatus=E&deviceName=9uRvX0qTB&systemVersion=0t&fundCategoryList=r7uAl&appTm=2&macAddress=rKH3cw&holdStatus=b&size=2295&deviceSerialNo=X5Dqq2&fundCode=VDOy&orderEndDt=UF&fundTxCode=pKrCijSXb&appDt=Pm2QxB3&deviceModel=Os6&page=5604&busiTypeList=uT0Cvrp72D&orderStartDt=aQSzq&txCode=1hFqAg&outletCode=nf&tradeStatusList=K48nLmR&tradeChannel=CtT7
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.dealOrderList 订单列表信息
 * @apiSuccess (响应结果) {String} data.dealOrderList.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
 * @apiSuccess (响应结果) {String} data.dealOrderList.appDt 申请日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.appTm 申请时间
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundShortName 基金简称
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.currency 币种代码
 * @apiSuccess (响应结果) {Number} data.dealOrderList.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.dealOrderList.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appVol 申请份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackVol 确认份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackAmt 确认金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.payStatus 支付状态
 * @apiSuccess (响应结果) {String} data.dealOrderList.redeemType 赎回方式 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundDivMode 分红方式 0-红利再投；1-现金红利
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundName 转入基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundCode 转入基金代码
 * @apiSuccessExample 响应结果示例
 * {"code":"UFAabTRZ3","data":{"dealOrderList":[{"ackVol":1508.859983241998,"appAmt":3711.4954090193664,"intoFundCode":"6","orderStatus":"tqgtJ","fundShortName":"KY51GZCME","fundDivMode":"H3X9NX","intoFundName":"Bi","dealNo":4819,"appTm":"uS37e2","fundCode":"Xo7gfPoC","ackAmt":4145.014541875304,"appVol":8943.990861342805,"redeemType":"iAB","appDt":"RAgij","currency":"I","businessType":"QMBA6tZ","fundName":"qDA","payStatus":"A"}]},"description":"ZW"}
 */
public interface QueryDealOrderListForAppTradeListFacade extends BaseFacade<QueryDealOrderListForTradeListRequest, QueryDealOrderListResponse> {

}
