package com.howbuy.dtms.order.client.domain.request.payment;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 重置交易支付标识请求
 * <AUTHOR>
 * @date 2025-07-07 19:02:55
 * @since JDK 1.8
 */
@Setter
@Getter
public class ResetTxPmtFlagRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 支付订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "支付订单号", isRequired = true)
    private String pmtDealNo;
}
