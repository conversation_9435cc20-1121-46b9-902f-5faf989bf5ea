package com.howbuy.dtms.order.client.domain.request.buy;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 柜台导入-储蓄罐买入请求类
 * @date 2024/8/13 17:11
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PiggyAppBuyRequest extends BaseRequest {

    public PiggyAppBuyRequest() {
        setTxCode(OrderTxCodes.HW_ORDER_PIGGY_BUY);
    }

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 支付方式  1-电汇、2-支票、3-海外储蓄罐
     */
    private String payMethod;

    /**
     * 买入金额
     */
    private BigDecimal buyAmt;

    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 折扣率
     */
    private BigDecimal discountRate;

    /**
     * 关联订单号
     */
    private String relationalDealNo;

    /**
     * 操作人
     */
    private String operator;
}
