/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundsreceived;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 资金到账匹配响应
 * <AUTHOR>
 * @date 2024/7/23 10:59
 * @since JDK 1.8
 */
@Getter
@Setter
public class ConfirmFundsReceivedResponse implements Serializable {

    private static final long serialVersionUID = -8428152516149003724L;
    /**
     * 描述
     */
    private String description;

    /**
     * 二次确认，需要用到的字段
     */
    private String field;


}