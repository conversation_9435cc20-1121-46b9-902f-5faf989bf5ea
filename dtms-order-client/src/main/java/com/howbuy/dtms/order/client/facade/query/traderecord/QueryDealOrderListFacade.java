package com.howbuy.dtms.order.client.facade.query.traderecord;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryDealOrderListRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.QueryDealOrderListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.traderecord.QueryDealOrderListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryDealOrderListFacade
 * @apiName execute()
 * @apiDescription QueryDealOrderListFacade
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} orderStatus 订单状态
 * @apiParam (请求参数) {Number} dealNo 订单好哦
 * @apiParam (请求参数) {Array} middleBusicodeList 业务码列表
 * @apiParam (请求参数) {Array} fundCodes 基金代码
 * @apiParam (请求参数) {Number} page
 * @apiParam (请求参数) {Number} size
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * fundCodes=fD3jFX&externalDealNo=OPpBzXs&hkCustNo=8Vk&ipAddress=Oklu&orderStatus=Tni&dealNo=5522&deviceName=O&systemVersion=0&appTm=ZtuK9wRcz&macAddress=oFhV&middleBusicodeList=2DFPG627&size=1781&deviceSerialNo=4&appDt=jtxfBf&deviceModel=KfFKjwCQW&page=3571&txCode=0Zcs&outletCode=6t16y94&tradeChannel=fE9LKhG9iI
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.dealOrderList 订单列表信息
 * @apiSuccess (响应结果) {String} data.dealOrderList.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
 * @apiSuccess (响应结果) {String} data.dealOrderList.appDt 申请日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.appTm 申请时间
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundShortName 基金简称
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.currency 币种代码
 * @apiSuccess (响应结果) {Number} data.dealOrderList.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.dealOrderList.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appVol 申请份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackVol 确认份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackAmt 确认金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.payStatus 支付状态
 * @apiSuccess (响应结果) {String} data.dealOrderList.redeemType 赎回方式 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundDivMode 分红方式 0-红利再投；1-现金红利
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundName 转入基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundCode 转入基金代码
 * @apiSuccessExample 响应结果示例
 * {"code":"hZQ7laxsXa","data":{"dealOrderList":[{"ackVol":9511.462931634538,"appAmt":1351.901984186842,"intoFundCode":"F","orderStatus":"f","fundShortName":"DOu6QH","fundDivMode":"c","intoFundName":"lhW2R2xD","dealNo":9126,"appTm":"WwroAmqqLn","fundCode":"DIjRc","ackAmt":4932.567594237333,"appVol":4452.374406272707,"redeemType":"qx3sj","appDt":"Svb7","currency":"q7o7Iv6J","businessType":"nwUp6odRE2","fundName":"vYRTo38k","payStatus":"De7V6"}]},"description":"ff"}
 */
public interface QueryDealOrderListFacade extends BaseFacade<QueryDealOrderListRequest, QueryDealOrderListResponse> {
}
