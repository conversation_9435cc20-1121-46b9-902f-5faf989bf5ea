/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.buy;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: APP实缴代办消息接口请求参数
 * @author: jinqing.rao
 * @date: 2025/4/29 9:39
 * @since JDK 1.8
 */
@Setter
@Getter
public class ChangePayMethodPageRequest extends BaseRequest {

    private static final long serialVersionUID = -6980689824036509421L;

    public ChangePayMethodPageRequest() {
        this.setTxCode(OrderTxCodes.HW_PAID_PENDING_MESSAGE_DETAIL);
    }

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中台业务码", isRequired = true)
    private String dealNo;

}
