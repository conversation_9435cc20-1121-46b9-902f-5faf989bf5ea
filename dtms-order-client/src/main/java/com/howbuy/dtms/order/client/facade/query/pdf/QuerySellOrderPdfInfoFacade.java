package com.howbuy.dtms.order.client.facade.query.pdf;

import com.howbuy.dtms.order.client.domain.request.pdf.QuerySellOrderPdfInfoRequest;
import com.howbuy.dtms.order.client.domain.response.pdf.QuerySellOrderPdfInfoResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.pdf.QuerySellOrderPdfInfoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QuerySellOrderPdfInfoFacade
 * @apiName execute()
 * @apiDescription 查询赎回订单PDF信息
 * @apiParam (请求参数) {String} dealNo 交易编号
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParamExample 请求参数示例
 * {
 *   "dealNo": "1234567890",
 *   "hkCustNo": "HKCUST001"
 * }
 * @apiSuccess (响应结果) {String} hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} custName 客户名称
 * @apiSuccess (响应结果) {String} productName 产品名称
 * @apiSuccess (响应结果) {String} productVol 产品份额
 * @apiSuccess (响应结果) {String} redeemType 赎回方式
 * @apiSuccess (响应结果) {String} redeemAmt 赎回金额
 * @apiSuccess (响应结果) {String} currency 币种
 * @apiSuccess (响应结果) {String} currencyDesc 币种描述
 * @apiSuccess (响应结果) {String} redeemVol 赎回份额
 * @apiSuccess (响应结果) {String} tradeDt 交易日期，YYYY-MM-DD
 * @apiSuccess (响应结果) {String} appDt 申请日期
 * @apiSuccessExample 响应结果示例
 * {
 *   "hkCustNo": "HKCUST001",
 *   "custName": "John Doe",
 *   "productName": "Fund A",
 *   "productVol": "1000",
 *   "redeemType": "Full",
 *   "redeemAmt": "10000.00",
 *   "currency": "USD",
 *   "currencyDesc": "United States Dollar",
 *   "redeemVol": "1000",
 *   "tradeDt": "2024-05-10",
 *   "appDt": "2024-05-09"
 * }
 */
public interface QuerySellOrderPdfInfoFacade extends BaseFacade<QuerySellOrderPdfInfoRequest, QuerySellOrderPdfInfoResponse> {
}