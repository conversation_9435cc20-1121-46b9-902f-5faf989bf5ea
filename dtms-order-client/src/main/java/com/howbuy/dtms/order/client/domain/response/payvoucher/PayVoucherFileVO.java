/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.payvoucher;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/4/10 16:27
 * @since JDK 1.8
 */
@Data
public class PayVoucherFileVO implements Serializable {

    private static final long serialVersionUID = 8501351269274399225L;
    /**
     * 文件id
     */
    private String fileId;
    /**
     * 文件路径
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "文件路径", isRequired = true)
    private String filePath;

    /**
     * 文件路径
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "文件名", isRequired = true)
    private String fileName;

    /**
     * 文件后缀
     */
    private String fileSuffix;

}
