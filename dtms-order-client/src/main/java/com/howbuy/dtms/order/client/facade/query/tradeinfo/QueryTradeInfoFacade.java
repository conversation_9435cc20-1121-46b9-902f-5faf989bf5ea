/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.tradeinfo;

import com.howbuy.dtms.order.client.domain.request.tradeinfo.QueryOpenDtInfoRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.tradeinfo.QueryOpenDtInfoResponse;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/13 16:05
 * @since JDK 1.8
 */
public interface QueryTradeInfoFacade {

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.tradeinfo.QueryTradeInfoFacade.queryOpenDtInfo()
     * @apiVersion 1.0.0
     * @apiGroup TradeInfoFacadeImpl
     * @apiName queryOpenDtInfo()
     * @apiDescription 查询开放日信息
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} payMethod 支付方式  1-电汇、2-支票、3-海外储蓄罐
     * @apiParam (请求体) {String} middleBusinessCode 中台业务码
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"pZ9xRk","hkCustNo":"mCLVPD","ipAddress":"UeUMvXfBRD","deviceName":"TsCTXy2I","systemVersion":"L9XiEP1","appTm":"ES3rROL0mS","middleBusinessCode":"GKw","macAddress":"nlsQeWm","deviceSerialNo":"nSRGhh","fundCode":"Njc4CWB4V","payMethod":"KHTKsWgCK","appDt":"p6","deviceModel":"3FSU3MP","txCode":"bHcCGG37K","outletCode":"9dJVwtfze","tradeChannel":"IuNTpcC"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.advanceStartDt 预约开始日期
     * @apiSuccess (响应结果) {String} data.advanceStartTm 预约开始时间
     * @apiSuccess (响应结果) {String} data.advanceEndDt 预约结束日期
     * @apiSuccess (响应结果) {String} data.advanceEndTm 预约结束时间
     * @apiSuccess (响应结果) {String} data.openStartDt 开放开始日期
     * @apiSuccess (响应结果) {String} data.openEndDt 开放结束日期
     * @apiSuccess (响应结果) {String} data.paymentDeadlineDt 打款截止日期
     * @apiSuccess (响应结果) {String} data.paymentDeadlineTime 打款截止时间
     * @apiSuccess (响应结果) {String} data.preSubmitTaDt 预计上报日期
     * @apiSuccess (响应结果) {String} data.preSubmitTaTm 预计上报时间
     * @apiSuccess (响应结果) {String} data.firstPayInRatio 首次实缴比例
     * @apiSuccess (响应结果) {String} data.openDt 开放日
     * @apiSuccess (响应结果) {String} data.fundStat 基金状态 0-可申购赎回、1-发行、4-停止交易、5-停止申购、6-停止赎回、8-基金终止、9-基金封闭
     * @apiSuccessExample 响应结果示例
     * {"code":"r8foO5P7My","data":{"advanceStartDt":"FhWC7nU0F","preSubmitTaTm":"B5M6pC","advanceEndTm":"7rRRjKPmMq","paymentDeadlineDt":"PJLU","openStartDt":"y1pnDZF0","advanceStartTm":"dSGfnMy7","advanceEndDt":"Obso","preSubmitTaDt":"7ekHLUf","openDt":"mmsTZzyt9","openEndDt":"reT","paymentDeadlineTime":"d4544uV"},"description":"0VRV"}
     */
    Response<QueryOpenDtInfoResponse> queryOpenDtInfo(QueryOpenDtInfoRequest request);


}
