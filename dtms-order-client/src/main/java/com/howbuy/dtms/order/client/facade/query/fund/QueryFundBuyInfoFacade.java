package com.howbuy.dtms.order.client.facade.query.fund;

import com.howbuy.dtms.order.client.domain.request.buy.QueryFundBuyInfoRequest;
import com.howbuy.dtms.order.client.domain.response.buy.QueryFundBuyInfoResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fund.QueryFundBuyInfoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundBuyInfoFacadeImpl
 * @apiName execute()
 * @apiDescription 查询产品基本购买信息
 * @apiParam (请求体) {String} hboneNo 一账通号
 * @apiParam (请求体) {List} fundCodeList 基金代码列表
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress IP地址
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {String} macAddress MAC地址
 * @apiParam (请求体) {String} deviceSerialNo 设备序列号
 * @apiParam (请求体) {String} deviceModel 设备型号
 * @apiParam (请求体) {String} deviceName 设备名称
 * @apiParam (请求体) {String} systemVersion 系统版本号
 * @apiParamExample 请求体示例
 * {"hboneNo":"HB123456789","fundCodeList":["000001","000002"],"tradeChannel":"9","outletCode":"001","ipAddress":"***********","externalDealNo":"EXT001","macAddress":"00:11:22:33:44:55","deviceSerialNo":"SN12345","deviceModel":"PC","deviceName":"Windows","systemVersion":"10"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.fundBuyInfoList 基金信息结果列表
 * @apiSuccess (响应结果) {String} data.fundBuyInfoList.fundCode 基金代码
 * @apiSuccess (响应结果) {BigDecimal} data.fundBuyInfoList.minAppAmt 购买限额
 * @apiSuccess (响应结果) {String} data.fundBuyInfoList.fundRiskLevel 产品风险等级
 * @apiSuccess (响应结果) {String} data.fundBuyInfoList.firstAppFlag 首次购买标识 0-非首次，1-首次
 * @apiSuccess (响应结果) {String} data.fundBuyInfoList.currency 币种  156-人民币、344-港元、392-日元、826-英镑、840-美元、978-欧元
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{"fundBuyInfoList":[{"fundCode":"000001","minAppAmt":1000.00,"fundRiskLevel":"R3","firstAppFlag":"1"},{"fundCode":"000002","minAppAmt":5000.00,"fundRiskLevel":"R2","firstAppFlag":"0"}]},"description":"成功"}
 */
public interface QueryFundBuyInfoFacade extends BaseFacade<QueryFundBuyInfoRequest, QueryFundBuyInfoResponse> {

} 