package com.howbuy.dtms.order.client.facade.query.querybuy;

import com.howbuy.dtms.order.client.domain.request.buy.QueryBuyInfoRequest;
import com.howbuy.dtms.order.client.domain.response.buy.BuyInfoVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.querybuy.QueryBuyInfoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryBuyInfoFacade
 * @apiName execute()
 * @apiDescription 下单页面信息查询接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} businessType 业务类型
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=oJeg&hkCustNo=u7eM9fkDwM&ipAddress=XQl&deviceName=UIPaj&systemVersion=ZPoF6&appTm=yPJ7Mzg&macAddress=Fz3nuaNN3&deviceSerialNo=plEhBYk&fundCode=yw&appDt=A&deviceModel=LKaIffXc8&businessType=3n&txCode=Bllcl&outletCode=Ay08Gl&tradeChannel=MMPu
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Object} data.buyFundInfoVO 买入基金信息
 * @apiSuccess (响应结果) {String} data.buyFundInfoVO.openStartDt 开放开始日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.buyFundInfoVO.openEndDt 开放结束日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.buyFundInfoVO.payEndDt 打款截止日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.buyFundInfoVO.payEndTm 打款截止时间 HHmmss
 * @apiSuccess (响应结果) {String} data.buyFundInfoVO.tradeDt 交易日期 yyyyMMdd
 * @apiSuccess (响应结果) {Number} data.buyFundInfoVO.minAppAmt 最小申请金额
 * @apiSuccess (响应结果) {Number} data.buyFundInfoVO.maxAppAmt 最大申请金额
 * @apiSuccess (响应结果) {Number} data.buyFundInfoVO.differential 级差
 * @apiSuccess (响应结果) {Object} data.prebookBuyInfoVO 预约购买信息
 * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookDealNo 预约单号
 * @apiSuccess (响应结果) {String} data.prebookBuyInfoVO.prebookPayMethod 预约支付方式 1-电汇、2-支票、3-海外储蓄罐
 * @apiSuccess (响应结果) {Number} data.prebookBuyInfoVO.prebookAppAmt 预约申请金额
 * @apiSuccess (响应结果) {Number} data.prebookBuyInfoVO.prebookDiscountRate 预约折扣率
 * @apiSuccess (响应结果) {Object} data.payMethodInfoVO 支付方式信息
 * @apiSuccess (响应结果) {String} data.payMethodInfoVO.hasSignCxg 是否签约储蓄罐 0-未签署 1-签署
 * @apiSuccess (响应结果) {String} data.payMethodInfoVO.isSupportCxgPay 是否支持储蓄罐支付 0-不支持 1-支持（是否签约储蓄罐1-签署 且 支持预约才有值）
 * @apiSuccess (响应结果) {Array} data.payMethodInfoVO.cxgCurrencyList 储蓄罐币种列表 156-人民币、344-港元、392-日元、826-英镑、840-美元、978欧元 （是否签约储蓄罐1-签署才有值）
 * @apiSuccess (响应结果) {String} data.payMethodInfoVO.cxgOrderEndTm 储蓄罐下单结束时间 HHmmss （是否签约储蓄罐1-签署才有值）
 * @apiSuccessExample 响应结果示例
 * {"code":"Znv1nQWe","data":{"prebookBuyInfoVO":{"prebookDiscountRate":9343.237736437883,"prebookAppAmt":5871.623031007227,"prebookPayMethod":"FyJRxgi","prebookDealNo":"ia"},"payMethodInfoVO":{"hasSignCxg":"dIAqx","cxgOrderEndTm":"N","cxgCurrencyList":["ffzr1"],"isSupportCxgPay":"e"},"buyFundInfoVO":{"maxAppAmt":4383.050544280391,"tradeDt":"xgCtNzo","payEndTm":"SXx","openStartDt":"ELnk31LxW","payEndDt":"F","openEndDt":"x9MyKrmuW","minAppAmt":36.85694795312444,"differential":2787.924683114108}},"description":"lLRm8Q8HO"}
 */
public interface QueryBuyInfoFacade extends BaseFacade<QueryBuyInfoRequest, BuyInfoVO> {
}
