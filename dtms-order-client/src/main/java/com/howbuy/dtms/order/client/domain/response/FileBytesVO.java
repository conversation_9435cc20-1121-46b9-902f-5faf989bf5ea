/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response;

import lombok.Data;

import java.io.Serializable;

/**
 * @description:(导出文件流 )
 * @param
 * @return 
 * @author: haoran.zhang
 * @date: 2023/9/19 16:29
 * @since JDK 1.8
 */
@Data
public class FileBytesVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 文件流
     */
    private String fileBytes;
}
