/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.PageRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/12/9 10:26
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryDealOrderListForTradeListRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = -833610993765367311L;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金类别列表
     * 1-公募、2-私募、9-其他
     */
    private List<String> fundCategoryList;

    /**
     * 业务类型列表
     * 1-买入、2-卖出、9-其他
     */
    private List<String> busiTypeList;

    /**
     * 交易状态列表
     * 1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单
     */
    private List<String> tradeStatusList;

    /**
     * 持仓状态
     * 1-持有、2-已清仓
     */
    private List<String> holdStatus;

    /**
     * 订单开始时间
     * 格式：YYYYMMdd
     */
    private String orderStartDt;

    /**
     * 订单结束时间
     * 格式：YYYYMMdd
     */
    private String orderEndDt;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

}