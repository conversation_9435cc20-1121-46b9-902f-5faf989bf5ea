package com.howbuy.dtms.order.client.facade.trade.sell;

import com.howbuy.dtms.order.client.domain.request.sell.SellWebRequest;
import com.howbuy.dtms.order.client.domain.response.sell.SellWebVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.sell.FundSellWebFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup FundSellWebFacade
 * @apiName execute()
 * @apiDescription APP基金下单接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} cpAcctNo 资金账号 赎回方向为1-电汇时必填
 * @apiParam (请求参数) {String} txPassword 交易密码
 * @apiParam (请求参数) {String} redeemMethod 赎回方式 1-按份额赎回；2-按金额赎回
 * @apiParam (请求参数) {String} redeemDirection 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
 * @apiParam (请求参数) {Number} appAmt 申请金额
 * @apiParam (请求参数) {Number} appVol 申请份额
 * @apiParam (请求参数) {String} prebookDealNo 预约单号
 * @apiParam (请求参数) {String} dealNo 订单号
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * txPassword=CDNSOGF&externalDealNo=sxblzrd&appAmt=1991.0572079918566&hkCustNo=4aJd&ipAddress=70N9&dealNo=DVk&deviceName=nta&systemVersion=f9f&redeemDirection=SS&prebookDealNo=Ze5UR&appTm=dSUEbowkjX&macAddress=LVF9inT0&redeemMethod=I1AHtEQiy&deviceSerialNo=dQWSO&fundCode=Vey51&appVol=9121.914366004019&cpAcctNo=gLSJ7&appDt=93xAOfEHF&deviceModel=EyDPTjME&txCode=hC&outletCode=D15IIN&tradeChannel=P8rF4uLd
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.dealNo 订单号
 * @apiSuccessExample 响应结果示例
 * {"code":"iZhzdLPgk","data":{"dealNo":"W91CGu50t"},"description":"W"}
 */
public interface FundSellWebFacade extends BaseFacade<SellWebRequest, SellWebVO> {

}
