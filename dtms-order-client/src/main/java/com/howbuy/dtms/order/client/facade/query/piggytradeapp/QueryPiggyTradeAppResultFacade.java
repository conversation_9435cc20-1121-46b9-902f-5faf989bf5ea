/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.piggytradeapp.QueryPiggyTradeAppResultRequest;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.QueryPiggyTradeAppResultResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.piggytradeapp.QueryPiggyTradeAppResultFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryPiggyTradeAppResultFacade
 * @apiName execute()
 * @apiDescription 查询储蓄罐交易申请结果接口
 * @apiParam (请求体) {Array} importAppIds 导入申请Id列表
 * @apiParamExample 请求体示例
 * {"importAppIds":["APP001","APP002"]}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.list PiggyTradeAppResultVO列表
 * @apiSuccess (响应结果) {String} data.list.importAppId 导入申请Id
 * @apiSuccess (响应结果) {String} data.list.isGenerated 是否已生成(0-未生成 1-已生成 2-生成失败)
 * @apiSuccess (响应结果) {String} data.list.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.list.buyAmt 购买金额
 * @apiSuccess (响应结果) {String} data.list.fee 手续费
 * @apiSuccess (响应结果) {String} data.list.remark 备注
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{"list":[{"importAppId":"APP001","isGenerated":"1","dealNo":"123456","buyAmt":"1000.00","fee":"10.00","remark":"测试"}]},"description":"成功"}
 * 
 * @description: 查询储蓄罐交易申请结果接口
 * <AUTHOR>
 * @date 2025-07-15 19:32:38
 * @since JDK 1.8
 */
public interface QueryPiggyTradeAppResultFacade extends BaseFacade<QueryPiggyTradeAppResultRequest, QueryPiggyTradeAppResultResponse> {
}
