/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.fundhold;

import com.howbuy.dtms.order.client.domain.request.fundhold.QueryFundHoldCustListRequest;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryFundHoldCustListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/30 13:52
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryFundHoldCustListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundHoldCustListFacade
 * @apiName 查询基金持仓的用户香港客户号
 * @apiDescription 查询基金持仓的用户香港客户号
 * @apiParam (请求参数) {Array} fundCodeList 基金编码集合
 * @apiParam (请求参数) {Array} hkCustNoList 客户编码集合
 * @apiParamExample 请求参数示例
 * hkCustNoList=t4etn5Fe2a&fundCodeList=y
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.holdCustNoList 持仓客户编码集合
 * @apiSuccessExample 响应结果示例
 * {"code":"SzaKwVKRb","data":{"holdCustNoList":["I6LyT"]},"description":"WG"}
 */
public interface QueryFundHoldCustListFacade extends BaseFacade<QueryFundHoldCustListRequest, QueryFundHoldCustListResponse> {

}
