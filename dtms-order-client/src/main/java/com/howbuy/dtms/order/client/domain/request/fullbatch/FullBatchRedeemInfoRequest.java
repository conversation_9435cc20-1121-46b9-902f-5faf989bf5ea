/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fullbatch;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (全委批量赎回项)
 * <AUTHOR>
 * @date 2025/4/16 14:34
 * @since JDK 1.8
 */
@Setter
@Getter
public class FullBatchRedeemInfoRequest implements Serializable {

    private static final long serialVersionUID = 6127796116717273626L;

    /**
     * 外部订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "外部订单号", isRequired = true)
    private String externalDealNo;

    /**
     * 基金交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 赎回方式
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "赎回方式", isRequired = true)
    private String redeemMethod;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;
}
