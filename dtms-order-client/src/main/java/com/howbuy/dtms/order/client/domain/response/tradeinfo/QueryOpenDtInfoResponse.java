/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.tradeinfo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/13 16:11
 * @since JDK 1.8
 */
@Data
public class QueryOpenDtInfoResponse implements Serializable {

    private static final long serialVersionUID = 6481366090979038232L;

    /**
     * 预约开始日期
     */
    private String advanceStartDt;

    /**
     * 预约开始时间
     */
    private String advanceStartTm;

    /**
     * 预约结束日期
     */
    private String advanceEndDt;

    /**
     * 预约结束时间
     */
    private String advanceEndTm;

    /**
     * 开放开始日期
     */
    private String openStartDt;

    /**
     * 开放结束日期
     */
    private String openEndDt;

    /**
     * 打款截止日期
     */
    private String paymentDeadlineDt;

    /**
     * 打款截止时间
     */
    private String paymentDeadlineTime;

    /**
     * 预计上报日期
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     */
    private String preSubmitTaTm;

    /**
     * 首次实缴比例
     */
    private BigDecimal firstPayInRatio;

    /**
     * 开放日
     */
    private String openDt;

    /**
     * 基金状态
     * 0-可申购赎回、1-发行、4-停止交易、5-停止申购、6-停止赎回、8-基金终止、9-基金封闭
     */
    private String fundStat;


}
