package com.howbuy.dtms.order.client.domain.request.contractsign;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/5/18 17:01
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryCustContractSignRequest extends BaseRequest {

    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 预约单号
     */
    private String signDealNo;
}
