/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.sell;

import com.howbuy.dtms.order.client.domain.request.sell.CanSellRequest;
import com.howbuy.dtms.order.client.domain.response.sell.dubbo.FundCanSellResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: CRM基金是否可赎回接口
 * <AUTHOR>
 * @date 2025/4/25 11:05
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.sell.CrmFundCanSellFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup FundCanSellFacade
 * @apiName execute()
 * @apiDescription CRM基金是否可赎回接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK12345678",
 *     "fundCode": "000001",
 *     "txCode": "022",
 *     "appDt": "20250425",
 *     "appTm": "103045",
 *     "tradeChannel": "11",
 *     "outletCode": "8888",
 *     "ipAddress": "*************",
 *     "externalDealNo": "EXT20250425001",
 *     "macAddress": "00:1B:44:11:3A:B7",
 *     "deviceSerialNo": "SERIAL123456",
 *     "deviceModel": "iPhone 15",
 *     "deviceName": "User's iPhone",
 *     "systemVersion": "iOS 18.0"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "000000",
 *     "description": "处理成功",
 *     "data": {
 *         "canSell": true,
 *         "message": "基金可以赎回",
 *         "minSellAmount": 100.00,
 *         "maxSellAmount": 10000.00,
 *         "availableShare": 5000.00,
 *         "freezeShare": 0.00,
 *         "sellFeeRate": 0.5
 *     }
 * }
 */
public interface CrmFundCanSellFacade extends BaseFacade<CanSellRequest, FundCanSellResponse> {

}
