package com.howbuy.dtms.order.client.domain.response.pdf;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 查询分级赎回订单PDF信息
 * @return
 * @author: jinqing.rao
 * @date: 2025/5/15 14:31
 * @since JDK 1.8
 */

public class QueryGradationCallOrderPdfInfoResponse implements Serializable {

    private static final long serialVersionUID = -5548391612073986004L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品份额
     */
    private String productVol;

    /**
     * 分红方式 0-红利再投 1-现金分红 2-N/A不适用
     */
    private String fundDivMode;

    /**
     * 交易日期，YYYY-MM-DD
     */
    private String tradeDt;

    /**
     * 币种
     */
    private String currency;

    /**
     * 认缴金额
     */
    private BigDecimal subAmt;

    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;

    /**
     * 首次实缴金额
     */
    private BigDecimal paidAmt;

    /**
     * 支付方式
     */
    private String payMethodList;

    /**
     * 申请时间(下单时间)
     */
    private String appDt;

    // Getters and setters
    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductVol() {
        return productVol;
    }

    public void setProductVol(String productVol) {
        this.productVol = productVol;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getSubAmt() {
        return subAmt;
    }

    public void setSubAmt(BigDecimal subAmt) {
        this.subAmt = subAmt;
    }

    public BigDecimal getEstimateFee() {
        return estimateFee;
    }

    public void setEstimateFee(BigDecimal estimateFee) {
        this.estimateFee = estimateFee;
    }

    public BigDecimal getPaidAmt() {
        return paidAmt;
    }

    public void setPaidAmt(BigDecimal paidAmt) {
        this.paidAmt = paidAmt;
    }

    public String getPayMethod() {
        return payMethodList;
    }

    public void setPayMethodList(String payMethod) {
        this.payMethodList = payMethod;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getPayMethodList() {
        return payMethodList;
    }
}