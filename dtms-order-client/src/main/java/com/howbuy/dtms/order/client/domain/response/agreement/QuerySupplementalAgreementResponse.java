package com.howbuy.dtms.order.client.domain.response.agreement;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询未签署补签协议响应对象
 * <AUTHOR>
 * @date 2024/03/06 17:35:21
 * @since JDK 1.8
 */
@Getter
@Setter
public class QuerySupplementalAgreementResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 补充协议列表
     */
    private List<AgreementInfo> agreementList;

    /**
     * 补充协议信息
     */
    @Getter
    @Setter
    public static class AgreementInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 补签协议ID
         */
        private String agreementId;

        /**
         * 协议签署截止时间
         * 格式：YYYY-MM-DD HH:MM
         */
        private String agreementSignEndDt;

        /**
         * 协议名称
         */
        private String agreementName;

        /**
         * 基金Code
         */
        private String fundCode;

        /**
         * 基金简称
         */
        private String fundAbbr;

        /**
         * 协议说明
         */
        private String agreementDescription;

        /**
         * 协议地址
         * 同一个产品，是一样的，所以通过静态链接地址访问
         */
        private String agreementUrl;


        /**
         * 签署状态
         */
        private String signStatus;


        /**
         * 签署时间
         */
        private String signDate;
    }
} 