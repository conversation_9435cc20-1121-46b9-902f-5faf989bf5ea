/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.payvoucher;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/4/10 16:38
 * @since JDK 1.8
 */
@Data
public class PayVoucherInfoVO {

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行英文名称
     */
    private String bankEnName;

    /**
     * 银行中文名称
     */
    private String bankChineseName;

    /**
     * SWIFT代码
     */
    private String swiftCode;

    /**
     * 币种代码
     */
    private String currency;

    /**
     * 汇款金额
     */
    private BigDecimal remitAmt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     */
    private String tradeChannel;

    /**
     * 资料id
     */
    private String orderId;

    /**
     * 文件类型id
     */
    private String fileTypeId;

    /**
     * 打款凭证文件列表
     */
    private List<PayVoucherFileVO> payVoucherFileVOList;

    /**
     * 审核意见
     */
    private String auditOpinion;

    /**
     * 退回原因
     */
    private String returnReason;

    /**
     * 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     */
    private String payVoucherStatus;
}
