package com.howbuy.dtms.order.client.facade.query.balancefactor;

import com.howbuy.dtms.order.client.domain.request.hkbalance.QueryFundHaveBalanceFactorRequest;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryFundHaveBalanceFactorResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.balancefactor.QueryFundHaveBalanceFactorFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundHaveBalanceFactorFacade
 * @apiDescription 查询有平衡因子的基金数据
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Array} data.fundCodeList 基金数据列表
 * @apiSuccessExample 响应结果示例
 * {"code":"Zf5SfE","data":{"fundCodeList":["123","456"]},"description":"S2eLLur"}
 */
public interface QueryFundHaveBalanceFactorFacade extends BaseFacade<QueryFundHaveBalanceFactorRequest, QueryFundHaveBalanceFactorResponse> {
}
