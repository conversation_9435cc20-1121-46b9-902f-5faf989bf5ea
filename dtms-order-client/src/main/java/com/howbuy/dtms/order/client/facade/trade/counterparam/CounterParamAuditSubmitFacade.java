package com.howbuy.dtms.order.client.facade.trade.counterparam;


import com.howbuy.dtms.order.client.domain.request.counterparam.CounterParamBusiAppRecordSubmitRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.counterparam.CounterParamBusiAppRecordSubmitVO;

public interface CounterParamAuditSubmitFacade {

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.counterparam.CounterParamAuditSubmitFacade.submitCounterParamBusiAppRecord()
     * @apiVersion 1.0.0
     * @apiGroup CounterParamAuditSubmitFacade
     * @apiName submitCounterParamBusiAppRecord()
     * @apiDescription 柜台参数业务审核申请提交接口
     * @apiParam (请求参数) {String} appId 业务申请ID
     * @apiParam (请求参数) {String} paramType 参数业务类型
     * @apiParam (请求参数) {String} operateType 操作类型
     * @apiParam (请求参数) {String} auditStatus 审核状态（0-无需审核、2-等待复核、3-审核通过、4-审核不通过、5-驳回至经办、7-作废、8-等待回访）
     * @apiParam (请求参数) {String} keyContent 业务关键内容json
     * @apiParam (请求参数) {String} checker 审核人
     * @apiParam (请求参数) {Number} checkTime 审核日期时间
     * @apiParam (请求参数) {String} remark 审核备注
     * @apiParam (请求参数) {String} creator 创建人
     * @apiParam (请求参数) {Number} createTimestamp 创建时间戳
     * @apiParam (请求参数) {String} modifier 修改人
     * @apiParam (请求参数) {Number} updateTimestamp 更新时间戳
     * @apiParam (请求参数) {String} oldContent 修改前内容  JSON
     * @apiParam (请求参数) {String} newContent 修改后内容 JSON
     * @apiParamExample 请求参数示例
     * creator=QAmPap&modifier=3&operateType=NFfpM&keyContent=59Vob9yMf1&remark=SI&checker=SjNvKde&updateTimestamp=2595458667881&oldContent=mqU&createTimestamp=2400701248415&paramType=V2z&newContent=7I6dkeODg&checkTime=526659416934&appId=P&auditStatus=Mlx
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.appId 申请ID
     * @apiSuccessExample 响应结果示例
     * {"code":"6K","data":{"appId":4945},"description":"pB"}
     */

    Response<CounterParamBusiAppRecordSubmitVO> submitCounterParamBusiAppRecord(CounterParamBusiAppRecordSubmitRequest request);


}
