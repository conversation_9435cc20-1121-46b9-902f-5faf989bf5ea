package com.howbuy.dtms.order.client.domain.request.contractsign;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/5/18 17:01
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryPrebookSignContractRequest extends BaseRequest {

    private static final long serialVersionUID = 8505763468450819446L;
    /**
     * 预约单号
     */
    private String prebookId;
}
