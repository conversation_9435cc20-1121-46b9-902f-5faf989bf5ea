/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fundtxacct;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/13 10:27
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AddFundTxAcctRequest extends BaseRequest {

    private static final long serialVersionUID = 6950824902733093565L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金交易账号类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号类型", isRequired = true)
    private String fundTxAccType;

    /**
     * 基金代码
     */
    private String fundCode;

}
