/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/8/29 9:47
 * @since JDK 1.8
 */
@Getter
@Setter
public class WebPayVoucherVO implements Serializable {

    private static final long serialVersionUID = -43114898197700901L;

    /**
     * 打款凭证订单号
     */
    private String voucherNo;

    /**
     * 打款凭证类型
     */
    private String voucherType;

    /**
     * 银行卡号 掩码
     */
    private String bankAcctMask;

    /**
     * 申请日期
     */
    private String appDate;

    /**
     * 申请时间
     */
    private String appTime;

    /**
     * 汇款金额
     */
    private BigDecimal remitAmt;

    /**
     * 汇款币种
     */
    private String remitCurrency;

    /**
     * 实际到账金额
     */
    private BigDecimal actualPayAmt;

    /**
     * 实际到账币种
     */
    private String actualPayCurrency;

    /**
     * 实际到账日期
     */
    private String actualPayDt;

    /**
     * 实际打款时间
     */
    private String actualPayTm;


    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 入账流水号
     */
    private String receiptSerialNo;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP
     */
    private String tradeChannel;

    /**
     * 备注
     */
    private String remark;


    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建日期时间
     */
    private Date createTime;

    /**
     * 更新日期时间
     */
    private Date updateTime;


}