/**
 * Copyright (c) 2024, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 买入手续费计算信息
 * <AUTHOR>
 * @date 2024/4/10 11:17
 * @since JDK 1.8
 */
@Data
public class BuyFeeComputeInfoVO implements Serializable {

    /**
     * 实际支付金额 
     */
    private BigDecimal actualPayAmt;

    /**
     * 手续费费率
     */
    private BigDecimal feeRate;

    /**
     * 预估手续费 
     */
    private BigDecimal estimateFee;

    /**
     * 原始手续费 
     */
    private BigDecimal originalFee;

    /**
     * 是否大于预约金额 0-否 1-是
     */
    private String isLargerPrebookAmt;

    /**
     * 折扣是否生效 0-否 1-是
     */
    private String validDiscountRate;

    /**
     * 实际折扣率
     */
    private BigDecimal actualDiscountRate;

    /**
     * 折扣类型
     */
    private String discountType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 预约折扣率
     */
    private BigDecimal prebookDiscountRate;


}
