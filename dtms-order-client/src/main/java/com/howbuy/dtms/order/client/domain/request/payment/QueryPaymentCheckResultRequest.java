package com.howbuy.dtms.order.client.domain.request.payment;

import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import com.howbuy.dtms.order.client.domain.request.PageRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 支付对账结果查询请求参数
 *
 * <AUTHOR>
 * @date 2025-07-07 17:13:51
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter
public class QueryPaymentCheckResultRequest extends PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    public QueryPaymentCheckResultRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_DEFAULT);
    }

    /**
     * 支付订单号
     */
    private String pmtDealNo;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 外部支付订单号
     */
    private String outPmtDealNo;

    /**
     * 基金代码列表
     */
    private List<String> fundCodes;

    /**
     * 支付对账日期（必填，格式：yyyyMMdd）
     */
    private String pmtCheckDt;

    /**
     * 支付对账标记列表
     * 0-无需对账；1-未对账；2-对账完成；3-对账不平
     */
    private List<String> pmtCompFlags;

    /**
     * 订单类型 订单类型 1-交易 2-edda入金
     */
    private String orderType;
}
