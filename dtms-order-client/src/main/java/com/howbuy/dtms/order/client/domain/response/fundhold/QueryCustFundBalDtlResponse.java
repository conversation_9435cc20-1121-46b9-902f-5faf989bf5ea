/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/15 19:14
 * @since JDK 1.8
 */
public class QueryCustFundBalDtlResponse implements Serializable {

    private static final long serialVersionUID = 1376326007027436912L;

    public List<CustFundBalDtlVO> getCustFundBalDtlVOList() {
        return custFundBalDtlVOList;
    }

    public void setCustFundBalDtlVOList(List<CustFundBalDtlVO> custFundBalDtlVOList) {
        this.custFundBalDtlVOList = custFundBalDtlVOList;
    }

    /**
     * 客户份额明细列表
     */
    private List<CustFundBalDtlVO> custFundBalDtlVOList = new ArrayList<>();
}
