package com.howbuy.dtms.order.client.facade.query.order;

import com.howbuy.dtms.order.client.domain.request.buy.HwDealOrderFundCodeRequest;
import com.howbuy.dtms.order.client.domain.response.buy.HwDealOrderFundCodeResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 通过订单状态和确认状态查询订单信息
 * @author: jinqing.rao
 * @date: 2024/8/12 13:19
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.order.QueryOrderFundCodeFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryOrderFundCodeFacade
 * @apiName 通过订单状态和确认状态查询订单信息
 * @apiDescription 通过订单状态和确认状态查询订单信息
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {Array} orderStatusList 订单状态
 * @apiParam (请求参数) {Array} ackStatusList 确认状态
 * @apiParamExample 请求参数示例
 * hkCustNo=MzacS3&orderStatusList=53RHH0A&ackStatusList=Z6F
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.fundCodeList 基金编码列表
 * @apiSuccessExample 响应结果示例
 * {"code":"uMMIxo","data":{"fundCodeList":["3iyNqm"]},"description":"zCIZ72IPQa"}
 */
public interface QueryOrderFundCodeFacade extends BaseFacade<HwDealOrderFundCodeRequest, HwDealOrderFundCodeResponse> {
}
