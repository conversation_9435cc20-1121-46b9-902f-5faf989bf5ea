/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.cash;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 查询用户资金总金额
 * <AUTHOR>
 * @date 2024/7/31 17:39
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryHkCashBalanceResponse implements Serializable {

    private static final long serialVersionUID = -6928078584149889922L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 数据日期
     */
    private String dataDt;

    /**
     * 换算后的总资产
     */
    private BigDecimal totalBalance;

    /**
     * 币种余额明细
     */
    private List<QueryEbrokerCustBalanceDtlDTO> queryEbrokerCustBalanceDtlDTO;

    @Setter
    @Getter
    public static class QueryEbrokerCustBalanceDtlDTO implements Serializable {

        private static final long serialVersionUID = -6928078584149889922L;

        /**
         * 货币代码
         */
        private String curCode;

        /**
         * 货币记账余额
         */
        private BigDecimal curBalance;

        /**
         * 在途的余额
         */
        private BigDecimal inTransitBalance;

        /**
         * 币种汇率
         */
        private BigDecimal currencyRate;

        /**
         * 换算后的余额
         */
        private BigDecimal changeBalance;
    }
}
