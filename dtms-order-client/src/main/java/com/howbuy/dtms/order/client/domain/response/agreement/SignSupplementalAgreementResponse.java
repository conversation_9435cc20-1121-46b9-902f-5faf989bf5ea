package com.howbuy.dtms.order.client.domain.response.agreement;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;

/**
 * @description: 线上补签协议响应对象
 * <AUTHOR>
 * @date 2024/03/06 17:35:21
 * @since JDK 1.8
 */
@Getter
@Setter
public class SignSupplementalAgreementResponse implements Serializable {


    private static final long serialVersionUID = 7462433922388406292L;

    /**
     * 签署状态
     */
    private String signStatus;
} 