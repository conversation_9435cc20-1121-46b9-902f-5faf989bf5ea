package com.howbuy.dtms.order.client.facade.query.cashbalance;

import com.howbuy.dtms.order.client.domain.request.cash.QueryHkCashBalanceRequest;
import com.howbuy.dtms.order.client.domain.response.cash.QueryHkCashBalanceResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 香港账户现金余额查询
 * @author: jinqing.rao
 * @date: 2024/7/31 17:41
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.cashbalance.QueryHkCashBalanceFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryHkCashBalanceFacade
 * @apiName execute()
 * @apiDescription 香港账户现金余额查询
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} disCurrency 展示币种类型
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=Z5rZk9f&disCurrency=llbv3Lytv&hkCustNo=tCwdppR&ipAddress=BR9fPirip&deviceName=8oKpQ8T3&systemVersion=dznBHzqGiS&appTm=lr&macAddress=IR9zFJxbpQ&deviceSerialNo=ESYQ1Ny6La&appDt=6vIQz55xKD&deviceModel=gSTy&txCode=yrIWbbjl&outletCode=eYDhM&tradeChannel=sj
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.dataDt 数据日期
 * @apiSuccess (响应结果) {Number} data.totalBalance 换算后的总资产
 * @apiSuccess (响应结果) {Array} data.queryEbrokerCustBalanceDtlDTO 币种余额明细
 * @apiSuccess (响应结果) {String} data.queryEbrokerCustBalanceDtlDTO.curCode 货币代码
 * @apiSuccess (响应结果) {Number} data.queryEbrokerCustBalanceDtlDTO.curBalance 货币记账余额
 * @apiSuccess (响应结果) {Number} data.queryEbrokerCustBalanceDtlDTO.inTransitBalance 在途的余额
 * @apiSuccess (响应结果) {Number} data.queryEbrokerCustBalanceDtlDTO.currencyRate 币种汇率
 * @apiSuccess (响应结果) {Number} data.queryEbrokerCustBalanceDtlDTO.changeBalance 换算后的余额
 * @apiSuccessExample 响应结果示例
 * {"code":"QZUohQCG","data":{"queryEbrokerCustBalanceDtlDTO":[{"changeBalance":2326.921027849962,"curBalance":2611.6764065882194,"inTransitBalance":4477.329034825914,"curCode":"Gbrn5REQX","currencyRate":2767.6623911246334}],"totalBalance":3828.2805620539752,"hkCustNo":"dXH","dataDt":"8hEJzCqpm8"},"description":"ZKWkpT3j"}
 */
public interface QueryHkCashBalanceFacade extends BaseFacade<QueryHkCashBalanceRequest, QueryHkCashBalanceResponse> {
}
