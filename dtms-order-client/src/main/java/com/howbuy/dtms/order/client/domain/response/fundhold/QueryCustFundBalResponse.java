/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/18 13:23
 * @since JDK 1.8
 */
public class QueryCustFundBalResponse implements Serializable {

    private static final long serialVersionUID = -4895266913797736130L;

    /**
     * 客户份额列表
     */
    private List<CustFundBalVO> custFundBalVOList = new ArrayList<>();

    public List<CustFundBalVO> getCustFundBalVOList() {
        return custFundBalVOList;
    }

    public void setCustFundBalVOList(List<CustFundBalVO> custFundBalVOList) {
        this.custFundBalVOList = custFundBalVOList;
    }
}
