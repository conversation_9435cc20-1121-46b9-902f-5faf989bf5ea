/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundtxacct;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/13 10:31
 * @since JDK 1.8
 */
public class AddFundTxAcctResponse implements Serializable {

    private static final long serialVersionUID = 7672672139629621401L;

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;
}
