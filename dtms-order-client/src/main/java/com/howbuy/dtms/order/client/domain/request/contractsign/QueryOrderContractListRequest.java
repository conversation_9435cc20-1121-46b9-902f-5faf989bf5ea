/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.contractsign;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 查询订单合同列表
 * @date 2024/5/15 15:45
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryOrderContractListRequest extends BaseRequest {

    private static final long serialVersionUID = 6656107044783178977L;

    public QueryOrderContractListRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_QUERY_ORDER_CONTRACT_LIST);
    }

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 订单状态列表 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private List<String> orderStatusList;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金代码
     */
    private List<String> fundCodeList;

}
