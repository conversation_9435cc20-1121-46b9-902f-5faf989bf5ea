package com.howbuy.dtms.order.client.facade.query.pdf;

import com.howbuy.dtms.order.client.domain.request.pdf.QueryGradationCallOrderPdfInfoRequest;
import com.howbuy.dtms.order.client.domain.response.pdf.QueryGradationCallOrderPdfInfoResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.pdf.QueryGradationCallOrderPdfInfoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryGradationCallOrderPdfInfoFacade
 * @apiName execute()
 * @apiDescription 查询分级赎回订单PDF信息
 * @apiParam (请求参数) {String} dealNo 交易编号
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParamExample 请求参数示例
 * {
 *   "dealNo": "1234567890",
 *   "hkCustNo": "HKCUST001"
 * }
 * @apiSuccess (响应结果) {String} hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} custName 客户名称
 * @apiSuccess (响应结果) {String} productName 产品名称
 * @apiSuccess (响应结果) {String} productVol 产品份额
 * @apiSuccess (响应结果) {String} fundDivMode 分红方式 0-红利再投 1-现金分红 2-N/A不适用
 * @apiSuccess (响应结果) {String} tradeDt 交易日期，YYYY-MM-DD
 * @apiSuccess (响应结果) {String} currency 币种
 * @apiSuccess (响应结果) {String} subAmt 认缴金额
 * @apiSuccess (响应结果) {String} fee 认购预估手续费
 * @apiSuccess (响应结果) {String} paidAmt 首次实缴金额
 * @apiSuccess (响应结果) {String} payMethod 支付方式
 * @apiSuccess (响应结果) {String} appDt 申请时间(下单时间)
 * @apiSuccessExample 响应结果示例
 * {
 *   "hkCustNo": "HKCUST001",
 *   "custName": "John Doe",
 *   "productName": "Fund A",
 *   "productVol": "1000",
 *   "fundDivMode": "0",
 *   "tradeDt": "2024-05-10",
 *   "currency": "USD",
 *   "subAmt": "10000.00",
 *   "fee": "10.00",
 *   "paidAmt": "5000.00",
 *   "payMethod": "Credit Card",
 *   "appDt": "2024-05-09"
 * }
 */
public interface QueryGradationCallOrderPdfInfoFacade extends BaseFacade<QueryGradationCallOrderPdfInfoRequest, QueryGradationCallOrderPdfInfoResponse> {
}