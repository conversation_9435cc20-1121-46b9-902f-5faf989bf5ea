/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.buy;

import com.howbuy.dtms.order.client.domain.request.buy.CounterBuyRequest;
import com.howbuy.dtms.order.client.domain.request.buy.CounterBuyValidateRequest;
import com.howbuy.dtms.order.client.domain.request.buy.CounterNewPiggyBuyRequest;
import com.howbuy.dtms.order.client.domain.request.buy.CounterPiggyBuyValidateRequest;
import com.howbuy.dtms.order.client.domain.response.Body;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.buy.CounterBuyVO;

/**
 * <AUTHOR>
 * @description: (柜台买入接口)
 * @date 2024/11/4 19:27
 * @since JDK 1.8
 */
public interface CounterBuyFacade {


    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.CounterBuyFacade.counterBuyValidate()
     * @apiVersion 1.0.0
     * @apiGroup CounterBuyFacadeImpl
     * @apiName counterBuyValidate()
     * @apiDescription 柜台买入校验
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} cpAcctNo 资金账号 支付方式为1-电汇时必填
     * @apiParam (请求体) {String} payMethod 支付方式  1-电汇、2-支票、3-海外储蓄罐
     * @apiParam (请求体) {Number} buyAmt 买入金额
     * @apiParam (请求体) {Number} estimateFee 预估手续费
     * @apiParam (请求体) {Number} applyDiscountRate 申请折扣率
     * @apiParam (请求体) {String} discountType 折扣类型
     * @apiParam (请求体) {Number} discountAmt 折扣金额
     * @apiParam (请求体) {String} prebookDealNo 预约单号
     * @apiParam (请求体) {String} businessCode 中台业务码
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"buyAmt":9873.893230010663,"externalDealNo":"r0S2fapyM9","hkCustNo":"oa0JOdCl3","ipAddress":"agNepFz","estimateFee":8398.0716225531,"deviceName":"vRFob","systemVersion":"t","prebookDealNo":"2s0k","appTm":"qXV93iEx4","businessCode":"LX","macAddress":"DN","deviceSerialNo":"4","fundCode":"q","payMethod":"TxnXG","cpAcctNo":"q9","appDt":"T4xloBlp3m","deviceModel":"Dt","preDiscountRate":7427.524566454754,"txCode":"vftq","outletCode":"JrW","tradeChannel":"P"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"cE","description":"Uf"}
     */
    Response<Body> counterBuyValidate(CounterBuyValidateRequest request);

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.CounterBuyFacade.counterBuy()
     * @apiVersion 1.0.0
     * @apiGroup CounterBuyFacadeImpl
     * @apiName counterBuy()
     * @apiDescription 柜台买入
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} cpAcctNo 资金账号 支付方式为1-电汇时必填
     * @apiParam (请求体) {String} payMethod 支付方式  1-电汇、2-支票、3-海外储蓄罐
     * @apiParam (请求体) {Number} buyAmt 买入金额
     * @apiParam (请求体) {Number} estimateFee 预估手续费
     * @apiParam (请求体) {Number} applyDiscountRate 申请折扣率
     * @apiParam (请求体) {String} discountType 折扣类型
     * @apiParam (请求体) {Number} discountAmt 折扣金额
     * @apiParam (请求体) {String} prebookDealNo 预约单号
     * @apiParam (请求体) {String} businessCode 中台业务码
     * @apiParam (请求体) {String} extOption 展期选项 1-本金展期,2-本金+收益展期,3-收益展期
     * @apiParam (请求体) {String} extControlNum 展期控制数
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"buyAmt":1895.8473168524438,"externalDealNo":"i","hkCustNo":"arHoAulNDA","ipAddress":"ywXHE8MiY","estimateFee":8079.690254965225,"extControlNum":"A76NQxVYY2","deviceName":"fpUSw4w","systemVersion":"X0f","prebookDealNo":"Bq","appTm":"z8h2","businessCode":"at","macAddress":"QO","deviceSerialNo":"XO2M","fundCode":"6uWMtHBi3","payMethod":"10E80GaO","applyDiscountRate":1915.467037136036,"extOption":"F7vZ8ykBsP","cpAcctNo":"I0","appDt":"ps","deviceModel":"KHxG3","txCode":"f1pD2j","outletCode":"7","tradeChannel":"F4FEy"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.dealDtlNo 订单明细号号
     * @apiSuccessExample 响应结果示例
     * {"code":"PcS92pbpsN","data":{"dealNo":"PSVNG9","dealDtlNo":"9LsEOh"},"description":"ysDG9"}
     */
    Response<CounterBuyVO> counterBuy(CounterBuyRequest request);

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.CounterBuyFacade.counterPiggyBuyValidate()
     * @apiVersion 1.0.0
     * @apiGroup CounterBuyFacadeImpl
     * @apiName counterPiggyBuyValidate()
     * @apiDescription 柜台储蓄罐买入校验
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} cpAcctNo 资金账号 支付方式为1-电汇时必填
     * @apiParam (请求体) {String} payMethod 支付方式  1-电汇、2-支票、3-海外储蓄罐
     * @apiParam (请求体) {Number} buyAmt 买入金额
     * @apiParam (请求体) {Number} estimateFee 预估手续费
     * @apiParam (请求体) {Number} applyDiscountRate 申请折扣率
     * @apiParam (请求体) {String} discountType 折扣类型
     * @apiParam (请求体) {Number} discountAmt 折扣金额
     * @apiParam (请求体) {String} prebookDealNo 预约单号
     * @apiParam (请求体) {String} businessCode 中台业务码
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"buyAmt":3739.261040935988,"externalDealNo":"JDrgFC2","hkCustNo":"Hzn","ipAddress":"ax8gqG9t","estimateFee":329.3536816711773,"deviceName":"H","systemVersion":"pAirDqzv","prebookDealNo":"jc0oRC7a","appTm":"qQnT6qi","macAddress":"GNfgMx9TuY","deviceSerialNo":"Pgszgy","fundCode":"IdsnnNlG","payMethod":"PVrb","applyDiscountRate":1418.908998342412,"cpAcctNo":"4PCDV9DKAl","appDt":"8KAuEah","deviceModel":"EZ09FNvj8L","txCode":"qWYk","outletCode":"G2hx","tradeChannel":"6YB6x"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"guhwCyGV","description":"EJj7ORoc"}
     */
    Response<Body> counterPiggyBuyValidate(CounterPiggyBuyValidateRequest request);

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.CounterBuyFacade.counterNewPiggyBuy()
     * @apiVersion 1.0.0
     * @apiGroup CounterBuyFacadeImpl
     * @apiName counterNewPiggyBuy()
     * @apiDescription 柜台储蓄罐买入
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} cpAcctNo 资金账号 支付方式为1-电汇时必填
     * @apiParam (请求体) {String} payMethod 支付方式  1-电汇、2-支票、3-海外储蓄罐
     * @apiParam (请求体) {Number} buyAmt 买入金额
     * @apiParam (请求体) {Number} estimateFee 预估手续费
     * @apiParam (请求体) {Number} applyDiscountRate 申请折扣率
     * @apiParam (请求体) {String} discountType 折扣类型
     * @apiParam (请求体) {Number} discountAmt 折扣金额
     * @apiParam (请求体) {String} prebookDealNo 预约单号
     * @apiParam (请求体) {String} businessCode 中台业务码
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"buyAmt":362.77430629662376,"externalDealNo":"JQ","hkCustNo":"cxBO","ipAddress":"zJBI","estimateFee":3721.095187423682,"deviceName":"bfw9eq","systemVersion":"bHvsVq","prebookDealNo":"Rbf1ri28i","appTm":"3ja","businessCode":"cIlXtCob","macAddress":"M","deviceSerialNo":"6zs5Dpnw","fundCode":"1M1Kak","payMethod":"NMvg9gY4","applyDiscountRate":7643.702232776829,"cpAcctNo":"4P","appDt":"hApkpdbd","deviceModel":"nSuA5ND","txCode":"p","outletCode":"d","tradeChannel":"ozEbT93e"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.dealDtlNo 订单明细号号
     * @apiSuccessExample 响应结果示例
     * {"code":"XQzVT","data":{"dealNo":"91q","dealDtlNo":"k02lBlR9x"},"description":"8XtzibMi"}
     */
    Response<CounterBuyVO> counterNewPiggyBuy(CounterNewPiggyBuyRequest request);

}
