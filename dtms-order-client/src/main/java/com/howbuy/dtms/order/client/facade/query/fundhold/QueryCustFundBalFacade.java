/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.fundhold;

import com.howbuy.dtms.order.client.domain.request.fundhold.*;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryCustFundBalDtlResponse;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryCustFundBalResponse;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryCustFundHoldDetailListResponse;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryCustFundHoldDetailResponse;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/14 10:07
 * @since JDK 1.8
 */
public interface QueryCustFundBalFacade {

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryCustFundBalFacade.queryCustFundHoldDetail()
     * @apiVersion 1.0.0
     * @apiGroup QueryCustFundBalFacadeImpl
     * @apiName queryCustFundHoldDetail()
     * @apiDescription 查询客户持仓详情
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} queryType 查询类型 0-非赎回下单 1-赎回下单
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"V0u9c2","hkCustNo":"BHu","ipAddress":"NFj","deviceName":"h","systemVersion":"CcvgynFOkB","appTm":"RIntK","macAddress":"i","deviceSerialNo":"w4g","fundCode":"1J","appDt":"Hrx0sfAyN","deviceModel":"WKFJq","txCode":"pNDF","outletCode":"A94T24mU0","tradeChannel":"P1"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Number} data.availableShare 可用份额
     * @apiSuccess (响应结果) {Number} data.lockVol 锁定份额
     * @apiSuccess (响应结果) {Number} data.freezeVol 冻结份额
     * @apiSuccess (响应结果) {Number} data.totalVol 总份额
     * @apiSuccessExample 响应结果示例
     * {"code":"3A2D","data":{"availableShare":4497.0217049521925},"description":"3R"}
     */
    Response<QueryCustFundHoldDetailResponse> queryCustFundHoldDetail(QueryCustFundHoldDetailRequest request);


    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryCustFundBalFacade.queryCustFundHoldDetailList()
     * @apiVersion 1.0.0
     * @apiGroup QueryCustFundBalFacadeImpl
     * @apiName queryCustFundHoldDetailList()
     * @apiDescription 查询客户基金持仓详情列表
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} fundTxAcctNo 基金交易账号
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"BatF","hkCustNo":"CjyyhsLK","ipAddress":"nhoi7I","deviceName":"e4iE4LW","systemVersion":"Ea","appTm":"BdjczV","macAddress":"OId","deviceSerialNo":"r2cZHBqp","appDt":"gE6d2X","deviceModel":"DvUEYuU3iq","txCode":"D9ipXZ","outletCode":"r4z8w0rc","tradeChannel":"VqhCl3No"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.custFundHoldDetailVOList 客户基金持仓明细
     * @apiSuccess (响应结果) {String} data.custFundHoldDetailVOList.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custFundHoldDetailVOList.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.custFundHoldDetailVOList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.custFundHoldDetailVOList.fundAbbr 基金简称
     * @apiSuccess (响应结果) {Number} data.custFundHoldDetailVOList.availableVol 可用份额
     * @apiSuccess (响应结果) {Number} data.custFundHoldDetailVOList.lockVol 锁定份额
     * @apiSuccess (响应结果) {Number} data.custFundHoldDetailVOList.freezeVol 冻结份额
     * @apiSuccess (响应结果) {Number} data.custFundHoldDetailVOList.totalVol 总份额
     * @apiSuccess (响应结果) {String} data.custFundHoldDetailVOList.remark 备注
     * @apiSuccessExample 响应结果示例
     * {"code":"JONK","data":{"custFundHoldDetailVOList":[{"fundAbbr":"o","freezeVol":8936.153985891859,"fundCode":"ku","hkCustNo":"2vB","lockVol":5189.158796082449,"totalVol":9269.458376379851,"remark":"hDnI","availableVol":4645.948450547987,"fundTxAcctNo":"wtatIscEUA"}]},"description":"a"}
     */
    Response<QueryCustFundHoldDetailListResponse> queryCustFundHoldDetailList(QueryCustFundHoldDetailListRequest request);


    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryCustFundBalFacade.queryCustFundBal()
     * @apiVersion 1.0.0
     * @apiGroup QueryCustFundBalFacadeImpl
     * @apiName queryCustFundBal()
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParamExample 请求体示例
     * {"externalDealNo":"WMFBpFPsnY","hkCustNo":"EERxaxyt","ipAddress":"eBpn","deviceName":"JaRV","systemVersion":"EHdzzkEBx8","appTm":"QXj19","macAddress":"95Y9c0dcRR","deviceSerialNo":"wl2VL4","fundCode":"q69bumuyUM","appDt":"sVMbDC1T","deviceModel":"6p","txCode":"EhHL3","outletCode":"eSTC4Y89SE","tradeChannel":"h"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.custFundBalVOList 客户份额列表
     * @apiSuccess (响应结果) {String} data.custFundBalVOList.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custFundBalVOList.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.custFundBalVOList.mainFundCode 主基金代码
     * @apiSuccess (响应结果) {String} data.custFundBalVOList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.custFundBalVOList.fundManCode 管理人代码
     * @apiSuccess (响应结果) {Number} data.custFundBalVOList.balanceVol 总余额
     * @apiSuccess (响应结果) {Number} data.custFundBalVOList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.custFundBalVOList.navDt 净值日期（最新）
     * @apiSuccess (响应结果) {String} data.custFundBalVOList.volUpdateDt 份额更新日期
     * @apiSuccessExample 响应结果示例
     * {"code":"sJMmj720m","data":{"custFundBalVOList":[{"fundManCode":"On6","fundCode":"ZoT0s","hkCustNo":"BVIjOr8","mainFundCode":"QHY","navDt":"0qOS07gsz","balanceVol":291.416993407716,"balanceFactor":1855.1244631641528,"volUpdateDt":"7ev76kh1","fundTxAcctNo":"mVg"}]},"description":"GCRKqVWzR"}
     */
    Response<QueryCustFundBalResponse> queryCustFundBal(QueryCustFundBalRequest request);

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryCustFundBalFacade.queryCustFundBalDtl()
     * @apiVersion 1.0.0
     * @apiGroup QueryCustFundBalFacadeImpl
     * @apiName queryCustFundBalDtl()
     * @apiDescription 查询客户份额明细
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"1","hkCustNo":"FM0q","ipAddress":"A","deviceName":"QF","systemVersion":"xLI","appTm":"ZbipU64M","macAddress":"wx2RfW","deviceSerialNo":"hA7cEpQ","fundCode":"7rsNwh","appDt":"c","deviceModel":"D","txCode":"PdBD0","outletCode":"BGzQwLLJ","tradeChannel":"qPkUSE0s"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.custFundBalDtlVOList 客户份额明细列表
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.volDtlNo 份额明细号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.mainFundCode 主基金代码
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundAbbr 基金简称
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundManCode 管理人代码
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.ackSerialNo 确认流水号
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.submitDealNo 上报订单号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.busiCode 业务代码
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.tradeDt 交易日期
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.ackDt 确认日期
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.initVol 初始份额
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.balanceVol 总余额
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.serialNumber 系列号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.shareRegDt 份额注册日期
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.lockEndDt 份额锁定结束日
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.initBalanceFactor 初始平衡因子
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.extOption 展期选项 1-本金展期,2-本金+收益展期,3-收益展期
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.extControlType 展期控制类型:1-月
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.extControlNum 展期控制数
     * @apiSuccessExample 响应结果示例
     * {"code":"TJ","data":{"custFundBalDtlVOList":[{"lockEndDt":"BypTN9Rs","extControlType":"K0p","initVol":4238.932824240012,"fundManCode":"6Le","ackSerialNo":7164,"serialNumber":"GuvuuRm","hkCustNo":"utwR4B","busiCode":"1","balanceVol":8529.640330968214,"shareRegDt":"G7H","submitDealNo":4357,"extControlNum":"mB8jRZtan","fundTxAcctNo":"7IpogGV","initBalanceFactor":3801.850143695017,"volDtlNo":"IIcB","fundCode":"d1lmwey7","tradeDt":"YJ4OjTFm","mainFundCode":"x6qr46VJ","extOption":"1eLhr","balanceFactor":3840.8981665102506,"ackDt":"m5"}]},"description":"W9mpOtfAF"}
     */
    Response<QueryCustFundBalDtlResponse> queryCustFundBalDtl(QueryCustFundBalDtlRequest request);


    /**
     * @api {DUBBO}  com.howbuy.dtms.order.client.facade.query.fundhold.QueryCustFundBalFacade.queryCustFundBalDtlByHkCustNoSelective()
     * @apiVersion 1.0.0
     * @apiGroup QueryCustFundBalFacade
     * @apiName queryCustFundBalDtlByHkCustNoSelective()
     * @apiDescription 根据香港客户号选择性的查询
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {String} fundAcctNo 基金交易账号
     * @apiParam (请求参数) {String} volDtlNo 份额明细号
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
     * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求参数) {String} outletCode 网点号
     * @apiParam (请求参数) {String} ipAddress ipAddress
     * @apiParam (请求参数) {String} externalDealNo 外部订单号
     * @apiParam (请求参数) {String} macAddress MAC地址
     * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
     * @apiParam (请求参数) {String} deviceModel 设备型号
     * @apiParam (请求参数) {String} deviceName 设备名称
     * @apiParam (请求参数) {String} systemVersion 系统版本号
     * @apiParamExample 请求参数示例
     * externalDealNo=xl&hkCustNo=UXme9kRr&ipAddress=Hblj&deviceName=Llz&systemVersion=o&appTm=HIZ9YR&macAddress=cgK6pOzh&volDtlNo=UdA4No5f&deviceSerialNo=abafLVrNQ&appDt=I8DqPKaAh&deviceModel=Ubs9uoK&txCode=c4MRQ00&fundAcctNo=2QhzEzY&outletCode=SVGVCW67Nf&tradeChannel=0NtlGYN
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.custFundBalDtlVOList 客户份额明细列表
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.volDtlNo 份额明细号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.mainFundCode 主基金代码
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundAbbr 基金简称
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.fundManCode 管理人代码
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.ackSerialNo 确认流水号
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.submitDealNo 上报订单号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.busiCode 业务代码
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.tradeDt 交易日期
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.ackDt 确认日期
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.initVol 初始份额
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.balanceVol 总余额
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.serialNumber 系列号
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.shareRegDt 份额注册日期
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.lockEndDt 份额锁定结束日
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.initBalanceFactor 初始平衡因子
     * @apiSuccess (响应结果) {Number} data.custFundBalDtlVOList.balanceFactor 平衡因子
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.extOption 展期选项 1-本金展期,2-本金+收益展期,3-收益展期
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.extControlType 展期控制类型:1-月
     * @apiSuccess (响应结果) {String} data.custFundBalDtlVOList.extControlNum 展期控制数
     * @apiSuccessExample 响应结果示例
     * {"code":"Ve8","data":{"custFundBalDtlVOList":[{"lockEndDt":"JVmZTwNT","extControlType":"C7Hmtn5Yw","initVol":1375.159640054724,"fundManCode":"z4kTxhahQg","ackSerialNo":3933,"serialNumber":"pj","hkCustNo":"9GyOoGNA","busiCode":"2fwVkvsO6u","balanceVol":2107.4986979388186,"shareRegDt":"bnXoF4AbXx","submitDealNo":3747,"extControlNum":"AQYl9P22PT","fundTxAcctNo":"ZkVoqO","fundAbbr":"R","initBalanceFactor":7404.581737036905,"volDtlNo":"VoJqp04J4R","fundCode":"NrLwbyN","tradeDt":"Vkzt4nFrk","mainFundCode":"VqiGq","extOption":"iDroMU","balanceFactor":5984.206066041084,"ackDt":"nd7snuKkgw"}]},"description":"DbDLsbszW"}
     */
    Response<QueryCustFundBalDtlResponse> queryCustFundBalDtlByHkCustNoSelective(QueryCustFundBalDtlByHkCustNoSelectiveRequest request);
}
