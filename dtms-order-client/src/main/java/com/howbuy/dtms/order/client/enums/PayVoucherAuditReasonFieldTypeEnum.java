/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.enums;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/2 19:02
 * @since JDK 1.8
 */
public enum PayVoucherAuditReasonFieldTypeEnum {

    // 字段类型 0-打款凭证属性字段 1-打款凭证文件字段

    Field("0", "打款凭证属性字段"),

    File("1", "打款凭证文件字段"),

    ;

    private String code;
    private String desc;

    PayVoucherAuditReasonFieldTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String getDescByCode(String code) {
        if (code != null && !code.isEmpty()) {
            PayVoucherAuditReasonFieldTypeEnum[] var1 = values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                PayVoucherAuditReasonFieldTypeEnum item = var1[var3];
                if (item.getCode().equals(code)) {
                    return item.getDesc();
                }
            }

            return "";
        } else {
            return "";
        }
    }

    public static String toString(String code) {
        return code != null && !code.isEmpty() ? String.format("%s-%s", code, getDescByCode(code)) : "";
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }

}
