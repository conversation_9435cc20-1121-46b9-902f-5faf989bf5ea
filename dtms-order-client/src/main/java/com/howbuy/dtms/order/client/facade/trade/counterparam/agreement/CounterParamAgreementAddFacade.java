/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.counterparam.agreement;

import com.howbuy.dtms.order.client.domain.request.agreement.CounterParamAgreementModifyRequest;
import com.howbuy.dtms.order.client.domain.response.agreement.CounterParamAgreementModifyResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 柜台参数补充协议添加接口
 * <AUTHOR>
 * @date 2025/3/20 15:08
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.counterparam.agreement.CounterParamAgreementAddFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup CounterParamAgreementAddFacadeImpl
 * @apiName execute()
 * @apiDescription 柜台参数补充协议添加
 * @apiParam (请求体) {String} agreementId 协议ID
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} fundCode 基金编码
 * @apiParam (请求体) {String} agreementType 协议类型
 * @apiParam (请求体) {String} agreementContent 协议内容
 * @apiParamExample 请求体示例
 * {
 *   "agreementId":"AGR202503200001",
 *   "hkCustNo":"HK10086",
 *   "fundCode":"000001",
 *   "agreementType":"1",
 *   "agreementContent":"补充协议内容"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {
 *   "code":"0000",
 *   "data":{
 *     "agreementId":"AGR202503200001"
 *   },
 *   "description":"协议添加成功"
 * }
 */
public interface CounterParamAgreementAddFacade extends BaseFacade<CounterParamAgreementModifyRequest, CounterParamAgreementModifyResponse> {

}
