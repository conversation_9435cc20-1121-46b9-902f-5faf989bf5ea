/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.piggytradeapp.BatchPiggyTradeAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.BatchPiggyTradeAppBuyResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.piggytradeapp.BatchPiggyTradeAppBuyFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup BatchPiggyTradeAppBuyFacadeImpl
 * @apiName execute()
 * @apiDescription 批量储蓄罐交易申请买入接口
 * @apiParam (请求体) {Array} piggyTradeAppBuyList 储蓄罐申请买入列表
 * @apiParam (请求体) {String} importAppId 导入申请id
 * @apiParam (请求体) {String} piggyAppSource 储蓄罐申请来源 0-excel 1-可用余额、2-客户控管表、3-退款控管表、4-自动赎回
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} fundTxAcctNo 基金交易账号
 * @apiParam (请求体) {String} appAmt 申请金额
 * @apiParam (请求体) {String} relationalDealNo 关联订单号
 * @apiParamExample 请求体示例
 * {
 *   "piggyTradeAppBuyList": [
 *     {
 *       "importAppId": "APP001",
 *       "piggyAppSource": "0",
 *       "hkCustNo": "HK001",
 *       "fundTxAcctNo": "FTA001",
 *       "appAmt": "10000.00",
 *       "relationalDealNo": "123456"
 *     }
 *   ]
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","description":"成功"}
 * @description: 批量储蓄罐交易申请买入接口
 * <AUTHOR>
 * @date 2025-07-16 14:45:30
 * @since JDK 1.8
 */
public interface BatchPiggyTradeAppBuyFacade extends BaseFacade<BatchPiggyTradeAppBuyRequest, BatchPiggyTradeAppBuyResponse> {
}
