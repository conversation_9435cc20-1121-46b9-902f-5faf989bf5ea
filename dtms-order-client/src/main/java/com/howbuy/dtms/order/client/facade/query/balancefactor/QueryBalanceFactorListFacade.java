/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.balancefactor;

import com.howbuy.dtms.order.client.domain.request.hkbalance.QueryBalanceFactorListRequest;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceFactorListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: (查询平衡因子列表)
 * <AUTHOR>
 * @date 2024/12/5 14:27
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.balancefactor.QueryBalanceFactorListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryBalanceFactorListFacade
 * @apiName execute()
 * @apiDescription QueryBalanceFactorListFacade
 * @apiParam (请求参数) {String} hboneNo 一账通
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {Array} fundCodeList 基金代码列表
 * @apiParam (请求参数) {String} startDt 净值日期开始
 * @apiParam (请求参数) {String} endDt 净值日期结束
 * @apiParam (请求参数) {Array} fundNavDtVOList 基金净值日期列表，优先级高于其他参数
 * @apiParam (请求参数) {String} fundNavDtVOList.fundCode 基金代码
 * @apiParam (请求参数) {Array} fundNavDtVOList.navDtList 净值日期列表
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * fundCodeList=P&externalDealNo=91&ipAddress=GzHNoseI&endDt=PC9RPjZ&deviceName=nb0kA&systemVersion=NDl3w&appTm=q8ABd4Yhx&macAddress=eE&deviceSerialNo=QKch&fundCode=UbO21&startDt=LYHujO&appDt=I0E6f&deviceModel=j4&txCode=fj3RX&outletCode=W2U1r&hboneNo=Yimndd&tradeChannel=pf
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.balanceFactorDomains 平衡因子列表
 * @apiSuccess (响应结果) {Number} data.balanceFactorDomains.balanceFactor 平衡因子
 * @apiSuccess (响应结果) {String} data.balanceFactorDomains.navDt 净值
 * @apiSuccess (响应结果) {String} data.balanceFactorDomains.fundCode 基金代码
 * @apiSuccessExample 响应结果示例
 * {"code":"lgt","data":{"balanceFactorDomains":[{"fundCode":"AqQqzyal","navDt":"S512YSou","balanceFactor":3074.585667623524}]},"description":"N5KsLfeW"}
 */
public interface QueryBalanceFactorListFacade extends BaseFacade<QueryBalanceFactorListRequest, QueryBalanceFactorListResponse> {

}