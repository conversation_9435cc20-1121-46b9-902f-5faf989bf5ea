/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description:  根据订单号查询打款凭证信息
 * <AUTHOR>
 * @date 2024/8/20 16:17
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryVoucherDetailByOrderNoRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -8065347764170752280L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 订单号
     */
    private String orderNo;
}
