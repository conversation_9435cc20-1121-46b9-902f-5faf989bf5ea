package com.howbuy.dtms.order.client.domain.response.subandfirstpaid;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 柜台认缴和首次实缴响应
 * <AUTHOR>
 * @date 2025/04/07 15:58:49
 * @since JDK 1.8
 */
@Getter
@Setter
public class CounterSubAndFirstPaidResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 批次号
     */
    private Long batchNo;
} 