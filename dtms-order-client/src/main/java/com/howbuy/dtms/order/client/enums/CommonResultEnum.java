package com.howbuy.dtms.order.client.enums;

/**
 * <AUTHOR>
 * @description: 返回状态枚举
 * @date 2023/2/1 13:47
 */
public enum CommonResultEnum implements IResultCode {
    //执行成功
    SUCCESS("0000","操作成功"),
    //执行失败
    FAIL("0001","执行失败"),
    //参数错误
    PARAM_ERROR("0002","参数错误"),
    //数据已存在
    DATA_EXITS("0010","数据已存在"),
    //导入数据错误
    IMPORT_DATA_ERROR("0011","导入数据错误"),
    // 超出风险承受能力
    BEYOND_RISK_TOLERANCE("0012", "超出风险承受能力"),
    // 不可购买
    CAN_NOT_BUY("0013", "不可购买"),
    // 产品不存在
    PRODUCT_NOT_EXITS("0014", "产品不存在"),
    // 产品不存在
    PRODUCT_NOT_ESTABLISH("0015", "产品未成立"),
    //报告不存在
    REPORT_NOT_EXIST("0016", "报告不存在"),
    // 查询不到对应数据
    NO_DATA("0018", "查询不到对应数据"),
    // 二次确认
    SECOND_CONFIRM("0017", "二次确认"),;
    /**
     * 编码
     */
    private String code;
    /**
     * 描述
     */
    private String description;

    CommonResultEnum(String code, String description){
        this.code=code;
        this.description=description;
    }

    /**
     * 通过code获得
     * @param code	系统返回参数编码
     * @return description 描述
     */
    public static String getDescription(String code){
        for(CommonResultEnum b : CommonResultEnum.values()){
            if(b.getCode().equals(code)){
                return b.description;
            }
        }
        return null;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
