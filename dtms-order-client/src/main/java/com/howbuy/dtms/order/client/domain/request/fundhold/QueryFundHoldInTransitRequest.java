/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fundhold;

import com.fasterxml.jackson.databind.ser.Serializers;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询是否持仓或者在途
 * <AUTHOR>
 * @date 2024/7/30 14:22
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundHoldInTransitRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -3719772139022396095L;
    /**
     * 基金编码
     */
    private List<String> fundCodeList;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;

}
