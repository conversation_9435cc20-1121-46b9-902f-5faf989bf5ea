/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.balancefactor;


import com.howbuy.dtms.order.client.domain.request.hkbalance.QueryBalanceFactorRequest;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceFactorResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;


/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.balancefactor.QueryBalanceFactorFacade.execute()
 * @apiGroup QueryBalanceFactorFacade
 * @apiName execute()
 * @apiDescription 平衡因子接口
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} fundCode 基金代码
 * @apiParam (请求体) {String} navDt 净值日期
 * @apiParam (请求体) {String} hbOneNo 一账通号
 * @apiParamExample 请求体示例
 * {"fundCode":"uA3Szy5","hkCustNo":"JjT5ZDMDL","navDt":"nDoz1GEZhy"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Number} data.balanceFactor 平衡因子
 * @apiSuccessExample 响应结果示例
 * {"code":"Zf5SfE","data":{"balanceFactor":9552.596094770204},"description":"S2eLLur"}
 */
public interface QueryBalanceFactorFacade extends BaseFacade<QueryBalanceFactorRequest, QueryBalanceFactorResponse> {

}