/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 查询产品基本购买信息响应
 * <AUTHOR>
 * @date 2025-03-12 10:12:36
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryFundBuyInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 基金信息结果列表
     */
    private List<FundBuyInfoVO> fundBuyInfoList;

    /**
     * 基金购买信息VO
     */
    @Getter
    @Setter
    public static class FundBuyInfoVO implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 购买限额
         */
        private BigDecimal minAppAmt;

        /**
         * 产品风险等级
         */
        private String fundRiskLevel;

        /**
         * 首次购买标识
         * 0-非首次，1-首次
         */
        private String firstAppFlag;

        /**
         * 币种  156-人民币、344-港元、392-日元、826-英镑、840-美元、978-欧元
         */
        private String currency;
    }
} 