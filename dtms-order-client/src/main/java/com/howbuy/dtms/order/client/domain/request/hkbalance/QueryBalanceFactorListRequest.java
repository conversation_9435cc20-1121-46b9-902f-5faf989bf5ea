package com.howbuy.dtms.order.client.domain.request.hkbalance;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;

import java.io.Serializable;
import java.util.List;


/**
 * @description:(请在此添加描述)
 * @return 
 * @author: xufancha<PERSON>
 * @date: 2024/12/5 14:27
 * @since JDK 1.8
 */
public class QueryBalanceFactorListRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 一账通
     */
    private String hboneNo;
    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金代码列表
     */
    private List<String> fundCodeList;
    /**
     * 净值日期开始
     */
    private String startDt;
    /**
     * 净值日期结束
     */
    private String endDt;

    /**
     * 基金净值日期列表，优先级高于其他参数
     */
    private List<FundNavDtVO> fundNavDtVOList;
    
    /**
     * 基金净值日期VO
     */
    public static class FundNavDtVO implements Serializable {
        private static final long serialVersionUID = 1L;
        
        /**
         * 基金代码
         */
        private String fundCode;
        
        /**
         * 净值日期列表
         */
        private List<String> navDtList;
        
        public String getFundCode() {
            return fundCode;
        }
        
        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }
        
        public List<String> getNavDtList() {
            return navDtList;
        }
        
        public void setNavDtList(List<String> navDtList) {
            this.navDtList = navDtList;
        }
    }

    public String getHboneNo() {
        return hboneNo;
    }

    public void setHboneNo(String hboneNo) {
        this.hboneNo = hboneNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getStartDt() {
        return startDt;
    }

    public void setStartDt(String startDt) {
        this.startDt = startDt;
    }

    public String getEndDt() {
        return endDt;
    }

    public void setEndDt(String endDt) {
        this.endDt = endDt;
    }

    public List<String> getFundCodeList() {
        return fundCodeList;
    }

    public void setFundCodeList(List<String> fundCodeList) {
        this.fundCodeList = fundCodeList;
    }

    public List<FundNavDtVO> getFundNavDtVOList() {
        return fundNavDtVOList;
    }

    public void setFundNavDtVOList(List<FundNavDtVO> fundNavDtVOList) {
        this.fundNavDtVOList = fundNavDtVOList;
    }
}
