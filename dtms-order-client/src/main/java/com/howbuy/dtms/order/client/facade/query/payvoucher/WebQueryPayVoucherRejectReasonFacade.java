/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.payvoucher;

import com.howbuy.dtms.order.client.domain.request.payvoucher.WebPayVoucherRejectRequest;
import com.howbuy.dtms.order.client.domain.response.payvoucher.WebPayVoucherRejectReasonListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/29 10:07
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payvoucher.WebQueryPayVoucherRejectReasonFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup WebQueryPayVoucherRejectReasonFacade
 * @apiName execute()
 * @apiDescription 查询打款凭证驳回记录，使用方：CRM
 * @apiParam (请求体) {String} voucherNo 打款凭证号
 * @apiParam (请求体) {String} txCode 交易码
 * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求体) {String} appTm 申请时间 HHmmss
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress ipAddress
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {String} macAddress MAC地址
 * @apiParam (请求体) {String} deviceSerialNo 设备序列号
 * @apiParam (请求体) {String} deviceModel 设备型号
 * @apiParam (请求体) {String} deviceName 设备名称
 * @apiParam (请求体) {String} systemVersion 系统版本号
 * @apiParamExample 请求体示例
 * {"externalDealNo":"Edbo","ipAddress":"m","deviceName":"AbRSvMV2Pc","systemVersion":"NU","voucherNo":"EbW0JuIo","appTm":"Qy9gB","macAddress":"2n7Tzj","deviceSerialNo":"SbfqL","appDt":"emh","deviceModel":"ZM4ONBteal","txCode":"zM6T","outletCode":"Y4","tradeChannel":"RkuErMIfGJ"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.rejectReasonList 打款凭证列表查询
 * @apiSuccess (响应结果) {String} data.rejectReasonList.id
 * @apiSuccess (响应结果) {String} data.rejectReasonList.auditOperator 审核人
 * @apiSuccess (响应结果) {Number} data.rejectReasonList.auditTime 审核时间
 * @apiSuccess (响应结果) {String} data.rejectReasonList.auditReason 审核原因
 * @apiSuccessExample 响应结果示例
 * {"code":"toN7ueDL","data":{"rejectReasonList":[{"auditReason":"SkyhJzFFpy","auditTime":717722756975,"id":"kj5UR","auditOperator":"YLeN"}]},"description":"io4Q2"}
 */

public interface WebQueryPayVoucherRejectReasonFacade extends BaseFacade<WebPayVoucherRejectRequest, WebPayVoucherRejectReasonListResponse> {
}
