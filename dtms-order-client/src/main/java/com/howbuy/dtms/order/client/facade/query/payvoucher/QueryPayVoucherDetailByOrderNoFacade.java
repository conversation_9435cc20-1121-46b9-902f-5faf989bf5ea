/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.payvoucher;

import com.howbuy.dtms.order.client.domain.request.payvoucher.QueryVoucherDetailByOrderNoRequest;
import com.howbuy.dtms.order.client.domain.response.payvoucher.PayVoucherDetailResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 通过订单号查询打款凭证详情
 * <AUTHOR>
 * @date 2024/7/23 14:29
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payvoucher.QueryPayVoucherDetailByOrderNoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryPayVoucherDetailByOrderNoFacade
 * @apiName execute()
 * @apiDescription 通过订单号查询打款凭证详情
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {Number} orderNo 订单号
 * @apiParamExample 请求参数示例
 * orderNo=3224&hkCustNo=pYl
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.voucherNo 香港客户号
 * @apiSuccess (响应结果) {String} data.voucherType 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
 * @apiSuccess (响应结果) {String} data.tradeOrderNo 交易订单号 类型为1-交易下单凭证时，订单号必传
 * @apiSuccess (响应结果) {String} data.remitCpAcctNo 汇款资金账号
 * @apiSuccess (响应结果) {String} data.remitCurrency 汇款币种
 * @apiSuccess (响应结果) {Number} data.remitAmt 汇款金额
 * @apiSuccess (响应结果) {String} data.bankName 银行名称
 * @apiSuccess (响应结果) {String} data.bankChineseName 银行中文名称
 * @apiSuccess (响应结果) {String} data.bankAcct 银行账户
 * @apiSuccess (响应结果) {String} data.swiftCode SWIFT代码
 * @apiSuccess (响应结果) {String} data.bankAcctDigest 银行账户摘要
 * @apiSuccess (响应结果) {String} data.remark 备注
 * @apiSuccess (响应结果) {String} data.auditStatus 审核状态
 * @apiSuccess (响应结果) {Array} data.fileList 文件列表
 * @apiSuccess (响应结果) {String} data.fileList.fileId 文件id
 * @apiSuccess (响应结果) {String} data.fileList.filePath 文件路径
 * @apiSuccess (响应结果) {String} data.fileList.fileName 文件路径
 * @apiSuccess (响应结果) {String} data.fileList.fileSuffix 文件后缀
 * @apiSuccess (响应结果) {Object} data.auditReason 审核不通过原因
 * @apiSuccess (响应结果) {String} data.auditReason.remitCpAcctNo 汇款资金账号
 * @apiSuccess (响应结果) {String} data.auditReason.remitCurrency 汇款币种
 * @apiSuccess (响应结果) {String} data.auditReason.remitAmt 汇款金额
 * @apiSuccess (响应结果) {String} data.auditReason.remark 备注
 * @apiSuccess (响应结果) {String} data.auditReason.fileReason 材料信息 不通过原因
 * @apiSuccessExample 响应结果示例
 * {"code":"P3JLcFg3XF","data":{"remitCpAcctNo":"ul9I","remitCurrency":"B5","bankChineseName":"TpUFqlM","voucherType":"h1MYl","hkCustNo":"Hd","bankAcct":"AifpRhHG","tradeOrderNo":"eywnxiGYl","swiftCode":"5aeNS","bankName":"SH","remark":"zo","bankAcctDigest":"nPCSsfC8L","voucherNo":"6e","auditReason":{"remitCpAcctNo":"dbpknQFfU","remitCurrency":"KAYYPCpW","fileReason":"ls8DCFQR","remark":"1YJz","remitAmt":"qn"},"auditStatus":"CV","remitAmt":2410.************,"fileList":[{"fileName":"fQBUn","fileSuffix":"4qE","filePath":"3bFF0","fileId":"Jt12ouUsh"}]},"description":"BD"}
 */
public interface QueryPayVoucherDetailByOrderNoFacade extends BaseFacade<QueryVoucherDetailByOrderNoRequest, PayVoucherDetailResponse> {

}
