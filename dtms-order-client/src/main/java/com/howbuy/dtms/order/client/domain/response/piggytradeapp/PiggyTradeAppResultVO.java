/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.piggytradeapp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 储蓄罐交易申请结果视图对象
 * <AUTHOR>
 * @date 2025-07-15 19:32:38
 * @since JDK 1.8
 */
@Getter
@Setter
public class PiggyTradeAppResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 导入申请Id
     */
    private String importAppId;

    /**
     * 是否已生成(0-未生成 1-已生成 2-生成失败)
     */
    private String isGenerated;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 购买金额
     */
    private String buyAmt;

    /**
     * 手续费
     */
    private String fee;

    /**
     * 备注
     */
    private String remark;
}
