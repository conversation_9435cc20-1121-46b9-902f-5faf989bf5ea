/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description:(上报订单信息)
 * @param null
 * @return 
 * @author: haoran.zhang
 * @date: 2024/12/14 12:19
 * @since JDK 1.8
 */
@Data
public class DealMultiSubmitInfoVO implements Serializable {
    /**
     * 系列号
     */
    private String serialNumber;
    /**
     * 份额注册日期
     */
    private String shareRegDt;

    /**
     * TA交易日期
     */
    private String taTradeDt;
    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 开放日期
     */
    private String openDt;
    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认净值
     */
    private BigDecimal ackNav;
    /**
     * 赎回费
     */
    private BigDecimal redeemFee;


    /**
     * 币种
     */
    private String currency;

    /**
     * 币种描述
     */
    private String currencyDesc;

}