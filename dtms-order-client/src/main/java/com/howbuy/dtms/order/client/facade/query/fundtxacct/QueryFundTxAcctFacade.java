/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.fundtxacct;

import com.howbuy.dtms.order.client.domain.request.fundtxacct.QueryFundTxAcctListRequest;
import com.howbuy.dtms.order.client.domain.request.fundtxacct.QueryFundTxAcctRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.fundtxacct.QueryFundTxAcctListResponse;
import com.howbuy.dtms.order.client.domain.response.fundtxacct.QueryFundTxAcctResponse;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/13 13:19
 * @since JDK 1.8
 */
public interface QueryFundTxAcctFacade {

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundtxacct.QueryFundTxAcctFacade.queryFundTxAcct()
     * @apiVersion 1.0.0
     * @apiGroup QueryFundTxAcctFacadeImpl
     * @apiName queryFundTxAcct()
     * @apiDescription 查询基金交易账号
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"ydYpaXojm","hkCustNo":"aBtx5VpKP","ipAddress":"YPfyE","deviceName":"0l","systemVersion":"gbMljEt","appTm":"lfs","macAddress":"al8","deviceSerialNo":"c","fundCode":"FWk1j558S","appDt":"a","deviceModel":"1TeU6y0w","txCode":"K4V4qIBe","outletCode":"N","tradeChannel":"DeSeyAko"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.fundTxAccType 0-非全委 1-全委
     * @apiSuccess (响应结果) {String} data.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.fundTxAcctStat 基金交易账号状态 1-正常 2-注销 3-冻结
     * @apiSuccessExample 响应结果示例
     * {"code":"JhBzPZ3u3","data":{"fundCode":"pqh","hkCustNo":"DqT3LLE","fundTxAcctStat":"jIO2cSRu","fundTxAccType":"8m7W4j","fundTxAcctNo":"zh"},"description":"362A3jbzgW"}
     */
    Response<QueryFundTxAcctResponse> queryFundTxAcct(QueryFundTxAcctRequest request);

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundtxacct.QueryFundTxAcctFacade.queryFundTxAcctList()
     * @apiVersion 1.0.0
     * @apiGroup QueryFundTxAcctFacadeImpl
     * @apiName queryFundTxAcctList()
     * @apiDescription 查询基金交易账号列表
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} fundTxAccType 基金交易账号类型 0-非全委 1-全委
     * @apiParamExample 请求体示例
     * {"externalDealNo":"zeB0MT","hkCustNo":"nnwY7I","ipAddress":"4u2NTF7","fundTxAccType":"4fZukHcxN","deviceName":"ggktX","systemVersion":"VcLDfjr","appTm":"28fL5R","macAddress":"6Fd1WTQDv","deviceSerialNo":"Wxm7beJzDM","fundCode":"ECIr3","appDt":"hLqRKzKs3e","deviceModel":"f","txCode":"2Tdmf4I","outletCode":"u","tradeChannel":"EVDWzuUh"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {Array} data.fundTxAcctVOList 基金交易账号列表
     * @apiSuccess (响应结果) {String} data.fundTxAcctVOList.hkCustNo 香港客户号
     * @apiSuccess (响应结果) {String} data.fundTxAcctVOList.fundTxAccType 0-非全委 1-全委
     * @apiSuccess (响应结果) {String} data.fundTxAcctVOList.fundCode 基金代码
     * @apiSuccess (响应结果) {String} data.fundTxAcctVOList.fundTxAcctNo 基金交易账号
     * @apiSuccess (响应结果) {String} data.fundTxAcctVOList.fundTxAcctStat 基金交易账号状态 1-正常 2-注销 3-冻结
     * @apiSuccessExample 响应结果示例
     * {"code":"t","data":{"fundTxAcctVOList":[{"fundCode":"vLJ6n6w","hkCustNo":"bww","fundTxAcctStat":"Mk4Xvmumc","fundTxAccType":"Q","fundTxAcctNo":"ag8fLcG0"}]},"description":"YdVNA9"}
     */
    Response<QueryFundTxAcctListResponse> queryFundTxAcctList(QueryFundTxAcctListRequest request);

}
