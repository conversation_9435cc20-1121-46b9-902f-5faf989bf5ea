package com.howbuy.dtms.order.client.domain.response.digitalsign;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (查询海外产品电子签约列表)
 * <AUTHOR>
 * @date 2023/5/17 14:44
 * @since JDK 1.8
 */
@Data
public class DigitalSignListVO implements Serializable {
    /**
     *待签约记录数
     */
    private String needSignCount;
    /**
     * 总数
     */
    private String total;
    /**
     *签约订单列表
     */
    private List<PreBookInfoVO> preBookList;

}