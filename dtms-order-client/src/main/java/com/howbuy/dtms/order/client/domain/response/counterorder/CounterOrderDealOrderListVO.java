/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.counterorder;

import com.howbuy.dtms.order.client.domain.response.PageVo;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.CmHkDealOrderVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/10/27 16:56
 * @since JDK 1.8
 */
@Deprecated
@EqualsAndHashCode(callSuper = true)
@Data
public class CounterOrderDealOrderListVO extends PageVo<CounterOrderDealOrderVO> {

}