package com.howbuy.dtms.order.client.domain.request.agreement;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 柜台参数审核补充协议删除请求
 * <AUTHOR>
 * @date 2025/3/20 15:09
 * @since JDK 1.8
 */
@Setter
@Getter
public class CounterParamAgreementDeleteRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 补充协议ID
     */
    private String agrId;

    /**
     * 操作人
     */
    private String operator;
}