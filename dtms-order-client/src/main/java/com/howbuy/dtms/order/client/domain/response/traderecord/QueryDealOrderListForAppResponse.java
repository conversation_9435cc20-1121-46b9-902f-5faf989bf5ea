package com.howbuy.dtms.order.client.domain.response.traderecord;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询交易订单列表响应对象
 * <AUTHOR>
 * @date 2024/03/06 17:35:21
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryDealOrderListForAppResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单列表
     */
    private List<DealOrderInfo> orderList;

    /**
     * 订单信息
     */
    @Getter
    @Setter
    public static class DealOrderInfo implements Serializable {
        
        private static final long serialVersionUID = 1L;

        /**
         * 订单号
         */
        private String dealNo;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 业务类型
         * 1-买入、2-卖出、9-其他
         */
        private String busiType;

        /**
         * 交易状态
         * 1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单、9-其他
         */
        private String tradeStatus;

        /**
         * 持仓状态
         * 1-持有、2-已清仓
         */
        private String holdStatus;

        /**
         * 订单日期
         * 格式：YYYYMMDD
         */
        private String orderDt;
    }
} 