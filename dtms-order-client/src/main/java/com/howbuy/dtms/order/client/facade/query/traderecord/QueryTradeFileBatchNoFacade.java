/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.traderecord;

import com.howbuy.dtms.order.client.domain.request.traderecord.QueryTradeRecordRequest;
import com.howbuy.dtms.order.client.domain.response.traderecord.QueryTradeRecordResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
* @Description 查询交易记录批次号接口
*
* <AUTHOR>
* @Date 2024/12/06 15:44
*/
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.traderecord.QueryTradeFileBatchNoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryTradeFileBatchNoFacadeImpl
 * @apiName execute()
 * @apiDescription 查询交易记录批次号接口
 * @apiParam (请求参数) {String} fileType 文件类型
 * @apiParam (请求参数) {String} startTime 开始时间 yyyy-MM-dd HH:mm:ss
 * @apiParamExample 请求参数示例
 * startTime=4&fileType=T5pVEH
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.batchNoList 批次号列表
 * @apiSuccessExample 响应结果示例
 * {"code":"LD5WRt","data":{"batchNoList":[8674]},"description":"o0QRU"}
 */
public interface QueryTradeFileBatchNoFacade extends BaseFacade<QueryTradeRecordRequest, QueryTradeRecordResponse> {
}
