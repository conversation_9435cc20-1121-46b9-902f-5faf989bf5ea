package com.howbuy.dtms.order.client.domain.request.digitalsign;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (查询海外产品电子签约列表接口)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryDigitalSignListRequest extends BaseRequest {
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
    /**
     * 交易方式	必填，默认全部，0-全部；1-购买；2-赎回
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易方式", isRequired = true)
    private String tradeMode;
    /**
     * 交易状态  必填，默认全部，0-全部；1-待签约；2-交易中；3-交易成功；4-交易失败；
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易状态", isRequired = true)
    private String tradeState;
    /**
     * 开始日期	 必填，yyyyMMdd
     */
    private String startPreDt;
    /**
     * 结束日期	 必填，yyyyMMdd
     */
    private String endPreDt;
    /**
     * 申请日期		 必填，yyyyMMdd
     */
    private String appDt;
    /**
     * 申请时间		 必填，HH24mmss
     */
    private String appTm;
    /**
     * 页码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Digit, fieldName = "页码", isRequired = true)
    private int pageNo = 1;
    /**
     * pageSize
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Digit, fieldName = "页条数", isRequired = true)
    private int pageSize = 50;
}