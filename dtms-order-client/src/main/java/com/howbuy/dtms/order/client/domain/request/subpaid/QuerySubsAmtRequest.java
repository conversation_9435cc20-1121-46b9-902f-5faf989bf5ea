package com.howbuy.dtms.order.client.domain.request.subpaid;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 认缴金额查询请求
 * <AUTHOR>
 * @date 2024-03-27 10:35:21
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class QuerySubsAmtRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -1667917741917563676L;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;
} 