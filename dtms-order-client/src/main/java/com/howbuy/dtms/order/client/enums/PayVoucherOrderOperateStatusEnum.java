/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.enums;

/**
 * <AUTHOR>
 * @description: 打款凭证 操作流水枚举
 * @date 2023/6/12
 * @since JDK 1.8
 */
public enum PayVoucherOrderOperateStatusEnum {


    /**
     * 1-申请提交
     */
    APPLY_UPLOAD("1", "申请提交"),
    /**
     * 2-再次提交
     */
    UPLOAD_AGAIN("2", "再次提交"),

    /**
     * 3-审核通过
     */
    APPROVE("3", "审核通过"),

    /**
     * 4-审核不通过
     */
    FAILURE("4", "审核不通过"),

    /**
     * 5-驳回至初审
     */
    REJECT_TO_FIRST_EXAMINE("5", "驳回至初审"),

    /**
     * 6-驳回至客户
     */
    REJECT_TO_CUSTOMER("6", "驳回至客户"),

    /**
     * 7-作废
     */
    VOIDED("7", "作废"),
    ;

    private String code;
    private String desc;

    private PayVoucherOrderOperateStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    /**
     * @return java.lang.String
     * @description:(根据枚举类型返回code值)
     * @author: your name
     * @date: 2023/3/9 14:54
     * @since JDK 1.8
     */
    public static String getEnumDesc(String code) {
        for (PayVoucherOrderOperateStatusEnum statusEnum : PayVoucherOrderOperateStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

    /**
     * @description:(请在此添加描述)
     * @param code
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/7/26 17:19
     * @since JDK 1.8
     */
    public static String getCodeAndDesc(String code) {
        for (PayVoucherOrderOperateStatusEnum statusEnum : PayVoucherOrderOperateStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getCode() + "-" + statusEnum.getDesc();
            }
        }
        return null;
    }
}
