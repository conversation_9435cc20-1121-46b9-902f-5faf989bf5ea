package com.howbuy.dtms.order.client.facade.trade.buy;

import com.howbuy.dtms.order.client.domain.request.subandfirstpaid.AppSubAndFirstPaidSubmitRequest;
import com.howbuy.dtms.order.client.domain.response.subandfirstpaid.AppSubAndFirstPaidSubmitResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.AppSubAndFirstPaidSubmitFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup AppSubAndFirstPaidFacadeImpl
 * @apiName execute()
 * @apiDescription APP认缴和首次实缴接口
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} fundCode 基金代码
 * @apiParam (请求体) {String} fundTxAcctNo 基金交易账号
 * @apiParam (请求体) {String} cpAcctNo 资金账号(支付方式为1-电汇时必填)
 * @apiParam (请求体) {String} payMethod 支付方式(1-电汇、2-支票、3-海外储蓄罐)
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {Number} appAmt 申请金额
 * @apiParam (请求体) {Number} estimateFee 预估手续费
 * @apiParam (请求体) {Number} applyDiscountRate 申请折扣率
 * @apiParam (请求体) {String} discountType 折扣类型
 * @apiParam (请求体) {Number} discountAmt 折扣金额
 * @apiParam (请求体) {String} tradeChannel 交易渠道
 * @apiParam (请求体) {String} appDt 申请日期(格式:YYYYMMDD)
 * @apiParam (请求体) {String} appTm 申请时间(格式:HHmmss)
 * @apiParamExample 请求体示例
 * {
 *   "hkCustNo": "HK10086",
 *   "fundCode": "000001",
 *   "fundTxAcctNo": "123456789",
 *   "cpAcctNo": "987654321",
 *   "payMethod": "1",
 *   "externalDealNo": "EXT20240407001",
 *   "appAmt": 100000.00,
 *   "estimateFee": 100.00,
 *   "applyDiscountRate": 0.95,
 *   "discountType": "1",
 *   "discountAmt": 5000.00,
 *   "tradeChannel": "1",
 *   "appDt": "20240407",
 *   "appTm": "155849"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.orderStatus 订单状态
 * @apiSuccess (响应结果) {String} data.orderStatusDesc 订单状态描述
 * @apiSuccessExample 响应结果示例
 * {
 *   "code": "0000",
 *   "description": "成功",
 *   "data": {
 *     "dealNo": "202404070001",
 *     "orderStatus": "01",
 *     "orderStatusDesc": "申请成功"
 *   }
 * }
 */
public interface AppSubAndFirstPaidSubmitFacade extends BaseFacade<AppSubAndFirstPaidSubmitRequest, AppSubAndFirstPaidSubmitResponse> {
} 