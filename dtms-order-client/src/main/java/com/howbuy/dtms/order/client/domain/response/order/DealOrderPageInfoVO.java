/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/10 17:52
 * @since JDK 1.8
 */
@Setter
@Getter
public class DealOrderPageInfoVO implements Serializable {
    /**
     * 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
     */
    private String businessType;

    /**
     * 中台业务码
     */
    private String middleBusiCode;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundShortName;

    /**
     * 基金名称
     */
    private String fundName;


    /**
     * 币种代码
     */
    private String currency;


    /**
     * 订单号
     */
    private Long dealNo;

    /**
     *  订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private String orderStatus;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 净申请金额
     */
    private BigDecimal netAppAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 赎回方式 赎回方式 1-按份额、2-按金额
     */
    private String redeemType;

    /**
     * 分红方式 0-红利再投；1-现金红利
     */
    private String fundDivMode;

    /**
     * 转入基金名称
     */
    private String intoFundName;

    /**
     * 转入基金代码
     */
    private String intoFundCode;
}
