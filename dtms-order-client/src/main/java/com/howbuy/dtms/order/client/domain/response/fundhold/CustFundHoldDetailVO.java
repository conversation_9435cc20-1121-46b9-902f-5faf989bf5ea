/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/15 14:19
 * @since JDK 1.8
 */
@Data
public class CustFundHoldDetailVO implements Serializable {

    private static final long serialVersionUID = -7838663648136324836L;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundAbbr;

    /**
     * 是否锁定期产品 0-否；1-是
     */
    private String hasLockPeriod;

    /**
     * 是否支持展期:1-是,0-否
     */
    private String supportExt;

    /**
     * 可用份额
     */
    private BigDecimal availableVol;

    /**
     * 锁定份额
     */
    private BigDecimal lockVol;

    /**
     * 冻结份额
     */
    private BigDecimal freezeVol;

    /**
     * 总份额
     */
    private BigDecimal totalVol;

    /**
     * 备注
     */
    private String remark;
}
