/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.counterorder;

import com.howbuy.dtms.order.client.domain.request.counterorder.CounterOrderSaveRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.buy.CounterOrderBuyVO;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/7 16:07
 * @since JDK 1.8
 */
public interface CounterOrderFacade {

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.counterorder.CounterOrderFacade.saveCounterOrderInfo()
     * @apiVersion 1.0.0
     * @apiGroup CounterOrderFacade
     * @apiName saveCounterOrderInfo()
     * @apiDescription 保存柜台订单信息接口
     * @apiParam (请求体) {Object} counterAuditOrderRequest
     * @apiParam (请求体) {Number} counterAuditOrderRequest.id 主键id
     * @apiParam (请求体) {String} counterAuditOrderRequest.appSerialNo 审核申请流水号
     * @apiParam (请求体) {String} counterAuditOrderRequest.hkCustNo 香港客户号
     * @apiParam (请求体) {String} counterAuditOrderRequest.counterBizType 中台业务码
     * @apiParam (请求体) {String} counterAuditOrderRequest.idNoDigest 证件号码掩码
     * @apiParam (请求体) {String} counterAuditOrderRequest.custChineseName 客户中文姓名
     * @apiParam (请求体) {String} counterAuditOrderRequest.fundCode 基金代码
     * @apiParam (请求体) {String} counterAuditOrderRequest.fundAbbr 基金简称
     * @apiParam (请求体) {Number} counterAuditOrderRequest.appAmt 申请金额
     * @apiParam (请求体) {Number} counterAuditOrderRequest.appVol 申请份额
     * @apiParam (请求体) {String} counterAuditOrderRequest.appDt 申请日期
     * @apiParam (请求体) {String} counterAuditOrderRequest.appTm 申请时间
     * @apiParam (请求体) {String} counterAuditOrderRequest.tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} counterAuditOrderRequest.outletCode 网点号
     * @apiParam (请求体) {Number} counterAuditOrderRequest.auditPassTimestamp 审核通过时间戳
     * @apiParam (请求体) {String} counterAuditOrderRequest.auditRemark 审核通过备注
     * @apiParam (请求体) {String} counterAuditOrderRequest.auditStatus 审核状态（0-无需审核、2-等待复核、3-审核通过、4-审核不通过、5-驳回至经办、7-作废、8-等待回访）
     * @apiParam (请求体) {String} counterAuditOrderRequest.revisitPerson 回访人
     * @apiParam (请求体) {Number} counterAuditOrderRequest.revisitTimestamp 回访日期时间
     * @apiParam (请求体) {String} counterAuditOrderRequest.revisitFileUrl 回访文件地址
     * @apiParam (请求体) {String} counterAuditOrderRequest.revisitReason 回访原因
     * @apiParam (请求体) {String} counterAuditOrderRequest.revisitFileName 回访文件名称
     * @apiParam (请求体) {String} counterAuditOrderRequest.revisit 是否需要回访
     * @apiParam (请求体) {String} counterAuditOrderRequest.creator 创建人
     * @apiParam (请求体) {String} counterAuditOrderRequest.reviewer 复核人
     * @apiParam (请求体) {String} counterAuditOrderRequest.modifier 修改人
     * @apiParam (请求体) {Number} counterAuditOrderRequest.createTimestamp 创建日期时间
     * @apiParam (请求体) {String} counterAuditOrderRequest.invstType 客户类型
     * @apiParam (请求体) {Number} counterAuditOrderRequest.updateTimestamp 更新日期时间
     * @apiParam (请求体) {Number} counterAuditOrderRequest.subTotalAmt 认缴总金额
     * @apiParam (请求体) {Number} counterAuditOrderRequest.cumPaidTotalAmt 累计实缴总金额
     * @apiParam (请求体) {Array} counterAuditOrderDtlRequestList
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.id 主键id
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.appSerialNo 审核申请流水号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.appSerialDtlNo 审核申请流水明细号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.hkCustNo 香港客户号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.custChineseName 客户中文姓名
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.idNoDigest 证件号码掩码
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.idType 证件类型
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.cpAcctNo 资金账号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.bankAcctMask 银行卡号掩码
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.bankName 银行名称
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.paymentType 支付方式
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.redeemType 赎回方式
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.redeemDirection 赎回方向
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.appAmt 申请金额
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.netAppAmt 净申请金额
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.appVol 申请份额
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.fee 手续费
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.appDiscount 申请折扣
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.feeRate 手续费率
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.feeCalMode 费用计算方式
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.extensionOption 展期选项
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.extensionCount 展期控制数
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.appDt 申请日期
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.appTm 申请时间
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.fundCode 基金代码
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.fundAbbr 基金简称
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.midleBusiCode 中台业务码
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.fundTxAcctNo 基金交易账号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.prebookDealNo 预约订单号
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.prebookDiscount 预约折扣
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.estimateFee 预估手续费
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.preAppAmt 预约申请金额
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.outletCode 网点号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.crmFileId CRM线上资料ID
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.revokeType 撤单类型
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.revokeReason 撤单原因
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inHkCustNo 转入香港客户号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inFundTxAcctNo 转入基金交易账号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inFundCode 转入基金代码
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inFundAbbr 转入基金简称
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inCustChineseName 转入客户中文姓名
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inIdNoDigest 转入证件号码掩码
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inIdType 转入证件类型
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.transferVol 过户份额
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.transferActualAmt 过户份额对应的实缴金额
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.transferSubsAmt 过户份额对应的认缴金额
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.transferAmt 转让价格
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.auditRemark 审核备注
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.dealNo 订单号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.dealNoDtl 订单号明细号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.inDealNo 转入订单号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.volDtlNo 份额明细号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.preSubmitTaDt 预计上报日期
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.preSubmitTaTm 预计上报时间
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.openDt 开放日
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.creator 创建人
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.modifier 修改人
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.createTimestamp 创建日期时间
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.updateTimestamp 更新日期时间
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.subAmt 认缴金额
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.cumPaidAmt 累计实缴金额
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.forceSubtractRule 强减规则：0-按指定明细、2-按先进先出
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.forceAddRule 强增规则：0-按指定明细、1-按指定日期
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.serialNumber 系列号
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.shareRegDt 份额注册日期
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.navDt 净值日期
     * @apiParam (请求体) {Number} counterAuditOrderDtlRequestList.nav 净值
     * @apiParam (请求体) {String} counterAuditOrderDtlRequestList.remark 备注
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"xYyhM","counterAuditOrderDtlRequestList":[{"inFundTxAcctNo":"JY","appAmt":9884.***********,"extensionOption":"hIYQcD","crmFileId":"Yf","bankAcctMask":"mJirBx48CU","fee":1271.*************,"modifier":"BRCxUH6ew","estimateFee":8697.************,"shareRegDt":"CG","inHkCustNo":"4YTSXRH","createTimestamp":*************,"revokeReason":"HWa7QyAD8","fundCode":"sl","appVol":7826.*************,"appSerialNo":"IhyCFXF8iF","custChineseName":"bjDt07iZXr","appDt":"9TrX","inIdNoDigest":"agMGQA","id":9915,"transferAmt":5639.************,"nav":764.************,"idType":"DC","serialNumber":"MeAJ4yu","inCustChineseName":"wKbqr","preSubmitTaDt":"leawfS8T","navDt":"7xFNYCMv","extensionCount":"r8r6Q","transferVol":7066.************,"dealNo":"wWt8SU","preSubmitTaTm":"rlZ7NwfttH","subAmt":3548.*************,"midleBusiCode":"H","openDt":"EzRJ2P","netAppAmt":9062.************,"outletCode":"iT9Jc66QJq","preAppAmt":5037.************,"inFundCode":"hN0I","revokeType":"r71f9","transferActualAmt":6511.***********,"bankName":"o6W4iaW","remark":"E","appSerialDtlNo":"dMtcn3i","feeRate":9191.************,"paymentType":"19Zs","redeemDirection":"t","fundTxAcctNo":"B1dYqO","fundAbbr":"TR","dealNoDtl":"nYTBbQ","cpAcctNo":"CdVOY","feeCalMode":"djmV","inDealNo":"Wbr","cumPaidAmt":9670.************,"appDiscount":4603.************,"prebookDiscount":5126.***********,"creator":"ceysLJw","hkCustNo":"YIhRLXAB","forceAddRule":"yS9vn6pds3","forceSubtractRule":"pkkWqE","auditRemark":"2SDfRx","idNoDigest":"Uy30u3E","updateTimestamp":*************,"prebookDealNo":"C5SpcC","appTm":"1im","volDtlNo":"E09fv","redeemType":"OArtavkYcO","inIdType":"eY","inFundAbbr":"E","transferSubsAmt":8231.************,"tradeChannel":"GLOjx6diWa"}],"ipAddress":"W","deviceName":"yEFupMU9z","systemVersion":"iBK1es5Qxu","appTm":"p4","macAddress":"2bRIXzcoEo","deviceSerialNo":"eTmPM","counterAuditOrderRequest":{"revisitPerson":"BA87chg","appAmt":2762.************,"revisitFileUrl":"vf4BahnZ","modifier":"RinIuJUj7","createTimestamp":**********66,"fundAbbr":"ByP","revisitReason":"YCt","cumPaidTotalAmt":5495.86336377278,"fundCode":"gblo","appVol":5973.381204389426,"invstType":"4EbuR1","appSerialNo":"pt","custChineseName":"D4Dt1Q83t","appDt":"v6jRM8C","id":565,"creator":"x","revisitTimestamp":3360542201893,"hkCustNo":"aIPH","revisit":"8T9VoUe5D","auditRemark":"Rfwa5","revisitFileName":"aceGGN","idNoDigest":"MMR5WNkyfz","reviewer":"BsD2alERqx","updateTimestamp":3061878047991,"counterBizType":"tQaFN","appTm":"KDvhmI","auditPassTimestamp":3448173700930,"subTotalAmt":3316.6475269360994,"auditStatus":"2ZEeX9haag","outletCode":"IdJRgiHTrZ","tradeChannel":"CqhSPVt"},"appDt":"cr","deviceModel":"Q","txCode":"fkr3G10","outletCode":"bAQ","tradeChannel":"X4pWWF"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.appSerialNo 柜台订单流水号
     * @apiSuccessExample 响应结果示例
     * {"code":"vNJ","data":{"appSerialNo":"PfISaMfNv3"},"description":"qc6"}
     */
    Response<CounterOrderBuyVO> saveCounterOrderInfo(CounterOrderSaveRequest request);
}
