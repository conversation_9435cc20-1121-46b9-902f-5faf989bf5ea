package com.howbuy.dtms.order.client.domain.response.payment;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 支付对账响应对象
 * <AUTHOR>
 * @date 2025-07-07
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentCheckResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    // 根据设计文档，响应结果通过Response的code和description字段返回
    // 此响应对象暂时为空，预留扩展字段

}
