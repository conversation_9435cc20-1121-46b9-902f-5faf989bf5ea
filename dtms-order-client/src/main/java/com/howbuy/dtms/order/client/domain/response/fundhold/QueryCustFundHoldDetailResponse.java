/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/14 10:18
 * @since JDK 1.8
 */
@Data
public class QueryCustFundHoldDetailResponse implements Serializable {

    private static final long serialVersionUID = -416163455984844509L;
    /**
     * 可用份额
     */
    @Deprecated
    private BigDecimal availableVol;

    /**
     * 锁定份额
     */
    @Deprecated
    private BigDecimal lockVol;

    /**
     * 冻结份额
     */
    @Deprecated
    private BigDecimal freezeVol;

    /**
     * 总份额
     */
    @Deprecated
    private BigDecimal totalVol;

    /**
     * 基金持仓明细列表
     */
    private List<CustFundHoldBaseDetailVO> custFundHoldBaseDetailVOList = new ArrayList<>();
}
