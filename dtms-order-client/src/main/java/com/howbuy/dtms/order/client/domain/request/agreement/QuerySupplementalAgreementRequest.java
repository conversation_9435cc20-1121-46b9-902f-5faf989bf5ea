package com.howbuy.dtms.order.client.domain.request.agreement;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 查询未签署补签协议请求对象
 * <AUTHOR>
 * @date 2024/03/06 17:35:21
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class QuerySupplementalAgreementRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金编码
     */
    private String fundCode;

    /**
     * 签署状态
     */
    private String signStatus;
} 