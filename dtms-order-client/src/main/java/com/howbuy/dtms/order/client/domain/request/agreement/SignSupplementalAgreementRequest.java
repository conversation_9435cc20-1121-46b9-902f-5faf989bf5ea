package com.howbuy.dtms.order.client.domain.request.agreement;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 线上补签协议请求对象
 * <AUTHOR>
 * @date 2024/03/06 17:35:21
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class SignSupplementalAgreementRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 补签协议ID
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "补签协议ID", isRequired = true)
    private List<String> agreementIdList;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金编码", isRequired = true)
    private String fundCode;
} 