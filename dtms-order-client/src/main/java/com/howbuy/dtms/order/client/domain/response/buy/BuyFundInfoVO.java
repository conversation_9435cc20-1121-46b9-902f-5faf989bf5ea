/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 买入基金信息
 * @date 2024/4/10 10:12
 * @since JDK 1.8
 */
@Data
public class BuyFundInfoVO implements Serializable {
    private static final long serialVersionUID = -8745933144681662861L;
    /**
     * 开放开始日期 yyyyMMdd
     */
    private String openStartDt;

    /**
     * 开放结束日期 yyyyMMdd
     */
    private String openEndDt;

    /**
     * 打款截止日期 yyyyMMdd
     */
    private String payEndDt;

    /**
     * 打款截止时间 HHmmss
     */
    private String payEndTm;

    /**
     * 交易日期 yyyyMMdd
     */
    private String tradeDt;

    /**
     * 最小申请金额
     */
    private BigDecimal minAppAmt;

    /**
     * 最大申请金额
     */
    private BigDecimal maxAppAmt;

    /**
     * 级差
     */
    private BigDecimal differential;

    /**
     * 首次实缴比例
     */
    private BigDecimal firstPayInRatio;

    /**
     * 本期实缴比例
     */
    private BigDecimal currentPayInRatio;

}
