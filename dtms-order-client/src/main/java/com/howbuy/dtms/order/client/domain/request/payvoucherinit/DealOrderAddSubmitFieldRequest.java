/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucherinit;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;

import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/10/22 9:31
 * @since JDK 1.8
 */
public class DealOrderAddSubmitFieldRequest extends BaseRequest {

    /**
     * 申请成功未确认,未上报的订单号
     */
    private List<String> appSuccessNoAckNoRepOrderNoList;

    /**
     * 申请成功未确认已上报的订单号
     */
    private List<String> appSuccessNoAckReportOrderNoList;

    /**
     * 是否同步订单明细基金管理人
     * 1 是 0 否
     */
    private String updateFundMan;

    public List<String> getAppSuccessNoAckNoRepOrderNoList() {
        return appSuccessNoAckNoRepOrderNoList;
    }

    public void setAppSuccessNoAckNoRepOrderNoList(List<String> appSuccessNoAckNoRepOrderNoList) {
        this.appSuccessNoAckNoRepOrderNoList = appSuccessNoAckNoRepOrderNoList;
    }

    public List<String> getAppSuccessNoAckReportOrderNoList() {
        return appSuccessNoAckReportOrderNoList;
    }

    public void setAppSuccessNoAckReportOrderNoList(List<String> appSuccessNoAckReportOrderNoList) {
        this.appSuccessNoAckReportOrderNoList = appSuccessNoAckReportOrderNoList;
    }

    public String getUpdateFundMan() {
        return updateFundMan;
    }

    public void setUpdateFundMan(String updateFundMan) {
        this.updateFundMan = updateFundMan;
    }
}
