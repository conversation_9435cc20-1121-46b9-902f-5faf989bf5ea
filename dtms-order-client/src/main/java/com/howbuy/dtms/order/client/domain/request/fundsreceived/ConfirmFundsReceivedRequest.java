/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fundsreceived;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: 资金到账匹配请求
 * <AUTHOR>
 * @date 2024/7/23 10:57
 * @since JDK 1.8
 */

@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class ConfirmFundsReceivedRequest extends BaseRequest {

    private static final long serialVersionUID = 5095131815926801586L;
    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 实际打款金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "实际打款金额", isRequired = true)
    private BigDecimal actualPayAmt;

    /**
     * 实际打款日期
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实际打款日期", isRequired = true)
    private String actualPayDt;

    /**
     * 实际打款时间
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实际打款时间", isRequired = true)
    private String actualPayTm;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 标识：question提示：【该笔订单实际到账币种与基金的币种不一致，请确认换汇后的实际打款金额是否正确】
     */
    private String currencyFlag;

    /**
     * 标识：question提示：【该笔订单的上传打款凭证为{未上传、已上传待审核、审核不通过}，请确认是否继续进行到账确认】
     */
    private String voucherStatusFlag;

}