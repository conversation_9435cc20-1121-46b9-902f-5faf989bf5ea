/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 打款凭证提交请求
 * @date 2024/4/10 16:22
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PayVoucherSubmitRequest extends BaseRequest {

    public PayVoucherSubmitRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_PAY_VOUCHER_SUBMIT);
    }

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true)
    private String dealNo;

    /**
     * 银行卡号掩码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "银行卡号掩码", isRequired = true)
    private String bankAcctMask;

    /**
     * 银行英文名称
     */
    private String bankEnName;

    /**
     * 银行中文名称
     */
    private String bankChineseName;

    /**
     * SWIFT代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "SWIFT代码", isRequired = true)
    private String swiftCode;

    /**
     * 币种代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "币种代码", isRequired = true)
    private String currency;

    /**
     * 汇款金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "汇款金额", isRequired = true)
    private BigDecimal remitAmt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 删除文件id列表
     */
    private List<String> delFileIdList;

    /**
     * 资料id
     */
    private String orderId;

    /**
     * 文件类型id
     */
    private String fileTypeId;

    /**
     * 打款凭证文件列表
     */
    private List<PayVoucherFileDTO> payVoucherFileList;

}
