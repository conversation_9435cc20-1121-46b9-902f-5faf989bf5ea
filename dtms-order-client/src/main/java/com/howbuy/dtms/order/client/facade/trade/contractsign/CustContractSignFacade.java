/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.contractsign;

import com.howbuy.dtms.order.client.domain.request.contractsign.CustContractSignRequest;
import com.howbuy.dtms.order.client.domain.response.contractsign.CustContractSignVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/5/14 18:55
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.contractsign.CustContractSignFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup CustContractSignFacade
 * @apiName execute()
 * @apiDescription 海外合同签约接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} tradeMode 交易方式 1-购买；2-赎回
 * @apiParam (请求参数) {String} contractSignFlag 协议签订标识
 * @apiParam (请求参数) {Array} contractList 协议list
 * @apiParam (请求参数) {String} contractList.fileCode 文件代码
 * @apiParam (请求参数) {String} contractList.fileName 文件名称
 * @apiParam (请求参数) {String} contractList.filePathUrl 文件路径
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=i&hkCustNo=dhgAAKsY&ipAddress=dGNocsEdO&deviceName=Knm&systemVersion=4Ob&appTm=8&macAddress=uqLL&deviceSerialNo=jk49ltnD&fundCode=2F07X&contractList={"filePathUrl":"fe","fileName":"MB","fileCode":"I9oEz"}&tradeMode=jT&appDt=Zqup4zV4&deviceModel=y7ImuHY&txCode=9nZjj9j2D2&contractSignFlag=9qNjSvjNV&outletCode=E&tradeChannel=gy
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.hkCustNo 客户号
 * @apiSuccess (响应结果) {String} data.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.signFlag 协议签署状态
 * @apiSuccess (响应结果) {String} data.signTime 协议签署日期时间 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.signDate 协议明细签署时间 yyyy-MM-dd HH:mm:ss
 * @apiSuccess (响应结果) {String} data.dealNo 订单号
 * @apiSuccess (响应结果) {Array} data.custContractDtlSignList 签署详情
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.fileType 文件代码
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.fileName 文件名称
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.fileTypeDesc 文件类型描述
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.filePath 文件路径
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.signFlag 协议明细签署状态	 0-未签署；1-已签署
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.signDate 协议明细签署时间
 * @apiSuccessExample 响应结果示例
 * {"code":"9L","data":{"signFlag":"a","signTime":"N","fundCode":"ya","hkCustNo":"iCLke5l","custContractDtlSignList":[{"signFlag":"Z7l","fileName":"nA6b","fileTypeDesc":"N0JVDMs","filePath":"gDYY","signDate":"1Zcz","fileType":"cIS1mqLCfx"}],"signDate":"JYmonUs4","dealNo":"KiMU5XA"},"description":"5EyIwhzfY"}
 */
public interface CustContractSignFacade extends BaseFacade<CustContractSignRequest, CustContractSignVO> {

}
