package com.howbuy.dtms.order.client.domain.request.subandfirstpaid;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: APP认缴和首次实缴请求参数
 * @author: jinqing.rao
 * @date: 2025/4/15 16:03
 * @since JDK 1.8
 */
@Getter
@Setter
public class AppSubAndFirstPaidSubmitRequest extends BaseRequest {
    private static final long serialVersionUID = 7064346872338976954L;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 支付方式(1-电汇、2-支票、3-海外储蓄罐)
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "支付方式", isRequired = true)
    private String payMethod;

    /**
     * 外部订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "外部订单号", isRequired = true)
    private String externalDealNo;

    /**
     * 实缴外部订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实缴外部订单号", isRequired = true)
    private String paidExternalDealNo;

    /**
     * 实缴净申请金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实缴净申请金额", isRequired = true)
    private BigDecimal paidNetAppAmt;

    /**
     * 认缴净申请金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "认缴净申请金额", isRequired = true)
    private BigDecimal subNetAppAmt;

    /**
     * 预估手续费
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预估手续费", isRequired = true)
    private BigDecimal estimateFee;

    /**
     * 手续费类型  1 认缴  2 实缴
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "手续费类型", isRequired = true)
    private String feeRateType;

    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true)
    private String dealNo;


    /**
     * 实缴订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实缴订单号", isRequired = true)
    private String paidDealNo;

    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 申请折扣率
     */
    private BigDecimal applyDiscountRate;

    /**
     * 折扣类型
     */
    private String discountType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 0-否 1-是
     */
    private String isAgreeCurrencyExchange;

} 