/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.hkdealorder;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/9 16:17
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryHkDealOrderRequest extends BaseRequest {
    /**
     * 开放日开始
     */
    private String openStartDt;

    /**
     * 开放日结束
     */
    private String openEndDt;

    /**
     * 申请开始
     */
    private String appStartDt;

    /**
     * 申请时间结束
     */
    private String appEndDt;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 业务类型
     */
    private String busiType;

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 基金代码
     */
    private String productCode;

    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 申请状态
     */
    private String appStatus;

    /**
     * 支付方式
     */
    private String paymentType;

    /**
     * 赎回去向
     */
    private String redeemDirection;

    /**
     * 付款状态
     */
    private String payStatus;

    /**
     * 订单状态
     */
    private String orderStatus;

    private int pageNo;

    private int pageSize;
}