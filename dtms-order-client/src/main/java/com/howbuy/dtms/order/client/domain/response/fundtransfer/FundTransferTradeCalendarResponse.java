/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundtransfer;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/12/11 12:27
 * @since JDK 1.8
 */
public class FundTransferTradeCalendarResponse implements Serializable {

    private static final long serialVersionUID = 8475983744987227832L;

    /**
     * 预计上报时间
     */
    private String expectReportDt;

    /**
     * 预计上报时间
     */
    private String expectReportTm;

    /**
     * 开放日
     */
    private String openDt;

    public String getExpectReportDt() {
        return expectReportDt;
    }

    public void setExpectReportDt(String expectReportDt) {
        this.expectReportDt = expectReportDt;
    }

    public String getExpectReportTm() {
        return expectReportTm;
    }

    public void setExpectReportTm(String expectReportTm) {
        this.expectReportTm = expectReportTm;
    }

    public String getOpenDt() {
        return openDt;
    }

    public void setOpenDt(String openDt) {
        this.openDt = openDt;
    }
}
