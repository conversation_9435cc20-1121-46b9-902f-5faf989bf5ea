package com.howbuy.dtms.order.client.facade.query.traderecord;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryDealOrderDetailRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.DealOrderDetailInfoResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;


/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.traderecord.QueryDealOrderDetailFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryDealOrderDetailFacade
 * @apiDescription 查询订单详情数据
 * @apiParam (请求体) {String} contractNo 订单号
 * @apiParam (请求体) {String} txCode 交易码
 * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求体) {String} appTm 申请时间 HHmmss
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress ipAddress
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {String} macAddress MAC地址
 * @apiParam (请求体) {String} deviceSerialNo 设备序列号
 * @apiParam (请求体) {String} deviceModel 设备型号
 * @apiParam (请求体) {String} deviceName 设备名称
 * @apiParam (请求体) {String} systemVersion 系统版本号
 * @apiParamExample 请求体示例
 * {"externalDealNo":"BfW","contractNo":"KPfKX0YmZX","ipAddress":"VL99im51","deviceName":"zPTjpCNkW","systemVersion":"UL6","appTm":"Wu1N2","macAddress":"uFNcz","deviceSerialNo":"cp384y1M","appDt":"wBX8x","deviceModel":"eRiMP","txCode":"V","outletCode":"a","tradeChannel":"Eri99"}
 * {"buyAmt":3350.750158773168,"appTm":"7YYQiOQOD","fundCode":"HWlTWRr","hkCustNo":"iH","ipAddress":"pHX8kYsE","appDt":"l4iB","outletCode":"If","tradeChannel":"WIT"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.fundCode 基金代码 (转入基金代码)
 * @apiSuccess (响应结果) {String} data.fundShortName 基金简称 (转入基金简称)
 * @apiSuccess (响应结果) {String} data.redeemMethod 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.redeemDirectionList 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
 * @apiSuccess (响应结果) {String} data.supportPrebookFlag 支持预约交易标识 0-不支持；1-仅支持购买预约；2-仅支持赎回预约；3-支持购买赎回预约
 * @apiSuccess (响应结果) {Number} data.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.appVol 申请份额
 * @apiSuccess (响应结果) {Number} data.appVolStr 申请份额
 * @apiSuccess (响应结果) {String} data.currency 币种代码
 * @apiSuccess (响应结果) {String} data.payEndDt 打款截止日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.payEndTm 打款截止时间 HHmmss
 * @apiSuccess (响应结果) {String} data.hkCpAcctNo 香港资金账号
 * @apiSuccess (响应结果) {String} data.swiftCode SWIFT代码
 * @apiSuccess (响应结果) {String} data.transferAckVol 转出确认份额
 * @apiSuccess (响应结果) {String} data.transferAckVolStr 转出确认份额
 * @apiSuccess (响应结果) {String} data.transferAckAmt 转出确认金额
 * @apiSuccess (响应结果) {String} data.transferNav 转出确认净值
 * @apiSuccess (响应结果) {String} data.transferFundName 转出基金名称
 * @apiSuccess (响应结果) {String} data.transferFundCode 转出基金代码
 * @apiSuccess (响应结果) {String} data.transferCurrency 转出币种
 * @apiSuccess (响应结果) {String} data.bankCode 银行代码
 * @apiSuccess (响应结果) {String} data.bankAcctMask 银行卡号掩码
 * @apiSuccess (响应结果) {String} data.bankEnName 银行英文名称
 * @apiSuccess (响应结果) {String} data.bankChineseName 银行中文名称
 * @apiSuccess (响应结果) {Array} data.bankCurrencyList 银行币种列表
 * @apiSuccess (响应结果) {Array} data.mutiCardList 关联银行卡列表
 * @apiSuccess (响应结果) {String} data.mutiCardList.cpAcctNo 资金账号
 * @apiSuccess (响应结果) {String} data.mutiCardList.bankAcctNo 银行卡号      a）默认展示：银行名称“前四位”+“****”+“后四位”；      b）若客户存在多张银行卡首尾4位数字一致，则增加中间位校验，即将中间位不一致的第一位展示出来，      样式为：银行名称“前四位”+“**”+“第一个不一样的”+“**”+“后四位”
 * @apiSuccess (响应结果) {String} data.mutiCardList.bankLogoUrl 银行logo地址
 * @apiSuccess (响应结果) {String} data.mutiCardList.bankName 银行名称
 * @apiSuccess (响应结果) {String} data.mutiCardList.bankCode 银行编号
 * @apiSuccess (响应结果) {Number} data.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.custChineseName 客户中文姓名
 * @apiSuccess (响应结果) {String} data.idType 证件类型
 * @apiSuccess (响应结果) {String} data.idNoCipher 证件号码密文
 * @apiSuccess (响应结果) {String} data.idNoDigest 证件号码摘要
 * @apiSuccess (响应结果) {String} data.idNoMask 证件号码掩码
 * @apiSuccess (响应结果) {String} data.invstType 投资者类型 0-机构；1-个人
 * @apiSuccess (响应结果) {String} data.qualificationType 投资者资质 PRO-投资者资质专业;NORMAL-投资者资质普通
 * @apiSuccess (响应结果) {String} data.custRiskLevel 客户风险等级
 * @apiSuccess (响应结果) {String} data.txCode 交易代码:HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验
 * @apiSuccess (响应结果) {String} data.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
 * @apiSuccess (响应结果) {String} data.productName 产品名称
 * @apiSuccess (响应结果) {String} data.productCode 产品代码
 * @apiSuccess (响应结果) {String} data.appDt 申请日期
 * @apiSuccess (响应结果) {String} data.appTm 申请时间
 * @apiSuccess (响应结果) {String} data.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {String} data.supportRepeal 是否支持撤单, 1 : 是 0 ： 否
 * @apiSuccess (响应结果) {String} data.prebookDealNo 预约单号
 * @apiSuccess (响应结果) {String} data.externalDealNo 外部单号
 * @apiSuccess (响应结果) {String} data.firstBuyFlag 首次购买标识，1-首次购买；2-追加购买
 * @apiSuccess (响应结果) {String} data.isAgreeCurrencyExchange 是否同意换汇 0-否 1-是
 * @apiSuccess (响应结果) {String} data.orderFormType 成单方式 1：纸质成单；2：电子成单；
 * @apiSuccess (响应结果) {String} data.memo 备注
 * @apiSuccess (响应结果) {String} data.tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiSuccess (响应结果) {String} data.ipAddress IP地址
 * @apiSuccess (响应结果) {String} data.outletCode 网点号
 * @apiSuccess (响应结果) {Number} data.dealDtlNo 订单明细号
 * @apiSuccess (响应结果) {String} data.cpAcctNo 资金账号
 * @apiSuccess (响应结果) {String} data.bankAcctCipher 银行账号密文
 * @apiSuccess (响应结果) {String} data.bankAcctDigest 银行账号摘要
 * @apiSuccess (响应结果) {String} data.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.mainFundCode 主基金代码
 * @apiSuccess (响应结果) {String} data.fundCategory 基金类别 1-公募、2-私募、9-其他
 * @apiSuccess (响应结果) {String} data.fundRiskLevel 基金风险等级
 * @apiSuccess (响应结果) {String} data.paymentTypeList 支付方式 1-电汇、2-支票、3-海外储蓄罐
 * @apiSuccess (响应结果) {String} data.redeemType 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.middleBusiCode 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出
 * @apiSuccess (响应结果) {Number} data.netAppAmt 净申请金额
 * @apiSuccess (响应结果) {Number} data.estimateFee 预估手续费
 * @apiSuccess (响应结果) {Number} data.prebookDiscount 预约折扣
 * @apiSuccess (响应结果) {Number} data.discountRate 折扣率
 * @apiSuccess (响应结果) {Number} data.feeRate 手续费率
 * @apiSuccess (响应结果) {String} data.feeCalMode 费用计算方式 0-外扣法、1-内扣法
 * @apiSuccess (响应结果) {Number} data.fee 手续费
 * @apiSuccess (响应结果) {Number} data.ackAmt 确认金额 (转入确认金额)
 * @apiSuccess (响应结果) {Number} data.ackVol 确认份额 (转入确认份额)
 * @apiSuccess (响应结果) {Number} data.ackVolStr 确认份额 (转入确认份额)
 * @apiSuccess (响应结果) {Number} data.ackNav 确认净值 (转入确认净值)
 * @apiSuccess (响应结果) {String} data.appStatus 申请状态 0-申请成功；1-申请失败；2-自行撤销；3-强制取消
 * @apiSuccess (响应结果) {String} data.ackStatus 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
 * @apiSuccess (响应结果) {String} data.payStatus 支付状态 0-无需支付；1-未打款；2-已打款；3-到账确认；4-退款；
 * @apiSuccess (响应结果) {String} data.payVoucherStatus 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
 * @apiSuccess (响应结果) {Number} data.actualPayAmt 实际打款金额
 * @apiSuccess (响应结果) {String} data.actualPayDt 实际打款日期
 * @apiSuccess (响应结果) {String} data.actualPayTm 实际打款时间
 * @apiSuccess (响应结果) {String} data.fundDivMode 分红方式 0-红利再投 1-现金分红 2-N/A不适用
 * @apiSuccess (响应结果) {String} data.openDt 开放日期
 * @apiSuccess (响应结果) {String} data.taTradeDt TA交易日期
 * @apiSuccess (响应结果) {String} data.submitTaDt 上报TA日期
 * @apiSuccess (响应结果) {String} data.ackDt 确认日期
 * @apiSuccess (响应结果) {String} data.taAckNo TA确认流水号
 * @apiSuccess (响应结果) {String} data.taCode TA代码
 * @apiSuccess (响应结果) {Number} data.createTimestamp 创建时间戳
 * @apiSuccess (响应结果) {Number} data.updateTimestamp 更新时间戳
 * @apiSuccess (响应结果) {Array} data.dealOrderMultiDetialInfoResponses 多笔订单明细数据
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.seriesNo 系列号
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.regDate 份额注册日期
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.tradeDate 交易日期
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.confirmDate 确认日期
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.confirmAmt 确认金额
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.confirmVol 确认份额
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.redeemFee 赎回费
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.confirmNav 确认净值
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.currency 币种
 * @apiSuccess (响应结果) {String} data.dealOrderMultiDetialInfoResponses.currencyDesc 币种描述
 * @apiSuccessExample 响应结果示例
 * {"code":"ggzVCy","data":{"outAckVol":"sQhHcNd","hkCpAcctNo":"N","discountRate":8081.************,"externalDealNo":"r6LEPd48Di","appAmt":6208.************,"bankAcctMask":"eWaIGkH","fee":5780.************,"memo":"4QFlXcMq","taTradeDt":"b0fN","fundShortName":"Lt18ZChyU","estimateFee":1188.*************,"productName":"ceCGYD9NDE","createTimestamp":*************,"redeemMethod":"BFKBu41k","taCode":"CnG","dealOrderMultiDetialInfoResponses":[{"redeemFee":"X1Tfhu","confirmDate":"iRW","seriesNo":"X","confirmVol":"vfg59","confirmNav":"x1WYlQ","regDate":"B2xip7UXRR","tradeDate":"LeiFC56L2","confirmAmt":"Xva"}],"fundCode":"VcuNYQ6","outAckAmt":"mvqjb0HYAL","appVol":5560.*************,"bankCurrencyList":["R369un"],"bankAcctCipher":"sGKIV33tsj","fundCategory":"gJKBQIF","custChineseName":"kgBkK","appDt":"ToG0BCD6","firstBuyFlag":"JSbS","ackVol":2403.*************,"idType":"d0tviDp","submitTaDt":"84Osb","taAckNo":"hGbWXb","ipAddress":"MQE7XO","dealNo":5694,"appStatus":"D6tGL","fundRiskLevel":"rH202dT","ackStatus":"TaZBLUjE","transferCurrency":"L2y","payEndDt":"JO","openDt":"H","txCode":"f1g0nA","fundName":"TRsPe","outletCode":"1nf","netAppAmt":1561.*************,"dealDtlNo":5898,"actualPayTm":"SGj8DMjW","orderFormType":"Q2Ye9envNx","bankEnName":"NOU5r7","swiftCode":"HSrwcPSJen","orderStatus":"HUQEWA1XX","fundDivMode":"0Pg","feeRate":95.*************,"transferFundName":"EBCNbvQw","mutiCardList":[{"bankLogoUrl":"5tQ2omlO9","bankCode":"uy5Ahe","bankAcctNo":"Td3jhGwxhy","cpAcctNo":"7RxPkd9Uk3","bankName":"OpR4jw8"}],"qualificationType":"f34TbK1gS","ackAmt":5425.***********,"payEndTm":"vo3TrDk","invstType":"FHuSO","supportPrebookFlag":"jx","mainFundCode":"Te","currency":"VFovnMMv9","cpAcctNo":"wk","redeemDirectionList":"Sdvg7PtG","feeCalMode":"tePpAOn","prebookDiscount":7615.************,"bankCode":"EtrziEU","bankChineseName":"AzXk","payVoucherStatus":"F3YGP","outNav":"wvJE","middleBusiCode":"Wf68n4z","isAgreeCurrencyExchange":"9VUjcE","hkCustNo":"5E3qTL","custRiskLevel":"7zkQVz","idNoDigest":"FMwC4kx","idNoMask":"G3IakR0n","bankAcctDigest":"4EQX0t","updateTimestamp":*************,"prebookDealNo":"nh","supportRepeal":"hTc7ryl9","ackNav":5123.************,"appTm":"kc","productCode":"9","paymentTypeList":"6XzIdRbn","redeemType":"vxYr","transferFundCode":"x41","actualPayAmt":8501.************,"businessType":"u4","ackDt":"4wY3","idNoCipher":"Jraj2mpnE","payStatus":"OlvKD","actualPayDt":"I0n","tradeChannel":"XVDE9Yg"},"description":"ovf"}
 */
public interface QueryDealOrderDetailFacade extends BaseFacade<QueryDealOrderDetailRequest, DealOrderDetailInfoResponse> {
}
