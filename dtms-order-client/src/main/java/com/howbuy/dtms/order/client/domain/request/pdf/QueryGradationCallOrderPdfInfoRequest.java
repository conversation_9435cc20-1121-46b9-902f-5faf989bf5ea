package com.howbuy.dtms.order.client.domain.request.pdf;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;

/**
 * <AUTHOR>
 * @description: 请求类 - 查询分级赎回订单PDF信息
 * @date 2024/5/9 16:17
 * @since JDK 1.8
 */


public class QueryGradationCallOrderPdfInfoRequest extends BaseRequest {

    private static final long serialVersionUID = -5758897558508405016L;

    public QueryGradationCallOrderPdfInfoRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_QUERY_GRADATION_CALL_ORDER_PDF);
    }

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }
}