/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 批量储蓄罐交易申请买入请求
 * <AUTHOR>
 * @date 2025-07-16 14:45:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class BatchPiggyTradeAppBuyRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 储蓄罐申请买入列表
     */
    private List<PiggyTradeAppBuyItem> piggyTradeAppBuyList;

    /**
     * 储蓄罐申请买入项
     */
    @Getter
    @Setter
    public static class PiggyTradeAppBuyItem implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 导入申请id
         */
        private String importAppId;

        /**
         * 储蓄罐申请来源 0-excel 1-可用余额、2-客户控管表、3-退款控管表、4-自动赎回
         */
        private String piggyAppSource;

        /**
         * 香港客户号
         */
        private String hkCustNo;

        /**
         * 基金交易账号
         */
        private String fundTxAcctNo;

        /**
         * 申请金额
         */
        private BigDecimal appAmt;

        /**
         * 关联订单号
         */
        private String relationalDealNo;
    }
}
