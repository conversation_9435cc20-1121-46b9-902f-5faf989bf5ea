/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fundhold;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/14 10:16
 * @since JDK 1.8
 */
public class QueryCustFundHoldDetailRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -1968699168548215134L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 查询类型 0-非赎回下单 1-赎回下单
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "查询类型", isRequired = true)
    private String queryType;

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public void setQueryType(String queryType) {
        this.queryType = queryType;
    }

    public String getQueryType() {
        return queryType;
    }
}
