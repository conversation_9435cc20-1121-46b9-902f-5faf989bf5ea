/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundtxacct;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/13 13:22
 * @since JDK 1.8
 */
@Data
public class QueryFundTxAcctResponse implements Serializable {

    private static final long serialVersionUID = 4118529540163192332L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 0-非全委 1-全委
     */
    private String fundTxAccType;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金交易账号状态 1-正常 2-注销 3-冻结
     */
    private String fundTxAcctStat;
}
