/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.asset;

import com.howbuy.dtms.order.client.domain.response.order.DealOrderVO;
import lombok.Getter;
import lombok.Setter;

import java.util.List;


/**
 * @description: 在途资产查询
 * <AUTHOR>
 * @date 2024/7/22 16:41
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryInTransitAssetsOrderVO {

    List<DealOrderVO> inTransitAssetsOrderVOList;
}
