package com.howbuy.dtms.order.client.domain.request.payment;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 支付对账请求对象
 * <AUTHOR>
 * @date 2025-07-07
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentCheckRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 支付对账日期，格式：yyyyMMdd
     */
    private String pmtCheckDt;

}
