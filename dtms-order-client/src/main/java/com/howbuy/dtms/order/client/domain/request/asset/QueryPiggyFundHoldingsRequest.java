/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.asset;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 客户储蓄罐基金持仓查询接口
 * <AUTHOR>
 * @date 2024/7/23 11:01
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryPiggyFundHoldingsRequest extends BaseRequest implements Serializable {


    private static final long serialVersionUID = 399522436783608193L;

    /**
     * 基金Code
     */
    private String fundCode;

    /**
     * 香港客户号
     */
    private String hkCustNo;
}
