/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.sell;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/4/25 16:24
 * @since JDK 1.8
 */
@Data
public class PrebookSellInfoVO implements Serializable {
    private static final long serialVersionUID = 3963002097307415814L;
    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 预约赎回方式 1-按份额赎回；2-按金额赎回
     */
    private String prebookRedeemMethod;

    /**
     * 预约申请金额
     */
    private BigDecimal prebookAppAmt;

    /**
     * 预约申请份额
     */
    private BigDecimal prebookAppVol;
}
