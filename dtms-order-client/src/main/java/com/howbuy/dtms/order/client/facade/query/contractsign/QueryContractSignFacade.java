/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.contractsign;

import com.howbuy.dtms.order.client.domain.request.contractsign.QueryContractSignRequest;
import com.howbuy.dtms.order.client.domain.response.contractsign.ConstractFileByteListVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 查询订单的合同文件字节流的接口
 * <AUTHOR>
 * @date 2024/12/3 15:08
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.contractsign.QueryContractSignFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryContractSignFacade
 * @apiName execute()
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} dealNo 订单号
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=v21&hkCustNo=RvqJL&ipAddress=u7uTWx&dealNo=oS2l&deviceName=U4Ef&systemVersion=Nd0eEmUp7Q&appTm=voFV&macAddress=BA&deviceSerialNo=m&fundCode=npp7Ac1u2&appDt=kMbaq&deviceModel=2SzRXatb5&txCode=pYx&outletCode=uI03Zq&tradeChannel=oOsZDakJh
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.custContractFileList 合同的文件字节信息列表
 * @apiSuccess (响应结果) {String} data.custContractFileList.id
 * @apiSuccess (响应结果) {String} data.custContractFileList.fileTypeName
 * @apiSuccess (响应结果) {String} data.custContractFileList.filePath
 * @apiSuccess (响应结果) {String} data.custContractFileList.fileBytes
 * @apiSuccess (响应结果) {String} data.custContractFileList.fileName
 * @apiSuccess (响应结果) {String} data.custContractFileList.valid
 * @apiSuccess (响应结果) {String} data.custContractFileList.isRequire
 * @apiSuccessExample 响应结果示例
 * {"code":"EZlY","data":{"custContractFileList":[{"valid":"omRinP","fileName":"d","filePath":"a","fileTypeName":"jd2QH","id":"SnRD","fileBytes":"9O","isRequire":"2rryD"}]},"description":"iI8NMkKO"}
 */
public interface QueryContractSignFacade extends BaseFacade<QueryContractSignRequest, ConstractFileByteListVO> {
}
