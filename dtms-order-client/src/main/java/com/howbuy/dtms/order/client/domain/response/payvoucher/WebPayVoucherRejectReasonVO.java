/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/8/29 9:47
 * @since JDK 1.8
 */
@Getter
@Setter
public class WebPayVoucherRejectReasonVO implements Serializable {

    private static final long serialVersionUID = -43114898197700901L;

    private String id;

    /**
     * 审核人
     */
    private String auditOperator;

    /**
     * 审核时间
     */
    private Date auditTime;

    /**
     * 审核原因
     */
    private String auditReason;


}