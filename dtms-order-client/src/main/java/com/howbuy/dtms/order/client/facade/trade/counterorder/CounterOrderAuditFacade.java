/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.counterorder;

import com.howbuy.dtms.order.client.domain.request.counterorder.CounterOrderAuditRequest;
import com.howbuy.dtms.order.client.domain.response.Body;
import com.howbuy.dtms.order.client.domain.response.Response;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/6 14:06
 * @since JDK 1.8
 */
public interface CounterOrderAuditFacade {

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.counterorder.CounterOrderAuditFacade.counterOrderAuditPass()
     * @apiVersion 1.0.0
     * @apiGroup CounterOrderAuditFacade
     * @apiName counterOrderAuditPass()
     * @apiDescription 审核通过接口
     * @apiParam (请求参数) {String} appSerialNo 交易申请流水号
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {String} operator 操作员
     * @apiParam (请求参数) {String} operatorTimestamp 操作时间戳
     * @apiParam (请求参数) {String} remark 审核备注
     * @apiParam (请求参数) {String} auditStatus 审核状态
     * @apiParam (请求参数) {String} counterBizType 柜台业务码
     * @apiParam (请求参数) {String} revisitFileUrl 回访文件地址，回访状态时必传
     * @apiParam (请求参数) {String} revisitFileName 回访文件名称，回访状态时必传
     * @apiParam (请求参数) {String} revisitPerson 回访人
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
     * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求参数) {String} outletCode 网点号
     * @apiParam (请求参数) {String} ipAddress ipAddress
     * @apiParam (请求参数) {String} externalDealNo 外部订单号
     * @apiParam (请求参数) {String} macAddress MAC地址
     * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
     * @apiParam (请求参数) {String} deviceModel 设备型号
     * @apiParam (请求参数) {String} deviceName 设备名称
     * @apiParam (请求参数) {String} systemVersion 系统版本号
     * @apiParamExample 请求参数示例
     * revisitPerson=Q5Yaz&externalDealNo=1JlR6b&revisitFileUrl=1UD&hkCustNo=OFy4L&ipAddress=q7pzsDZ&remark=fgr&revisitFileName=tR9FNmu3Q&deviceName=p8EVjtPdS&systemVersion=yv&operator=ZHLeiJb&counterBizType=SV&appTm=YDKpS1NrI&macAddress=MImjhYnwmh&deviceSerialNo=DhYUzzPlB&appSerialNo=6&operatorTimestamp=6jIofcnLWj&auditStatus=9ujg4z&appDt=G9tqu&deviceModel=6iLh1y&txCode=p5&outletCode=R17tNfvE&tradeChannel=t2kZNGu
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"cX7EHl","description":"H4jyIWS8dm"}
     */
    Response<Body> counterOrderAuditPass(CounterOrderAuditRequest request);

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.counterorder.CounterOrderAuditFacade.counterOrderAuditNotPass()
     * @apiVersion 1.0.0
     * @apiGroup CounterOrderAuditFacade
     * @apiName counterOrderAuditNotPass()
     * @apiDescription 审核不通过接口
     * @apiParam (请求参数) {String} appSerialNo 交易申请流水号
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {String} operator 操作员
     * @apiParam (请求参数) {String} operatorTimestamp 操作时间戳
     * @apiParam (请求参数) {String} remark 审核备注
     * @apiParam (请求参数) {String} auditStatus 审核状态
     * @apiParam (请求参数) {String} counterBizType 柜台业务码
     * @apiParam (请求参数) {String} revisitFileUrl 回访文件地址，回访状态时必传
     * @apiParam (请求参数) {String} revisitFileName 回访文件名称，回访状态时必传
     * @apiParam (请求参数) {String} revisitPerson 回访人
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
     * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求参数) {String} outletCode 网点号
     * @apiParam (请求参数) {String} ipAddress ipAddress
     * @apiParam (请求参数) {String} externalDealNo 外部订单号
     * @apiParam (请求参数) {String} macAddress MAC地址
     * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
     * @apiParam (请求参数) {String} deviceModel 设备型号
     * @apiParam (请求参数) {String} deviceName 设备名称
     * @apiParam (请求参数) {String} systemVersion 系统版本号
     * @apiParamExample 请求参数示例
     * revisitPerson=Fffbf0f&externalDealNo=1&revisitFileUrl=WKE6zW6tkI&hkCustNo=Vttt2&ipAddress=jz&remark=NVi&revisitFileName=820BU46DDu&deviceName=aGTFTnC&systemVersion=wG&operator=r2KgfUkz&counterBizType=GKzOpqe81F&appTm=6qHPFCNlcB&macAddress=FdITjLc0F&deviceSerialNo=ZTe&appSerialNo=a2XaW&operatorTimestamp=Ck30TRb8nx&auditStatus=zzOocuPSH&appDt=aATYWsPa&deviceModel=lSYId7b&txCode=Eq9H4Mnd&outletCode=rBchx2TzZJ&tradeChannel=Nwryzrck
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"qxL","description":"iLrGaISlE9"}
     */
    Response<Body> counterOrderAuditNotPass(CounterOrderAuditRequest request);
}
