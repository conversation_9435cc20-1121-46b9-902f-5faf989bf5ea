/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.fundhold;

import com.howbuy.dtms.order.client.domain.request.fundhold.QueryCustFundHoldFundRequest;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryCustFundHoldFundResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 查询用户持仓基金列表
 * <AUTHOR>
 * @date 2024/7/30 15:43
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryCustFundHoldFundFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryCustFundHoldFundFacade
 * @apiName 香港客户号查询海外持仓基金
 * @apiDescription 香港客户号查询海外持仓基金
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParamExample 请求参数示例
 * hkCustNo=9fwP5S9
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.fundCodeList 持仓基金Code
 * @apiSuccessExample 响应结果示例
 * {"code":"15T","data":{"fundCodeList":["4w0YF5SX"]},"description":"h9uTmlEIvF"}
 */
public interface QueryCustFundHoldFundFacade extends BaseFacade<QueryCustFundHoldFundRequest, QueryCustFundHoldFundResponse> {

}
