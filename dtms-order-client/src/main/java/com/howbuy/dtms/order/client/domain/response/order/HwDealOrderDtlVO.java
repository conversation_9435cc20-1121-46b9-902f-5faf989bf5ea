/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/13 15:08
 * @since JDK 1.8
 */
public class HwDealOrderDtlVO implements Serializable {
    private static final long serialVersionUID = -8279955966381288763L;
    /**
     * ID
     */
    private Long id;

    /**
     * 订单明细号
     */
    private Long dealDtlNo;

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金简称
     */
    private String fundAbbr;

    /**
     * 主基金代码
     */
    private String mainFundCode;

    /**
     * 基金类别 1-公募、2-私募、9-其他
     */
    private String fundCategory;

    /**
     * 基金风险等级
     */
    private String fundRiskLevel;

    /**
     * 赎回方式 1-按份额、2-按金额
     */
    private String redeemType;

    /**
     * 赎回方向列表 1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：支票
     */
    private String redeemDirectionList;

    /**
     * 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘
     */
    private String middleBusiCode;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 净申请金额
     */
    private BigDecimal netAppAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;

    /**
     * 预约折扣
     */
    private BigDecimal prebookDiscount;

    /**
     * 折扣率
     */
    private BigDecimal discountRate;

    /**
     * 折扣类型 1-折扣率 2-折扣金额
     */
    private String discountType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 手续费率
     */
    private BigDecimal feeRate;

    /**
     * 费用计算方式 0-外扣法、1-内扣法
     */
    private String feeCalMode;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认净值
     */
    private BigDecimal ackNav;

    /**
     * 确认净值日期
     */
    private String ackNavDt;

    /**
     * 币种
     */
    private String currency;

    /**
     * 申请状态 0-申请成功；1-申请失败；2-自行撤销；3-强制取消
     */
    private String appStatus;

    /**
     * 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
     */
    private String ackStatus;

    /**
     * 转让价格
     */
    private BigDecimal transferPrice;

    /**
     * 分红方式 0-红利再投 1-现金分红 2-N/A不适用
     */
    private String fundDivMode;

    /**
     * 开放日期
     */
    private String openDt;

    /**
     * 打款截止日期
     */
    private String payEndDt;

    /**
     * 打款截止时间
     */
    private String payEndTm;

    /**
     * 产品打款截止日期
     */
    private String productPayEndDt;

    /**
     * 产品打款截止时间
     */
    private String productPayEndDm;

    /**
     * TA交易日期
     */
    private String taTradeDt;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * TA确认流水号
     */
    private String taAckNo;

    /**
     * 上报状态 0-未上报、1-上报中、2-上报成功、3-需重新上报、4-撤回上报、5-无需上报
     */
    private String submitStatus;

    /**
     * 预计上报日期
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     */
    private String preSubmitTaTm;

    /**
     * 管理人代码
     */
    private String fundManCode;

    /**
     * 展期选项 1-本金展期,2-本金+收益展期,3-收益展期,4-到期赎回
     */
    private String extOption;

    /**
     * 展期控制类型:1-月
     */
    private String extControlType;

    /**
     * 展期控制数
     */
    private String extControlNum;

    /**
     * 撤单时间 yyyyMMDDhhmmss
     */
    private String cancelDate;

    /**
     * 撤单原因
     */
    private String cancelCause;

    /**
     * 撤单资金账号
     */
    private String cancelCpAcctNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 转入基金代码
     */
    private String intoFundCode;

    /**
     * 转入基金母基金
     */
    private String intoMainFundCode;

    /**
     * 转入基金简称
     */
    private String intoFundAbbr;

    /**
     * 转入币种
     */
    private String intoCurrency;

    /**
     * 转入确认金额
     */
    private BigDecimal intoAckAmt;

    /**
     * 转入确认份额
     */
    private BigDecimal intoAckVol;

    /**
     * 转入确认净值
     */
    private BigDecimal intoAckNav;

    /**
     * 转入确认净值日期
     */
    private String intoAckNavDt;

    /**
     * 转入基金交易账号
     */
    private String intoFundTxAcctNo;

    /**
     * 关联订单明细号
     */
    private Long relationalDealDtlNo;

    /**
     * 份额明细流水号
     */
    private String volDtlNo;

    /**
     * 认缴金额
     */
    private BigDecimal subAmt;

    /**
     * 记录状态 0-正常；1-已删除
     */
    private String recStat;

    /**
     * 创建时间戳
     */
    private Date createTimestamp;

    /**
     * 更新时间戳
     */
    private Date updateTimestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDealDtlNo() {
        return dealDtlNo;
    }

    public void setDealDtlNo(Long dealDtlNo) {
        this.dealDtlNo = dealDtlNo;
    }

    public Long getDealNo() {
        return dealNo;
    }

    public void setDealNo(Long dealNo) {
        this.dealNo = dealNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundName() {
        return fundName;
    }

    public void setFundName(String fundName) {
        this.fundName = fundName;
    }

    public String getFundAbbr() {
        return fundAbbr;
    }

    public void setFundAbbr(String fundAbbr) {
        this.fundAbbr = fundAbbr;
    }

    public String getMainFundCode() {
        return mainFundCode;
    }

    public void setMainFundCode(String mainFundCode) {
        this.mainFundCode = mainFundCode;
    }

    public String getFundCategory() {
        return fundCategory;
    }

    public void setFundCategory(String fundCategory) {
        this.fundCategory = fundCategory;
    }

    public String getFundRiskLevel() {
        return fundRiskLevel;
    }

    public void setFundRiskLevel(String fundRiskLevel) {
        this.fundRiskLevel = fundRiskLevel;
    }

    public String getRedeemType() {
        return redeemType;
    }

    public void setRedeemType(String redeemType) {
        this.redeemType = redeemType;
    }

    public String getRedeemDirectionList() {
        return redeemDirectionList;
    }

    public void setRedeemDirectionList(String redeemDirectionList) {
        this.redeemDirectionList = redeemDirectionList;
    }

    public String getMiddleBusiCode() {
        return middleBusiCode;
    }

    public void setMiddleBusiCode(String middleBusiCode) {
        this.middleBusiCode = middleBusiCode;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getNetAppAmt() {
        return netAppAmt;
    }

    public void setNetAppAmt(BigDecimal netAppAmt) {
        this.netAppAmt = netAppAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public BigDecimal getEstimateFee() {
        return estimateFee;
    }

    public void setEstimateFee(BigDecimal estimateFee) {
        this.estimateFee = estimateFee;
    }

    public BigDecimal getPrebookDiscount() {
        return prebookDiscount;
    }

    public void setPrebookDiscount(BigDecimal prebookDiscount) {
        this.prebookDiscount = prebookDiscount;
    }

    public BigDecimal getDiscountRate() {
        return discountRate;
    }

    public void setDiscountRate(BigDecimal discountRate) {
        this.discountRate = discountRate;
    }

    public String getDiscountType() {
        return discountType;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public BigDecimal getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(BigDecimal discountAmt) {
        this.discountAmt = discountAmt;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getAckAmt() {
        return ackAmt;
    }

    public void setAckAmt(BigDecimal ackAmt) {
        this.ackAmt = ackAmt;
    }

    public BigDecimal getAckVol() {
        return ackVol;
    }

    public void setAckVol(BigDecimal ackVol) {
        this.ackVol = ackVol;
    }

    public BigDecimal getAckNav() {
        return ackNav;
    }

    public void setAckNav(BigDecimal ackNav) {
        this.ackNav = ackNav;
    }

    public String getAckNavDt() {
        return ackNavDt;
    }

    public void setAckNavDt(String ackNavDt) {
        this.ackNavDt = ackNavDt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getAppStatus() {
        return appStatus;
    }

    public void setAppStatus(String appStatus) {
        this.appStatus = appStatus;
    }

    public String getAckStatus() {
        return ackStatus;
    }

    public void setAckStatus(String ackStatus) {
        this.ackStatus = ackStatus;
    }

    public BigDecimal getTransferPrice() {
        return transferPrice;
    }

    public void setTransferPrice(BigDecimal transferPrice) {
        this.transferPrice = transferPrice;
    }

    public String getFundDivMode() {
        return fundDivMode;
    }

    public void setFundDivMode(String fundDivMode) {
        this.fundDivMode = fundDivMode;
    }

    public String getOpenDt() {
        return openDt;
    }

    public void setOpenDt(String openDt) {
        this.openDt = openDt;
    }

    public String getPayEndDt() {
        return payEndDt;
    }

    public void setPayEndDt(String payEndDt) {
        this.payEndDt = payEndDt;
    }

    public String getPayEndTm() {
        return payEndTm;
    }

    public String getProductPayEndDt() {
        return productPayEndDt;
    }

    public void setProductPayEndDt(String productPayEndDt) {
        this.productPayEndDt = productPayEndDt;
    }

    public String getProductPayEndDm() {
        return productPayEndDm;
    }

    public void setProductPayEndDm(String productPayEndDm) {
        this.productPayEndDm = productPayEndDm;
    }

    public void setPayEndTm(String payEndTm) {
        this.payEndTm = payEndTm;
    }

    public String getTaTradeDt() {
        return taTradeDt;
    }

    public void setTaTradeDt(String taTradeDt) {
        this.taTradeDt = taTradeDt;
    }

    public String getAckDt() {
        return ackDt;
    }

    public void setAckDt(String ackDt) {
        this.ackDt = ackDt;
    }

    public String getTaAckNo() {
        return taAckNo;
    }

    public void setTaAckNo(String taAckNo) {
        this.taAckNo = taAckNo;
    }

    public String getSubmitStatus() {
        return submitStatus;
    }

    public void setSubmitStatus(String submitStatus) {
        this.submitStatus = submitStatus;
    }

    public String getPreSubmitTaDt() {
        return preSubmitTaDt;
    }

    public void setPreSubmitTaDt(String preSubmitTaDt) {
        this.preSubmitTaDt = preSubmitTaDt;
    }

    public String getPreSubmitTaTm() {
        return preSubmitTaTm;
    }

    public void setPreSubmitTaTm(String preSubmitTaTm) {
        this.preSubmitTaTm = preSubmitTaTm;
    }

    public String getFundManCode() {
        return fundManCode;
    }

    public void setFundManCode(String fundManCode) {
        this.fundManCode = fundManCode;
    }

    public String getExtOption() {
        return extOption;
    }

    public void setExtOption(String extOption) {
        this.extOption = extOption;
    }

    public String getExtControlType() {
        return extControlType;
    }

    public void setExtControlType(String extControlType) {
        this.extControlType = extControlType;
    }

    public String getExtControlNum() {
        return extControlNum;
    }

    public void setExtControlNum(String extControlNum) {
        this.extControlNum = extControlNum;
    }

    public String getCancelDate() {
        return cancelDate;
    }

    public void setCancelDate(String cancelDate) {
        this.cancelDate = cancelDate;
    }

    public String getCancelCause() {
        return cancelCause;
    }

    public void setCancelCause(String cancelCause) {
        this.cancelCause = cancelCause;
    }

    public String getCancelCpAcctNo() {
        return cancelCpAcctNo;
    }

    public void setCancelCpAcctNo(String cancelCpAcctNo) {
        this.cancelCpAcctNo = cancelCpAcctNo;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }

    public String getIntoFundCode() {
        return intoFundCode;
    }

    public void setIntoFundCode(String intoFundCode) {
        this.intoFundCode = intoFundCode;
    }

    public String getIntoMainFundCode() {
        return intoMainFundCode;
    }

    public void setIntoMainFundCode(String intoMainFundCode) {
        this.intoMainFundCode = intoMainFundCode;
    }

    public String getIntoFundAbbr() {
        return intoFundAbbr;
    }

    public void setIntoFundAbbr(String intoFundAbbr) {
        this.intoFundAbbr = intoFundAbbr;
    }

    public String getIntoCurrency() {
        return intoCurrency;
    }

    public void setIntoCurrency(String intoCurrency) {
        this.intoCurrency = intoCurrency;
    }

    public BigDecimal getIntoAckAmt() {
        return intoAckAmt;
    }

    public void setIntoAckAmt(BigDecimal intoAckAmt) {
        this.intoAckAmt = intoAckAmt;
    }

    public BigDecimal getIntoAckVol() {
        return intoAckVol;
    }

    public void setIntoAckVol(BigDecimal intoAckVol) {
        this.intoAckVol = intoAckVol;
    }

    public BigDecimal getIntoAckNav() {
        return intoAckNav;
    }

    public void setIntoAckNav(BigDecimal intoAckNav) {
        this.intoAckNav = intoAckNav;
    }

    public String getIntoAckNavDt() {
        return intoAckNavDt;
    }

    public void setIntoAckNavDt(String intoAckNavDt) {
        this.intoAckNavDt = intoAckNavDt;
    }

    public String getIntoFundTxAcctNo() {
        return intoFundTxAcctNo;
    }

    public void setIntoFundTxAcctNo(String intoFundTxAcctNo) {
        this.intoFundTxAcctNo = intoFundTxAcctNo;
    }

    public Long getRelationalDealDtlNo() {
        return relationalDealDtlNo;
    }

    public void setRelationalDealDtlNo(Long relationalDealDtlNo) {
        this.relationalDealDtlNo = relationalDealDtlNo;
    }

    public String getVolDtlNo() {
        return volDtlNo;
    }

    public void setVolDtlNo(String volDtlNo) {
        this.volDtlNo = volDtlNo;
    }

    public BigDecimal getSubAmt() {
        return subAmt;
    }

    public void setSubAmt(BigDecimal subAmt) {
        this.subAmt = subAmt;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }
}
