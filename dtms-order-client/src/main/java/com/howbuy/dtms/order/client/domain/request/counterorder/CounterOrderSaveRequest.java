/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.counterorder;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/7 16:08
 * @since JDK 1.8
 */
public class CounterOrderSaveRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 1551777222435831478L;

    private CounterAuditOrderRequest counterAuditOrderRequest;
    
    private List<CounterAuditOrderDtlRequest> counterAuditOrderDtlRequestList;

    public CounterAuditOrderRequest getCounterAuditOrderRequest() {
        return counterAuditOrderRequest;
    }

    public void setCounterAuditOrderRequest(CounterAuditOrderRequest counterAuditOrderRequest) {
        this.counterAuditOrderRequest = counterAuditOrderRequest;
    }

    public List<CounterAuditOrderDtlRequest> getCounterAuditOrderDtlRequestList() {
        return counterAuditOrderDtlRequestList;
    }

    public void setCounterAuditOrderDtlRequestList(List<CounterAuditOrderDtlRequest> counterAuditOrderDtlRequestList) {
        this.counterAuditOrderDtlRequestList = counterAuditOrderDtlRequestList;
    }
}
