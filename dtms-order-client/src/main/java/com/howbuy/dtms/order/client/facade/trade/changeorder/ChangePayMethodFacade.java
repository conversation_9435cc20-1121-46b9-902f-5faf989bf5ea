package com.howbuy.dtms.order.client.facade.trade.changeorder;

import com.howbuy.dtms.order.client.domain.request.changeorder.ChangePayMethodRequest;
import com.howbuy.dtms.order.client.domain.response.changeorder.ChangePayMethodResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.changeorder.ChangePayMethodFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup ChangePayMethodFacadeImpl
 * @apiName execute()
 * @apiDescription 支付方式修改
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} dealNo 订单号
 * @apiParam (请求体) {String} payMethod 支付方式(1-电汇、2-支票、3-海外储蓄罐)
 * @apiParam (请求体) {String} cpAcctNo 资金账号(支付方式为1-电汇时必填)
 * @apiParam (请求体) {String} tradeChannel 交易渠道
 * @apiParam (请求体) {String} appDt 申请日期(格式:YYYYMMDD)
 * @apiParam (请求体) {String} appTm 申请时间(格式:HHmmss)
 * @apiParamExample 请求体示例
 * {"hkCustNo":"HK10086","dealNo":"D20250424001","payMethod":"1","cpAcctNo":"6225887744556633","tradeChannel":"9","appDt":"20250424","appTm":"164118"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{"code":"0000","description":"支付方式修改成功"},"description":"成功"}
 */
public interface ChangePayMethodFacade extends BaseFacade<ChangePayMethodRequest, ChangePayMethodResponse> {

} 