/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 交易订单(资金)VO
 * <AUTHOR>
 * @date 2025-07-11 13:35:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class DealOrderForCapitalVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 业务代码
     */
    private String busiCode;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 币种代码
     */
    private String currency;

    /**
     * 产品打款截止日期
     */
    private String productPayEndDt;

    /**
     * 产品打款截止时间
     */
    private String productPayEndTm;

    /**
     * 打款截止日期
     */
    private String payEndDt;

    /**
     * 打款截止时间
     */
    private String payEndTm;

    /**
     * 预计上报日
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     */
    private String preSubmitTaTm;

    /**
     * 开放日期
     */
    private String openDt;

    /**
     * 支付方式
     */
    private String paymentType;

    /**
     * 付款状态
     */
    private String payStatus;

    /**
     * 净申请金额
     */
    private BigDecimal netAppAmt;

    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 关联订单号(订单表的订单号)
     */
    private Long relationalDealNo;

    /**
     * 入账流水号
     */
    private String receiptSerialNo;
}
