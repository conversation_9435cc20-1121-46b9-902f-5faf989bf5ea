/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/20 00:41
 * @since JDK 1.8
 */
@Data
public class QueryBalanceDetailResponse implements Serializable {

    /**
     * 持仓份额
     */
    private String totalBalanceVol;

    /**
     * 可用份额
     */
    private String availableBalanceVol;

    /**
     * 是否支持展期:1-是,0-否
     */
    private String supportExt;

    /**
     * 不可用份额
     */
    private String unavailableBalanceVol;

    /**
     * 基金分组明细数据
     */
    private List<BalanceDetailGroupInfoVO> balanceDetailInfoList;


}