/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.producttrade;

import com.howbuy.dtms.order.client.domain.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/10/27 17:15
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryHkTradeRequest extends PageRequest {

    private List<String> tradeIdList;

    private String tradeId;

    private String custName;

    private String custNo;

    private String hboneNo;

    private String idNo;

    private String productCode;

    private String tradeStatus;

    private String orderStatus;

    private String payStatus;

    private String tradeType;

    private String isOnlineSign;

    private String signStatus;

    private String payStartDt;

    private String payEndDt;

    private String signStartDt;

    private String signEndDt;


}