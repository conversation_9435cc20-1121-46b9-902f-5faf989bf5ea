/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.contractsign;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: 订单合同签署明细VO
 * <AUTHOR>
 * @date 2024/5/15 16:14
 * @since JDK 1.8
 */
@Data
public class OrderContractSignDtlVO implements Serializable {

    private static final long serialVersionUID = 7881758807344522113L;
    /**
     * 文件代码
     */
    private String fileType;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件类型描述
     */
    private String fileTypeDesc;
    /**
     * 文件路径
     */
    private String filePath;

}
