/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain;

import com.howbuy.dtms.common.enums.YesOrNoEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Data
public class QueryAcctBalanceVO implements Serializable {
    /**
     * 分销机构号
     */
    @Deprecated
    private String disCode;
    /**
     * 分销机构号列表-股权直销改造
     */
    private List<String> disCodeList;
    /**
     * 交易账号
     */
    private String txAcctNo;
    /**
     * 总市值
     */
    private BigDecimal totalMarketValue;
    /**
     * 展示币种换算后总市值
     */
    private BigDecimal disPlayCurrencyTotalMarketValue;
    /**
     * 在途总金额
     */
    private BigDecimal totalUnconfirmedAmt;

    /**
     * 展示币种在途总金额
     */
    private BigDecimal totalDisCurUnconfirmedAmt;
    /**
     * 待确认笔数
     */
    private Integer totalUnconfirmedNum;

    /**
     * 赎回待确认笔数
     */
    private Integer redeemUnconfirmedNum;

    /**
     * 当前总收益
     */
    private BigDecimal totalCurrentAsset;
    /**
     * 总收益计算状态: 0-计算中;1-计算成功
     */
    private String totalIncomCalStat;
    /**
     * 总回款
     */
    private BigDecimal totalCashCollection;
    /**
     * 是否持有好臻产品 0:没有,1:有
     */
    private String hasHZProduct= YesOrNoEnum.NO.getValue();
    /**
     * 是否持有好买香港产品  0:没有,1:有
     */
    private String hasHKProduct =YesOrNoEnum.NO.getValue();
    /**
     * 持仓明细列表
     */
    private List<BalanceBeanVO> balanceList;

    /**
     * 在途数据
     */
    private List<UnconfirmeProductVO> unconfirmeProducts;
}