/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.contractsign;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/5/22 17:35
 * @since JDK 1.8
 */
@Data
public class CustContractSignVO  implements Serializable {
    private static final long serialVersionUID = 310127930799499125L;
    /**
     * 客户号
     */
    private String hkCustNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 协议签署状态
     */
    private String signFlag;

    /**
     * 协议签署日期时间 yyyyMMdd
     */
    private String signTime;

    /**
     * 协议明细签署时间 yyyy-MM-dd HH:mm:ss
     */
    private String signDate;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 签署详情
     */
    private List<CustContractDtlSignVO> custContractDtlSignList;

}