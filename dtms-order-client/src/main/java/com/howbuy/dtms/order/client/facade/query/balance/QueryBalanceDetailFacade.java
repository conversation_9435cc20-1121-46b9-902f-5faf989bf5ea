package com.howbuy.dtms.order.client.facade.query.balance;

import com.howbuy.dtms.order.client.domain.request.hkbalance.QueryBalanceDeatailRequest;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceDetailResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;


/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.balance.QueryBalanceDetailFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryBalanceDetailFacade
 * @apiName buyFeeCompute()
 * @apiDescription 查询持仓份额明细数据
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} hbOneNo 一账通号
 * @apiParam (请求体) {String} fundCode 基金代码
 * @apiParam (请求体) {String} txCode 交易码
 * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求体) {String} appTm 申请时间 HHmmss
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress ipAddress
 * @apiParamExample 请求体示例
 * {"buyAmt":9702.019409152635,"appTm":"zdi3l2W8H","fundCode":"i8fU9yH","hkCustNo":"sCTGmtx0HD","ipAddress":"317vPNhS0","appDt":"SwZ","outletCode":"yzqZwhua9","tradeChannel":"iUWUmZ5kE"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Number} data.totalBalanceVol 持仓份额
 * @apiSuccess (响应结果) {Number} data.availableBalanceVol 可用份额
 * @apiSuccess (响应结果) {Number} data.unavailableBalanceVol 不可用份额
 * @apiSuccess (响应结果) {Array} data.balanceDetailInfoList 基金分组明细数据
 * @apiSuccess (响应结果) {String} data.balanceDetailInfoList.fundName 基金名称
 * @apiSuccess (响应结果) {Array} data.balanceDetailInfoList.balanceDetailList 基金列表
 * @apiSuccessExample 响应结果示例
 * {"code":"4MzSy4","data":{"originalFee":6697.6376900336445,"isLargerPrebookAmt":"KY7UGrHtFW","prebookDiscountRate":7495.700311508721,"validDiscountRate":"UyRYGuo","actualPayAmt":1095.528369587916,"estimateFee":3203.140698548105,"actualDiscountRate":9365.20309439356,"feeRate":287.02202947865294},"description":"3VcA8"}
 */
public interface QueryBalanceDetailFacade extends BaseFacade<QueryBalanceDeatailRequest, QueryBalanceDetailResponse> {

}
