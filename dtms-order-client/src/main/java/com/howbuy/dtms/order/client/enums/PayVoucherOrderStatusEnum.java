/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.enums;

/**
 * <AUTHOR>
 * @description: 打款凭证审核状态枚举
 * @date 2023/6/12
 * @since JDK 1.8
 */
public enum PayVoucherOrderStatusEnum {

    /**
     * 0-无需审核
     */
    NOT_NEED_EXAMINE("0", "无需审核"),

    /**
     * 1-等待审核
     */
    WAIT_EXAMINE("1", "等待审核"),
    /**
     * 2-等待复核
     */
    WAIT_REVIEW("2", "等待复核"),

    /**
     * 3-审核通过
     */
    APPROVE("3", "审核通过"),

    /**
     * 4-审核不通过
     */
    FAILURE("4", "审核不通过"),

    /**
     * 5-驳回至初审
     */
    REJECT_TO_FIRST_EXAMINE("5", "驳回至经办"),

    /**
     * 6-驳回至客户
     */
    REJECT_TO_CUSTOMER("6", "驳回至客户"),

    /**
     * 7-作废
     */
    VOIDED("7", "作废"),
    ;

    private String code;
    private String desc;

    private PayVoucherOrderStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    /**
     * @return java.lang.String
     * @description:(根据枚举类型返回code值)
     * @author: your name
     * @date: 2023/3/9 14:54
     * @since JDK 1.8
     */
    public static String getEnumDesc(String code) {
        for (PayVoucherOrderStatusEnum statusEnum : PayVoucherOrderStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

    /**
     * @description:(请在此添加描述)
     * @param code
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/7/26 16:23
     * @since JDK 1.8
     */
    public static String getCodeAndDesc(String code) {
        for (PayVoucherOrderStatusEnum statusEnum : PayVoucherOrderStatusEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getCode() + "-" + statusEnum.getDesc();
            }
        }
        return null;
    }
}
