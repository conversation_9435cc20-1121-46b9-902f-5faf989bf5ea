/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 查询是否有持仓或者在途订单
 * <AUTHOR>
 * @date 2024/7/30 14:23
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryFundHoldInTransitResponse implements Serializable {

    private static final long serialVersionUID = 5272292268550208384L;

    /**
     * 是否持仓
     */
    private String whetherFundHold;

    /**
     * 是否在途
     */
    private String whetherInTransitOrder;

    /**
     * 是否在途打款凭证
     */
    private String whetherInTransitVoucher;
}
