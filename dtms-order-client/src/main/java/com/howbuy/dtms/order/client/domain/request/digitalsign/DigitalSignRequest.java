package com.howbuy.dtms.order.client.domain.request.digitalsign;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (签约)
 * <AUTHOR>
 * @date 2023/5/17 14:34
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DigitalSignRequest extends BaseRequest {
    /**
     * 香港客户号 必填
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
    /**
     * 预约流水号 必填
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预约流水号", isRequired = true)
    private String preBookId;
}