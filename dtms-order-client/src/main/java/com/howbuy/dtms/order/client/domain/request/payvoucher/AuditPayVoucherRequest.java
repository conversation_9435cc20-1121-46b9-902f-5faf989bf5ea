/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/22 17:23
 * @since JDK 1.8
 */

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class AuditPayVoucherRequest extends BaseRequest {

    private static final long serialVersionUID = -9222050713795863455L;
    /**
     * 打款凭证流水号
     */
    private String voucherNo;

    /**
     * 交易订单号 类型为1-交易下单凭证时，订单号必传
     */
    private String tradeOrderNo;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 汇款银行卡 是否审核通过
     */
    private boolean remitCpAcctNoPassFlag;

    /**
     * 汇款银行卡 审核意见
     */
    private String remitCpAcctNoRemark;

    /**
     * 汇款币种 是否审核通过
     */
    private boolean remitCurrencyPassFlag;

    /**
     * 汇款币种 审核意见
     */
    private String remitCurrencyRemark;

    /**
     * 汇款金额 是否审核通过
     */
    private boolean remitAmtPassFlag;

    /**
     * 汇款金额 审核意见
     */
    private String remitAmtRemark;

    /**
     * 备注 是否审核通过
     */
    private boolean remarkPassFlag;

    /**
     * 备注 审核意见
     */
    private String remarkRemark;

    /**
     * 	实际到账金额
     */
    private BigDecimal actualPayAmt;

    /**
     * 实际到账币种
     */
    private String actualPayCurrency;

    /**
     * 实际到账日期
     */
    private String actualPayDt;

    /**
     * 实际到账时间
     */
    private String actualPayTm;

    /**
     * 入账流水号
     */
    private String receiptSerialNo;


    /**
     * 凭证文件审核意见
     */
    private List<AuditPayVoucherFileTypeDTO> fileTypeAuditList;


    /**
     * 审核状态
     */
    private String auditStatus;


    /**
     * 重复凭证 1 是 0 否
     */
    private String duplicateVoucher;



}