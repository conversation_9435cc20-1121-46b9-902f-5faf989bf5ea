package com.howbuy.dtms.order.client.facade.query.payvoucher;

import com.howbuy.dtms.order.client.domain.request.payvoucher.PayVoucherDuplicateCheckRequest;
import com.howbuy.dtms.order.client.domain.response.payvoucher.PayVoucherDuplicateCheckResponse;
import com.howbuy.dtms.order.client.domain.response.payvoucher.PayVoucherListDetailResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payvoucher.PayVoucherDuplicateCheckFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup PayVoucherDuplicateCheckFacade
 * @apiName execute()
 * @apiDescription APP 打款凭证提交检验相同的打款凭证是否重复提交
 * @apiParam (请求参数) {String} cpAcctNo 香港资金账号
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} swiftCode 银行swiftCode码值
 * @apiParam (请求参数) {String} remitCurrency 汇款账户币种
 * @apiParam (请求参数) {String} remitAmt 汇款金额
 * @apiParamExample 请求参数示例
 * remitCurrency=vHaDjYFIMX&hkCustNo=glHl23xqT4&swiftCode=cRH0f4VpD&cpAcctNo=q0uSh5kUBm&remitAmt=03Hft0EslZ
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.voucherNo 打款凭证订单号
 * @apiSuccess (响应结果) {String} data.appDate 申请日期
 * @apiSuccess (响应结果) {String} data.appTime 申请时间
 * @apiSuccess (响应结果) {Number} data.remitAmt 汇款金额
 * @apiSuccess (响应结果) {String} data.voucherType 打款凭证类型: 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
 * @apiSuccess (响应结果) {String} data.remitCurrency 汇款币种
 * @apiSuccess (响应结果) {Number} data.actualPayAmt 实际到账金额
 * @apiSuccess (响应结果) {String} data.actualPayCurrency 实际到账币种
 * @apiSuccess (响应结果) {String} data.auditStatus 审核状态
 * @apiSuccess (响应结果) {String} data.auditReason 审核原因
 * @apiSuccessExample 响应结果示例
 * {"code":"Qf7p","data":{"voucherNo":"h","remitCurrency":"br6VgrGQ","appTime":"vmxv","auditReason":"h2ocZ","voucherType":"eNC","auditStatus":"aSDQ7cfWdi","appDate":"2","actualPayAmt":22.25722994112944,"remitAmt":3270.778783885875,"actualPayCurrency":"8Y2m1F"},"description":"loRW"}
 */
public interface PayVoucherDuplicateCheckFacade extends BaseFacade<PayVoucherDuplicateCheckRequest, PayVoucherDuplicateCheckResponse> {

}
