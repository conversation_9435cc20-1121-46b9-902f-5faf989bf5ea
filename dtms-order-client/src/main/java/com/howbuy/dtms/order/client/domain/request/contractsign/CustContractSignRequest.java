package com.howbuy.dtms.order.client.domain.request.contractsign;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.common.enums.ContractSignFlagEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import com.howbuy.dtms.order.client.domain.response.contractsign.AgreementVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description:产品合同协议签署接口
 * @date 2023/5/18 17:01
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustContractSignRequest extends BaseRequest {

    private static final long serialVersionUID = -4529303745219804091L;

    public CustContractSignRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_CONTRACT_SIGN);
    }

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;
    /**
     * 交易方式 1-购买；2-赎回
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易方式", isRequired = true)
    private String tradeMode;
    /**
     * 预约单号
     * 兼容预约电子签约
     * 交易电子签约预约单号为空
     */
    @Deprecated
    private String perbookId;
    /**
     * 协议签订标识
     */
    private String contractSignFlag = ContractSignFlagEnum.SIGN.getKey();
    /**
     * 协议list
     */
    private List<AgreementVO> contractList;

}
