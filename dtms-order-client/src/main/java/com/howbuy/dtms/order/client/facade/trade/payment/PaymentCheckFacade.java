package com.howbuy.dtms.order.client.facade.trade.payment;

import com.howbuy.dtms.order.client.domain.request.payment.PaymentCheckRequest;
import com.howbuy.dtms.order.client.domain.response.payment.PaymentCheckResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.payment.PaymentCheckFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup PaymentCheckFacade
 * @apiName execute()
 * @apiDescription 支付对账接口
 * @apiParam (请求体) {String} pmtCheckDt 支付对账日期，格式：yyyyMMdd，必填
 * @apiParamExample 请求体示例
 * {"pmtCheckDt":"20250707"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","description":"支付对账执行成功"}
 */
public interface PaymentCheckFacade extends BaseFacade<PaymentCheckRequest, PaymentCheckResponse> {

}
