/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.buy;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/4 19:28
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CounterBuyRequest extends BaseRequest {

    private static final long serialVersionUID = -5560573872824886636L;


    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 资金账号 支付方式为1-电汇时必填
     */
    private String cpAcctNo;

    /**
     * 基金交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
    private String fundTxAcctNo;

    /**
     * 支付方式  1-电汇、2-支票、3-海外储蓄罐
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "支付方式", isRequired = true)
    private String payMethod;

    /**
     * 买入金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "买入金额", isRequired = true)
    private BigDecimal buyAmt;

    /**
     * 预估手续费
     * String：Money时手续费为0校验不通过
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预估手续费", isRequired = true)
    private BigDecimal estimateFee;

    /**
     * 申请折扣率
     */
    private BigDecimal applyDiscountRate;

    /**
     * 折扣类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "折扣类型", isRequired = true)
    private String discountType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 中台业务码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中台业务码", isRequired = true)
    private String businessCode;

    /**
     * 展期选项 1-本金展期,2-本金+收益展期,3-收益展期
     */
    private String extOption;

    /**
     * 展期控制数
     */
    private String extControlNum;

}
