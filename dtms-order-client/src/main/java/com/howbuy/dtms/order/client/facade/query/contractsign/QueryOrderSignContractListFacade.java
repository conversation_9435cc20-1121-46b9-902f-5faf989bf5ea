/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.contractsign;

import com.howbuy.dtms.order.client.domain.request.contractsign.QueryOrderContractListRequest;
import com.howbuy.dtms.order.client.domain.response.contractsign.OrderContractSignListVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 查询订单合同签署列表接口
 * <AUTHOR>
 * @date 2025/5/9 18:54
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.contractsign.QueryOrderSignContractListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryOrderSignContractListFacade
 * @apiName execute()
 * @apiDescription 查询订单合同签署列表接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {Array} orderStatusList 订单状态列表(1-申请成功、2-部分确认、3-确认成功、4-确认失败、5-自行撤销、6-强制取消)
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {Array} fundCodeList 基金代码列表
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK123456",
 *     "orderStatusList": ["1", "3"],
 *     "fundCode": "000001",
 *     "fundCodeList": ["000001", "000002"]
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.orderContractSignVOList 订单合同签署列表
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.orderStatus 订单状态(1-申请成功、2-部分确认、3-确认成功、4-确认失败、5-自行撤销、6-强制取消)
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.businessType 业务类型(10-买入、11-卖出、17-非交易过户、18-修改分红方式、19-强制赎回、20-红利下发、21-份额强增、22-份额强减)
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.middleBusiCode 中台业务代码(1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出)
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.fundAbbr 基金简称
 * @apiSuccess (响应结果) {String} data.orderContractSignVOList.signDate 签署时间(yyyy-MM-dd HH:mm:ss)
 * @apiSuccess (响应结果) {Array} data.orderContractSignVOList.orderContractSignDtlVOList 订单合同签署明细列表
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "orderContractSignVOList": [
 *             {
 *                 "dealNo": "ORD202412270001",
 *                 "orderStatus": "3",
 *                 "businessType": "10",
 *                 "middleBusiCode": "1122",
 *                 "fundCode": "000001",
 *                 "fundName": "香港基金A",
 *                 "fundAbbr": "港基A",
 *                 "signDate": "2024-12-27 15:30:00",
 *                 "orderContractSignDtlVOList": [
 *                     {
 *                         "fileType": "001",
 *                         "fileName": "基金认购协议.pdf",
 *                         "signFlag": "1"
 *                     }
 *                 ]
 *             }
 *         ]
 *     },
 *     "description": "查询成功"
 * }
 */
public interface QueryOrderSignContractListFacade extends BaseFacade<QueryOrderContractListRequest, OrderContractSignListVO> {

}
