package com.howbuy.dtms.order.client.facade.query.sell;

import com.howbuy.dtms.order.client.domain.request.sell.dubbo.QueryPiggySellInfoRequest;
import com.howbuy.dtms.order.client.domain.response.sell.dubbo.PiggySellInfoResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.sell.QuerySellInfoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QuerySellInfoFacade
 * @apiName 储蓄罐赎回页面信息查询
 * @apiDescription 储蓄罐赎回页面信息查询
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=NI2&hkCustNo=YIu&ipAddress=1KzNRc8&deviceName=j8RVuB9mU&systemVersion=E4GKAOF5&appTm=UvRUbr&macAddress=fpvUf&deviceSerialNo=BTi&fundCode=4WvdBV8I&appDt=7oRdwUrD&deviceModel=6&txCode=eML8Js77&outletCode=gOqfql2&tradeChannel=U3iV13
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Object} data.piggySellFundInfoResp 卖出基金信息
 * @apiSuccess (响应结果) {String} data.piggySellFundInfoResp.openStartDt 开放开始日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.piggySellFundInfoResp.openEndDt 开放结束日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.piggySellFundInfoResp.advanceEndDt 预约结束日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.piggySellFundInfoResp.advanceEndTm 预约结束时间 HHmmss
 * @apiSuccess (响应结果) {String} data.piggySellFundInfoResp.tradeDt 交易日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.piggySellFundInfoResp.isInTransitOrder 是否在途订单 0-否、1-是
 * @apiSuccess (响应结果) {Number} data.piggySellFundInfoResp.totalVol 总份额
 * @apiSuccess (响应结果) {Number} data.piggySellFundInfoResp.availableVol 可用份额
 * @apiSuccess (响应结果) {Number} data.piggySellFundInfoResp.totalAsset 总资产
 * @apiSuccess (响应结果) {Number} data.piggySellFundInfoResp.availableAsset 可用资产
 * @apiSuccess (响应结果) {Number} data.piggySellFundInfoResp.nav 最新基金净值（从DB获取）
 * @apiSuccess (响应结果) {String} data.piggySellFundInfoResp.navDt 最新净值日期（从DB获取）
 * @apiSuccess (响应结果) {Object} data.piggyPrebookSellInfoResp 预约卖出信息
 * @apiSuccess (响应结果) {String} data.piggyPrebookSellInfoResp.prebookDealNo 预约单号
 * @apiSuccess (响应结果) {String} data.piggyPrebookSellInfoResp.prebookRedeemMethod 预约赎回方式 1-按份额赎回；2-按金额赎回
 * @apiSuccess (响应结果) {Number} data.piggyPrebookSellInfoResp.prebookAppAmt 预约申请金额
 * @apiSuccess (响应结果) {Number} data.piggyPrebookSellInfoResp.prebookAppVol 预约申请份额
 * @apiSuccess (响应结果) {String} data.hasSignCxg 是否签约储蓄罐 0-未签署 1-签署
 * @apiSuccessExample 响应结果示例
 * {"code":"TpjCwWm4","data":{"hasSignCxg":"tWRT8","piggySellFundInfoResp":{"totalAsset":2627.0994995753726,"nav":5925.148526032972,"advanceEndTm":"ogeJ5","tradeDt":"xV9Aj","openStartDt":"7","advanceEndDt":"A0ipaqlIAJ","totalVol":1711.1747830227819,"navDt":"00qq15dyV","isInTransitOrder":"kst","openEndDt":"4JfVMRe","availableAsset":4731.38306990949,"availableVol":5435.712800779072},"piggyPrebookSellInfoResp":{"prebookRedeemMethod":"O1jw","prebookAppVol":3863.2665808329834,"prebookAppAmt":4846.171096056213,"prebookDealNo":"SVg1MpkEwO"}},"description":"orX"}
 */

// 这个接口的命名不是很规范,实际上是储蓄罐基金赎回的页面查询信息
public interface QuerySellInfoFacade extends BaseFacade<QueryPiggySellInfoRequest, PiggySellInfoResponse> {

}
