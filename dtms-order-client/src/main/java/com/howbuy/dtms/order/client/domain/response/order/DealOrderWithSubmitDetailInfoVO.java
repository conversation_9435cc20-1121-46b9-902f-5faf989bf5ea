/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: (订单信息 包括上报明细信息)
 * <AUTHOR>
 * @date 2024年12月14日 09:58:21
 * @since JDK 1.8
 */
@Setter
@Getter
public class DealOrderWithSubmitDetailInfoVO implements Serializable {

    /**
     * 香港资金账号
     */
    private String hkCpAcctNo;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
     */
    private String businessType;

    /**
     * 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出
     */
    private String middleBusiCode;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 基金代码
     */
    private String fundCode;


    /**
     * 基金简称
     */
    private String fundShortName;

    /**
     * 基金名称
     */
    private String fundName;


    /**
     * 币种代码
     */
    private String currency;


    /**
     * 订单号
     */
    private Long dealNo;

    /**
     *  订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private String orderStatus;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 赎回方式 赎回方式 1-按份额、2-按金额
     */
    private String redeemType;

    /**
     * 分红方式 0-红利再投；1-现金红利
     */
    private String fundDivMode;


    /**
     * 主基金代码
     */
    private String mainFundCode;



    /**
     * 赎回方向列表 1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：基金转投
     */
    private String redeemDirectionList;

    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;


    /**
     * 确认手续费
     */
    private BigDecimal ackFee;

    /**
     * 手续费
     * 优先取值：确认手续费，为空时取预估手续费
     */
    private BigDecimal fee;


    /**
     * 支付方式 1-电汇、2-支票、3-海外储蓄罐
     */
    private String paymentTypeList;


    /**
     * 上报信息列表
     */
    private List<DealMultiSubmitInfoVO> submitInfoList;



    /**
     * 开放日期
     */
    private String openDt;

    /**
     * TA交易日期
     */
    private String taTradeDt;

    /**
     * 上报TA日期
     */
    private String submitTaDt;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 确认净值(转入确认净值)
     */
    private BigDecimal ackNav;

    /**
     * 转出确认净值
     */
    private BigDecimal transferNav;

    /**
     * SWIFT代码
     */
    private String swiftCode;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行英文名称
     */
    private String bankEnName;

    /**
     * 银行中文名称
     */
    private String bankChineseName;

    /**
     * 备注
     */
    private String memo;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     */
    private String tradeChannel;


    /**
     * 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
     */
    private String ackStatus;


    //NOTICE:  如果为 基金转换 。  原始表设计，属性， 默认为 转出 的属性
    //NOTICE: 以下转入属性 命名：
    /**
     * 转入基金代码
     */
    private String intoFundCode;

    /**
     * 转入母基金代码
     */
    private String intoMainFundCode;

    /**
     * 转入基金简称
     */
    private String intoFundAbbr;

    /**
     * 转入币种
     */
    private String intoCurrency;

    /**
     * 转入确认金额
     */
    private BigDecimal intoAckAmt;

    /**
     * 转入确认份额
     */
    private BigDecimal intoAckVol;

    /**
     * 转入确认净值
     */
    private BigDecimal intoAckNav;

    /**
     * 转入确认净值日期
     */
    private String intoAckNavDt;

    /**
     * 转入基金交易账号
     */
    private String intoFundTxAcctNo;

    /**
     * 认缴金额
     */
    private BigDecimal subAmt;


    /**
     * 创建时间戳
     */
    private Date createTimestamp;

    /**
     * 更新时间戳
     */
    private Date updateTimestamp;


}
