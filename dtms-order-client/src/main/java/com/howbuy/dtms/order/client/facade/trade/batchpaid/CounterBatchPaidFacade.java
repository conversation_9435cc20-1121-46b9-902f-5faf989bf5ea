package com.howbuy.dtms.order.client.facade.trade.batchpaid;

import com.howbuy.dtms.order.client.domain.request.batchpaid.CounterBatchPaidRequest;
import com.howbuy.dtms.order.client.domain.response.batchpaid.CounterBatchPaidResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.batchpaid.CounterBatchPaidFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup CounterBatchPaidFacadeImpl
 * @apiName execute()
 * @apiDescription 柜台批量实缴
 * @apiParam (请求体) {String} fundCode 基金代码
 * @apiParam (请求体) {List} custPaidDetailList 客户实缴列表
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP
 * @apiParam (请求体) {String} appDt 申请日期(格式:YYYYMMDD)
 * @apiParam (请求体) {String} appTm 申请时间(格式:HHmmss)
 * @apiParam (请求体) {String} custPaidDetailList.externalDealNo 外部订单号
 * @apiParam (请求体) {String} custPaidDetailList.hkCustNo 香港客户号
 * @apiParam (请求体) {String} custPaidDetailList.fundTxAcctNo 基金交易账号
 * @apiParam (请求体) {BigDecimal} custPaidDetailList.netAppAmt 净申请金额
 * @apiParam (请求体) {BigDecimal} custPaidDetailList.estimateFee 预估费用
 * @apiParam (请求体) {BigDecimal} custPaidDetailList.appDisCount 申请折扣率
 * @apiParam (请求体) {String} custPaidDetailList.paymentType 支付方式
 * @apiParam (请求体) {String} custPaidDetailList.cpAcctNo 资金账号
 * @apiParamExample 请求体示例
 * {
 *   "fundCode": "000001",
 *   "custPaidDetailList": [{
 *     "hkCustNo": "HK001",
 *     "fundTxAcctNo": "123456",
 *     "netAppAmt": 50000.00,
 *     "estimateFee": 50.00,
 *     "appDisCount": 1.00,
 *     "paymentType": "1",
 *     "cpAcctNo": "6225887845122365"
 *   }],
 *   "tradeChannel": "1",
 *   "appDt": "20250325",
 *   "appTm": "110450"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccessExample 响应结果示例
 * {
 *   "code": "0000",
 *   "description": "成功"
 * }
 */
public interface CounterBatchPaidFacade extends BaseFacade<CounterBatchPaidRequest, CounterBatchPaidResponse> {

} 