/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fullbatch;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/4/14 19:52
 * @since JDK 1.8
 */
@Getter
@Setter
public class FullBatchSubsInfoRequest implements Serializable {

    private static final long serialVersionUID = 2906059069111286911L;
    /**
     * 外部订单号
     */
    private String externalDealNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 中台业务码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中台业务码", isRequired = true)
    private String midBusinessCode;

    /**
     * 净申请金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "净申请金额", isRequired = true)
    private BigDecimal netAppAmt;

    /**
     * 预估费用
     * String：Money时手续费为0校验不通过
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预估费用", isRequired = true)
    private BigDecimal estimateFee;

    /**
     * 申请金额
     */
    private String appAmt;

    /**
     * 展期选项
     */
    private String extensionOption;

    /**
     * 展期控制数
     */
    private String extensionCount;

}
