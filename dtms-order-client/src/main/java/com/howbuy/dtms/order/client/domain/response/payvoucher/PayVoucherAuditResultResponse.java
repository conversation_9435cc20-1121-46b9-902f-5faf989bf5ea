/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 打款凭证审核结果响应
 * <AUTHOR>
 * @date 2024/7/25 16:17
 * @since JDK 1.8
 */
@Setter
@Getter
public class PayVoucherAuditResultResponse implements Serializable {

    private static final long serialVersionUID = -5328846429607854179L;

    /**
     * 汇款资金账号
     */
    private String remitCpAcctNo;

    /**
     * 汇款币种
     */
    private String remitCurrency;

    /**
     * 汇款金额
     */
    private String remitAmt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 材料信息 不通过原因
     */
    private String fileReason;

}
