/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.buy;

import com.howbuy.dtms.order.client.domain.request.buy.FeeComputeRequest;
import com.howbuy.dtms.order.client.domain.response.buy.BuyFeeComputeInfoVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 基金申购费用计算接口
 * <AUTHOR>
 * @date 2025/3/20 15:10
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.BuyFeeComputeFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup BuyFeeComputeFacade
 * @apiName execute()
 * @apiDescription 基金申购费用计算接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {Number} buyAmt 买入金额
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * buyAmt=3460.126476472265&externalDealNo=7&hkCustNo=iDloP&ipAddress=Obmc4y83&deviceName=LgmMYea13&systemVersion=Q&appTm=SC37h2&macAddress=v5&deviceSerialNo=fT&fundCode=EksDyJSRc&appDt=T9o3x&deviceModel=WR&txCode=Z&outletCode=APfz&tradeChannel=9i93Is
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Number} data.actualPayAmt 实际支付金额
 * @apiSuccess (响应结果) {Number} data.feeRate 手续费费率
 * @apiSuccess (响应结果) {Number} data.estimateFee 预估手续费
 * @apiSuccess (响应结果) {Number} data.originalFee 原始手续费
 * @apiSuccess (响应结果) {String} data.isLargerPrebookAmt 是否大于预约金额 0-否 1-是
 * @apiSuccess (响应结果) {String} data.validDiscountRate 折扣是否生效 0-否 1-是
 * @apiSuccess (响应结果) {Number} data.actualDiscountRate 实际折扣率
 * @apiSuccess (响应结果) {String} data.discountType 折扣类型
 * @apiSuccess (响应结果) {Number} data.discountAmt 折扣金额
 * @apiSuccess (响应结果) {Number} data.prebookDiscountRate 预约折扣率
 * @apiSuccessExample 响应结果示例
 * {"code":"Gt","data":{"originalFee":7058.11549725786,"isLargerPrebookAmt":"GvBqJ6P008","discountAmt":3963.12171793334,"prebookDiscountRate":5290.154189856038,"validDiscountRate":"U9zoSf","actualPayAmt":4071.0606750935995,"estimateFee":4966.688093487074,"discountType":"WrQV","actualDiscountRate":3213.7151592351274,"feeRate":8271.236524938311},"description":"dOy3cwwB2"}
 */
public interface BuyFeeComputeFacade extends BaseFacade<FeeComputeRequest, BuyFeeComputeInfoVO> {
}
