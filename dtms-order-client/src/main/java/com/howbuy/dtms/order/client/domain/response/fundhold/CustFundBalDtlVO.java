/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/15 19:14
 * @since JDK 1.8
 */
@Data
public class CustFundBalDtlVO implements Serializable {

    private static final long serialVersionUID = 7152637093140692919L;
    /**
     * 份额明细号
     */
    private String volDtlNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 主基金代码
     */
    private String mainFundCode;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundAbbr;

    /**
     * 管理人代码
     */
    private String fundManCode;

    /**
     * 确认流水号
     */
    private Long ackSerialNo;

    /**
     * 上报订单号
     */
    private Long submitDealNo;

    /**
     * 业务代码
     */
    private String busiCode;

    /**
     * 交易日期
     */
    private String tradeDt;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 初始份额
     */
    private BigDecimal initVol;

    /**
     * 总余额
     */
    private BigDecimal balanceVol;

    /**
     * 系列号
     */
    private String serialNumber;

    /**
     * 份额注册日期
     */
    private String shareRegDt;

    /**
     * 份额锁定结束日
     */
    private String lockEndDt;

    /**
     * 初始平衡因子
     */
    private BigDecimal initBalanceFactor;

    /**
     * 平衡因子
     */
    private BigDecimal balanceFactor;

    /**
     * 展期选项 1-本金展期,2-本金+收益展期,3-收益展期
     */
    private String extOption;

    /**
     * 展期控制类型:1-月
     */
    private String extControlType;

    /**
     * 展期控制数
     */
    private String extControlNum;
}
