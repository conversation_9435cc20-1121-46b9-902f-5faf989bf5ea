/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.traderecord;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 查询交易记录批次号
 * @Date 2024/12/06 15:34
 */
public class QueryTradeRecordResponse implements Serializable {

    private static final long serialVersionUID = -6576410537525355324L;

    /**
     * 批次号列表
     */
    private List<Long> batchNoList;

    public List<Long> getBatchNoList() {
        return batchNoList;
    }

    public void setBatchNoList(List<Long> batchNoList) {
        this.batchNoList = batchNoList;
    }
}
