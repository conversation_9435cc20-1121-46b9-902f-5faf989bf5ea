/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询交易订单(资金)响应对象
 * <AUTHOR>
 * @date 2025-07-11 13:35:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryDealOrderForCapitalResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 交易订单(资金)VO列表
     */
    private List<DealOrderForCapitalVO> dealOrderForCapitalVOList;
}
