/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.producttrade;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/10/27 16:56
 * @since JDK 1.8
 */
@Data
public class CmHkProductTradeExportVO {

    @ExcelIgnore
    private String id;

    /**
     * 客户姓名
     */
    @ExcelProperty(value = "客户姓名")
    private String custName;

    /**
     * 客户编号
     */
    @ExcelProperty(value = "客户编号")
    private String custNo;

    /**
     * 产品名称
     */
    @ExcelProperty(value = "产品名称")
    private String productName;

    /**
     * 产品代码
     */
    @ExcelProperty(value = "产品代码")
    private String productCode;

    /**
     * 交易类型
     */
    @ExcelProperty(value = "交易类型")
    private String tradeType;

    /**
     * 预约状态
     */
    @ExcelProperty(value = "预约状态")
    private String orderStatus;

    /**
     * 是否线上签约
     */
    @ExcelProperty(value = "是否线上签约")
    private String isOnlineSign;

    /**
     * 签约状态
     */
    @ExcelProperty(value = "签约状态")
    private String signStatus;

    /**
     * 签约时间
     */
    @ExcelProperty(value = "签约时间")
    private Date signDt;

    /**
     * 打款状态
     */
    @ExcelProperty(value = "打款状态")
    private String payStatus;

    /**
     * 交易状态
     */
    @ExcelProperty(value = "交易状态")
    private String tradeStatus;

    /**
     * 币种
     */
    @ExcelProperty(value = "币种")
    private String currency;

    /**
     * 申请金额
     */
    @ExcelProperty(value = "申请金额")
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    @ExcelProperty(value = "申请份额")
    private BigDecimal appVol;

    /**
     * 确认金额
     */
    @ExcelProperty(value = "确认金额")
    private BigDecimal ackAmt;

    /**
     * 确认份额
     */
    @ExcelProperty(value = "确认份额")
    private BigDecimal ackVol;

}