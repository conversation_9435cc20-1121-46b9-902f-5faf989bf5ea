/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/10 10:12
 * @since JDK 1.8
 */
@Data
public class ChangePayMethodPagelVO implements Serializable{

    private static final long serialVersionUID = -7944389443806479651L;
    /**
     * 订单明细信息
     */
    private ChangePayMethodOrderVO changePayMethodOrderVO;

    /**
     * 支付方式信息
     */
    private PayMethodInfoVO payMethodInfoVO;

    @Setter
    @Getter
    public static class ChangePayMethodOrderVO implements Serializable {

        private static final long serialVersionUID = 4306567561021343718L;
        /**
         * 支付方式  1 银行卡  2 支票  3 银行卡
         */
        private String paymentType;

        /**
         * 打款截止日期
         */
        private String payEndDate;

        /**
         * 打款截止日期 hh:mm
         */
        private String payEndTime;

        /**
         * 基金名称
         */
        private String fundName;

        /**
         * 基金代码
         */
        private String fundCode;

        /**
         * 实缴金额
         */
        private BigDecimal appAmt;

        /**
         *  如果是银行卡支付，会返回资金账号
         */
        private String cpAcctNo;
    }

}
