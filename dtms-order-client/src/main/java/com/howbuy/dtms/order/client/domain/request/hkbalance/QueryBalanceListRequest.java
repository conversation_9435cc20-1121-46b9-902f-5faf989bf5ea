/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.hkbalance;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.PageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/9 16:17
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryBalanceListRequest extends BaseRequest {

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金代码
     */
    private String fundCode;

    private int pageNo;

    private int pageSize;


}