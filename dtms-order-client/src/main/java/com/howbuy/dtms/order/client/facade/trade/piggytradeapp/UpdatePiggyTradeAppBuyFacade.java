/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.piggytradeapp.UpdatePiggyTradeAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.UpdatePiggyTradeAppBuyResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.piggytradeapp.UpdatePiggyTradeAppBuyFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup UpdatePiggyTradeAppBuyFacadeImpl
 * @apiName execute()
 * @apiDescription 修改储蓄罐交易申请买入接口
 * @apiParam (请求体) {String} importAppId 导入申请id，必填
 * @apiParam (请求体) {String} buyAmt 买入金额，必填
 * @apiParam (请求体) {String} discountRate 折扣率，必填
 * @apiParam (请求体) {String} operator 操作人，必填
 * @apiParam (请求体) {String} remark 备注，必填
 * @apiParamExample 请求体示例
 * {
 *   "importAppId": "APP001",
 *   "buyAmt": "10000.00",
 *   "discountRate": "0.8",
 *   "operator": "admin",
 *   "remark": "修改买入金额"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{},"description":"成功"}
 */
public interface UpdatePiggyTradeAppBuyFacade extends BaseFacade<UpdatePiggyTradeAppBuyRequest, UpdatePiggyTradeAppBuyResponse> {

}
