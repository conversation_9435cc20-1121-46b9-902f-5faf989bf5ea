/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/4/10 10:12
 * @since JDK 1.8
 */
@Setter
@Getter
public class BuyInfoVO implements Serializable {

    private static final long serialVersionUID = -7077657504828560160L;
    /**
     * 买入基金信息
     */
    private BuyFundInfoVO buyFundInfoVO;

    /**
     * 预约购买信息
     */
    private PrebookBuyInfoVO prebookBuyInfoVO;

    /**
     * 支付方式信息
     */
    private PayMethodInfoVO payMethodInfoVO;


}
