/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 修改储蓄罐交易申请买入请求
 * <AUTHOR>
 * @date 2025-07-18 16:09:17
 * @since JDK 1.8
 */
@Getter
@Setter
public class UpdatePiggyTradeAppBuyRequest extends BaseRequest {

    /**
     * 导入申请id
     */
    private String importAppId;

    /**
     * 买入金额
     */
    private String buyAmt;

    /**
     * 折扣率
     */
    private String discountRate;

    /**
     * 折扣率
     */
    private String fee;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

}
