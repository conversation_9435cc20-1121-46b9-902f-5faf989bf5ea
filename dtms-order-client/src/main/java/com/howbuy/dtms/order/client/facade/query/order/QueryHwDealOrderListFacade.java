/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.order;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryHwDealOrderListRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.QueryHwDealOrderListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/29 15:56
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.order.QueryHwDealOrderListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryHwDealOrderListFacade
 * @apiName execute()
 * @apiDescription QueryHwDealOrderListFacade
 * @apiParam (请求参数) {Array} dealNoList 订单号
 * @apiParam (请求参数) {String} appStartDt 申请开始时间
 * @apiParam (请求参数) {String} appEndDt 申请日期 结束
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {Array} custType 客户类型
 * @apiParam (请求参数) {String} openStartDt 开放日 开始
 * @apiParam (请求参数) {String} openEndDt 开放日 结束
 * @apiParam (请求参数) {Array} fundName 产品代码
 * @apiParam (请求参数) {Array} midBusinessCode 中台业务码
 * @apiParam (请求参数) {Array} orderStatus 订单状态
 * @apiParam (请求参数) {String} dealNo 订单号
 * @apiParam (请求参数) {String} preDealNo 预约单号
 * @apiParam (请求参数) {Array} payStatus 付款状态
 * @apiParam (请求参数) {Array} paymentTypeList 支付方式
 * @apiParam (请求参数) {Array} tradeChannels 交易渠道
 * @apiParam (请求参数) {Number} page
 * @apiParam (请求参数) {Number} size
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * appEndDt=ScmK9HRl&preDealNo=dVUEh&externalDealNo=fgeWPFUy&tradeChannels=Ik&openStartDt=7ewNkJ7&midBusinessCode=Qh&orderStatus=6SaadM&deviceName=y&systemVersion=V&appStartDt=mb&appDt=NjUea&openEndDt=zdfFyQCBp&hkCustNo=4ChdD&ipAddress=2QNLyW28Q&dealNo=4w5ucSHayE&appTm=V&macAddress=2t&size=1972&deviceSerialNo=hc&paymentTypeList=GJdZ&dealNoList=FKehfc1&custType=gBIpT&deviceModel=GblpeXZ&page=534&fundName=gk2Zw5PH&txCode=ySxdKYS9f&payStatus=Tyxm&outletCode=BqER&tradeChannel=KQN
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Number} data.total 总记录数
 * @apiSuccess (响应结果) {Number} data.pages 总页数
 * @apiSuccess (响应结果) {Array} data.list 结果
 * @apiSuccess (响应结果) {Number} data.list.id id
 * @apiSuccess (响应结果) {Number} data.list.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.list.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.list.custChineseName 客户中文姓名
 * @apiSuccess (响应结果) {String} data.list.idType 证件类型
 * @apiSuccess (响应结果) {String} data.list.idNoCipher 证件号码密文
 * @apiSuccess (响应结果) {String} data.list.idNoDigest 证件号码摘要
 * @apiSuccess (响应结果) {String} data.list.idNoMask 证件号码掩码
 * @apiSuccess (响应结果) {String} data.list.invstType 投资者类型 0-机构；1-个人
 * @apiSuccess (响应结果) {String} data.list.qualificationType 投资者资质 PRO-投资者资质专业;NORMAL-投资者资质普通
 * @apiSuccess (响应结果) {String} data.list.custRiskLevel 客户风险等级
 * @apiSuccess (响应结果) {String} data.list.cpAcctNo 资金账号
 * @apiSuccess (响应结果) {String} data.list.bankAcctCipher 银行账号密文
 * @apiSuccess (响应结果) {String} data.list.bankAcctDigest 银行账号摘要
 * @apiSuccess (响应结果) {String} data.list.bankAcctMask 银行账号掩码
 * @apiSuccess (响应结果) {String} data.list.swiftCode swift编码
 * @apiSuccess (响应结果) {String} data.list.bankCode 银行代码
 * @apiSuccess (响应结果) {String} data.list.bankName 银行名称
 * @apiSuccess (响应结果) {String} data.list.bankChineseName 银行中文名称
 * @apiSuccess (响应结果) {String} data.list.txCode 交易代码:HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验
 * @apiSuccess (响应结果) {String} data.list.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
 * @apiSuccess (响应结果) {String} data.list.middleBusiCode 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘
 * @apiSuccess (响应结果) {String} data.list.productName 产品名称
 * @apiSuccess (响应结果) {String} data.list.productAbbr 产品简称
 * @apiSuccess (响应结果) {String} data.list.productCode 产品代码
 * @apiSuccess (响应结果) {Number} data.list.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.list.appVol 申请份额
 * @apiSuccess (响应结果) {String} data.list.currency 币种
 * @apiSuccess (响应结果) {String} data.list.openDt 开放日期
 * @apiSuccess (响应结果) {String} data.list.taTradeDt TA交易日期
 * @apiSuccess (响应结果) {String} data.list.appDt 申请日期
 * @apiSuccess (响应结果) {String} data.list.appTm 申请时间
 * @apiSuccess (响应结果) {String} data.list.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {String} data.list.paymentTypeList 支付方式列表 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
 * @apiSuccess (响应结果) {String} data.list.payStatus 支付状态 0-无需付款 1-未付款 2-付款中 3-部分成功 4-成功 5-失败 6-退款
 * @apiSuccess (响应结果) {String} data.list.payVoucherStatus 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
 * @apiSuccess (响应结果) {Number} data.list.actualPayAmt 实际打款金额
 * @apiSuccess (响应结果) {String} data.list.actualPayDt 实际打款日期
 * @apiSuccess (响应结果) {String} data.list.actualPayTm 实际打款时间
 * @apiSuccess (响应结果) {String} data.list.prebookDealNo 预约单号
 * @apiSuccess (响应结果) {String} data.list.externalDealNo 外部单号
 * @apiSuccess (响应结果) {Number} data.list.relationalDealNo 关联订单号(订单表的订单号)
 * @apiSuccess (响应结果) {Number} data.list.batchNo 批次号
 * @apiSuccess (响应结果) {String} data.list.firstBuyFlag 首次购买标识，1-首次购买；2-追加购买
 * @apiSuccess (响应结果) {String} data.list.isAgreeCurrencyExchange 是否同意换汇 0-否 1-是
 * @apiSuccess (响应结果) {String} data.list.orderFormType 成单方式 1：纸质成单；2：电子成单；
 * @apiSuccess (响应结果) {String} data.list.supportPrebookFlag 支持预约交易标识，0-不支持；1-支持
 * @apiSuccess (响应结果) {String} data.list.memo 备注
 * @apiSuccess (响应结果) {String} data.list.tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiSuccess (响应结果) {String} data.list.ipAddress IP地址
 * @apiSuccess (响应结果) {String} data.list.outletCode 网点号
 * @apiSuccess (响应结果) {String} data.list.recStat 记录状态 0-正常；1-已删除
 * @apiSuccess (响应结果) {Number} data.list.createTimestamp 创建时间戳
 * @apiSuccess (响应结果) {Number} data.list.updateTimestamp 更新时间戳
 * @apiSuccessExample 响应结果示例
 * {"code":"PWDJsV6","data":{"total":9355,"pages":2617,"list":[{"externalDealNo":"hiqz","appAmt":6032.*************,"bankAcctMask":"ZPX6Jqu2X","taTradeDt":"QPn","memo":"z","productName":"eIzRP","createTimestamp":*************,"appVol":5404.************,"bankAcctCipher":"Bqx","custChineseName":"CfUg","appDt":"3oMSpo6l8","id":6110,"firstBuyFlag":"7Nn4gCh","idType":"JsaCf","ipAddress":"1jUo1ju","dealNo":7261,"openDt":"2","txCode":"ZI3n","outletCode":"JHlKRXu","actualPayTm":"FKL","orderFormType":"Dv","swiftCode":"Ik7wIn","orderStatus":"ZSzmVtW07","bankName":"0au3F","productAbbr":"twFSlB","qualificationType":"j5FYedHW","invstType":"mJQq","supportPrebookFlag":"rLTcR0Z","cpAcctNo":"LnEiuvx","currency":"cAYhLj","bankCode":"8dgjrYvwG","bankChineseName":"NWY4GRTAe","payVoucherStatus":"MgkBG","middleBusiCode":"2","isAgreeCurrencyExchange":"stRI5Xy","hkCustNo":"iGjV","custRiskLevel":"ouLMUZOes","idNoDigest":"25PI6mq","idNoMask":"A7VVCJ0","bankAcctDigest":"bGvk","updateTimestamp":*************,"prebookDealNo":"2nWenXB","appTm":"hVjk65uA","productCode":"4sHBCP","paymentTypeList":"YteB5vSR","actualPayAmt":5025.************,"businessType":"adZShU","idNoCipher":"wLKjqZc72","payStatus":"cd0oj","actualPayDt":"GPeqFZ7","recStat":"IqpoL1sv11","tradeChannel":"X6LlzTRPx"}]},"description":"34lh0y"}
 */
public interface QueryHwDealOrderListFacade extends BaseFacade<QueryHwDealOrderListRequest, QueryHwDealOrderListResponse> {

}