/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.tradereported;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 更新上报订单状态req
 * @Date 2024/12/18 16:34
 */
@Getter
@Setter
public class UpdateTradeReportedStatusRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 6168474112690672115L;

    /**
     * 上报状态 (1-成功 0-失败)
     * 1-成功
     *  -更新上报订单表状态为上报成功
     * 0-失败
     *  -逻辑删除作废对应的上报订单
     *  -更新对应的订单明细的上报状态【需重新上报】
     */
    private String submitStatus;

    /**
     * 订单明细列表
     */
    private List<DealOrderDtlReportedDTO> list;

    /**
     * 订单明细上报回退DTO
     */
    @Getter
    @Setter
    public static class DealOrderDtlReportedDTO implements Serializable {

        private static final long serialVersionUID = -543936535518065145L;

        /**
         * 订单明细号
         */
        private Long dealDtlNo;

        /**
         * 上报订单号
         */
        private List<SubmitReportedDTO> list;

        /**
         * 上报订单回退DTO
         */
        @Getter
        @Setter
        public static class SubmitReportedDTO implements Serializable {

            private static final long serialVersionUID = -3232556954305524602L;

            /**
             * 上报订单号
             */
            private Long submitDealNo;

        }
    }
}
