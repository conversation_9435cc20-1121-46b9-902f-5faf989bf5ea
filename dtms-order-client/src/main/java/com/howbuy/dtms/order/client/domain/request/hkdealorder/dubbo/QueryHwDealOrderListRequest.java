/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo;

import com.howbuy.dtms.order.client.domain.request.PageRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/29 15:58
 * @since JDK 1.8
 */
@Data
public class QueryHwDealOrderListRequest extends PageRequest {
    /**
     * 订单号
     */
    private List<String> dealNoList;

    /**
     * 申请开始时间
     */
    private String appStartDt;
    /**
     * 申请日期 结束
     */
    private String appEndDt;
    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 客户类型
     */
    private List<String> custType;
    /**
     * 开放日 开始
     */
    private String openStartDt;
    /**
     * 开放日 结束
     */
    private String openEndDt;
    /**
     * 产品代码
     */
    private List<String> fundName;
    /**
     * 中台业务码
     */
    private List<String> midBusinessCode;
    /**
     * 订单状态
     */
    private List<String> orderStatus;
    /**
     * 订单号
     */
    private String dealNo;
    /**
     * 预约单号
     */
    private String preDealNo;
    /**
     * 付款状态
     */
    private List<String> payStatus;
    /**
     * 支付方式
     */
    private List<String> paymentTypeList;
    /**
     * 交易渠道
     */
    private List<String> tradeChannels;
}