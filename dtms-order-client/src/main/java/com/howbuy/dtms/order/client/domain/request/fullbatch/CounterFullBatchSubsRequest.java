package com.howbuy.dtms.order.client.domain.request.fullbatch;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 柜台全委专户认申购请求
 * @date 2025-04-14 19:21:16
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class CounterFullBatchSubsRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
    private String fundTxAcctNo;

    /**
     * 全委批量认申购列表
     */
    private List<FullBatchSubsInfoRequest> fullBatchSubsList;

    /**
     * 交易渠道
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易渠道", isRequired = true)
    private String tradeChannel;

    /**
     * 申请日期
     * 格式：YYYYMMDD
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "申请日期", isRequired = true)
    private String appDt;

    /**
     * 申请时间
     * 格式：HHmmss
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "申请时间", isRequired = true)
    private String appTm;
} 