/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo;

import com.howbuy.dtms.order.client.domain.response.order.DealOrderWithSubmitDetailInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:(查询订单信息，包括上报明细信息的请求响应)
 * @return
 * @author: ha<PERSON>.zhang
 * @date: 2024/12/14 10:06
 * @since JDK 1.8
 */
@Data
public class QueryDealOrderWithSubmitDetailResponse implements Serializable {


    /**
     * 订单[附带上报明细信息] 列表信息
     */
    private List<DealOrderWithSubmitDetailInfoVO> dealOrderList;

}