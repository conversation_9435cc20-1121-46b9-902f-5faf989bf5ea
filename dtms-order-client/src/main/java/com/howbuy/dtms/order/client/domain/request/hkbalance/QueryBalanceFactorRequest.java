package com.howbuy.dtms.order.client.domain.request.hkbalance;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBalanceFactorRequest extends BaseRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 一账通号
     */
    private String hbOneNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 净值日期
     */
    private String navDt;

}
