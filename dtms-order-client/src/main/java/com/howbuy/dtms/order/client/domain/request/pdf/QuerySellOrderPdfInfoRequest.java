package com.howbuy.dtms.order.client.domain.request.pdf;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 请求类 - 查询赎回订单PDF信息
 * <AUTHOR>
 * @date 2024/5/9 16:17
 * @since JDK 1.8
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class QuerySellOrderPdfInfoRequest extends BaseRequest {

    private static final long serialVersionUID = -5758897558508405016L;

    public QuerySellOrderPdfInfoRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_QUERY_SELL_ORDER_PDF);
    }

    private String dealNo;

    private String hkCustNo;

}