package com.howbuy.dtms.order.client.domain.response.pdf;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 查询赎回订单PDF信息
 * @return
 * @author: jinqing.rao
 * @date: 2025/5/15 14:31
 * @since JDK 1.8
 */

public class QuerySellOrderPdfInfoResponse implements Serializable {

    private static final long serialVersionUID = -5548391612073986004L;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户名称
     */
    private String custName;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品份额
     */
    private String productVol;

    /**
     * 赎回方式
     */
    private String redeemType;

    /**
     * 赎回金额
     */
    private BigDecimal redeemAmt;

    /**
     * 币种
     */
    private String currency;

    /**
     * 币种描述
     */
    private String currencyDesc;

    /**
     * 赎回份额
     */
    private BigDecimal redeemVol;

    /**
     * 份额精度
     */
    private Integer volPrecision;

    /**
     * 交易日期，YYYY-MM-DD
     */
    private String tradeDt;

    /**
     * 申请日期
     */
    private String appDt;


    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 赎回赎回方向列表
     */
    private String redeemDirectionList;

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductVol() {
        return productVol;
    }

    public void setProductVol(String productVol) {
        this.productVol = productVol;
    }

    public String getRedeemType() {
        return redeemType;
    }

    public void setRedeemType(String redeemType) {
        this.redeemType = redeemType;
    }

    public BigDecimal getRedeemAmt() {
        return redeemAmt;
    }

    public void setRedeemAmt(BigDecimal redeemAmt) {
        this.redeemAmt = redeemAmt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyDesc() {
        return currencyDesc;
    }

    public void setCurrencyDesc(String currencyDesc) {
        this.currencyDesc = currencyDesc;
    }

    public BigDecimal getRedeemVol() {
        return redeemVol;
    }

    public void setRedeemVol(BigDecimal redeemVol) {
        this.redeemVol = redeemVol;
    }

    public Integer getVolPrecision() {
        return volPrecision;
    }

    public void setVolPrecision(Integer volPrecision) {
        this.volPrecision = volPrecision;
    }

    public String getTradeDt() {
        return tradeDt;
    }

    public void setTradeDt(String tradeDt) {
        this.tradeDt = tradeDt;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getRedeemDirectionList() {
        return redeemDirectionList;
    }

    public void setRedeemDirectionList(String redeemDirectionList) {
        this.redeemDirectionList = redeemDirectionList;
    }
}