/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/1 18:08
 * @since JDK 1.8
 */
@Getter
@Setter
public class CustFundHoldBaseDetailVO implements Serializable {

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 可用份额
     */
    private BigDecimal availableVol;

    /**
     * 锁定份额
     */
    private BigDecimal lockVol;

    /**
     * 冻结份额
     */
    private BigDecimal freezeVol;

    /**
     * 总份额
     */
    private BigDecimal totalVol;

}
