/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.agreement;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/3/20 15:10
 * @since JDK 1.8
 */
@Setter
@Getter
public class CounterParamAgreementModifyRequest extends BaseRequest  implements Serializable {

    private static final long serialVersionUID = 6872364444190128506L;

    /**
     * 业务ID
     */
    private String agrId;
    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 管理人代码
     */
    private String fundManCode;

    /**
     * 基金简称
     */
    private String fundAddr;


    /**
     * 协议名称
     */
    private String agreementName;

    /**
     * 协议路径
     */
    private String agreementUrl;

    /**
     * 补签截止时间
     */
    private String agreementSignEndDt;

    /**
     * 补签原因
     */
    private String agreementReason;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 客户信息
     */
    private List<ParamsAuditDetailCustDTO> paramsAuditDetailCustInfoVO;

    @Setter
    @Getter
    public static class ParamsAuditDetailCustDTO implements Serializable {

        private static final long serialVersionUID = 3806065374119208710L;

        /**
         * 补充协议ID
         */
        private String agrId;

        /**
         * 补充协议明细ID
         */
        private String agrDtlId;
        /**
         * 香港客户号
         */
        private String hkCustNo;

        /**
         * 客户中文名称
         */
        private String custChineseName;

        /**
         * 客户英文名称
         */
        private String custEnName;

        /**
         * 证件类型
         */
        private String idType;

        /**
         * 证件号码
         */
        private String idNo;

        /**
         * 是否需要补充协议
         */
        private String needSupplementalAgreement;

        /**
         * 0 新增  1 修改
         */
        private String modifyType;
    }
}
