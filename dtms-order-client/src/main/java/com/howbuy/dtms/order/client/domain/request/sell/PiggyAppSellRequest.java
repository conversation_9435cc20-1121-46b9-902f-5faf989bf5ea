package com.howbuy.dtms.order.client.domain.request.sell;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 储蓄罐申请卖出请求类
 * @date 2024/8/13 17:11
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PiggyAppSellRequest extends BaseRequest {

    public PiggyAppSellRequest() {
        setTxCode(OrderTxCodes.HW_ORDER_PIGGY_SELL);
    }

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 预计上报日期
     */
    private String preSubmitTaDt;

    /**
     * 赎回方式 1-按份额赎回；2-按金额赎回
     */
    private String redeemMethod;

    /**
     * 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
     */
    private String redeemDirection;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 交易订单号
     */
    private Long dealNo;

    /**
     * 关联订单号,订单表的订单号
     */
    private Long relationalDealNo;

    /**
     * 操作人
     */
    private String operator;

}
