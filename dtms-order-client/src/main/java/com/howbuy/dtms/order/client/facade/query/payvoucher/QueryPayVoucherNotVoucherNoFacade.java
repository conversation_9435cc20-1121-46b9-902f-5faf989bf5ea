package com.howbuy.dtms.order.client.facade.query.payvoucher;

import com.howbuy.dtms.order.client.domain.request.payvoucher.QueryPayVoucherNotVoucherNoRequest;
import com.howbuy.dtms.order.client.domain.response.payvoucher.QueryPayVoucherNotVoucherNoResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payvoucher.QueryPayVoucherNotVoucherNoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryPayVoucherNotVoucherNoFacade
 * @apiName execute()
 * @apiDescription 根据香港客户号打款凭证类型审核状态查询打款凭证订单信息
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} voucherType 打款凭证类型
 * @apiParam (请求参数) {Array} auditStatus 审核状态
 * @apiParamExample 请求参数示例
 * voucherType=EEswCchI8&hkCustNo=cBs&auditStatus=ashVDyKdre
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.voucherNo 打款凭证号
 * @apiSuccessExample 响应结果示例
 * {"code":"BRoRZMLor","data":{"voucherNo":["Yjt8d4Fmo"]},"description":"8zPO5rNll9"}
 */
public interface QueryPayVoucherNotVoucherNoFacade extends BaseFacade<QueryPayVoucherNotVoucherNoRequest, QueryPayVoucherNotVoucherNoResponse> {
}
