/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.contractsign;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (合同及协议详情)
 * <AUTHOR>
 * @date 2023/5/17 17:28
 * @since JDK 1.8
 */
@Data
public class AgreementVO implements Serializable {

    private static final long serialVersionUID = -2736052235991980538L;
    /**
     * 文件代码
     */
    private String fileCode;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件路径
     */
    private String filePathUrl;
}