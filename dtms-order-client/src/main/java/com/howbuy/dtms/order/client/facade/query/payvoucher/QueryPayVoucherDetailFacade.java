/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.payvoucher;

import com.howbuy.dtms.order.client.domain.request.payvoucher.PayVoucherRequest;
import com.howbuy.dtms.order.client.domain.response.payvoucher.PayVoucherDetailResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 根据打款凭证流水号查询打款凭证流水
 * <AUTHOR>
 * @date 2024/7/23 14:29
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.payvoucher.QueryPayVoucherDetailFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryPayVoucherDetailFacade
 * @apiName execute()
 * @apiDescription 根据打款凭证流水号查询打款凭证流水
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} voucherNo 打款凭证流水号
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=u&hkCustNo=zXw4KF&ipAddress=t7&deviceName=DZc6f&systemVersion=TJ0W7xvCS&voucherNo=qLxR&appTm=3nPQFCw&macAddress=daD4RS&deviceSerialNo=q&appDt=dy7&deviceModel=NRYzZE95ZE&txCode=4O6C&outletCode=uK92O&tradeChannel=HW
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.voucherNo 香港客户号
 * @apiSuccess (响应结果) {String} data.voucherType 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
 * @apiSuccess (响应结果) {String} data.tradeOrderNo 交易订单号 类型为1-交易下单凭证时，订单号必传
 * @apiSuccess (响应结果) {String} data.remitCpAcctNo 汇款资金账号
 * @apiSuccess (响应结果) {String} data.remitCurrency 汇款币种
 * @apiSuccess (响应结果) {Number} data.remitAmt 汇款金额
 * @apiSuccess (响应结果) {String} data.bankName 银行名称
 * @apiSuccess (响应结果) {String} data.bankChineseName 银行中文名称
 * @apiSuccess (响应结果) {String} data.bankAcct 银行账户
 * @apiSuccess (响应结果) {String} data.swiftCode SWIFT代码
 * @apiSuccess (响应结果) {String} data.bankAcctDigest 银行账户摘要
 * @apiSuccess (响应结果) {String} data.bankAcctMask 银行账号掩码
 * @apiSuccess (响应结果) {String} data.tradeChannel 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP
 * @apiSuccess (响应结果) {String} data.remark 备注
 * @apiSuccess (响应结果) {String} data.auditStatus 审核状态
 * @apiSuccess (响应结果) {Array} data.fileList 文件列表
 * @apiSuccess (响应结果) {String} data.fileList.fileId 文件id
 * @apiSuccess (响应结果) {String} data.fileList.filePath 文件路径
 * @apiSuccess (响应结果) {String} data.fileList.fileName 文件路径
 * @apiSuccess (响应结果) {String} data.fileList.fileSuffix 文件后缀
 * @apiSuccess (响应结果) {Object} data.auditReason 审核不通过原因
 * @apiSuccess (响应结果) {String} data.auditReason.remitCpAcctNo 汇款资金账号
 * @apiSuccess (响应结果) {String} data.auditReason.remitCurrency 汇款币种
 * @apiSuccess (响应结果) {String} data.auditReason.remitAmt 汇款金额
 * @apiSuccess (响应结果) {String} data.auditReason.remark 备注
 * @apiSuccess (响应结果) {String} data.auditReason.fileReason 材料信息 不通过原因
 * @apiSuccessExample 响应结果示例
 * {"code":"9bZ","data":{"remitCpAcctNo":"e","remitCurrency":"0q5FgQ","bankChineseName":"yUp8","voucherType":"6TuP","bankAcctMask":"fcodi","hkCustNo":"P8","bankAcct":"Q","tradeOrderNo":"6rNot5yN","swiftCode":"57kNehpN","bankName":"3qyvZfY3","remark":"fddPlW9T","bankAcctDigest":"2WGxOtM4W","voucherNo":"zU07IO4Mez","auditReason":{"remitCpAcctNo":"dSaI5","remitCurrency":"W","fileReason":"guLotlf","remark":"7qYd","remitAmt":"hNE2"},"auditStatus":"YXJS179","remitAmt":7303.************,"fileList":[{"fileName":"iN8q","fileSuffix":"y","filePath":"jlb9eDbpXl","fileId":"Rw45Wv49"}],"tradeChannel":"F"},"description":"d7mD5"}
 */
public interface QueryPayVoucherDetailFacade extends BaseFacade<PayVoucherRequest, PayVoucherDetailResponse> {
}
