/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.sell.dubbo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/16 13:13
 * @since JDK 1.8
 */
@Setter
@Getter
public class PiggySellInfoResponse implements Serializable {

    private static final long serialVersionUID = -8147917243201914558L;

    /**
     * 卖出基金信息
     */
    private PiggySellFundInfoResponse piggySellFundInfoResp;

    /**
     * 预约卖出信息
     */
    private PiggyPrebookSellInfoResponse piggyPrebookSellInfoResp;

    /**
     * 是否签约储蓄罐 0-未签署 1-签署
     */
    private String hasSignCxg;
}
