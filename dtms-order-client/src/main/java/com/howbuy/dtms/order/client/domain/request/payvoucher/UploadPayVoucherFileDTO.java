/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 打款凭证材料DTO
 * <AUTHOR>
 * @date 2024/7/22 16:29
 * @since JDK 1.8
 */

@Setter
@Getter
public class UploadPayVoucherFileDTO  implements Serializable {

    private static final long serialVersionUID = 5732154601711267879L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 资料名称
     */
    private String fileName;

    /**
     * 资料相对路径
     */
    private String fileUrl;

    /**
     * 资料类型
     */
    private String fileType;

}