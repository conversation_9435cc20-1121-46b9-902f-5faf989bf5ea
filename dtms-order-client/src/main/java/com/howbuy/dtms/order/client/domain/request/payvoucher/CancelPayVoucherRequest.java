/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/22 17:18
 * @since JDK 1.8
 */

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class CancelPayVoucherRequest extends BaseRequest {

    private static final long serialVersionUID = 1009434681643041915L;
    /**
     * 上传打款凭证流水号 （若传了，则在原订单基础上更新订单）
     */
    private String voucherNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 操作人
     */
    private String operator;


}