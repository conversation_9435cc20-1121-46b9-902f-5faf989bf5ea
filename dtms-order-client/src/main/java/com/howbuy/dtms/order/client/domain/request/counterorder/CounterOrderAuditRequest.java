/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.counterorder;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/11/6 14:23
 * @since JDK 1.8
 */
public class CounterOrderAuditRequest extends BaseRequest {

    /**
     * 交易申请流水号
     */
    private String appSerialNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 操作员
     */
    private String operator;

    /**
     * 操作时间戳
     */
    private String operatorTimestamp;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 柜台业务码
     */
    private String counterBizType;


     /**
     * 回访文件地址，回访状态时必传
     */
    private String revisitFileUrl;

    /**
     * 回访文件名称，回访状态时必传
     */
    private String revisitFileName;

     /**
     * 回访人
     */
    private String revisitPerson;

    /**
     * 回访原因
     */
    private String revisitReason;

    /**
     * 是否需要回访
     */
    private String revisit;

    public String getAppSerialNo() {
        return appSerialNo;
    }

    public void setAppSerialNo(String appSerialNo) {
        this.appSerialNo = appSerialNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getOperatorTimestamp() {
        return operatorTimestamp;
    }

    public void setOperatorTimestamp(String operatorTimestamp) {
        this.operatorTimestamp = operatorTimestamp;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getCounterBizType() {
        return counterBizType;
    }

    public void setCounterBizType(String counterBizType) {
        this.counterBizType = counterBizType;
    }

    public String getRevisitFileUrl() {
        return revisitFileUrl;
    }

    public void setRevisitFileUrl(String revisitFileUrl) {
        this.revisitFileUrl = revisitFileUrl;
    }

    public String getRevisitFileName() {
        return revisitFileName;
    }

    public void setRevisitFileName(String revisitFileName) {
        this.revisitFileName = revisitFileName;
    }

    public String getRevisitPerson() {
        return revisitPerson;
    }

    public void setRevisitPerson(String revisitPerson) {
        this.revisitPerson = revisitPerson;
    }

    public String getRevisit() {
        return revisit;
    }

    public void setRevisit(String revisit) {
        this.revisit = revisit;
    }

    public String getRevisitReason() {
        return revisitReason;
    }

    public void setRevisitReason(String revisitReason) {
        this.revisitReason = revisitReason;
    }
}
