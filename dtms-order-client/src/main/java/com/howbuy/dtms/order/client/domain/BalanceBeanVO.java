/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @since JDK 1.8
 */
@Data
public class BalanceBeanVO implements Serializable {
    /**
     * 分销代码
     */
    private String disCode;
    /**
     * 分销代码列表
     */
    private List<String> disCodeList;
    /**
     * 产品代码
     */
    private String productCode;
    /**
     * 子产品代码
     */
    private String subProductCode;
    /**
     * 产品名称
     */
    private String productName;
    /**
     * 子产品名称
     */
    private String subProductName;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品子类型(好买产品线)
     */
    private String productSubType;
    /**
     * 总份额
     */
    private BigDecimal balanceVol;
    /**
     * 待确认份额
     */
    private BigDecimal unconfirmedVol;
    /**
     * 待确认金额
     */
    private BigDecimal unconfirmedAmt;
    /**
     * 币种
     */
    private String currency;
    /**
     * 净值
     */
    private BigDecimal nav;
    /**
     * 净值日期
     */
    private String navDt;
    /**
     * 净值分红标识 0-否，1-是
     */
    private String navDivFlag = "0";
    /**
     * 市值
     */
    private BigDecimal marketValue;
    /**
     * 当前币种的市值
     */
    private BigDecimal currencyMarketValue;

    /**
     * 展示币种对应的市值
     */
    private BigDecimal disCurMarketValue;

    /**
     * 销售类型: 1-直销;2-代销
     */
    private String scaleType;
    /**
     * 好买香港代销标识: 0-否; 1-是
     */
    private String hkSaleFlag;
    /**
     * 分期成立标识(证券类有此标识:0-否,1-是)
     */
    private String StageEstablishFlag;
    /**
     * 分次call标识(股权类有此标识:0-否,1-是)
     */
    private String fractionateCallFlag;
    /**
     * 产品存续期限(类似于5+3+2这种说明)
     */
    private String fundCXQXStr;
    /**
     * 净购买金额(投资成本)
     */
    private BigDecimal netBuyAmount;
    /**
     * 净购买金额(投资成本)(当前币种)
     */
    private BigDecimal currencyNetBuyAmount;
    /**
     * 认缴金额
     */
    private BigDecimal paidInAmt;
    /**
     * 收益日期
     */
    private String incomeDt;
    /**
     * 0-计算中；1-计算完成
     */
    private String incomeCalStat;
    /**
     * 当前收益(人民币）
     */
    private BigDecimal currentAsset;
    /**
     * 当前收益（当前币种）
     */
    private BigDecimal currentAssetCurrency;
    /**
     * 累计收益
     */
    private BigDecimal accumIncome;
    /**
     * 累计收益(人民币)
     */
    private BigDecimal accumIncomeRmb;
    /**
     * 累计已实现收益
     */
    private BigDecimal accumRealizedIncome;
    /**
     * 累计已实现收益人民币
     */
    private BigDecimal accumRealizedIncomeRmb;

    /**
     * 是否复构 0-否 1-是
     **/
    private String rePurchaseFlag;

    /**
     * 业绩比较基准
     **/
    private String benchmark;
    /**
     * 业绩比较基准类型：0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
     **/
    private String benchmarkType;

    /**
     * 起息日
     **/
    private String valueDate;

    /**
     * 到期日
     **/
    private String dueDate;

    /**
     * 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
     */
    private String standardFixedIncomeFlag;

    // 投资期限
    private String investmentHorizon;

    // 合作商户
    private String cooperation;

    // 清盘中标识 0-否 1-是
    private String crisisFlag;
    // 七日年化收益
    private BigDecimal yieldIncome;
    // 七日年化日期/万份收益日期
    private String yieldIncomeDt;
    /**
     * 万份收益
     */
    private BigDecimal copiesIncome;

    /**
     * 是否海外产品 0-否 1-是
     */
    private String hwSaleFlag;
    /**
     * 登记日期
     */
    private String regDt;
    /**
     * 一级监管分类
     */
    private String oneStepType;
    /**
     * 二级监管分类
     */
    private String twoStepType;
    /**
     * 三级监管分类
     */
    private String secondStepType;
    /**
     * 产品销售类型 0-好买 1-海外 2-其他
     */
    private String productSaleType;
    /**
     * NA产品收费类型 10201-好买收费 0-管理人收费
     */
    private String naProductFeeType;
    /**
     * 累计应收管理费
     */
    private BigDecimal receivManageFee;
    /**
     * 累计应收业绩报酬
     */
    private BigDecimal receivPreformFee;
    /**
     * NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
     */
    private BigDecimal currencyMarketValueExFee;
    /**
     * NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
     */
    private BigDecimal marketValueExFee;
    /**
     * 当前收益（股权新算法）不含费
     */
    private BigDecimal balanceIncomeNew;
    /**
     * 当前收益（股权新算法）不含费-人民币
     */
    private BigDecimal balanceIncomeNewRmb;
    /**
     * 平衡因子
     */
    private BigDecimal balanceFactor;
    /**
     * 平衡因子转换完成 1-是 0-否
     */
    private String convertFinish;
    /**
     * 平衡因子日期
     */
    private String balanceFactorDate;

    /**
     * 收益率
     */
    private BigDecimal yieldRate;


    /**************持仓2.0 crm页面新增字段start****************/
    /**
     * 累计收益（股权固收新算法）不含费
     */
    private BigDecimal accumIncomeNew;
    /**
     * 累计收益（股权固收新算法）不含费-人民币
     */
    private BigDecimal accumIncomeNewRmb;
    /**
     * 持仓总成本(人民币）
     */
    private BigDecimal balanceCost;
    /**
     * 持仓总成本（当前币种）
     */
    private BigDecimal balanceCostCurrency;
    /**
     * 日收益(人民币）
     */
    private BigDecimal dailyAsset;
    /**
     * 日收益（当前币种）
     */
    private BigDecimal dailyAssetCurrency;
    /**
     * 私募股权回款
     */
    private BigDecimal cashCollection;
    /**
     * 私募股权回款(当前币种)
     */
    private BigDecimal currencyCashCollection;

    // 累计收益率
    private BigDecimal accumYieldRate;
    // 累计成本
    private BigDecimal accumCost;
    // 累计成本(人民币)
    private BigDecimal accumCostRmb;
    // 持仓浮盈亏
    private BigDecimal balanceFloatIncome;
    // 持仓浮盈亏(人民币)
    private BigDecimal balanceFloatIncomeRmb;
    // 持仓浮盈亏比率
    private BigDecimal balanceFloatIncomeRate;
    // 最新收益率
    private BigDecimal dayAssetRate;
    // 最新资产增长率
    private BigDecimal dayIncomeGrowthRate;

    // 投资总成本——新
    private BigDecimal accumCostNew;
    // 投资总成本——新
    private BigDecimal accumCostRmbNew;
    // 参考市值
    private BigDecimal balanceAmt;
    // 参考市值(人民币)
    private BigDecimal balanceAmtRmb;
    // 累计总回款
    private BigDecimal accumCollection;
    // 累计总回款(人民币)
    private BigDecimal accumCollectionRmb;
    // NA费后参考市值
    private BigDecimal balanceAmtExFee;
    // NA费后参考市值(人民币)
    private BigDecimal balanceAmtExFeeRmb;

    // 非交易接口(获取NA标识)：SXZ -- NA产品收费类型 （10201：好买收费 0：管理人收费）
    private String sxz;

    /**
     * 当前收益(当前币种）
     */
    private BigDecimal currentIncome;

    /**
     * 当前收益(人民币）
     */
    private BigDecimal currentIncomeRmb;

    /**
     * 当前累计收益(当前币种）
     */
    private BigDecimal currentAccumIncome;

    /**
     * 当前累计收益(人民币）
     */
    private BigDecimal currentAccumIncomeRmb;
    /**
     * 是否拆单产品 1-是
     */
    private String stageFlag;
    /**
     * 产品成立日期
     */
    private String establishDt;
    /****************持仓2.2**********************/
    /**
     * 收益计算日期
     */
    private String assetUpdateDate;
    /**
     * 单位持仓成本去费
     */
    private BigDecimal unitBalanceCostExFee;
    /**
     * 单位持仓成本去费(人民币)
     */
    private BigDecimal unitBalanceCostExFeeRmb;

    /**
     * 股权转让标识
     */
    private String ownershipTransferIdentity;

    /**
     * 是否海外储蓄罐(1:是;0:否)
     */
    private String sfhwcxg;


    /**
     * 股权产品期限说明
     */
    private String cpqxsm;

    /**
     * 净值披露方式(1-净值,2-份额收益)--持仓特殊产品指标控制需求新增 20221122
     */
    private String navDisclosureType;

    /**
     * 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上
     * 异常标志(0-否 1-是)--持仓特殊产品指标控制需求新增 20221122
     */
    private String abnormalFlag = "0";

    /**
     * 人民币市值-是否控制表人为置空(0-否 1-是)
     */
    private String marketValueCtl = "0";
    /**
     * 当前币种市值-是否控制表人为置空
     */
    private String currencyMarketValueCtl = "0";
    /**
     * NA产品费后市值（当前币种）-是否控制表人为置空
     */
    private String currencyMarketValueExFeeCtl = "0";
    /**
     * NA产品费后市值（人民币）-是否控制表人为置空
     */
    private String marketValueExFeeCtl = "0";
    /**
     * 人民币收益-是否控制表人为置空
     */
    private String currentAssetCtl = "0";
    /**
     * 当前币种收益-是否控制表人为置空
     */
    private String currentAssetCurrencyCtl = "0";
    /**
     * 人民币日收益-是否控制表人为置空
     */
    private String dailyAssetCtl = "0";
    /**
     * 当前币种日收益-是否控制表人为置空
     */
    private String dailyAssetCurrencyCtl = "0";
    /**
     * 累计收益-是否控制表人为置空
     */
    private String accumIncomeCtl = "0";
    /**
     * 累计收益人民币-是否控制表人为置空
     */
    private String accumIncomeRmbCtl = "0";
    /**
     * 累计已实现收益-是否控制表人为置空
     */
    private String accumRealizedIncomeCtl = "0";
    /**
     * 累计已实现收益人民币-是否控制表人为置空
     */
    private String accumRealizedIncomeRmbCtl = "0";
    /**
     * 当前收益（股权新算法）不含费-是否控制表人为置空
     */
    private String balanceIncomeNewCtl = "0";
    /**
     * 当前收益（股权新算法）不含费人民币-是否控制表人为置空
     */
    private String balanceIncomeNewRmbCtl = "0";
    /**
     * 累计收益（股权固收新算法）不含费-是否控制表人为置空
     */
    private String accumIncomeNewCtl = "0";
    /**
     * 累计收益（股权固收新算法）不含费人民币-是否控制表人为置空
     */
    private String accumIncomeNewRmbCtl = "0";
    /**
     * 收益率-是否控制表人为置空
     */
    private String yieldRateCtl = "0";
    /**
     * 份额-是否控制表人为置空
     */
    private String balanceVolCtl = "0";
    /**
     * 待确认份额-是否控制表人为置空
     */
    private String unconfirmedVolCtl = "0";
    /**
     * 净值-是否控制表人为置空
     */
    private String navCtl = "0";

    // 千禧年持仓功能适配需求 20230216
    /**
     * 是否为千禧年产品 0-否、1-是
     */
    private String qianXiFlag = "0";

    /**
     * 待投金额（人民币）
     */
    private BigDecimal unPaidInAmt;

    /**
     * 待投金额（当前币种）
     */
    private BigDecimal currencyUnPaidInAmt;

    /**
     * 认缴总金额
     */
    private BigDecimal subTotalAmt;

    /**
     * 实缴总金额
     */
    private BigDecimal paidTotalAmt;

    /**
     * 实缴认缴百分比
     */
    private String paidSubTotalRatio;

    /**
     * 币种换算的换算汇率,需要结合APP展示的币种是人民币和美元走不同的计算逻辑
     */
    private BigDecimal rmbZJJ;

    /**
     * 换算成对应的币种 和 rmbZJJ是对应的, 例如当前产品是美元,换算成人明币  conversionCurrency 是人民币，rmbZJJ 是对应的汇率
     */
    private String conversionCurrency;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

}