/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.piggytradeapp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 修改储蓄罐交易申请买入响应
 * <AUTHOR>
 * @date 2025-07-18 16:09:17
 * @since JDK 1.8
 */
@Getter
@Setter
public class UpdatePiggyTradeAppBuyResponse implements Serializable {
    private static final long serialVersionUID = 8520604241079381978L;

    // 当前接口只需要返回成功状态，无需额外数据字段

}
