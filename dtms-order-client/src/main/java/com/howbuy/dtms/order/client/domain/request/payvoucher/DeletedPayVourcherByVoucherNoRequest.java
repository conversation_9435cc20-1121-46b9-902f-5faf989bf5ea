package com.howbuy.dtms.order.client.domain.request.payvoucher;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 删除打款凭证请求对象
 * <AUTHOR>
 * @date 2024/03/06 17:35:21
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class DeletedPayVourcherByVoucherNoRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;
    /**
     * 打款凭证号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "打款凭证号", isRequired = true)
    private String voucherNo;

    /**
     * 打款凭证类型
     * 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     */

    private String voucherType;

    /**
     * 打款凭证状态
     * 2-等待复核、3-审核通过、4-审核不通过、6-驳回至客户、7-作废
     */

    private String voucherStatus;

    /**
     * 是否重复
     * 0-否、1-是
     */
    private String repeat;
} 