/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.enums;
/**
 * <AUTHOR>
 * @description: 海外打款凭证文件类型枚举
 * @date 2024/8/2 17:03
 * @since JDK 1.8
 */
public enum PayVoucherOrderFileTypeEnum {

    // 0-打款凭证
    NO("0", "打款凭证"),
    ;

    private String code;
    private String desc;

    PayVoucherOrderFileTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String getDescByCode(String code) {
        if (code != null && !code.isEmpty()) {
            PayVoucherOrderFileTypeEnum[] var1 = values();
            int var2 = var1.length;

            for (int var3 = 0; var3 < var2; ++var3) {
                PayVoucherOrderFileTypeEnum item = var1[var3];
                if (item.getCode().equals(code)) {
                    return item.getDesc();
                }
            }

            return "";
        } else {
            return "";
        }
    }

    public static String toString(String code) {
        return code != null && !code.isEmpty() ? String.format("%s-%s", code, getDescByCode(code)) : "";
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return this.desc;
    }


}