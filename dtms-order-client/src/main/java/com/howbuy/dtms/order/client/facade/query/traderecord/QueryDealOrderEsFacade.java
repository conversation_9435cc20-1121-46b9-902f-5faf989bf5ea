/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.traderecord;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryDealOrderDetailRequest;
import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryDealOrderEsRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.QueryDealOrderEsResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;


/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.traderecord.QueryDealOrderEsFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryDealOrderEsFacade
 * @apiName query()
 * @apiDescription 提供es的查询接口
 * @apiParam (请求参数) {String} dealNo 订单号
 * @apiParamExample 请求参数示例
 * dealNo=xF
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.txCode 交易代码 :HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验'
 * @apiSuccess (响应结果) {String} data.appDate 申请日期
 * @apiSuccess (响应结果) {String} data.orderStatus 订单状态  1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {String} data.payStatus 支付状态 0-无需付款 1-未付款 2-付款中 3-部分成功 4-成功 5-失败 6-退款
 * @apiSuccess (响应结果) {String} data.mBusiCode 中台业务码  1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘 112A-认缴 112B-实缴 1136-基金转换 119F-展期修改 113B-系列合并转出 113C-系列合并转入 119A-交易过户申请 119B-交易过户赎回 119C-交易过户申购 119D-平衡因子更新 119E-平衡因子兑换
 * @apiSuccess (响应结果) {String} data.fundDivMode 分红方式 0-红利再投 1-现金分红 2-N/A不适用
 * @apiSuccess (响应结果) {String} data.taTradeDt TA交易日期
 * @apiSuccess (响应结果) {Number} data.appAmt 申请金额 (转出申请金额)
 * @apiSuccess (响应结果) {Number} data.appVol 申请份额 (转出申请份额)
 * @apiSuccess (响应结果) {Number} data.ackAmt 确认金额(转出确认金额)
 * @apiSuccess (响应结果) {Number} data.transferAppAmt 转入申请金额
 * @apiSuccess (响应结果) {Number} data.transferAckVol 转入确认份额
 * @apiSuccess (响应结果) {Number} data.transferAckAmt 转入确认金额
 * @apiSuccess (响应结果) {String} data.transferFundCode 转入基金代码
 * @apiSuccess (响应结果) {String} data.transferFundName 转入基金名称
 * @apiSuccess (响应结果) {String} data.fundAcctNo 基金交易账号
 * @apiSuccess (响应结果) {String} data.ackDt 确认日期
 * @apiSuccess (响应结果) {String} data.appTime 申请时间
 * @apiSuccess (响应结果) {Number} data.updateDtm
 * @apiSuccess (响应结果) {Number} data.createDtm
 * @apiSuccess (响应结果) {String} data.recStat 记录状态 0-正常；1-已删除
 * @apiSuccess (响应结果) {String} data.currency 币种
 * @apiSuccess (响应结果) {String} data.highFundInvPlanFlag 是否高端定投 0-否 1-是
 * @apiSuccess (响应结果) {String} data.prebookstate 预约状态（1-未确认、2-已确认、4-已撤销）
 * @apiSuccess (响应结果) {String} data.continuanceFlag 顺延标识，0-否、1-是
 * @apiSuccess (响应结果) {String} data.isHkProduct 是否是香港产品,1:是,0:不是
 * @apiSuccess (响应结果) {String} data.redeemType 赎回方式,1-按份额赎回；2-按金额赎回
 * @apiSuccessExample 响应结果示例
 * {"code":"fw5CFyJNl","data":{"appAmt":2398.2169812185725,"updateDtm":1709025241513,"highFundInvPlanFlag":"4Rawocm","orderStatus":"OYb2pTx9h","taTradeDt":"lu","appDate":"21GJ","fundDivMode":"Xa","continuanceFlag":"H44j9","isHkProduct":"vyMmGIX","mBusiCode":"ZJ","transferAckVol":1526.945284145551,"transferAckAmt":5176.635151498866,"transferFundName":"lVLKTfNJwZ","fundCode":"7ww6MU4Cxi","ackAmt":9612.215061312705,"appVol":9646.033544677275,"prebookstate":"Wap","currency":"xLsDLDJCEa","fundAcctNo":"vHXurD4","appTime":"PsORcZ","transferAppAmt":7284.580429836699,"createDtm":3764127282035,"dealNo":"9jHP7T","redeemType":"3G","transferFundCode":"bFymgzw9","fundName":"O","txCode":"R4Iy7lC","ackDt":"Yo874d","payStatus":"kRAOO6","recStat":"I4zl58Y"},"description":"IT44v4N"}
 */
public interface QueryDealOrderEsFacade extends BaseFacade<QueryDealOrderEsRequest, QueryDealOrderEsResponse> {

}