/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.digitalsign;

import com.howbuy.dtms.order.client.domain.response.BankCardVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (查询海外产品电子签约详情)
 * <AUTHOR>
 * @date 2023/5/17 14:44
 * @since JDK 1.8
 */
@Data
public class DigitalSignDetailVO implements Serializable {
    /**
     *签约订单信息
     */
    private SignDealInfoVO signDealInfo;
    /**
     *交易信息
     */
    private TradeInfoVO tradeInfo;
    /**
     *银行卡列表
     */
    private List<BankCardVO> bankCardList;
}