package com.howbuy.dtms.order.client.domain.request.traderecord;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description: 查询交易订单列表请求对象
 * <AUTHOR>
 * @date 2024/03/06 17:35:21
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class QueryDealOrderListForAppRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金类别
     * 1-公募、2-私募、9-其他
     */
    private String fundCategoryList;

    /**
     * 业务类型
     * 1-买入、2-卖出、9-其他
     */
    private List<String> busiTypeList;

    /**
     * 交易状态
     * 1-付款中、2-确认中、3-交易成功、4-交易失败、5-已撤单、9-其他
     */
    private List<String> tradeStatusList;

    /**
     * 持仓状态
     * 1-持有、2-已清仓
     */
    private List<String> holdStatusList;

    /**
     * 订单开始时间
     */
    private String orderStartDt;

    /**
     * 订单结束时间
     */
    private String orderEndDt;

    /**
     * 基金代码
     */
    private String fundCode;
} 