/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.counterparam;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/3/18 13:50
 * @since JDK 1.8
 */
@Setter
@Getter
public class CounterParamBusiAppRecordSubmitRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 8074810034105485486L;

    /**
     * 参数业务类型
     */
    private String paramType;

    /**
     * 操作类型
     */
    private String operateType;

    /**
     * 审核状态（0-无需审核、2-等待复核、3-审核通过、4-审核不通过、5-驳回至经办、7-作废、8-等待回访）
     */
    private String auditStatus;

    /**
     * 业务关键内容json
     */
    private String keyContent;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核日期时间
     */
    private Date checkTime;

    /**
     * 审核备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 创建时间戳
     */
    private Date createTimestamp;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 更新时间戳
     */
    private Date updateTimestamp;

    /**
     * 修改前内容  JSON
     */
    private String oldContent;

    /**
     * 修改后内容 JSON
     */
    private String newContent;
}
