/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.counterparam;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/3/18 13:50
 * @since JDK 1.8
 */
@Setter
@Getter
public class CounterParamBusiAppRecordAuditRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 8074810034105485486L;

    /**
     * 业务申请ID
     */
    private String appId;

    /**
     * 参数业务类型
     */
    private String paramType;

    /**
     * 参数业务类型 对应的操作类型
     */
    private String operateType;

    /**
     * 审核状态（0-无需审核、2-等待复核、3-审核通过、4-审核不通过、5-驳回至经办、7-作废、8-等待回访）
     */
    private String auditStatus;

    /**
     * 审核人
     */
    private String checker;

    /**
     * 审核日期时间
     */
    private Date checkTime;

    /**
     * 审核备注
     */
    private String remark;

}
