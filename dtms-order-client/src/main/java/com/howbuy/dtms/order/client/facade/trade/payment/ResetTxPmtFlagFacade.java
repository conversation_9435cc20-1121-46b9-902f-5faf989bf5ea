package com.howbuy.dtms.order.client.facade.trade.payment;

import com.howbuy.dtms.order.client.domain.request.payment.ResetTxPmtFlagRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.ResetTxPmtFlagResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.payment.ResetTxPmtFlagFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup ResetTxPmtFlagFacade
 * @apiName execute()
 * @apiDescription 重置交易支付标识接口
 * @apiParam (请求体) {String} pmtDealNo 支付订单号
 * @apiParam (请求体) {String} txCode 交易码
 * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求体) {String} appTm 申请时间 HHmmss
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress ipAddress
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {String} macAddress MAC地址
 * @apiParam (请求体) {String} deviceSerialNo 设备序列号
 * @apiParam (请求体) {String} deviceModel 设备型号
 * @apiParam (请求体) {String} deviceName 设备名称
 * @apiParam (请求体) {String} systemVersion 系统版本号
 * @apiParamExample 请求体示例
 * {"externalDealNo":"CxoC","ipAddress":"4YDSOUwd","deviceName":"bAovoG","systemVersion":"NIZZMx","pmtDealNo":"Pm","appTm":"Wob3","macAddress":"C8YN","deviceSerialNo":"n","appDt":"DfHqY","deviceModel":"a","txCode":"QryC","outletCode":"7GaEs","tradeChannel":"MItvD"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"ciNEEVYM8V","description":"i0PIL"}
 */
public interface ResetTxPmtFlagFacade extends BaseFacade<ResetTxPmtFlagRequest, ResetTxPmtFlagResponse> {
}
