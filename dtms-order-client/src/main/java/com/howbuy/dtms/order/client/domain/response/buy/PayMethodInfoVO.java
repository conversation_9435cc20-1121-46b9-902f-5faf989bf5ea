/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 支付方式信息
 * @date 2024/4/10 10:13
 * @since JDK 1.8
 */
@Data
public class PayMethodInfoVO implements Serializable {

    private static final long serialVersionUID = 6746355418637736444L;
    /**
     * 是否签约储蓄罐 0-未签署 1-签署
     */
    private String hasSignCxg;

    /**
     * 是否支持储蓄罐支付 0-不支持 1-支持（是否签约储蓄罐1-签署 且 支持预约才有值）
     */
    private String isSupportCxgPay;

    /**
     * 储蓄罐币种列表 156-人民币、344-港元、392-日元、826-英镑、840-美元、978欧元 （是否签约储蓄罐1-签署才有值）
     */
    private List<String> cxgCurrencyList;

    /**
     * 储蓄罐下单结束时间 HHmmss （是否签约储蓄罐1-签署才有值）
     */
    private String cxgOrderEndTm;

}
