/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.sell;

import com.howbuy.dtms.order.client.domain.request.sell.QuerySellInfoRequest;
import com.howbuy.dtms.order.client.domain.response.sell.SellInfoVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 查询基金赎回信息接口
 * <AUTHOR>
 * @date 2024/12/27 17:00
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.sell.QueryFundSellInfoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundSellInfoFacade
 * @apiName execute()
 * @apiDescription 查询基金赎回信息接口，用于获取客户指定基金的赎回相关信息
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK123456",
 *     "fundCode": "000001"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Object} data.sellFundInfoVO 赎回基金信息
 * @apiSuccess (响应结果) {String} data.sellFundInfoVO.openStartDt 开放开始日期(yyyyMMdd)
 * @apiSuccess (响应结果) {String} data.sellFundInfoVO.openEndDt 开放结束日期(yyyyMMdd)
 * @apiSuccess (响应结果) {String} data.sellFundInfoVO.advanceEndDt 预约结束日期(yyyyMMdd)
 * @apiSuccess (响应结果) {String} data.sellFundInfoVO.advanceEndTm 预约结束时间(HHmmss)
 * @apiSuccess (响应结果) {String} data.sellFundInfoVO.tradeDt 交易日期(yyyyMMdd)
 * @apiSuccess (响应结果) {String} data.sellFundInfoVO.isInTransitOrder 是否在途订单(0-否、1-是)
 * @apiSuccess (响应结果) {BigDecimal} data.sellFundInfoVO.totalVol 总份额
 * @apiSuccess (响应结果) {BigDecimal} data.sellFundInfoVO.availableVol 可用份额
 * @apiSuccess (响应结果) {BigDecimal} data.sellFundInfoVO.totalAsset 总资产
 * @apiSuccess (响应结果) {BigDecimal} data.sellFundInfoVO.availableAsset 可用资产
 * @apiSuccess (响应结果) {BigDecimal} data.sellFundInfoVO.nav 最新基金净值
 * @apiSuccess (响应结果) {String} data.sellFundInfoVO.navDt 最新净值日期(yyyyMMdd)
 * @apiSuccess (响应结果) {Object} data.prebookSellInfoVO 预约赎回信息
 * @apiSuccess (响应结果) {String} data.hasSignCxg 是否已签署储蓄罐(0-否、1-是)
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "sellFundInfoVO": {
 *             "openStartDt": "20241201",
 *             "openEndDt": "20241231",
 *             "advanceEndDt": "20241230",
 *             "advanceEndTm": "150000",
 *             "tradeDt": "20241227",
 *             "isInTransitOrder": "0",
 *             "totalVol": 10000.50,
 *             "availableVol": 8500.30,
 *             "totalAsset": 12000.60,
 *             "availableAsset": 10200.36,
 *             "nav": 1.2000,
 *             "navDt": "20241226"
 *         },
 *         "prebookSellInfoVO": {},
 *         "hasSignCxg": "1"
 *     },
 *     "description": "查询成功"
 * }
 */
public interface QueryFundSellInfoFacade extends BaseFacade<QuerySellInfoRequest, SellInfoVO> {
}
