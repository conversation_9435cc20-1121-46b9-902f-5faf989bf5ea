/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.hkbalance;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/7 14:29
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBalanceRequest extends BaseRequest {

    public QueryBalanceRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_QUERY_BALANCE);
    }

    /**
     * 一账通号
     */
    private String hbOneNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 分销机构
     */
    private List<String> disCodeList;

    /**
     * 币种展示类型
     */
    private String currencyDisType;

    /**
     *  前端展示币种 默认人民币
     */
    private String disPlayCurrency = "156";

    /**
     * 兼容老逻辑,注意,字段不传也是查持仓的
     * 持仓状态,0:不持仓,1:持仓,,2:全部
     * 默认查持仓的
     */
    private String balanceStatus = "1";


    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;
}