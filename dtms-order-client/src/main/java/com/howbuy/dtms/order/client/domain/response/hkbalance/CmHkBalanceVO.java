/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.hkbalance;

import lombok.Data;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/10/27 16:56
 * @since JDK 1.8
 */
@Data
public class CmHkBalanceVO {

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 证件号码
     */
    private String idNo;

    /**
     * 用户名
     */
    private String custName;

    /**
     * 份额
     */
    private String balanceVol;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金类型
     */
    private String jjlx;

    /**
     * 冻结份额
     */
    private String frozenVol;

    /**
     * 币种
     */
    private String currency;

}