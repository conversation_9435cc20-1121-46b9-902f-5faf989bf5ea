/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @since JDK 1.8
 */
@Data
public class UnconfirmeProductVO implements Serializable {
    private static final long serialVersionUID = -5610418332175150805L;

    /**
     * 产品代码
     */
    private String fundCode;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品子类型
     */
    private String productSubType;
    /**
     * 待确认金额(人民币)
     */
    private BigDecimal unconfirmedAmt;
    /**
     * 好买香港代销标识: 0-否; 1-是
     */
    private String hkSaleFlag;

    /**
     * 销售渠道
     */
    private String disCode;

    /**
     * 币种换算的换算汇率,需要结合APP展示的币种是人民币和美元走不同的计算逻辑
     */
    private BigDecimal rmbZJJ;

    /**
     * 换算成对应的币种 和 rmbZJJ是对应的, 例如当前产品是美元,换算成人明币  conversionCurrency 是人民币，rmbZJJ 是对应的汇率
     */
    private String conversionCurrency;

    /**
     * 币种
     */
    private String currency;
}