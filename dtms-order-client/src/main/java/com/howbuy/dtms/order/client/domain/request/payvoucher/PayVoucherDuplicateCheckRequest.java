/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/10 19:34
 * @since JDK 1.8
 */
@Setter
@Getter
public class PayVoucherDuplicateCheckRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -2124747794210228338L;

    /**
     * 香港资金账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港资金账号", isRequired = true)
    private String cpAcctNo;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 银行swiftCode码值
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "银行swiftCode码值", isRequired = true)
    private String swiftCode;

    /**
     * 汇款账户币种
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "汇款账户币种", isRequired = true)
    private String remitCurrency;

    /**
     * 汇款金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "汇款金额", isRequired = true)
    private String remitAmt;

    /**
     * 审核状态
     */
    private List<String> auditStatusList;

}
