/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request;


import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (DtmsBaseRequest)
 * @date 2023/5/18 9:38
 * @since JDK 1.8
 */
@Data
public class BaseRequest implements Serializable {

    /**
     * 交易码
     */
    private String txCode = OrderTxCodes.HW_ORDER_DEFAULT;

    /**
     * 申请日期 yyyyMMdd
     */
    private String appDt;

    /**
     * 申请时间 HHmmss
     */
    private String appTm;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     */
    private String tradeChannel;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * ipAddress
     */
    private String ipAddress;

    /**
     * 外部订单号
     */
    private String externalDealNo;

    /**
     * 并发控制锁的key，无具体含义
     */
    private transient String currentKey;

    /**
     * MAC地址
     */
    private String macAddress;

    /**
     * 设备序列号
     */
    private String deviceSerialNo;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 系统版本号
     */
    private String systemVersion;
}