/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.sell;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/4/10 10:12
 * @since JDK 1.8
 */
@Data
public class SellInfoVO implements Serializable {
    private static final long serialVersionUID = -5480008727776616545L;
    /**
     * 卖出基金信息
     */
    private SellFundInfoVO sellFundInfoVO;

    /**
     * 预约卖出信息
     */
    private PrebookSellInfoVO prebookSellInfoVO;

    /**
     * 是否签约储蓄罐 0-未签署 1-签署
     */
    private String hasSignCxg;


}
