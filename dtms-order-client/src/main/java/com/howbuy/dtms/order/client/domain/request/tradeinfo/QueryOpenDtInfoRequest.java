/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.tradeinfo;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/13 16:09
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryOpenDtInfoRequest extends BaseRequest {
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;


    /**
     * 支付方式  1-电汇、2-支票、3-海外储蓄罐
     */
    private String payMethod;

    /**
     * 中台业务码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中台业务码", isRequired = true)
    private String businessCode;
}
