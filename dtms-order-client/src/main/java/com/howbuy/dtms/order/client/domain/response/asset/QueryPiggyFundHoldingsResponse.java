/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.asset;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/23 11:02
 * @since JDK 1.8
 */
@Setter
@Getter
public class QueryPiggyFundHoldingsResponse implements Serializable {

    private static final long serialVersionUID = -1244966639553419166L;

    /**
     * 总持仓份额
     */
    private BigDecimal totalVol;

    /**
     * 冻结份额
     */
    private BigDecimal freezeShare;

    /**
     * 开放开始日期 yyyyMMdd
     */
    private String openStartDt;

    /**
     * 开放结束日期 yyyyMMdd
     */
    private String openEndDt;

    /**
     * 预约结束日期 yyyyMMdd
     */
    private String advanceEndDt;

    /**
     * 预约结束时间 HHmmss
     */
    private String advanceEndTm;

    /**
     * 交易日期 yyyyMMdd
     */
    private String tradeDt;

    /**
     * 是否在途订单 0-否、1-是
     */
    private String isInTransitOrder;

    /**
     * 可用份额
     */
    private BigDecimal availableVol;

    /**
     * 总资产
     */
    private BigDecimal totalAsset;

    /**
     * 可用资产
     */
    private BigDecimal availableAsset;

    /**
     * 最新基金净值（从DB获取）
     */
    private BigDecimal nav;

    /**
     * 最新净值日期（从DB获取）
     */
    private String navDt;
}
