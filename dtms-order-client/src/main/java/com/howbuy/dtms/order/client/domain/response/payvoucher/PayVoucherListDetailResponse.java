/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/23 14:16
 * @since JDK 1.8
 */
@Getter
@Setter
public class PayVoucherListDetailResponse implements Serializable {

    private static final long serialVersionUID = -6944524979753981068L;
    /**
     * 打款凭证订单号
     */
    private String voucherNo;

    /**
     * 申请日期
     */
    private String appDate;

    /**
     * 申请时间
     */
    private String appTime;

    /**
     * 汇款金额
     */
    private BigDecimal remitAmt;

    /**
     * 打款凭证类型: 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     */
    private String voucherType;

    /**
     * 汇款币种
     */
    private String remitCurrency;

    /**
     * 实际到账金额
     */
    private BigDecimal actualPayAmt;

    /**
     * 实际到账币种
     */
    private String actualPayCurrency;

    /**
     * 审核状态
     */
    private String auditStatus;


    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 重复打款凭证
     */
    private String duplicateVoucher;

    /**
     * 客户删除
     */
    private String cusDelete;

    /**
     * 交易订单号
     */
    private String tradeOrderNo;


}