package com.howbuy.dtms.order.client.domain.request.changeorder;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 支付方式修改请求
 * @date 2025-04-24 16:41:18
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class ChangePayMethodRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true)
    private String dealNo;

    /**
     * 支付方式
     * 1-电汇、2-支票、3-海外储蓄罐
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "支付方式", isRequired = true)
    private String payMethod;

    /**
     * 资金账号
     * 支付方式为1-电汇时必填
     */
    private String cpAcctNo;
} 