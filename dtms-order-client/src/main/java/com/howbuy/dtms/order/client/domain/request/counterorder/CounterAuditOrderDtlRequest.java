/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.counterorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 柜台审核订单明细请求对象
 * <AUTHOR>
 * @date 2024/11/7 16:10
 * @since JDK 1.8
 */
public class CounterAuditOrderDtlRequest implements Serializable {

    private static final long serialVersionUID = -8131152022977352515L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 审核申请流水号
     */
    private String appSerialNo;

    /**
     * 审核申请流水明细号
     */
    private String appSerialDtlNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户中文姓名
     */
    private String custChineseName;

    /**
     * 证件号码掩码
     */
    private String idNoDigest;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 支付方式
     */
    private String paymentType;

    /**
     * 赎回方式
     */
    private String redeemType;

    /**
     * 赎回方向
     */
    private String redeemDirection;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 净申请金额
     */
    private BigDecimal netAppAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 申请折扣
     */
    private BigDecimal appDiscount;

    /**
     * 折扣类型 1-折扣率 2-折扣金额
     */
    private String discountType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 手续费率
     */
    private BigDecimal feeRate;

    /**
     * 费用计算方式
     */
    private String feeCalMode;

    /**
     * 展期选项
     */
    private String extensionOption;

    /**
     * 展期控制数
     */
    private String extensionCount;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     */
    private String tradeChannel;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundAbbr;

    /**
     * 中台业务码
     */
    private String midleBusiCode;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;


    /**
     * 基金交易账号类型
     */
    private String fundTxAccType;

    /**
     * 全委账号编码 生成基金交易账号
     */
    private String fullTxAcctCode;

    /**
     * 预约订单号
     */
    private String prebookDealNo;

    /**
     * 预约折扣
     */
    private BigDecimal prebookDiscount;

    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;

    /**
     * 预约申请金额
     */
    private BigDecimal preAppAmt;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * CRM线上资料ID
     */
    private String crmFileId;

    /**
     * 撤单类型
     */
    private String revokeType;

    /**
     * 撤单原因
     */
    private String revokeReason;

    /**
     * 转入香港客户号
     */
    private String inHkCustNo;

    /**
     * 转入基金交易账号
     */
    private String inFundTxAcctNo;

    /**
     * 转入基金代码
     */
    private String inFundCode;

    /**
     * 转入基金简称
     */
    private String inFundAbbr;

    /**
     * 转入客户中文姓名
     */
    private String inCustChineseName;

    /**
     * 转入证件号码掩码
     */
    private String inIdNoDigest;

    /**
     * 转入证件类型
     */
    private String inIdType;

    /**
     * 过户份额
     */
    private BigDecimal transferVol;

    /**
     * 过户份额对应的实缴金额
     */
    private BigDecimal transferActualAmt;

    /**
     * 过户份额对应的认缴金额
     */
    private BigDecimal transferSubsAmt;

    /**
     * 转让价格
     */
    private BigDecimal transferAmt;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 订单号明细号
     */
    private String dealNoDtl;

    /**
     * 转入订单号
     */
    private String inDealNo;

    /**
     * 份额明细号
     */
    private String volDtlNo;


    /**
     *预计上报日期
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     */
    private String preSubmitTaTm;

    /**
     * 开放日
     */
    private String openDt;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建日期时间
     */
    private Date createTimestamp;

    /**
     * 更新日期时间
     */
    private Date updateTimestamp;

    /**
     * 认缴金额
     */
    private BigDecimal subAmt;

    /**
     * 累计实缴金额
     */
    private BigDecimal cumPaidAmt;

    /**
     * 强减规则：0-按指定明细、2-按先进先出
     */
    private String forceSubtractRule;

    /**
     * 强增规则：0-按指定明细、1-按指定日期
     */
    private String forceAddRule;

    /**
     * 系列号
     */
    private String serialNumber;

    /**
     * 份额注册日期
     */
    private String shareRegDt;

    /**
     * 净值日期
     */
    private String navDt;

    /**
     * 净值
     */
    private BigDecimal nav;

    /**
     * 备注
     */
    private String remark;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppSerialNo() {
        return appSerialNo;
    }

    public void setAppSerialNo(String appSerialNo) {
        this.appSerialNo = appSerialNo;
    }

    public String getAppSerialDtlNo() {
        return appSerialDtlNo;
    }

    public void setAppSerialDtlNo(String appSerialDtlNo) {
        this.appSerialDtlNo = appSerialDtlNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCustChineseName() {
        return custChineseName;
    }

    public void setCustChineseName(String custChineseName) {
        this.custChineseName = custChineseName;
    }

    public String getIdNoDigest() {
        return idNoDigest;
    }

    public void setIdNoDigest(String idNoDigest) {
        this.idNoDigest = idNoDigest;
    }

    public String getIdType() {
        return idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getBankAcctMask() {
        return bankAcctMask;
    }

    public void setBankAcctMask(String bankAcctMask) {
        this.bankAcctMask = bankAcctMask;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getRedeemType() {
        return redeemType;
    }

    public void setRedeemType(String redeemType) {
        this.redeemType = redeemType;
    }

    public String getRedeemDirection() {
        return redeemDirection;
    }

    public void setRedeemDirection(String redeemDirection) {
        this.redeemDirection = redeemDirection;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getNetAppAmt() {
        return netAppAmt;
    }

    public String getFundTxAccType() {
        return fundTxAccType;
    }

    public void setFundTxAccType(String fundTxAccType) {
        this.fundTxAccType = fundTxAccType;
    }

    public String getFullTxAcctCode() {
        return fullTxAcctCode;
    }

    public void setFullTxAcctCode(String fullTxAcctCode) {
        this.fullTxAcctCode = fullTxAcctCode;
    }

    public void setNetAppAmt(BigDecimal netAppAmt) {
        this.netAppAmt = netAppAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public BigDecimal getFee() {
        return fee;
    }

    public void setFee(BigDecimal fee) {
        this.fee = fee;
    }

    public BigDecimal getAppDiscount() {
        return appDiscount;
    }

    public void setAppDiscount(BigDecimal appDiscount) {
        this.appDiscount = appDiscount;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public String getFeeCalMode() {
        return feeCalMode;
    }

    public void setFeeCalMode(String feeCalMode) {
        this.feeCalMode = feeCalMode;
    }

    public String getExtensionOption() {
        return extensionOption;
    }

    public void setExtensionOption(String extensionOption) {
        this.extensionOption = extensionOption;
    }

    public String getExtensionCount() {
        return extensionCount;
    }

    public void setExtensionCount(String extensionCount) {
        this.extensionCount = extensionCount;
    }

    public String getTradeChannel() {
        return tradeChannel;
    }

    public void setTradeChannel(String tradeChannel) {
        this.tradeChannel = tradeChannel;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundAbbr() {
        return fundAbbr;
    }

    public void setFundAbbr(String fundAbbr) {
        this.fundAbbr = fundAbbr;
    }

    public String getMidleBusiCode() {
        return midleBusiCode;
    }

    public void setMidleBusiCode(String midleBusiCode) {
        this.midleBusiCode = midleBusiCode;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }

    public String getPrebookDealNo() {
        return prebookDealNo;
    }

    public void setPrebookDealNo(String prebookDealNo) {
        this.prebookDealNo = prebookDealNo;
    }

    public BigDecimal getPrebookDiscount() {
        return prebookDiscount;
    }

    public void setPrebookDiscount(BigDecimal prebookDiscount) {
        this.prebookDiscount = prebookDiscount;
    }

    public BigDecimal getEstimateFee() {
        return estimateFee;
    }

    public void setEstimateFee(BigDecimal estimateFee) {
        this.estimateFee = estimateFee;
    }

    public BigDecimal getPreAppAmt() {
        return preAppAmt;
    }

    public void setPreAppAmt(BigDecimal preAppAmt) {
        this.preAppAmt = preAppAmt;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getCrmFileId() {
        return crmFileId;
    }

    public void setCrmFileId(String crmFileId) {
        this.crmFileId = crmFileId;
    }

    public String getRevokeType() {
        return revokeType;
    }

    public void setRevokeType(String revokeType) {
        this.revokeType = revokeType;
    }

    public String getRevokeReason() {
        return revokeReason;
    }

    public void setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason;
    }

    public String getInHkCustNo() {
        return inHkCustNo;
    }

    public void setInHkCustNo(String inHkCustNo) {
        this.inHkCustNo = inHkCustNo;
    }

    public String getInFundTxAcctNo() {
        return inFundTxAcctNo;
    }

    public void setInFundTxAcctNo(String inFundTxAcctNo) {
        this.inFundTxAcctNo = inFundTxAcctNo;
    }

    public String getInFundCode() {
        return inFundCode;
    }

    public void setInFundCode(String inFundCode) {
        this.inFundCode = inFundCode;
    }

    public String getInFundAbbr() {
        return inFundAbbr;
    }

    public void setInFundAbbr(String inFundAbbr) {
        this.inFundAbbr = inFundAbbr;
    }

    public String getInCustChineseName() {
        return inCustChineseName;
    }

    public void setInCustChineseName(String inCustChineseName) {
        this.inCustChineseName = inCustChineseName;
    }

    public String getInIdNoDigest() {
        return inIdNoDigest;
    }

    public void setInIdNoDigest(String inIdNoDigest) {
        this.inIdNoDigest = inIdNoDigest;
    }

    public String getInIdType() {
        return inIdType;
    }

    public void setInIdType(String inIdType) {
        this.inIdType = inIdType;
    }

    public BigDecimal getTransferVol() {
        return transferVol;
    }

    public void setTransferVol(BigDecimal transferVol) {
        this.transferVol = transferVol;
    }

    public BigDecimal getTransferActualAmt() {
        return transferActualAmt;
    }

    public void setTransferActualAmt(BigDecimal transferActualAmt) {
        this.transferActualAmt = transferActualAmt;
    }

    public BigDecimal getTransferSubsAmt() {
        return transferSubsAmt;
    }

    public void setTransferSubsAmt(BigDecimal transferSubsAmt) {
        this.transferSubsAmt = transferSubsAmt;
    }

    public BigDecimal getTransferAmt() {
        return transferAmt;
    }

    public void setTransferAmt(BigDecimal transferAmt) {
        this.transferAmt = transferAmt;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getDealNoDtl() {
        return dealNoDtl;
    }

    public void setDealNoDtl(String dealNoDtl) {
        this.dealNoDtl = dealNoDtl;
    }

    public String getInDealNo() {
        return inDealNo;
    }

    public void setInDealNo(String inDealNo) {
        this.inDealNo = inDealNo;
    }

    public String getVolDtlNo() {
        return volDtlNo;
    }

    public void setVolDtlNo(String volDtlNo) {
        this.volDtlNo = volDtlNo;
    }

    public String getPreSubmitTaDt() {
        return preSubmitTaDt;
    }

    public void setPreSubmitTaDt(String preSubmitTaDt) {
        this.preSubmitTaDt = preSubmitTaDt;
    }

    public String getPreSubmitTaTm() {
        return preSubmitTaTm;
    }

    public void setPreSubmitTaTm(String preSubmitTaTm) {
        this.preSubmitTaTm = preSubmitTaTm;
    }

    public String getOpenDt() {
        return openDt;
    }

    public void setOpenDt(String openDt) {
        this.openDt = openDt;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    public BigDecimal getSubAmt() {
        return subAmt;
    }

    public void setSubAmt(BigDecimal subAmt) {
        this.subAmt = subAmt;
    }

    public BigDecimal getCumPaidAmt() {
        return cumPaidAmt;
    }

    public void setCumPaidAmt(BigDecimal cumPaidAmt) {
        this.cumPaidAmt = cumPaidAmt;
    }

    public String getForceSubtractRule() {
        return forceSubtractRule;
    }

    public void setForceSubtractRule(String forceSubtractRule) {
        this.forceSubtractRule = forceSubtractRule;
    }

    public String getForceAddRule() {
        return forceAddRule;
    }

    public void setForceAddRule(String forceAddRule) {
        this.forceAddRule = forceAddRule;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getShareRegDt() {
        return shareRegDt;
    }

    public void setShareRegDt(String shareRegDt) {
        this.shareRegDt = shareRegDt;
    }

    public String getNavDt() {
        return navDt;
    }

    public void setNavDt(String navDt) {
        this.navDt = navDt;
    }

    public BigDecimal getNav() {
        return nav;
    }

    public void setNav(BigDecimal nav) {
        this.nav = nav;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDiscountType() {
        return discountType;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public BigDecimal getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(BigDecimal discountAmt) {
        this.discountAmt = discountAmt;
    }
}
