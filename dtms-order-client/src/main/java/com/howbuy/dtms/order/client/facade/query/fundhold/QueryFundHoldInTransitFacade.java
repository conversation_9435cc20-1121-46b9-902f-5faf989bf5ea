/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.fundhold;

import com.howbuy.dtms.order.client.domain.request.fundhold.QueryFundHoldInTransitRequest;
import com.howbuy.dtms.order.client.domain.response.fundhold.QueryFundHoldInTransitResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/30 14:32
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryFundHoldInTransitFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundHoldInTransitFacade
 * @apiName execute()
 * @apiDescription 账户中心定制化接口, 查询是否有持仓或者在途订单
 * @apiParam (请求参数) {Array} fundCodeList 基金编码
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} cpAcctNo 资金账号
 * @apiParamExample 请求参数示例
 * fundCodeList=6Ruh&hkCustNo=3I&cpAcctNo=P4pA4T
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.whetherFundHold 是否持仓
 * @apiSuccess (响应结果) {String} data.whetherInTransitOrder 是否在途
 * @apiSuccessExample 响应结果示例
 * {"code":"KSupT","data":{"whetherFundHold":"YgoNk0","whetherInTransitOrder":"U9M"},"description":"pY"}
 */
public interface QueryFundHoldInTransitFacade extends BaseFacade<QueryFundHoldInTransitRequest, QueryFundHoldInTransitResponse> {

}
