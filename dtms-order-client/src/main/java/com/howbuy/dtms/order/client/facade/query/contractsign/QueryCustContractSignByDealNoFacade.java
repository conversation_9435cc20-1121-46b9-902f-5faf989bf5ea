/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.contractsign;

import com.howbuy.dtms.order.client.domain.request.contractsign.QueryContractSignRequest;
import com.howbuy.dtms.order.client.domain.response.contractsign.CustContractSignVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 根据订单号查询客户合同签署情况接口
 * <AUTHOR>
 * @date 2025/12/27 15:30
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.contractsign.QueryCustContractSignByDealNoFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryCustContractSignByDealNoFacade
 * @apiName execute()
 * @apiDescription 根据订单号查询客户合同签署情况接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} dealNo 订单号
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK123456",
 *     "fundCode": "000001",
 *     "dealNo": "ORD202412270001"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.hkCustNo 客户号
 * @apiSuccess (响应结果) {String} data.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.signFlag 协议签署状态
 * @apiSuccess (响应结果) {String} data.signTime 协议签署日期时间(yyyyMMdd)
 * @apiSuccess (响应结果) {String} data.signDate 协议明细签署时间(yyyy-MM-dd HH:mm:ss)
 * @apiSuccess (响应结果) {String} data.dealNo 订单号
 * @apiSuccess (响应结果) {Array} data.custContractDtlSignList 签署详情列表
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.fileType 文件代码
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.fileName 文件名称
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.fileTypeDesc 文件类型描述
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.filePath 文件路径
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.signFlag 协议明细签署状态(0-未签署、1-已签署)
 * @apiSuccess (响应结果) {String} data.custContractDtlSignList.signDate 协议明细签署时间
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "hkCustNo": "HK123456",
 *         "fundCode": "000001",
 *         "signFlag": "1",
 *         "signTime": "20241227",
 *         "signDate": "2024-12-27 15:30:00",
 *         "dealNo": "ORD202412270001",
 *         "custContractDtlSignList": [
 *             {
 *                 "fileType": "001",
 *                 "fileName": "基金认购协议.pdf",
 *                 "fileTypeDesc": "基金认购协议",
 *                 "filePath": "/contract/files/ORD202412270001_001.pdf",
 *                 "signFlag": "1",
 *                 "signDate": "2024-12-27 15:30:00"
 *             },
 *             {
 *                 "fileType": "002",
 *                 "fileName": "风险提示书.pdf",
 *                 "fileTypeDesc": "风险提示书",
 *                 "filePath": "/contract/files/ORD202412270001_002.pdf",
 *                 "signFlag": "1",
 *                 "signDate": "2024-12-27 15:32:00"
 *             }
 *         ]
 *     },
 *     "description": "查询成功"
 * }
 */
public interface QueryCustContractSignByDealNoFacade extends BaseFacade<QueryContractSignRequest, CustContractSignVO> {
}
