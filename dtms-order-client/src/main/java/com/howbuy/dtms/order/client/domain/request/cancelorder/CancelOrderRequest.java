/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.cancelorder;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description: 撤单请求
 * @date 2024/5/6 18:58
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CancelOrderRequest extends BaseRequest {

    private static final long serialVersionUID = 7205740837717458977L;

    public CancelOrderRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_CANCEL_ORDER);
    }

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "订单号", isRequired = true)
    private String dealNo;

    /**
     * 交易密码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易密码", isRequired = true)
    private String txPassword;

    /**
     * 撤单类型 1-自行撤销；2-强制取消
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "撤单类型", isRequired = true)
    private String cancelType;
}
