/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundtransfer;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/1/6 17:12
 * @since JDK 1.8
 */
public class CounterFundTransferVO implements Serializable {

    private static final long serialVersionUID = -3000117964461442991L;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 订单明细号号
     */
    private String dealDtlNo;

    public String getDealNo() {
        return dealNo;
    }

    public void setDealNo(String dealNo) {
        this.dealNo = dealNo;
    }

    public String getDealDtlNo() {
        return dealDtlNo;
    }

    public void setDealDtlNo(String dealDtlNo) {
        this.dealDtlNo = dealDtlNo;
    }
}
