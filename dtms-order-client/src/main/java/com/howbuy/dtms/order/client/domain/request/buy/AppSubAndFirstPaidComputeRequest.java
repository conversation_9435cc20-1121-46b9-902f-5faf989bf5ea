/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.buy;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * @description: 手续费计算
 * <AUTHOR>
 * @date 2024/4/10 11:15
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AppSubAndFirstPaidComputeRequest extends BaseRequest {

    private static final long serialVersionUID = -2681648741679550827L;

    public AppSubAndFirstPaidComputeRequest() {
        this.setTxCode(OrderTxCodes.APP_SUB_AND_FIRST_PAID_COMPUTE);
    }

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 买入金额 (分次CAll 表示认缴)
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "买入金额", isRequired = true)
    private BigDecimal subAmt;

    /**
     * 实缴金额
     */
    private BigDecimal paidAmt;

}
