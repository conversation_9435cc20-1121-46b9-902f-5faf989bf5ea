/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.order;

import org.apache.commons.compress.utils.Lists;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/13 15:06
 * @since JDK 1.8
 */
public class QueryDealOrderAndDtlResponse implements Serializable {

    private static final long serialVersionUID = 9184557569424131470L;


    /**
     * 海外订单VO
     */
    private HwDealOrderVO hwDealOrderVO;

    /**
     * 海外订单明细VO
     */
    private List<HwDealOrderDtlVO> hwDealOrderDtlVO = Lists.newArrayList();

    public HwDealOrderVO getHwDealOrderVO() {
        return hwDealOrderVO;
    }

    public void setHwDealOrderVO(HwDealOrderVO hwDealOrderVO) {
        this.hwDealOrderVO = hwDealOrderVO;
    }

    public List<HwDealOrderDtlVO> getHwDealOrderDtlVO() {
        return hwDealOrderDtlVO;
    }

    public void setHwDealOrderDtlVO(List<HwDealOrderDtlVO> hwDealOrderDtlVO) {
        this.hwDealOrderDtlVO = hwDealOrderDtlVO;
    }
}
