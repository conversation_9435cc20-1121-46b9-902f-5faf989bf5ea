/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.enums;

/**
 * <AUTHOR>
 * @description: 打款凭证类型枚举
 * @date 2023/6/12
 * @since JDK 1.8
 */
public enum PayVoucherTypeEnum {

    // 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
    OPEN_ACCOUNT("0", "开户入金确认凭证"),
    ORDER("1", "交易下单凭证"),
    CASH_ACCOUNT("2", "存入现金账户凭证")
    ;

    private String code;
    private String desc;

    private PayVoucherTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


    /**
     * @return java.lang.String
     * @description:(根据枚举类型返回code值)
     * @author: your name
     * @date: 2023/3/9 14:54
     * @since JDK 1.8
     */
    public static String getEnumDesc(String code) {
        for (PayVoucherTypeEnum statusEnum : PayVoucherTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getDesc();
            }
        }
        return null;
    }

    /**
     * @description:(请在此添加描述)
     * @param code
     * @return java.lang.String
     * @author: jin.wang03
     * @date: 2024/7/26 16:25
     * @since JDK 1.8
     */
    public static String getCodeAndeDesc(String code) {
        for (PayVoucherTypeEnum statusEnum : PayVoucherTypeEnum.values()) {
            if (statusEnum.getCode().equals(code)) {
                return statusEnum.getCode() + "-" + statusEnum.getDesc();
            }
        }
        return null;
    }
}
