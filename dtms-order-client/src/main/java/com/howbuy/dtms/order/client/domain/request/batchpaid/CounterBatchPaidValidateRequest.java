package com.howbuy.dtms.order.client.domain.request.batchpaid;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 柜台批量实缴校验请求
 * <AUTHOR>
 * @date 2025-03-24 19:07:29
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class CounterBatchPaidValidateRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 客户实缴列表
     */
    private List<CustPaidInfo> custPaidList;

    /**
     * 交易渠道
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "交易渠道", isRequired = true)
    private String tradeChannel;

    /**
     * 申请日期
     * 格式：YYYYMMDD
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "申请日期", isRequired = true)
    private String appDt;

    /**
     * 申请时间
     * 格式：HHmmss
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "申请时间", isRequired = true)
    private String appTm;

    /**
     * 客户实缴信息
     */
    @Setter
    @Getter
    @EqualsAndHashCode
    public static class CustPaidInfo implements Serializable {

        private static final long serialVersionUID = -7795461075344793867L;
        /**
         * 香港客户号
         */
        @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
        private String hkCustNo;

        /**
         * 基金交易账号
         */
        @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
        private String fundTxAcctNo;

        /**
         * 净申请金额
         */
        @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "净申请金额", isRequired = true)
        private BigDecimal netAppAmt;

        /**
         * 预估费用
         * String：Money时手续费为0校验不通过
         */
        @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "预估费用", isRequired = true)
        private BigDecimal estimateFee;

        /**
         * 申请折扣率
         * String：Money时精度校验不通过
         */
        @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "申请折扣率", isRequired = true)
        private BigDecimal appDisCount;

        /**
         * 支付方式
         */
        @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "支付方式", isRequired = true)
        private String paymentType;

        /**
         * 资金账号
         */
        private String cpAcctNo;
    }
} 