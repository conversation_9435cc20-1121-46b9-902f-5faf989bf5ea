/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.buy;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: 买入手续费计算信息
 * <AUTHOR>
 * @date 2024/4/10 11:17
 * @since JDK 1.8
 */
public class AppSubAndFirstPaidComputeInfoVO implements Serializable {

    private static final long serialVersionUID = 4506593553725273214L;
    /**
     * 实际支付金额 
     */
    private BigDecimal actualPayAmt;

    /**
     * 手续费费率
     */
    private BigDecimal feeRate;
    /**
     * 手续费类型  1 认缴  2 实缴
     */
    private String feeRateType;

    /**
     * 预估手续费 
     */
    private BigDecimal estimateFee;

    /**
     * 原始手续费 
     */
    private BigDecimal originalFee;

    /**
     * 是否大于预约金额 0-否 1-是
     */
    private String isLargerPrebookAmt;

    /**
     * 折扣是否生效 0-否 1-是
     */
    private String validDiscountRate;

    /**
     * 实际折扣率
     */
    private BigDecimal actualDiscountRate;

    /**
     * 折扣类型
     */
    private String discountType;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;

    /**
     * 预约折扣率
     */
    private BigDecimal prebookDiscountRate;


    public BigDecimal getActualPayAmt() {
        return actualPayAmt;
    }

    public void setActualPayAmt(BigDecimal actualPayAmt) {
        this.actualPayAmt = actualPayAmt;
    }

    public BigDecimal getFeeRate() {
        return feeRate;
    }

    public void setFeeRate(BigDecimal feeRate) {
        this.feeRate = feeRate;
    }

    public String getFeeRateType() {
        return feeRateType;
    }

    public void setFeeRateType(String feeRateType) {
        this.feeRateType = feeRateType;
    }

    public BigDecimal getEstimateFee() {
        return estimateFee;
    }

    public void setEstimateFee(BigDecimal estimateFee) {
        this.estimateFee = estimateFee;
    }

    public BigDecimal getOriginalFee() {
        return originalFee;
    }

    public void setOriginalFee(BigDecimal originalFee) {
        this.originalFee = originalFee;
    }

    public String getIsLargerPrebookAmt() {
        return isLargerPrebookAmt;
    }

    public void setIsLargerPrebookAmt(String isLargerPrebookAmt) {
        this.isLargerPrebookAmt = isLargerPrebookAmt;
    }

    public String getValidDiscountRate() {
        return validDiscountRate;
    }

    public void setValidDiscountRate(String validDiscountRate) {
        this.validDiscountRate = validDiscountRate;
    }

    public BigDecimal getActualDiscountRate() {
        return actualDiscountRate;
    }

    public void setActualDiscountRate(BigDecimal actualDiscountRate) {
        this.actualDiscountRate = actualDiscountRate;
    }

    public String getDiscountType() {
        return discountType;
    }

    public void setDiscountType(String discountType) {
        this.discountType = discountType;
    }

    public BigDecimal getDiscountAmt() {
        return discountAmt;
    }

    public void setDiscountAmt(BigDecimal discountAmt) {
        this.discountAmt = discountAmt;
    }

    public BigDecimal getPrebookDiscountRate() {
        return prebookDiscountRate;
    }

    public void setPrebookDiscountRate(BigDecimal prebookDiscountRate) {
        this.prebookDiscountRate = prebookDiscountRate;
    }
}
