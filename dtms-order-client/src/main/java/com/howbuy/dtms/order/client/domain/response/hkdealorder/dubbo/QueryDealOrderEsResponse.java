/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/22 10:32
 * @since JDK 1.8
 */
@Data
public class QueryDealOrderEsResponse implements Serializable {

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 申请时间
     */
    private Date appDtm;

    /**
     * 基金代码(转出基金代码)
     */
    private String fundCode;

    /**
     * 基金名称(转出基金名称)
     */
    private String fundName;

    /**
     * 交易代码 :HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验'
     */
    private String txCode;

    /**
     * 申请日期
     */
    private String appDate;

    /**
     * 订单状态  1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private String orderStatus;

    /**
     * 支付状态 0-无需付款 1-未付款 2-付款中 3-部分成功 4-成功 5-失败 6-退款
     */
    private String payStatus;

    /**
     * 中台业务码  1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘 112A-认缴 112B-实缴 1136-基金转换 119F-展期修改 113B-系列合并转出 113C-系列合并转入 119A-交易过户申请 119B-交易过户赎回 119C-交易过户申购 119D-平衡因子更新 119E-平衡因子兑换
     */
    private String mBusiCode;

    /**
     * 分红方式 0-红利再投 1-现金分红 2-N/A不适用
     */
    private String fundDivMode;

    /**
     * TA交易日期
     */
    private String taTradeDt;

    /**
     * 申请金额 (转出申请金额)
     */
    private BigDecimal appAmt;

    /**
     * 申请份额 (转出申请份额)
     */
    private BigDecimal appVol;

    /**
     * 确认金额(转出确认金额)
     */
    private BigDecimal ackAmt;

    /**
     * 转入申请金额
     */
    private BigDecimal transferAppAmt;

    /**
     * 转入确认份额
     */
    private BigDecimal transferAckVol;

    /**
     * 转入确认金额
     */
    private BigDecimal transferAckAmt;

    /**
     * 转入基金代码
     */
    private String transferFundCode;

    /**
     * 转入基金名称
     */
    private String transferFundName;

    /**
     * 基金交易账号
     */
    private String fundAcctNo;

    /**
     * 分销机构号
     */
    private String disCode;

    /**
     * 公募客户号
     */
    private String custNo;


    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * 申请时间
     */
    private String appTime;


    private Date updateDtm;

    private Date createDtm;


    /**
     * 记录状态 0-正常；1-已删除
     */
    private String recStat;
    /**
     * 币种
     */
    private String currency;
    /**
     * 是否高端定投 0-否 1-是
     */
    private String highFundInvPlanFlag;

    /**
     * 预约状态（1-未确认、2-已确认、4-已撤销）
     */
    private String prebookstate;
    /**
     * 顺延标识，0-否、1-是
     */
    private String continuanceFlag;

    /**
     * 是否是香港产品,1:是,0:不是
     */
    private String isHkProduct;

    /**
     * 赎回方式,1-按份额赎回；2-按金额赎回
     */
    private String redeemType;

}