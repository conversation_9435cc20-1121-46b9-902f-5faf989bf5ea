/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo;

import com.howbuy.dtms.order.client.domain.response.BankCardVO;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.CmHkDealMultiDetialInfoVO;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/19 10:31
 * @since JDK 1.8
 */
@Data
public class DealOrderDetailInfoResponse implements Serializable {


    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundShortName;

    /**
     * 赎回方式 1-按份额、2-按金额
     */
    private String redeemMethod;

    /**
     * 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
     */
    private String redeemDirectionList;

    /**
     * 支持预约交易标识 0-不支持；1-仅支持购买预约；2-仅支持赎回预约；3-支持购买赎回预约
     */
    private String supportPrebookFlag;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 申请份额str
     */
    private String appVolStr;

    /**
     * 币种代码
     */
    private String currency;

    /**
     * 打款截止日期 yyyyMMdd
     */
    private String payEndDt;

    /**
     * 打款截止时间 HHmmss
     */
    private String payEndTm;

    /**
     * 香港资金账号
     */
    private String hkCpAcctNo;

    /**
     * SWIFT代码
     */
    private String swiftCode;

    /**
     * 转出确认份额
     */
    private BigDecimal transferAckVol;

    /**
     * 转出确认份额-str 字段
     */
    private String transferAckVolStr;

    /**
     * 转出确认金额
     */
    private BigDecimal transferAckAmt;

    /**
     * 转出确认净值
     */
    private BigDecimal transferNav;

    /**
     * 转出基金名称
     */
    private String transferFundName;

    /**
     * 转出基金代码
     */
    private String transferFundCode;

    /**
     * 转出币种
     */
    private String transferCurrency;

    /**
     * 银行代码
     */
    private String bankCode;

    /**
     * 银行卡号掩码
     */
    private String bankAcctMask;

    /**
     * 银行英文名称
     */
    private String bankEnName;

    /**
     * 银行中文名称
     */
    private String bankChineseName;

    /**
     * 银行币种列表
     */
    private List<String> bankCurrencyList;

    /** 关联银行卡列表 */
    private List<BankCardVO> mutiCardList;

    /**
     * 关联订单号列表(储蓄罐产品)
     */
    private List<String> relationOrderList;

    /**
     * 是否分次Call
     */
    private String gradationCall;

    /**
     * 认缴金额
     */
    private BigDecimal subAmt;


    /*******************************************待定字段,先全部给出********************************************/

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户中文姓名
     */
    private String custChineseName;

    /**
     * 证件类型
     */
    private String idType;

    /**
     * 证件号码密文
     */
    private String idNoCipher;

    /**
     * 证件号码摘要
     */
    private String idNoDigest;

    /**
     * 证件号码掩码
     */
    private String idNoMask;

    /**
     * 投资者类型 0-机构；1-个人
     */
    private String invstType;

    /**
     * 投资者资质 PRO-投资者资质专业;NORMAL-投资者资质普通
     */
    private String qualificationType;

    /**
     * 客户风险等级
     */
    private String custRiskLevel;

    /**
     * 交易代码:HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验
     */
    private String txCode;

    /**
     * 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
     */
    private String businessType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private String orderStatus;

    /**
     * 是否支持撤单, 1 : 是 0 ： 否
     */
    private String supportRepeal;

    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 外部单号
     */
    private String externalDealNo;

    /**
     * 首次购买标识，1-首次购买；2-追加购买
     */
    private String firstBuyFlag;

    /**
     * 是否同意换汇 0-否 1-是
     */
    private String isAgreeCurrencyExchange;

    /**
     * 成单方式 1：纸质成单；2：电子成单；
     */
    private String orderFormType;

    /**
     * 备注
     */
    private String memo;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     */
    private String tradeChannel;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * 订单明细号
     */
    private Long dealDtlNo;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 银行账号密文
     */
    private String bankAcctCipher;

    /**
     * 银行账号摘要
     */
    private String bankAcctDigest;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 主基金代码
     */
    private String mainFundCode;

    /**
     * 基金类别 1-公募、2-私募、9-其他
     */
    private String fundCategory;

    /**
     * 基金风险等级
     */
    private String fundRiskLevel;

    /**
     * 支付方式 1-电汇、2-支票、3-海外储蓄罐
     */
    private String paymentTypeList;

    /**
     * 赎回方式 1-按份额、2-按金额
     */
    private String redeemType;

    /**
     * 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出
     */
    private String middleBusiCode;

    /**
     * 净申请金额
     */
    private BigDecimal netAppAmt;


    /**
     * 预估手续费
     */
    private BigDecimal estimateFee;

    /**
     * 预约折扣
     */
    private BigDecimal prebookDiscount;

    /**
     * 折扣率
     */
    private BigDecimal discountRate;

    /**
     * 手续费率
     */
    private BigDecimal feeRate;

    /**
     * 费用计算方式 0-外扣法、1-内扣法
     */
    private String feeCalMode;

    /**
     * 手续费
     */
    private BigDecimal fee;

    /**
     * 确认金额 (转入确认金额)
     */
    private BigDecimal ackAmt;


    /**
     * 确认份额 (转入确认份额)
     */
    private BigDecimal ackVol;

    /**
     *  确认份额 (转入确认份额)
     */
    private String ackVolStr;

    /**
     * 确认净值 (转入确认净值)
     */
    private BigDecimal ackNav;


    /**
     * 申请状态 0-申请成功；1-申请失败；2-自行撤销；3-强制取消
     */
    private String appStatus;

    /**
     * 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
     */
    private String ackStatus;

    /**
     * 支付状态 0-无需支付；1-未打款；2-已打款；3-到账确认；4-退款；
     */
    private String payStatus;

    /**
     * 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
     */
    private String payVoucherStatus;

    /**
     * 实际打款金额
     */
    private BigDecimal actualPayAmt;

    /**
     * 实际打款日期
     */
    private String actualPayDt;

    /**
     * 实际打款时间
     */
    private String actualPayTm;

    /**
     * 分红方式 0-红利再投 1-现金分红 2-N/A不适用
     */
    private String fundDivMode;

    /**
     * 开放日期
     */
    private String openDt;

    /**
     * TA交易日期
     */
    private String taTradeDt;

    /**
     * 上报TA日期
     */
    private String submitTaDt;

    /**
     * 确认日期
     */
    private String ackDt;

    /**
     * TA确认流水号
     */
    private String taAckNo;

    /**
     * TA代码
     */
    private String taCode;
    /**
     * 创建时间戳
     */
    private Date createTimestamp;

    /**
     * 更新时间戳
     */
    private Date updateTimestamp;

    /**
     * 多笔订单明细数据
     */
    private List<CmHkDealMultiDetialInfoVO> dealOrderMultiDetialInfoResponses;



}