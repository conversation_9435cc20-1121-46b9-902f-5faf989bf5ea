package com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/6/8 16:53
 */
public class QueryBalanceFactorListResponse implements Serializable {
    /**
     * 平衡因子列表
     */
    private List<BalanceFactorDomainVO> balanceFactorDomains;

    public static class BalanceFactorDomainVO implements Serializable {
        private static final long serialVersionUID = 1L;
        /**
         * 平衡因子
         */
        private BigDecimal balanceFactor;

        /**
         * 净值
         */
        private String navDt;
        /**
         * 基金代码
         */
        private String fundCode;

        public BigDecimal getBalanceFactor() {
            return balanceFactor;
        }

        public void setBalanceFactor(BigDecimal balanceFactor) {
            this.balanceFactor = balanceFactor;
        }

        public String getNavDt() {
            return navDt;
        }

        public void setNavDt(String navDt) {
            this.navDt = navDt;
        }

        public String getFundCode() {
            return fundCode;
        }

        public void setFundCode(String fundCode) {
            this.fundCode = fundCode;
        }
    }

    public List<BalanceFactorDomainVO> getBalanceFactorDomains() {
        return balanceFactorDomains;
    }

    public void setBalanceFactorDomains(List<BalanceFactorDomainVO> balanceFactorDomains) {
        this.balanceFactorDomains = balanceFactorDomains;
    }
}
