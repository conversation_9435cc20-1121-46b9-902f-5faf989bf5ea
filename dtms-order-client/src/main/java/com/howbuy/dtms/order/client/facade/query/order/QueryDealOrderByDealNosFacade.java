/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.order;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryDealOrderByDealNosRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.QueryDealOrderListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/4/1 14:06
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.order.QueryDealOrderByDealNosFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryDealOrderListByDealNosFacade
 * @apiName execute()
 * @apiDescription 根据订单号查询订单列表
 * @apiParam (请求参数) {Array} dealNos 订单号集合
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=nJfBQfeUP&ipAddress=QyBM6HF5&deviceName=a&systemVersion=qgemk&appTm=1bCX8atZq&macAddress=4b2V1j&deviceSerialNo=q&dealNos=tt&appDt=i6miMFv&deviceModel=LYB&txCode=S&outletCode=i&tradeChannel=hInNizVVYU
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.dealOrderList 订单列表信息
 * @apiSuccess (响应结果) {String} data.dealOrderList.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
 * @apiSuccess (响应结果) {String} data.dealOrderList.appDt 申请日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.appTm 申请时间
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundShortName 基金简称
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.currency 币种代码
 * @apiSuccess (响应结果) {Number} data.dealOrderList.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.dealOrderList.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appVol 申请份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackVol 确认份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackAmt 确认金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.payStatus 支付状态
 * @apiSuccess (响应结果) {String} data.dealOrderList.redeemType 赎回方式 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundDivMode 分红方式 0-红利再投；1-现金红利
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundName 转入基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundCode 转入基金代码
 * @apiSuccessExample 响应结果示例
 * {"code":"I","data":{"dealOrderList":[{"ackVol":1750.3184722907083,"appAmt":8641.717111635071,"intoFundCode":"YVTtXZ9H3E","orderStatus":"opto","fundShortName":"IlpfxM1","fundDivMode":"3kGBEy3ef","intoFundName":"jLlnA","dealNo":9068,"appTm":"JzF4w","fundCode":"oAMW","ackAmt":9821.117844608103,"appVol":49.94043084816768,"redeemType":"t","appDt":"BA95i1Sz58","currency":"9ZqDEjj","businessType":"oJw5HY","fundName":"PuiXuyopiZ","payStatus":"sU"}]},"description":"0iDlsCxbKn"}
 */
public interface QueryDealOrderByDealNosFacade extends BaseFacade<QueryDealOrderByDealNosRequest, QueryDealOrderListResponse> {

}
