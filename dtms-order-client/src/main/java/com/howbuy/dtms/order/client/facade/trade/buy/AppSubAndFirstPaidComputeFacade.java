/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.buy;

import com.howbuy.dtms.order.client.domain.request.buy.AppSubAndFirstPaidComputeRequest;
import com.howbuy.dtms.order.client.domain.response.buy.AppSubAndFirstPaidComputeInfoVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 申购认缴实缴手续费计算接口
 * <AUTHOR>
 * @date 2025/3/20 15:15
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.AppSubAndFirstPaidComputeFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup AppSubAndFirstPaidComputeFacade
 * @apiName execute()
 * @apiDescription 申购认缴实缴手续费计算接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {BigDecimal} subAmt 买入金额(分次Call表示认缴)
 * @apiParam (请求参数) {BigDecimal} paidAmt 实缴金额
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK10086",
 *     "fundCode": "000001",
 *     "subAmt": 10000.00,
 *     "paidAmt": 5000.00
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {BigDecimal} data.actualPayAmt 实际支付金额
 * @apiSuccess (响应结果) {BigDecimal} data.feeRate 手续费费率
 * @apiSuccess (响应结果) {String} data.feeRateType 手续费类型(1-认缴、2-实缴)
 * @apiSuccess (响应结果) {BigDecimal} data.estimateFee 预估手续费
 * @apiSuccess (响应结果) {BigDecimal} data.originalFee 原始手续费
 * @apiSuccess (响应结果) {String} data.isLargerPrebookAmt 是否大于预约金额(0-否、1-是)
 * @apiSuccess (响应结果) {String} data.validDiscountRate 折扣是否生效(0-否、1-是)
 * @apiSuccess (响应结果) {BigDecimal} data.actualDiscountRate 实际折扣率
 * @apiSuccess (响应结果) {String} data.discountType 折扣类型
 * @apiSuccess (响应结果) {BigDecimal} data.discountAmt 折扣金额
 * @apiSuccess (响应结果) {BigDecimal} data.prebookDiscountRate 预约折扣率
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "actualPayAmt": 5120.00,
 *         "feeRate": 1.5,
 *         "feeRateType": "2",
 *         "estimateFee": 75.00,
 *         "originalFee": 150.00,
 *         "isLargerPrebookAmt": "0",
 *         "validDiscountRate": "1",
 *         "actualDiscountRate": 0.8,
 *         "discountType": "1",
 *         "discountAmt": 30.00,
 *         "prebookDiscountRate": 0.8
 *     },
 *     "description": "手续费计算成功"
 * }
 */
public interface AppSubAndFirstPaidComputeFacade extends BaseFacade<AppSubAndFirstPaidComputeRequest, AppSubAndFirstPaidComputeInfoVO> {
}
