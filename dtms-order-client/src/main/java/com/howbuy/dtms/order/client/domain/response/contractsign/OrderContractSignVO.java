/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.contractsign;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/5/15 16:12
 * @since JDK 1.8
 */
@Data
public class OrderContractSignVO implements Serializable {
    private static final long serialVersionUID = 7057396083827744323L;
    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
     */
    private String orderStatus;

    /**
     * 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
     */
    private String businessType;

    /**
     * 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出
     */
    private String middleBusiCode;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金名称
     */
    private String fundName;

    /**
     * 基金简称
     */
    private String fundAbbr;

    /**
     * 签署时间 yyyy-MM-dd HH:mm:ss
     */
    private String signDate;

    /**
     * 订单合同签署明细列表
     */
    private List<OrderContractSignDtlVO> orderContractSignDtlVOList;

}
