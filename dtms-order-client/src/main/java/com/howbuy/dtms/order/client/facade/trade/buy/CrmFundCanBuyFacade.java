/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.buy;

import com.howbuy.dtms.order.client.domain.request.buy.CanBuyRequest;
import com.howbuy.dtms.order.client.domain.response.buy.FundCanBuyResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 基金是否可购买接口
 * <AUTHOR>
 * @date 2024/9/13 15:46
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.CrmFundCanBuyFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup FundCanBuyFacade
 * @apiName execute()
 * @apiDescription 基金是否可购买校验接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=VNTZsC&hkCustNo=Ezm9L&ipAddress=NaKB6&deviceName=cT5J9pc6m&systemVersion=wn0ECKN&appTm=EVZtRT&macAddress=Avn&deviceSerialNo=yF&fundCode=3&appDt=dmTc&deviceModel=Ti&txCode=ankCyL6s&outletCode=iWcpNG2&tradeChannel=1
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"2hGfEGGN1X","description":"KSAltjrFY"}
 */
public interface CrmFundCanBuyFacade extends BaseFacade<CanBuyRequest, FundCanBuyResponse> {

}
