package com.howbuy.dtms.order.client.facade.query.order;

import com.howbuy.dtms.order.client.domain.request.order.QueryFundInTransitRequest;
import com.howbuy.dtms.order.client.domain.response.order.QueryFundInTransitResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.order.QueryFundInTransitFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryFundInTransitFacade
 * @apiName execute()
 * @apiDescription 查询申请成功且需要上报的订单信息
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金Code
 * @apiParamExample 请求参数示例
 * fundCode=2szdJ9uPJ&hkCustNo=ggdxh
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.dealOrderResponseList 订单信息
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.fundShortName 基金简称
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.redeemMethod 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.redeemDirectionList 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.supportPrebookFlag 支持预约交易标识 0-不支持；1-仅支持购买预约；2-仅支持赎回预约；3-支持购买赎回预约
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.appVol 申请份额
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.currency 币种代码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.payEndDt 打款截止日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.payEndTm 打款截止时间 HHmmss
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.hkCpAcctNo 香港资金账号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.swiftCode SWIFT代码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.bankCode 银行代码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.bankAcctMask 银行卡号掩码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.bankEnName 银行英文名称
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.bankChineseName 银行中文名称
 * @apiSuccess (响应结果) {Array} data.dealOrderResponseList.bankCurrencyList 银行币种列表
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.custChineseName 客户中文姓名
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.idType 证件类型
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.idNoCipher 证件号码密文
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.idNoDigest 证件号码摘要
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.idNoMask 证件号码掩码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.invstType 投资者类型 0-机构；1-个人
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.qualificationType 投资者资质 PRO-投资者资质专业;NORMAL-投资者资质普通
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.custRiskLevel 客户风险等级
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.txCode 交易代码:HW0001-网上买入；HW0002-网上卖出；HW0003-买入校验
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.productName 产品名称
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.productCode 产品代码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.appDt 申请日期
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.appTm 申请时间
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.prebookDealNo 预约单号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.externalDealNo 外部单号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.firstBuyFlag 首次购买标识，1-首次购买；2-追加购买
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.isAgreeCurrencyExchange 是否同意换汇 0-否 1-是
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.orderFormType 成单方式 1：纸质成单；2：电子成单；
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.memo 备注
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.ipAddress IP地址
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.outletCode 网点号
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.dealDtlNo 订单明细号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.cpAcctNo 资金账号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.bankAcctCipher 银行账号密文
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.bankAcctDigest 银行账号摘要
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.mainFundCode 主基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.fundCategory 基金类别 1-公募、2-私募、9-其他
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.fundRiskLevel 基金风险等级
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.paymentTypeList 支付方式 1-电汇、2-支票、3-海外储蓄罐
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.redeemType 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.middleBusiCode 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.netAppAmt 净申请金额
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.estimateFee 预估手续费
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.prebookDiscount 预约折扣
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.discountRate 折扣率
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.feeRate 手续费率
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.feeCalMode 费用计算方式 0-外扣法、1-内扣法
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.fee 手续费
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.ackAmt 确认金额
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.ackVol 确认份额
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.ackNav 确认净值
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.appStatus 申请状态 0-申请成功；1-申请失败；2-自行撤销；3-强制取消
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.ackStatus 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.payStatus 支付状态 0-无需支付；1-未打款；2-已打款；3-到账确认；4-退款；
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.payVoucherStatus 打款凭证状态 0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.actualPayAmt 实际打款金额
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.actualPayDt 实际打款日期
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.actualPayTm 实际打款时间
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.fundDivMode 分红方式 0-红利再投 1-现金分红 2-N/A不适用
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.openDt 开放日期
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.taTradeDt TA交易日期
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.submitTaDt 上报TA日期
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.ackDt 确认日期
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.taAckNo TA确认流水号
 * @apiSuccess (响应结果) {String} data.dealOrderResponseList.taCode TA代码
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.createTimestamp 创建时间戳
 * @apiSuccess (响应结果) {Number} data.dealOrderResponseList.updateTimestamp 更新时间戳
 * @apiSuccessExample 响应结果示例
 * {"code":"SZyx","data":{"dealOrderResponseList":[{"hkCpAcctNo":"KirUOF","discountRate":7143.************,"externalDealNo":"Ekd","appAmt":8136.***********,"bankAcctMask":"p","fee":4506.*************,"memo":"hW","taTradeDt":"n1phrg4XDd","fundShortName":"moOkvg8stf","estimateFee":6668.************,"productName":"uwtD2kLSCM","createTimestamp":*************,"redeemMethod":"B9fB0O","taCode":"D31CDg9","fundCode":"XeVK9Ogr","appVol":6972.************,"bankCurrencyList":["jUj"],"bankAcctCipher":"nZLP2lo8t","fundCategory":"vrgyjBGv5","custChineseName":"2E","appDt":"JTkiNdzO","firstBuyFlag":"vcY","ackVol":6290.************,"idType":"V4MFPNen","submitTaDt":"HJ3bO6G","taAckNo":"EMe83t7","ipAddress":"9","dealNo":6583,"appStatus":"wkpPzUOXx","fundRiskLevel":"nW","ackStatus":"R","payEndDt":"ddX7S2dDp","openDt":"FMo","txCode":"lKKQCL5","fundName":"d2KMdu","outletCode":"EUyp3kc3mo","netAppAmt":4476.************,"dealDtlNo":3953,"actualPayTm":"SLWOcHnnBf","orderFormType":"hJo8nC","bankEnName":"nnV5KZw","swiftCode":"dsjwAKV","orderStatus":"ImqWupvU","fundDivMode":"je","feeRate":6883.************,"qualificationType":"VdinLH3i","ackAmt":7068.************,"payEndTm":"EFTja9","invstType":"1s9Cym","supportPrebookFlag":"Ce4a6E","mainFundCode":"mf","currency":"1nVYizr2","cpAcctNo":"CgxFiEF","redeemDirectionList":"MGZ5CVDi","feeCalMode":"RoFt8FF","prebookDiscount":7164.************,"bankCode":"Bu","bankChineseName":"Py02iK3Nhl","payVoucherStatus":"niqm45qr","middleBusiCode":"MF9pOzadg","isAgreeCurrencyExchange":"gZnCMuE","hkCustNo":"dx1VKim1","custRiskLevel":"kl","idNoDigest":"QOxlpe","idNoMask":"sm8cuP","bankAcctDigest":"fF4IG6","updateTimestamp":*************,"prebookDealNo":"FN","ackNav":9886.************,"appTm":"UTDW0gIT","productCode":"QEOwl5GkLM","paymentTypeList":"cIx","redeemType":"5","actualPayAmt":746.************,"businessType":"xZc","ackDt":"hi35A","idNoCipher":"GNq","payStatus":"E","actualPayDt":"b7WcDk6y","tradeChannel":"kHzu2GXM"}]},"description":"s5xtv"}
 */
public interface QueryFundInTransitFacade extends BaseFacade<QueryFundInTransitRequest, QueryFundInTransitResponse> {

}
