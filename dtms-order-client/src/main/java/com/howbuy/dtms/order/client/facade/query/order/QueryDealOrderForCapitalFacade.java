/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.order;

import com.howbuy.dtms.order.client.domain.request.order.QueryDealOrderForCapitalRequest;
import com.howbuy.dtms.order.client.domain.response.order.QueryDealOrderForCapitalResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.order.QueryDealOrderForCapitalFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryDealOrderForCapitalFacade
 * @apiName execute()
 * @apiDescription 查询交易订单(资金)接口
 * @apiParam (请求体) {Array} dealNos 订单号列表，限制最大200
 * @apiParamExample 请求体示例
 * {"dealNos":["123456789","987654321"]}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.dealOrderForCapitalList 交易订单(资金)VO列表
 * @apiSuccess (响应结果) {Number} data.dealOrderForCapitalList.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.busiCode 业务代码
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.currency 币种代码
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.productPayEndDt 产品打款截止日期
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.productPayEndTm 产品打款截止时间
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.payEndDt 打款截止日期
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.payEndTm 打款截止时间
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.preSubmitTaDt 预计上报日
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.preSubmitTaTm 预计上报时间
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.openDt 开放日期
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.paymentType 支付方式
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.payStatus 付款状态
 * @apiSuccess (响应结果) {Number} data.dealOrderForCapitalList.netAppAmt 净申请金额
 * @apiSuccess (响应结果) {Number} data.dealOrderForCapitalList.estimateFee 预估手续费
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.orderStatus 订单状态
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.cpAcctNo 资金账号
 * @apiSuccess (响应结果) {String} data.dealOrderForCapitalList.receiptSerialNo 入账流水号
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{"dealOrderForCapitalList":[{"dealNo":123456789,"busiCode":"020","fundCode":"F001","currency":"USD","productPayEndDt":"20250711","productPayEndTm":"150000","payEndDt":"20250711","payEndTm":"150000","preSubmitTaDt":"20250712","preSubmitTaTm":"090000","openDt":"20250711","paymentType":"1","payStatus":"1","netAppAmt":10000.00,"estimateFee":50.00,"orderStatus":"1","cpAcctNo":"123456","receiptSerialNo":"REC001"}]},"description":"成功"}
 */
public interface QueryDealOrderForCapitalFacade extends BaseFacade<QueryDealOrderForCapitalRequest, QueryDealOrderForCapitalResponse> {
}
