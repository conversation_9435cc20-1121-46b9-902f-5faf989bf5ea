package com.howbuy.dtms.order.client.facade.query.querybuy;

import com.howbuy.dtms.order.client.domain.request.buy.ChangePayMethodPageRequest;
import com.howbuy.dtms.order.client.domain.response.buy.ChangePayMethodPagelVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.querybuy.ChangePayMethodPageFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup ChangePayMethodPageFacade
 * @apiName execute()
 * @apiDescription 修改支付方式页面查询接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} dealNo 订单号
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK123456",
 *     "dealNo": "ORD202412270001"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Object} data.changePayMethodOrderVO 订单明细信息
 * @apiSuccess (响应结果) {String} data.changePayMethodOrderVO.paymentType 支付方式(1-银行卡、2-支票、3-银行卡)
 * @apiSuccess (响应结果) {String} data.changePayMethodOrderVO.payEndDate 打款截止日期
 * @apiSuccess (响应结果) {String} data.changePayMethodOrderVO.payEndTime 打款截止时间(hh:mm)
 * @apiSuccess (响应结果) {String} data.changePayMethodOrderVO.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.changePayMethodOrderVO.fundCode 基金代码
 * @apiSuccess (响应结果) {BigDecimal} data.changePayMethodOrderVO.appAmt 实缴金额
 * @apiSuccess (响应结果) {String} data.changePayMethodOrderVO.cpAcctNo 资金账号(银行卡支付时返回)
 * @apiSuccess (响应结果) {Object} data.payMethodInfoVO 支付方式信息
 * @apiSuccess (响应结果) {String} data.payMethodInfoVO.hasSignCxg 是否签约储蓄罐(0-未签署、1-签署)
 * @apiSuccess (响应结果) {String} data.payMethodInfoVO.isSupportCxgPay 是否支持储蓄罐支付(0-不支持、1-支持)
 * @apiSuccess (响应结果) {Array} data.payMethodInfoVO.cxgCurrencyList 储蓄罐币种列表
 * @apiSuccess (响应结果) {String} data.payMethodInfoVO.cxgOrderEndTm 储蓄罐下单结束时间(HHmmss)
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "changePayMethodOrderVO": {
 *             "paymentType": "1",
 *             "payEndDate": "2024-12-31",
 *             "payEndTime": "15:30",
 *             "fundName": "香港基金A",
 *             "fundCode": "000001",
 *             "appAmt": 10000.00,
 *             "cpAcctNo": "6226090000000001"
 *         },
 *         "payMethodInfoVO": {
 *             "hasSignCxg": "1",
 *             "isSupportCxgPay": "1",
 *             "cxgCurrencyList": ["156", "840"],
 *             "cxgOrderEndTm": "153000"
 *         }
 *     },
 *     "description": "成功"
 * }
 */
public interface ChangePayMethodPageFacade extends BaseFacade<ChangePayMethodPageRequest, ChangePayMethodPagelVO> {
}
