package com.howbuy.dtms.order.client.facade.trade.cancel;

import com.howbuy.dtms.order.client.domain.request.cannel.CounterCancelRequest;
import com.howbuy.dtms.order.client.domain.request.cannel.CounterCancelValidateRequest;
import com.howbuy.dtms.order.client.domain.response.Body;
import com.howbuy.dtms.order.client.domain.response.Response;

public interface CounterCancelFacade {


    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.cancel.CounterCancelFacade.counterCancelValidate()
     * @apiVersion 1.0.0
     * @apiGroup CounterCancelFacade
     * @apiName counterCancelValidate()
     * @apiDescription 柜台撤单校验接口
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {String} dealNo
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
     * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求参数) {String} outletCode 网点号
     * @apiParam (请求参数) {String} ipAddress ipAddress
     * @apiParam (请求参数) {String} externalDealNo 外部订单号
     * @apiParam (请求参数) {String} macAddress MAC地址
     * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
     * @apiParam (请求参数) {String} deviceModel 设备型号
     * @apiParam (请求参数) {String} deviceName 设备名称
     * @apiParam (请求参数) {String} systemVersion 系统版本号
     * @apiParamExample 请求参数示例
     * externalDealNo=deO5&hkCustNo=M0Bkhh&ipAddress=Mu&dealNo=tP&deviceName=9&systemVersion=cM3oobvua&appTm=9ZqVAF&macAddress=Vco7bJXW&deviceSerialNo=17cRZaGP&appDt=9&deviceModel=1Q9Lvy3lS&txCode=uIL&outletCode=IarJ3hls7&tradeChannel=fr1DjK
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"sN1G","description":"x9nuvKp"}
     */
    Response<Body> counterCancelValidate(CounterCancelValidateRequest request);

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.cancel.CounterCancelFacade.counterCancel()
     * @apiVersion 1.0.0
     * @apiGroup CounterCancelFacade
     * @apiName counterCancel()
     * @apiDescription 柜台撤单接口
     * @apiParam (请求参数) {String} hkCustNo 香港客户号
     * @apiParam (请求参数) {String} dealNo 订单号
     * @apiParam (请求参数) {String} cancelType 撤单类型 1-自行撤销；2-强制取消
     * @apiParam (请求参数) {String} txCode 交易码
     * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
     * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求参数) {String} outletCode 网点号
     * @apiParam (请求参数) {String} ipAddress ipAddress
     * @apiParam (请求参数) {String} externalDealNo 外部订单号
     * @apiParam (请求参数) {String} macAddress MAC地址
     * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
     * @apiParam (请求参数) {String} deviceModel 设备型号
     * @apiParam (请求参数) {String} deviceName 设备名称
     * @apiParam (请求参数) {String} systemVersion 系统版本号
     * @apiParamExample 请求参数示例
     * externalDealNo=Xp0FXUAj&cancelType=rzt&hkCustNo=N9MESxn&ipAddress=K1d6&dealNo=Y&deviceName=yWsm&systemVersion=D&appTm=o9ew22a7x&macAddress=fzUTjUOou&deviceSerialNo=aKmbWsV&appDt=uAfV&deviceModel=f&txCode=AtK&outletCode=qvrW&tradeChannel=7InzfMmg
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccessExample 响应结果示例
     * {"code":"7","description":"miYiTiVhd"}
     */
    Response<Body> counterCancel(CounterCancelRequest request);
}
