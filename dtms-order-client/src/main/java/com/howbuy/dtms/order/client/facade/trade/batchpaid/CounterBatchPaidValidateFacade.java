package com.howbuy.dtms.order.client.facade.trade.batchpaid;

import com.howbuy.dtms.order.client.domain.request.batchpaid.CounterBatchPaidValidateRequest;
import com.howbuy.dtms.order.client.domain.response.batchpaid.CounterBatchPaidValidateResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.batchpaid.CounterBatchPaidValidateFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup CounterBatchPaidValidateFacadeImpl
 * @apiName execute()
 * @apiDescription 柜台批量实缴校验接口
 * @apiParam (请求体) {String} fundCode 基金代码
 * @apiParam (请求体) {List} custPaidList 客户实缴列表
 * @apiParam (请求体) {String} custPaidList.hkCustNo 香港客户号
 * @apiParam (请求体) {String} custPaidList.fundTxAcctNo 基金交易账号
 * @apiParam (请求体) {String} custPaidList.netAppAmt 净申请金额
 * @apiParam (请求体) {String} custPaidList.estimateFee 预估费用
 * @apiParam (请求体) {String} custPaidList.appDisCount 申请折扣率
 * @apiParam (请求体) {String} custPaidList.paymentType 支付方式
 * @apiParam (请求体) {String} custPaidList.cpAcctNo 资金账号(非必填)
 * @apiParam (请求体) {String} tradeChannel 交易渠道
 * @apiParam (请求体) {String} appDt 申请日期(格式:YYYYMMDD)
 * @apiParam (请求体) {String} appTm 申请时间(格式:HHmmss)
 * @apiParamExample 请求体示例
 * {
 *   "fundCode": "000001",
 *   "custPaidList": [{
 *     "hkCustNo": "HK10086",
 *     "fundTxAcctNo": "100001",
 *     "netAppAmt": "10000.00",
 *     "estimateFee": "50.00",
 *     "appDisCount": "1.00",
 *     "paymentType": "1",
 *     "cpAcctNo": "6225880112345678"
 *   }],
 *   "tradeChannel": "1",
 *   "appDt": "20240324",
 *   "appTm": "190729"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {
 *   "code": "0000",
 *   "description": "成功"
 * }
 */
public interface CounterBatchPaidValidateFacade extends BaseFacade<CounterBatchPaidValidateRequest, CounterBatchPaidValidateResponse> {

} 