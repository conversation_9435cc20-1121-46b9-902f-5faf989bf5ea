package com.howbuy.dtms.order.client.domain.response;


import com.howbuy.dtms.order.client.enums.CommonResultEnum;
import com.howbuy.dtms.order.client.enums.IResultCode;

import java.io.Serializable;

/**
 * 通用返回结果封装类
 * <AUTHOR>
 * @date 2023/1/16 10:45
 */
public class Response<T> implements Serializable {
    private static final long serialVersionUID = 6504835003164969647L;
    /**
     * 状态码
     */
    private String code;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 数据封装
     */
    private T data;

    public Response() {
    }

    public Response(String code, String description, T data) {
        this.code = code;
        this.description = description;
        this.data = data;
    }

    public static <T> Response<T> ok() {
        return ok(null);
    }

    /**
     * 成功返回结果
     * @param data 获取的数据
     */
    public static <T> Response<T> ok(T data) {
        return new Response<T>(CommonResultEnum.SUCCESS.getCode(), CommonResultEnum.SUCCESS.getDescription(), data);
    }

    /**
     * 成功返回结果
     * @param data 获取的数据
     */
    public static <T> Response<T> ok(String description, T data) {
        return new Response<T>(CommonResultEnum.SUCCESS.getCode(), description, data);
    }

    /**
     * 失败返回结果
     * @param code 状态码
     * @param description 描述信息
     * @param data 数据封装
     */
    public static <T> Response<T> fail(String code, String description, T data) {
        return new Response<T>(code, description, data);
    }
    /**
     * 失败返回结果
     * @param code 状态码
     * @param description 描述信息
     */
    public static <T> Response<T> fail(String code, String description) {
        return new Response<T>(code, description, null);
    }

    /**
     * 失败返回结果
     * @param description 描述信息
     */
    public static <T> Response<T> fail(String description) {
        return fail(CommonResultEnum.FAIL.getCode(), description, null);
    }

    /**
     * 失败返回结果
     * @param resultCode 描述信息
     */
    public static <T> Response<T> fail(IResultCode resultCode) {
        return fail(resultCode.getCode(), resultCode.getDescription(), null);
    }

    /**
     * 失败返回结果
     * @param resultCode 描述信息
     * @param data 数据
     */
    public static <T> Response<T> fail(IResultCode resultCode, T data) {
        return fail(resultCode.getCode(), resultCode.getDescription(), data);
    }

    /**
     * 失败返回结果
     * @return
     */
    public static <T> Response<T> fail() {
        return fail(CommonResultEnum.FAIL.getCode());
    }

    public boolean isSuccess(){
        return CommonResultEnum.SUCCESS.getCode().equals(this.getCode());
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }
}
