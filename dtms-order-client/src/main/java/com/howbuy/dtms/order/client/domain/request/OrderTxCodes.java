/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request;

/**
 * <AUTHOR>
 * @description: 交易码
 * @date 2024/4/18 13:58
 * @since JDK 1.8
 */
public class OrderTxCodes {

    /**
     * HW0000-默认
     */
    public static final String HW_ORDER_DEFAULT = "HW0000";

    /**
     * HW0001-网上买入
     */
    public static final String HW_ORDER_WEB_BUY = "HW0001";

    /**
     * HW0002-网上卖出
     */
    public static final String HW_ORDER_WEB_SELL = "HW0002";

    /**
     * HW0003-柜台买入校验
     */
    public static final String HW_ORDER_BUY_COUNTER_VALIDATE = "HW0003";

    /**
     * HW0004-柜台卖出校验
     */
    public static final String HW_ORDER_SELL_COUNTER_VALIDATE = "HW0004";

    /**
     * HW0005-买入手续费计算
     */
    public static final String HW_ORDER_BUY_FEE_COMPUTE = "HW0005";

    /**
     * HW0006-是否可买入
     */
    public static final String HW_ORDER_CAN_BUY = "HW0006";

    /**
     * HW0007-是否可卖出
     */
    public static final String HW_ORDER_CAN_SELL = "HW0007";

    /**
     * HW0008-柜台买入
     */
    public static final String HW_ORDER_BUY_COUNTER = "HW0008";

    /**
     * HW0009-柜台卖出
     */
    public static final String HW_ORDER_SELL_COUNTER = "HW0009";

    /**
     * HW0010-撤单
     */
    public static final String HW_ORDER_CANCEL_ORDER = "HW0010";

    /**
     * HW0011-撤单校验
     */
    public static final String HW_ORDER_CANCEL_ORDER_VALIDATE = "HW0011";

    /**
     * HW0012-打款凭证提交
     */
    public static final String HW_ORDER_PAY_VOUCHER_SUBMIT = "HW0012";

    /**
     * HW0013-合同签署
     */
    public static final String HW_ORDER_CONTRACT_SIGN = "HW0013";

    /**
     * HW0014-查询打款凭证
     */
    public static final String HW_ORDER_QUERY_PAY_VOUCHER = "HW0014";

    /**
     * HW0015-查询资产
     */
    public static final String HW_ORDER_QUERY_BALANCE = "HW0015";

    /**
     * HW0016-查询资产在途
     */
    public static final String HW_ORDER_QUERY_BALANCE_FINRECEIPT = "HW0016";

    /**
     * HW0017-查询订单信息PDF
     */
    public static final String HW_ORDER_QUERY_ORDER_PDF = "HW0017";

    /**
     * HW0018-查询买入信息
     */
    public static final String HW_ORDER_QUERY_BUY_INFO = "HW0018";

    /**
     * HW0019-查询订单信息
     */
    public static final String HW_ORDER_QUERY_ORDER = "HW0019";

    /**
     * HW0020-查询订单分页
     */
    public static final String HW_ORDER_QUERY_ORDER_PAGE = "HW0020";

    /**
     * HW0021-查询卖出信息
     */
    public static final String HW_ORDER_QUERY_SELL_INFO = "HW0021";

    /**
     * HW0022-查询订单合同签署
     */
    public static final String HW_ORDER_QUERY_DEAL_CONTRACT_SIGN = "HW0022";

    /**
     * HW0023-查询订单合同列表
     */
    public static final String HW_ORDER_QUERY_ORDER_CONTRACT_LIST = "HW0023";

    /**
     * HW0024-查询首次交易
     */
    public static final String HW_ORDER_QUERY_FIRST_TRADE = "HW0024";

    /**
     * HW0025-储蓄罐基金购买接口
     */
    public static final String HW_ORDER_PIGGY_BUY = "HW0025";

    /**
     * HW0026-储蓄罐基金卖出接口
     */
    public static final String HW_ORDER_PIGGY_SELL = "HW0026";


    /**
     * HW0027-APP批量实缴消息
     */
    public static final String HW_PAID_PENDING_MESSAGE_DETAIL= "HW0027";

    /**
     * HW0028-APP认缴首次实缴计算
     */
    public static final String APP_SUB_AND_FIRST_PAID_COMPUTE = "HW0028";

    /**
     * HW0017-查询购买类订单信息PDF
     */
    public static final String HW_ORDER_QUERY_BUY_ORDER_PDF = "HW0029";
    /**
     * HW0017-查询卖出类订单信息PDF
     */
    public static final String HW_ORDER_QUERY_SELL_ORDER_PDF = "HW0030";

    /**
     * HW0017-查询分次CAll基金调仓类订单信息PDF
     */
    public static final String HW_ORDER_QUERY_GRADATION_CALL_ORDER_PDF = "HW0031";
}
