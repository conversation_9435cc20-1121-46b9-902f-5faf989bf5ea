package com.howbuy.dtms.order.client.facade.query.fundtransfer;

import com.howbuy.dtms.order.client.domain.request.fundtransfer.FundTransferTradeCalendarRequest;
import com.howbuy.dtms.order.client.domain.response.fundtransfer.FundTransferTradeCalendarResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundtransfer.FundTransferTradeCalendarFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup FundTransferTradeCalendarFacade
 * @apiName execute()
 * @apiDescription FundTransferTradeCalendarFacade
 * @apiParam (请求参数) {String} outFundCode 转出基金
 * @apiParam (请求参数) {String} inFundCode 转入基金
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=Q5Z&hkCustNo=HrYSj&ipAddress=S&deviceName=QuYuxIKlSj&systemVersion=xz&appTm=3dMk5pYr&macAddress=vzAr6VtyVN&deviceSerialNo=w&appDt=aa0UB&deviceModel=JrxYv7uM&txCode=EoWSoUs9&outFundCode=7Pa&outletCode=empJweAW&inFundCode=t&tradeChannel=x
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.expectReportDt 预计上报时间
 * @apiSuccess (响应结果) {String} data.expectReportTm 预计上报时间
 * @apiSuccess (响应结果) {String} data.openDt 开放日
 * @apiSuccessExample 响应结果示例
 * {"code":"C","data":{"expectReportDt":"5yjGJrzov0","openDt":"b0Ry","expectReportTm":"NMt"},"description":"pF"}
 */
public interface FundTransferTradeCalendarFacade extends BaseFacade<FundTransferTradeCalendarRequest, FundTransferTradeCalendarResponse> {
}
