package com.howbuy.dtms.order.client.domain.request.subandfirstpaid;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: 柜台认缴和首次实缴请求
 * <AUTHOR>
 * @date 2025/04/07 15:58:49
 * @since JDK 1.8
 */
@Setter
@Getter
@EqualsAndHashCode(callSuper = true)
public class CounterSubAndFirstPaidRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 基金交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
    private String fundTxAcctNo;

    /**
     * 资金账号
     * 支付方式为1-电汇时必填
     */
    private String cpAcctNo;

    /**
     * 支付方式
     * 1-电汇、2-支票、3-海外储蓄罐
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "支付方式", isRequired = true)
    private String payMethod;

    /**
     * 实缴外部订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实缴外部订单号", isRequired = true)
    private String paidExternalDealNo;

    /**
     * 实缴净申请金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "实缴净申请金额", isRequired = true)
    private BigDecimal paidNetAppAmt;

    /**
     * 实缴预估手续费
     * String：Money时手续费为0校验不通过
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实缴预估手续费", isRequired = true)
    private BigDecimal paidEstimateFee;

    /**
     * 实缴申请折扣率
     */
    private BigDecimal paidApplyDiscountRate;

    /**
     * 实缴折扣类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实缴折扣类型", isRequired = true)
    private String paidDiscountType;

    /**
     * 实缴折扣金额
     */
    private BigDecimal paidDiscountAmt;

    /**
     * 认缴外部订单号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "认缴外部订单号", isRequired = true)
    private String subExternalDealNo;

    /**
     * 认缴净申请金额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "认缴净申请金额", isRequired = true)
    private BigDecimal subNetAppAmt;

    /**
     * 认缴预估手续费
     * String：Money时手续费为0校验不通过
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "认缴预估手续费", isRequired = true)
    private BigDecimal subEstimateFee;

    /**
     * 认缴申请折扣率
     */
    private BigDecimal subApplyDiscountRate;

    /**
     * 认缴折扣类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "认缴折扣类型", isRequired = true)
    private String subDiscountType;

    /**
     * 认缴折扣金额
     */
    private BigDecimal subDiscountAmt;

    /**
     * 预约单号
     */
    private String prebookDealNo;

    /**
     * 展期选项
     * 1-本金展期,2-本金+收益展期,3-收益展期
     */
    private String extOption;

    /**
     * 展期控制数
     */
    private String extControlNum;
} 