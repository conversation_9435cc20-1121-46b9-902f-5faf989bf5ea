/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.fundhold;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/15 13:35
 * @since JDK 1.8
 */
public class QueryCustFundHoldDetailListResponse implements Serializable {
    private static final long serialVersionUID = 2769999975438719722L;

    public List<CustFundHoldDetailVO> getCustFundHoldDetailVOList() {
        return custFundHoldDetailVOList;
    }

    public void setCustFundHoldDetailVOList(List<CustFundHoldDetailVO> custFundHoldDetailVOList) {
        this.custFundHoldDetailVOList = custFundHoldDetailVOList;
    }

    /**
     * 客户基金持仓明细
     */
    private List<CustFundHoldDetailVO> custFundHoldDetailVOList = new ArrayList<>();
}
