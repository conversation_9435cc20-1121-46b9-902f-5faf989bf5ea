package com.howbuy.dtms.order.client.facade.query.fundhold;

import com.howbuy.dtms.order.client.domain.request.asset.QueryPiggyFundHoldingsRequest;
import com.howbuy.dtms.order.client.domain.response.asset.QueryPiggyFundHoldingsResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 储蓄挂底层基金查询接口
 * @return
 * @author: jinqing.rao
 * @date: 2024/7/23 11:00
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.fundhold.QueryPiggyFundHoldingsFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryPiggyFundHoldingsFacade
 * @apiName 储蓄罐持仓信息查询
 * @apiDescription 储蓄罐持仓信息查询
 * @apiParam (请求参数) {String} fundCode 基金Code
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * externalDealNo=AuKgbK&hkCustNo=Qb&ipAddress=XH&deviceName=Kb5T&systemVersion=MdguFl&appTm=ly&macAddress=jSbbXR6vF&deviceSerialNo=SO0xLuGgWx&fundCode=d&appDt=A04RD&deviceModel=EVArReH7XC&txCode=MJ&outletCode=ggRugSL&tradeChannel=Nn0PsGXQV
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Number} data.totalVol 总持仓份额
 * @apiSuccess (响应结果) {Number} data.freezeShare 冻结份额
 * @apiSuccess (响应结果) {String} data.openStartDt 开放开始日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.openEndDt 开放结束日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.advanceEndDt 预约结束日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.advanceEndTm 预约结束时间 HHmmss
 * @apiSuccess (响应结果) {String} data.tradeDt 交易日期 yyyyMMdd
 * @apiSuccess (响应结果) {String} data.isInTransitOrder 是否在途订单 0-否、1-是
 * @apiSuccess (响应结果) {Number} data.availableVol 可用份额
 * @apiSuccess (响应结果) {Number} data.totalAsset 总资产
 * @apiSuccess (响应结果) {Number} data.availableAsset 可用资产
 * @apiSuccess (响应结果) {Number} data.nav 最新基金净值（从DB获取）
 * @apiSuccess (响应结果) {String} data.navDt 最新净值日期（从DB获取）
 * @apiSuccessExample 响应结果示例
 * {"code":"jb6","data":{"totalAsset":4533.01615804226,"nav":5389.015928871205,"advanceEndTm":"Bg0gqyi","openStartDt":"C","navDt":"gDiX","isInTransitOrder":"v","availableAsset":2475.0794249055907,"tradeDt":"YO5","freezeShare":7882.274013043188,"totalVol":1103.3581190521868,"advanceEndDt":"lzWKMnkXFp","openEndDt":"V2YSkPrAn","availableVol":5274.520878904824},"description":"U7Nnq"}
 */
public interface QueryPiggyFundHoldingsFacade extends BaseFacade<QueryPiggyFundHoldingsRequest, QueryPiggyFundHoldingsResponse> {
}
