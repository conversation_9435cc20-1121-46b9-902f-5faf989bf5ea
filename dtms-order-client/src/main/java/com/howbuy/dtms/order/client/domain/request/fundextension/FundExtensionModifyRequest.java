/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fundextension;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/12/2 16:36
 * @since JDK 1.8
 */
public class FundExtensionModifyRequest extends BaseRequest {

    private static final long serialVersionUID = 2570944674414028834L;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金编码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金编码", isRequired = true)
    private String fundCode;

    /**
     * 基金交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
    private String fundTxAcctNo;

    /**
     * 展期选项 1-本金展期,2-本金+收益展期,3-收益展期,4-到期赎回
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "展期选项", isRequired = true)
    private String extOption;

    /**
     * 展期控制类型:1-月
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "展期控制类型", isRequired = true)
    private String extControlType;

    /**
     * 展期控制数
     */
    private String extControlNum;

    /**
     * 份额明细号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "份额明细号", isRequired = true)
    private String volDtlNo;

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getExtOption() {
        return extOption;
    }

    public void setExtOption(String extOption) {
        this.extOption = extOption;
    }

    public String getExtControlType() {
        return extControlType;
    }

    public void setExtControlType(String extControlType) {
        this.extControlType = extControlType;
    }

    public String getExtControlNum() {
        return extControlNum;
    }

    public void setExtControlNum(String extControlNum) {
        this.extControlNum = extControlNum;
    }

    public String getVolDtlNo() {
        return volDtlNo;
    }

    public void setVolDtlNo(String volDtlNo) {
        this.volDtlNo = volDtlNo;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }
}
