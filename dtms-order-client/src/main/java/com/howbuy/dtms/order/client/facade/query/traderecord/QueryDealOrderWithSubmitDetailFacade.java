package com.howbuy.dtms.order.client.facade.query.traderecord;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryDealOrderWithSubmitDetailRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.QueryDealOrderWithSubmitDetailResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description:(查询订单信息，包括上报明细信息的接口)
 * @return
 * @author: haoran.zhang
 * @date: 2024/12/14 10:13
 * @since JDK 1.8
 */
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.traderecord.QueryDealOrderWithSubmitDetailFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryDealOrderWithSubmitDetailFacade
 * @apiName execute()
 * @apiDescription QueryDealOrderWithSubmitDetailFacade
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} orderStatus 订单状态
 * @apiParam (请求参数) {Number} dealNo 订单号
 * @apiParam (请求参数) {Array} middleBusicodeList 业务码列表
 * @apiParam (请求参数) {Array} fundCodes 基金代码
 * @apiParam (请求参数) {Number} page
 * @apiParam (请求参数) {Number} size
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * fundCodes=g2&externalDealNo=fNHbeMc7IT&hkCustNo=L3qcFV&ipAddress=6dBnW&orderStatus=LpDbOzYtFj&dealNo=7290&deviceName=GNwRD&systemVersion=s2d9PWt&appTm=KO0eU3hKW&macAddress=ow4S&middleBusicodeList=U1g6&size=7869&deviceSerialNo=nb5P3V&appDt=QqYui&deviceModel=LEjrTD&page=5046&txCode=ENv4UgRo&outletCode=Lc&tradeChannel=Pek8mMlD
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.dealOrderList 订单[附带上报明细信息] 列表信息
 * @apiSuccess (响应结果) {String} data.dealOrderList.hkCpAcctNo 香港资金账号
 * @apiSuccess (响应结果) {String} data.dealOrderList.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.dealOrderList.productCode 产品代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.productName 产品名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.businessType 业务类型:10-买入;11-卖出;17-非交易过户;18-修改分红方式;19-强制赎回;20-红利下发;21-份额强增;22-份额强减
 * @apiSuccess (响应结果) {String} data.dealOrderList.middleBusiCode 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出
 * @apiSuccess (响应结果) {String} data.dealOrderList.appDt 申请日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.appTm 申请时间
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundShortName 基金简称
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.currency 币种代码
 * @apiSuccess (响应结果) {Number} data.dealOrderList.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.dealOrderList.orderStatus 订单状态 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消；
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.appVol 申请份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackVol 确认份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackAmt 确认金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.payStatus 支付状态
 * @apiSuccess (响应结果) {String} data.dealOrderList.redeemType 赎回方式 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.dealOrderList.fundDivMode 分红方式 0-红利再投；1-现金红利
 * @apiSuccess (响应结果) {String} data.dealOrderList.mainFundCode 主基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.redeemDirectionList 赎回方向列表 1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：基金转投
 * @apiSuccess (响应结果) {Number} data.dealOrderList.estimateFee 预估手续费
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackFee 确认手续费
 * @apiSuccess (响应结果) {Number} data.dealOrderList.fee 手续费
 * @apiSuccess (响应结果) {String} data.dealOrderList.paymentTypeList 支付方式 1-电汇、2-支票、3-海外储蓄罐
 * @apiSuccess (响应结果) {Array} data.dealOrderList.submitInfoList 上报信息列表
 * @apiSuccess (响应结果) {String} data.dealOrderList.openDt 开放日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.taTradeDt TA交易日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.submitTaDt 上报TA日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.ackDt 确认日期
 * @apiSuccess (响应结果) {Number} data.dealOrderList.ackNav 确认净值(转入确认净值)
 * @apiSuccess (响应结果) {Number} data.dealOrderList.transferNav 转出确认净值
 * @apiSuccess (响应结果) {String} data.dealOrderList.swiftCode SWIFT代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.bankCode 银行代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.bankAcctMask 银行卡号掩码
 * @apiSuccess (响应结果) {String} data.dealOrderList.bankEnName 银行英文名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.bankChineseName 银行中文名称
 * @apiSuccess (响应结果) {String} data.dealOrderList.memo 备注
 * @apiSuccess (响应结果) {String} data.dealOrderList.tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiSuccess (响应结果) {String} data.dealOrderList.ackStatus 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundCode 转入基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoMainFundCode 转入母基金代码
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundAbbr 转入基金简称
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoCurrency 转入币种
 * @apiSuccess (响应结果) {Number} data.dealOrderList.intoAckAmt 转入确认金额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.intoAckVol 转入确认份额
 * @apiSuccess (响应结果) {Number} data.dealOrderList.intoAckNav 转入确认净值
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoAckNavDt 转入确认净值日期
 * @apiSuccess (响应结果) {String} data.dealOrderList.intoFundTxAcctNo 转入基金交易账号
 * @apiSuccessExample 响应结果示例
 * {"code":"yBnD","data":{"dealOrderList":[{"hkCpAcctNo":"lUDcF","intoMainFundCode":"BaWYdwy","appAmt":4368.************,"bankAcctMask":"IEODTKtP","fee":6936.*************,"taTradeDt":"0FHpw","memo":"uHxrT84Y0","fundShortName":"yKAyhHsyf","estimateFee":8331.************,"ackFee":57788,"intoAckAmt":7267.************,"productName":"uWXf8hlCi","intoFundTxAcctNo":"Bq","fundCode":"tx0MTZ","appVol":1716.*************,"appDt":"6NYIzNK0","ackVol":8211.************,"submitTaDt":"mIuBnSL","intoFundAbbr":"quCw","intoAckNav":4230.************,"intoCurrency":"A","dealNo":3345,"ackStatus":"J","openDt":"jop","fundName":"NyxMJ9y","bankEnName":"0WB","orderStatus":"dkUFvM","swiftCode":"nmq4","fundDivMode":"MrMfK","intoAckNavDt":"vDLWej","ackAmt":5950.************,"mainFundCode":"P3vrXK","currency":"QN","redeemDirectionList":"XYMPul1","submitInfoList":[],"bankCode":"kMOGT","bankChineseName":"gl3","intoFundCode":"SHdBvGe7","middleBusiCode":"coO","hkCustNo":"UL1Y8oHhV","ackNav":4187.************,"appTm":"I6E","productCode":"0mWb8Tj2","paymentTypeList":"thDW4fO","redeemType":"v","intoAckVol":5024.************,"transferNav":1909.*************,"businessType":"v9HxpG","ackDt":"Tp742xb","payStatus":"V","tradeChannel":"8MhHPvMP"}]},"description":"RpJQhCRo3n"}
 */
public interface QueryDealOrderWithSubmitDetailFacade extends BaseFacade<QueryDealOrderWithSubmitDetailRequest, QueryDealOrderWithSubmitDetailResponse> {
}
