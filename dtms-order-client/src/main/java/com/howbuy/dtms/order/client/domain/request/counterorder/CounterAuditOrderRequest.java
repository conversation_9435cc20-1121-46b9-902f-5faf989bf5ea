/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.counterorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 柜台审核订单请求对象
 * <AUTHOR>
 * @date 2024/11/7 16:09
 * @since JDK 1.8
 */
public class CounterAuditOrderRequest implements Serializable {

    private static final long serialVersionUID = -6473200447289086414L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 审核申请流水号
     */
    private String appSerialNo;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 中台业务码
     */
    private String counterBizType;

    /**
     * 证件号码掩码
     */
    private String idNoDigest;

    /**
     * 客户中文姓名
     */
    private String custChineseName;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundAbbr;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 申请日期
     */
    private String appDt;

    /**
     * 申请时间
     */
    private String appTm;

    /**
     * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     */
    private String tradeChannel;

    /**
     * 网点号
     */
    private String outletCode;

    /**
     * 审核通过时间戳
     */
    private Date auditPassTimestamp;

    /**
     * 审核通过备注
     */
    private String auditRemark;

    /**
     * 审核状态（0-无需审核、2-等待复核、3-审核通过、4-审核不通过、5-驳回至经办、7-作废、8-等待回访）
     */
    private String auditStatus;

    /**
     * 回访人
     */
    private String revisitPerson;

    /**
     * 回访日期时间
     */
    private Date revisitTimestamp;

    /**
     * 回访文件地址
     */
    private String revisitFileUrl;

    /**
     * 回访原因
     */
    private String revisitReason;

    /**
     * 回访文件名称
     */
    private String revisitFileName;

    /**
     * 是否需要回访
     */
    private String revisit;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 复核人
     */
    private String reviewer;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 创建日期时间
     */
    private Date createTimestamp;

    /**
     * 客户类型
     */
    private String invstType;

    /**
     * 更新日期时间
     */
    private Date updateTimestamp;

    /**
     * 认缴总金额
     */
    private BigDecimal subTotalAmt;

    /**
     * 累计实缴总金额
     */
    private BigDecimal cumPaidTotalAmt;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppSerialNo() {
        return appSerialNo;
    }

    public void setAppSerialNo(String appSerialNo) {
        this.appSerialNo = appSerialNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCounterBizType() {
        return counterBizType;
    }

    public void setCounterBizType(String counterBizType) {
        this.counterBizType = counterBizType;
    }

    public String getIdNoDigest() {
        return idNoDigest;
    }

    public void setIdNoDigest(String idNoDigest) {
        this.idNoDigest = idNoDigest;
    }

    public String getCustChineseName() {
        return custChineseName;
    }

    public void setCustChineseName(String custChineseName) {
        this.custChineseName = custChineseName;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundAbbr() {
        return fundAbbr;
    }

    public void setFundAbbr(String fundAbbr) {
        this.fundAbbr = fundAbbr;
    }

    public BigDecimal getAppAmt() {
        return appAmt;
    }

    public void setAppAmt(BigDecimal appAmt) {
        this.appAmt = appAmt;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getAppDt() {
        return appDt;
    }

    public void setAppDt(String appDt) {
        this.appDt = appDt;
    }

    public String getAppTm() {
        return appTm;
    }

    public void setAppTm(String appTm) {
        this.appTm = appTm;
    }

    public String getTradeChannel() {
        return tradeChannel;
    }

    public void setTradeChannel(String tradeChannel) {
        this.tradeChannel = tradeChannel;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public Date getAuditPassTimestamp() {
        return auditPassTimestamp;
    }

    public void setAuditPassTimestamp(Date auditPassTimestamp) {
        this.auditPassTimestamp = auditPassTimestamp;
    }

    public String getAuditRemark() {
        return auditRemark;
    }

    public void setAuditRemark(String auditRemark) {
        this.auditRemark = auditRemark;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(String auditStatus) {
        this.auditStatus = auditStatus;
    }

    public String getRevisitPerson() {
        return revisitPerson;
    }

    public void setRevisitPerson(String revisitPerson) {
        this.revisitPerson = revisitPerson;
    }

    public Date getRevisitTimestamp() {
        return revisitTimestamp;
    }

    public void setRevisitTimestamp(Date revisitTimestamp) {
        this.revisitTimestamp = revisitTimestamp;
    }

    public String getRevisitFileUrl() {
        return revisitFileUrl;
    }

    public void setRevisitFileUrl(String revisitFileUrl) {
        this.revisitFileUrl = revisitFileUrl;
    }

    public String getRevisitReason() {
        return revisitReason;
    }

    public void setRevisitReason(String revisitReason) {
        this.revisitReason = revisitReason;
    }

    public String getRevisitFileName() {
        return revisitFileName;
    }

    public void setRevisitFileName(String revisitFileName) {
        this.revisitFileName = revisitFileName;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }

    public String getInvstType() {
        return invstType;
    }

    public void setInvstType(String invstType) {
        this.invstType = invstType;
    }

    public String getRevisit() {
        return revisit;
    }

    public void setRevisit(String revisit) {
        this.revisit = revisit;
    }

    public BigDecimal getSubTotalAmt() {
        return subTotalAmt;
    }

    public void setSubTotalAmt(BigDecimal subTotalAmt) {
        this.subTotalAmt = subTotalAmt;
    }

    public BigDecimal getCumPaidTotalAmt() {
        return cumPaidTotalAmt;
    }

    public void setCumPaidTotalAmt(BigDecimal cumPaidTotalAmt) {
        this.cumPaidTotalAmt = cumPaidTotalAmt;
    }
}
