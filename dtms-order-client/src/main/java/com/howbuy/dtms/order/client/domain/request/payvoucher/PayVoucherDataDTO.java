/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/22 16:37
 * @since JDK 1.8
 */
@Setter
@Getter
public class PayVoucherDataDTO implements Serializable {

    private static final long serialVersionUID = 3699688537638571167L;
    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 汇款资金账号
     */
    private String remitCpAcctNo;

    /**
     * 汇款币种
     */
    private String remitCurrency;

    /**
     * 汇款金额
     */
    private BigDecimal remitAmt;

    /**
     * 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     */
    private String voucherType;

    /**
     * 交易订单号 类型为1-交易下单凭证时，订单号必传
     */
    private String tradeOrderNo;

    /**
     * 是否同意换汇 0-否 1-是
     */
    private String agreeSwap;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件来源 1-DTMS_ORDER 2-CRM
     */
    private String fileSource;

    /**
     * 入账流水号
     */
    private String receiptSerialNo;


}