/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.piggytradeapp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 查询储蓄罐交易申请结果响应
 * <AUTHOR>
 * @date 2025-07-15 19:32:38
 * @since JDK 1.8
 */
@Getter
@Setter
public class QueryPiggyTradeAppResultResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 储蓄罐交易申请结果列表
     */
    private List<PiggyTradeAppResultVO> list;
}
