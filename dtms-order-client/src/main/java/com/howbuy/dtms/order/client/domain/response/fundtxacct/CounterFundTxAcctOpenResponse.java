package com.howbuy.dtms.order.client.domain.response.fundtxacct;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 柜台基金交易账号校验响应参数
 * <AUTHOR>
 * @date 2025/4/17 14:20
 * @since JDK 1.8
 */
@Getter
@Setter
public class CounterFundTxAcctOpenResponse implements Serializable {

    private static final long serialVersionUID = 2362028750861737333L;
    /**
     * 基金交易账号存在信息
     */
    private List<ExitFundTxAcctInfo> exitFundTxAcctInfoList;
    @Setter
    @Getter
    public static class ExitFundTxAcctInfo implements Serializable{

        private static final long serialVersionUID = -6579277184615620192L;
        /**
         * 基金交易账号类型
         */
        private String fundTxAcctType;

        /**
         * 基金交易账号
         */
        private String fundTxAcctNo;
    }
} 