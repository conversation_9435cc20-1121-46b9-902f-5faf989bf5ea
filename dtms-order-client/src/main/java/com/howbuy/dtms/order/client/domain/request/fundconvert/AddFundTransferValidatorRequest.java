/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fundconvert;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/12/4 18:46
 * @since JDK 1.8
 */
public class AddFundTransferValidatorRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = -7635383557775637999L;

    /**
     * 转出基金
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "转出基金", isRequired = true)
    private String outFundCode;

    /**
     * 转入基金
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "转入基金", isRequired = true)
    private String inFundCode;

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 申请份额
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "申请份额", isRequired = true)
    private BigDecimal appVol;

    /**
     * 基金交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
    private String fundTxAcctNo;

    public String getOutFundCode() {
        return outFundCode;
    }

    public void setOutFundCode(String outFundCode) {
        this.outFundCode = outFundCode;
    }

    public String getInFundCode() {
        return inFundCode;
    }

    public void setInFundCode(String inFundCode) {
        this.inFundCode = inFundCode;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public BigDecimal getAppVol() {
        return appVol;
    }

    public void setAppVol(BigDecimal appVol) {
        this.appVol = appVol;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }
}
