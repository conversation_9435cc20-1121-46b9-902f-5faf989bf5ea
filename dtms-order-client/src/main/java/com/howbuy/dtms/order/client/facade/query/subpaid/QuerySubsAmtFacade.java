package com.howbuy.dtms.order.client.facade.query.subpaid;

import com.howbuy.dtms.order.client.domain.request.subpaid.QuerySubsAmtRequest;
import com.howbuy.dtms.order.client.domain.response.subpaid.QuerySubsAmtResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.subpaid.QuerySubsAmtFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QuerySubsAmtFacade
 * @apiName execute()
 * @apiDescription 认缴金额查询接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金代码
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK123456",
 *     "fundCode": "000001"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.subTotalAmt 认缴总金额
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "subTotalAmt": "10000.00"
 *     },
 *     "description": "成功"
 * }
 */
public interface QuerySubsAmtFacade extends BaseFacade<QuerySubsAmtRequest, QuerySubsAmtResponse> {
} 