package com.howbuy.dtms.order.client.facade.query.order;

import com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo.QueryHwDealOrderDtlListRequest;
import com.howbuy.dtms.order.client.domain.response.hkdealorder.dubbo.QueryHwDealOrderDtlListResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.order.QueryHwDealOrderDtlListFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryHwDealOrderDtlListFacade
 * @apiName execute()
 * @apiDescription QueryHwDealOrderDtlListFacade
 * @apiParam (请求参数) {Array} dealDtlNoList 订单明细号
 * @apiParam (请求参数) {String} startDate 预计上报日开始
 * @apiParam (请求参数) {String} endDate 预计上报日结束
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {Array} businessCodes 中台业务码 多选
 * @apiParam (请求参数) {String} startOpenDate 开放日 开始
 * @apiParam (请求参数) {String} endOpenDate 开放日 结束
 * @apiParam (请求参数) {Array} fundManCodes 管理人
 * @apiParam (请求参数) {Array} fundCodes 基金代码
 * @apiParam (请求参数) {String} dealNo 订单号
 * @apiParam (请求参数) {String} dealDtlNo 订单明细号
 * @apiParam (请求参数) {Array} fundCategory 基金类别
 * @apiParam (请求参数) {Array} appStatusList 申请状态
 * @apiParam (请求参数) {Array} submitStatusList 上报状态
 * @apiParam (请求参数) {Array} ackStatusList 确认状态
 * @apiParam (请求参数) {String} redeemType 赎回方式
 * @apiParam (请求参数) {Array} redeemDirection 回款方向
 * @apiParam (请求参数) {Array} extOptionList 展期选项 支持多选
 * @apiParam (请求参数) {String} fundTxAccType 0-非全委 1-全委
 * @apiParam (请求参数) {Number} page
 * @apiParam (请求参数) {Number} size
 * @apiParam (请求参数) {String} txCode 交易码
 * @apiParam (请求参数) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求参数) {String} appTm 申请时间 HHmmss
 * @apiParam (请求参数) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求参数) {String} outletCode 网点号
 * @apiParam (请求参数) {String} ipAddress ipAddress
 * @apiParam (请求参数) {String} externalDealNo 外部订单号
 * @apiParam (请求参数) {String} macAddress MAC地址
 * @apiParam (请求参数) {String} deviceSerialNo 设备序列号
 * @apiParam (请求参数) {String} deviceModel 设备型号
 * @apiParam (请求参数) {String} deviceName 设备名称
 * @apiParam (请求参数) {String} systemVersion 系统版本号
 * @apiParamExample 请求参数示例
 * fundCodes=1GbbvTX&externalDealNo=pE0BZF8xJm&endDate=VEP&businessCodes=JrgJe5LNh&deviceName=Gf&systemVersion=pTlySFCVA8&redeemDirection=np&endOpenDate=3k0&fundCategory=vLGP&appDt=J2H0&ackStatusList=yQCHaNair&hkCustNo=PLODk0D&ipAddress=12Iz7PmK&dealNo=KTDgYCbaQ&startOpenDate=KcvZ8vW5hQ&extOptionList=AMg&appTm=E1jg&macAddress=5ff&appStatusList=i&size=7877&deviceSerialNo=xI&dealDtlNoList=TUDs&fundManCodes=HlEQXQBiuX&redeemType=h0l6QNn&deviceModel=zs&submitStatusList=j&page=5061&txCode=3j&startDate=uN&outletCode=HhlxzG0&dealDtlNo=h593t0Jp7t&tradeChannel=iW
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Number} data.total 总记录数
 * @apiSuccess (响应结果) {Number} data.pages 总页数
 * @apiSuccess (响应结果) {Array} data.list 结果
 * @apiSuccess (响应结果) {String} data.list.custName 客户中文名称
 * @apiSuccess (响应结果) {Number} data.list.id ID
 * @apiSuccess (响应结果) {Number} data.list.dealDtlNo 订单明细号
 * @apiSuccess (响应结果) {Number} data.list.dealNo 订单号
 * @apiSuccess (响应结果) {String} data.list.hkCustNo 香港客户号
 * @apiSuccess (响应结果) {String} data.list.cpAcctNo 资金账号
 * @apiSuccess (响应结果) {String} data.list.fundCode 基金代码
 * @apiSuccess (响应结果) {String} data.list.fundName 基金名称
 * @apiSuccess (响应结果) {String} data.list.fundAbbr 基金简称
 * @apiSuccess (响应结果) {String} data.list.mainFundCode 主基金代码
 * @apiSuccess (响应结果) {String} data.list.fundCategory 基金类别 1-公募、2-私募、9-其他
 * @apiSuccess (响应结果) {String} data.list.fundRiskLevel 基金风险等级
 * @apiSuccess (响应结果) {String} data.list.redeemType 赎回方式 1-按份额、2-按金额
 * @apiSuccess (响应结果) {String} data.list.redeemDirectionList 赎回方向列表 1111；第一位：电汇；第二位：留账；第三位：海外储蓄罐；第四位：支票
 * @apiSuccess (响应结果) {String} data.list.middleBusiCode 中台业务代码 1120-认购、1122-申购、1124-赎回、1143-分红、1142-强赎、1144-强增、1145-强减、1134-非交易过户入、1135-非交易过户出、1151-基金终止、1150-基金清盘
 * @apiSuccess (响应结果) {Number} data.list.appAmt 申请金额
 * @apiSuccess (响应结果) {Number} data.list.netAppAmt 净申请金额
 * @apiSuccess (响应结果) {Number} data.list.appVol 申请份额
 * @apiSuccess (响应结果) {Number} data.list.estimateFee 预估手续费
 * @apiSuccess (响应结果) {Number} data.list.prebookDiscount 预约折扣
 * @apiSuccess (响应结果) {Number} data.list.discountRate 折扣率
 * @apiSuccess (响应结果) {Number} data.list.feeRate 手续费率
 * @apiSuccess (响应结果) {String} data.list.feeCalMode 费用计算方式 0-外扣法、1-内扣法
 * @apiSuccess (响应结果) {Number} data.list.fee 手续费
 * @apiSuccess (响应结果) {Number} data.list.ackAmt 确认金额
 * @apiSuccess (响应结果) {Number} data.list.ackVol 确认份额
 * @apiSuccess (响应结果) {Number} data.list.ackNav 确认净值
 * @apiSuccess (响应结果) {String} data.list.currency 币种
 * @apiSuccess (响应结果) {String} data.list.appStatus 申请状态 0-申请成功；1-申请失败；2-自行撤销；3-强制取消
 * @apiSuccess (响应结果) {String} data.list.ackStatus 确认状态 0-无需确认；1-未确认；2-确认中；3-部分确认；4-确认成功；5-确认失败
 * @apiSuccess (响应结果) {Number} data.list.transferPrice 转让价格
 * @apiSuccess (响应结果) {String} data.list.fundDivMode 分红方式 0-红利再投 1-现金分红 2-N/A不适用
 * @apiSuccess (响应结果) {String} data.list.openDt 开放日期
 * @apiSuccess (响应结果) {String} data.list.payEndDt 打款截止日期
 * @apiSuccess (响应结果) {String} data.list.payEndTm 打款截止时间
 * @apiSuccess (响应结果) {String} data.list.productPayEndDt 产品打款截止日期
 * @apiSuccess (响应结果) {String} data.list.productPayEndDm 产品打款截止时间
 * @apiSuccess (响应结果) {String} data.list.taTradeDt TA交易日期
 * @apiSuccess (响应结果) {String} data.list.ackDt 确认日期
 * @apiSuccess (响应结果) {String} data.list.taAckNo TA确认流水号
 * @apiSuccess (响应结果) {String} data.list.submitStatus 上报状态 0-未上报、1-上报中、2-上报成功、3-需重新上报、4-撤回上报、5-无需上报
 * @apiSuccess (响应结果) {String} data.list.preSubmitTaDt 预计上报日期
 * @apiSuccess (响应结果) {String} data.list.preSubmitTaTm 预计上报时间
 * @apiSuccess (响应结果) {String} data.list.fundManCode 管理人代码
 * @apiSuccess (响应结果) {String} data.list.extOption 展期选项 1-本金展期,2-本金+收益展期,3-收益展期,4-到期赎回
 * @apiSuccess (响应结果) {String} data.list.extControlType 展期控制类型:1-月
 * @apiSuccess (响应结果) {String} data.list.extControlNum 展期控制数
 * @apiSuccess (响应结果) {String} data.list.cancelDate 撤单时间 yyyyMMDDhhmmss
 * @apiSuccess (响应结果) {String} data.list.cancelCause 撤单原因
 * @apiSuccess (响应结果) {String} data.list.fundTxAcctNo 基金交易账号
 * @apiSuccess (响应结果) {String} data.list.intoFundCode 转入基金代码
 * @apiSuccess (响应结果) {String} data.list.intoFundAbbr 转入基金简称
 * @apiSuccess (响应结果) {Number} data.list.intoAckAmt 转入确认金额
 * @apiSuccess (响应结果) {Number} data.list.intoAckVol 转入确认份额
 * @apiSuccess (响应结果) {Number} data.list.intoAckNav 转入确认净值
 * @apiSuccess (响应结果) {String} data.list.intoFundTxAcctNo 转入基金交易账号
 * @apiSuccess (响应结果) {Number} data.list.relationalDealDtlNo 关联订单明细号
 * @apiSuccess (响应结果) {String} data.list.volDtlNo 份额明细流水号
 * @apiSuccess (响应结果) {String} data.list.recStat 记录状态 0-正常；1-已删除
 * @apiSuccess (响应结果) {Number} data.list.createTimestamp 创建时间戳
 * @apiSuccess (响应结果) {Number} data.list.updateTimestamp 更新时间戳
 * @apiSuccessExample 响应结果示例
 * {"code":"v","data":{"total":9204,"pages":8205,"list":[{"discountRate":7605.528245141463,"appAmt":3638.1725253643817,"fee":1806.875479148422,"taTradeDt":"IKSK099RB","estimateFee":1252.8639747837967,"extControlNum":"AgrD0AJH8","intoAckAmt":3874.9938740885605,"createTimestamp":571296432688,"intoFundTxAcctNo":"xSd","fundCode":"KQK9D","appVol":5478.753179109156,"fundCategory":"lRgITN","id":8446,"ackVol":6404.256146418613,"submitTaDt":"KWWXWfh","intoFundAbbr":"884ze7yIj","taAckNo":"PEv","preSubmitTaDt":"amUW50N6z","intoAckNav":9747.586425855594,"dealNo":918,"appStatus":"KUBgYfR","cancelCause":"G8z5kB4m","preSubmitTaTm":"KAoW","fundRiskLevel":"Hv8w1","ackStatus":"5LcrYWneMP","openDt":"d","payEndDt":"XrjEJ1","fundName":"pGMh","netAppAmt":6036.003774422663,"dealDtlNo":8558,"transferPrice":7157.423556349702,"extControlType":"80","cancelDate":"cbWlGFjB","relationalDealDtlNo":8134,"fundDivMode":"8z","feeRate":725.4561044997398,"fundTxAcctNo":"thR","fundAbbr":"CI","ackAmt":9634.543409797054,"payEndTm":"NhywOG","mainFundCode":"FUhg49","cpAcctNo":"LM6yikcM6a","currency":"qr","redeemDirectionList":"8KO","feeCalMode":"FW","prebookDiscount":1686.5964019387725,"submitStatus":"Xtk","fundManCode":"Dekc","intoFundCode":"51IK7W75","middleBusiCode":"3gzVB9","hkCustNo":"BT1w","custName":"BeiX7mYje","updateTimestamp":3374115168885,"ackNav":7335.571809121396,"volDtlNo":"OSTC6Ie","redeemType":"oJ","extOption":"mAv","intoAckVol":4287.165798646578,"ackDt":"TiJh","recStat":"2GSXh0wng"}]},"description":"aXX4"}
 */
public interface QueryHwDealOrderDtlListFacade extends BaseFacade<QueryHwDealOrderDtlListRequest, QueryHwDealOrderDtlListResponse> {
}
