/**
 * Copyright (c) 2024, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.finreceipt;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/19 20:14
 * @since JDK 1.8
 */
@Data
public class QueryFinReceiptResponse implements Serializable {

    /**
     * 一账通账号
     */
    private String hbOneNo;

    /**
     * 购买待退款订单数
     */
    private Integer buyUnrefundedPiece;
    /**
     * 赎回待回款订单数
     */
    private Integer redeemUnrefundedPiece;

    /**
     * 待付款订单
     */
    private List<String> unpaidList;
    /**
     * 待确认订单
     */
    private List<String> unconfirmedList;

}