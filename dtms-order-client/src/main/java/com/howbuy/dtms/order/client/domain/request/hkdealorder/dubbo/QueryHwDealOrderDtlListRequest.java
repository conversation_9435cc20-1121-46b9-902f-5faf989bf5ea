/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.hkdealorder.dubbo;

import com.howbuy.dtms.order.client.domain.request.PageRequest;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/12/1 18:06
 * @since JDK 1.8
 */
@Data
public class QueryHwDealOrderDtlListRequest extends PageRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 订单明细号
     */
    private List<String> dealDtlNoList;

    /**
     * 预计上报日开始
     */
    private String startDate;

    /**
     * 预计上报日结束
     */
    private String endDate;

    /**
     * 交易日期开始
     */
    private String startTradeDate;

    /**
     * 交易日期结束
     */
    private String endTradeDate;

    /**
     * 确认日期开始
     */
    private String startAckDt;

    /**
     * 确认日期结束
     */
    private String endAckDt;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 中台业务码 多选
     */
    private List<String> businessCodes;

    /**
     * 开放日 开始
     */
    private String startOpenDate;

    /**
     * 开放日 结束
     */
    private String endOpenDate;

    /**
     * 管理人
     */
    private List<String> fundManCodes;

    /**
     * 基金代码
     */
    private List<String> fundCodes;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 订单明细号
     */
    private String dealDtlNo;

    /**
     * 基金类别
     */
    private List<String> fundCategory;

    /**
     * 申请状态
     */
    private List<String> appStatusList;

    /**
     * 上报状态
     */
    private List<String> submitStatusList;

    /**
     * 确认状态
     */
    private List<String> ackStatusList;

    /**
     * 赎回方式
     */
    private String redeemType;

    /**
     * 回款方向
     */
    private List<String> redeemDirection;

    /**
     * 展期选项 支持多选
     */
    private List<String> extOptionList;

    /**
     * 0-非全委 1-全委
     */
    private String fundTxAccType;

}