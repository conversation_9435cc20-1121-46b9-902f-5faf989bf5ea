/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/6/30 15:12
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageRequest extends BaseRequest {
    private int page = 1;

    private int size = 20;
}
