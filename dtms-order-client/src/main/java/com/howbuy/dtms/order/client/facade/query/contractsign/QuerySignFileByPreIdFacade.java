/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.query.contractsign;

import com.howbuy.dtms.order.client.domain.request.contractsign.QueryPrebookSignContractRequest;
import com.howbuy.dtms.order.client.domain.response.contractsign.PrebookSignContractVO;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @description: 根据预约单号查询签约文件接口
 * <AUTHOR>
 * @date 2025/6/9 15:42
 * @since JDK 1.8
 */

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.contractsign.QuerySignFileByPreIdFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QuerySignFileByPreIdFacade
 * @apiName execute()
 * @apiDescription 根据预约单号查询签约文件接口
 * @apiParam (请求参数) {String} prebookId 预约单号
 * @apiParamExample 请求参数示例
 * {
 *     "prebookId": "PRE202412270001"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.contracts 合同详情列表
 * @apiSuccess (响应结果) {String} data.contracts.fileType 文件代码
 * @apiSuccess (响应结果) {String} data.contracts.fileName 文件名称
 * @apiSuccess (响应结果) {String} data.contracts.fileTypeDesc 文件类型描述
 * @apiSuccess (响应结果) {String} data.contracts.filePath 文件路径
 * @apiSuccess (响应结果) {String} data.contracts.signFlag 协议明细签署状态(0-未签署、1-已签署)
 * @apiSuccess (响应结果) {String} data.contracts.signDate 协议明细签署时间
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "contracts": [
 *             {
 *                 "fileType": "001",
 *                 "fileName": "基金认购协议.pdf",
 *                 "fileTypeDesc": "基金认购协议",
 *                 "filePath": "/contract/files/PRE202412270001_001.pdf",
 *                 "signFlag": "1",
 *                 "signDate": "2024-12-27 10:30:00"
 *             },
 *             {
 *                 "fileType": "002",
 *                 "fileName": "风险提示书.pdf",
 *                 "fileTypeDesc": "风险提示书",
 *                 "filePath": "/contract/files/PRE202412270001_002.pdf",
 *                 "signFlag": "0",
 *                 "signDate": ""
 *             }
 *         ]
 *     },
 *     "description": "查询成功"
 * }
 */
public interface QuerySignFileByPreIdFacade extends BaseFacade<QueryPrebookSignContractRequest, PrebookSignContractVO> {

}
