package com.howbuy.dtms.order.client.facade.query.balance;


import com.howbuy.dtms.order.client.domain.request.hkbalance.QueryBalanceRequest;
import com.howbuy.dtms.order.client.domain.response.hkbalance.dubbo.QueryBalanceResponse;
import com.howbuy.dtms.order.client.facade.BaseFacade;



/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.query.balance.QueryBalanceFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup QueryBalanceFacade
 * @apiDescription 查询持仓详情页数据
 * @apiParam (请求体) {String} fundCode 基金代码
 * @apiParam (请求体) {String} hbOneNo 一账通号
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {Array} disCodeList 分销机构
 * @apiParam (请求体) {String} disPlayCurrency 前端展示币种 默认人民币
 * @apiParam (请求体) {String} txCode 交易码
 * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
 * @apiParam (请求体) {String} appTm 申请时间 HHmmss
 * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
 * @apiParam (请求体) {String} outletCode 网点号
 * @apiParam (请求体) {String} ipAddress ipAddress
 * @apiParam (请求体) {String} externalDealNo 外部订单号
 * @apiParam (请求体) {String} macAddress MAC地址
 * @apiParam (请求体) {String} deviceSerialNo 设备序列号
 * @apiParam (请求体) {String} deviceModel 设备型号
 * @apiParam (请求体) {String} deviceName 设备名称
 * @apiParam (请求体) {String} systemVersion 系统版本号
 * @apiParamExample 请求体示例
 * {"externalDealNo":"Mr","hkCustNo":"Eexf0K","ipAddress":"Y6wVm7OXh2","disPlayCurrency":"sq9PcyKI5","deviceName":"pJD","systemVersion":"EcoOEWg6","appTm":"9Pndw","macAddress":"uw6x","disCodeList":["6tgRWm"],"deviceSerialNo":"eUWhq","fundCode":"t4PgR","appDt":"LHnw5PatO","deviceModel":"qR","txCode":"DevN2g","outletCode":"FtwxP","tradeChannel":"b6Fimo"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {Array} data.disCodeList 分销机构号列表-股权直销改造
 * @apiSuccess (响应结果) {String} data.txAcctNo 交易账号
 * @apiSuccess (响应结果) {Number} data.totalMarketValue 总市值
 * @apiSuccess (响应结果) {Number} data.disPlayCurrencyTotalMarketValue 展示币种换算后总市值
 * @apiSuccess (响应结果) {Number} data.totalUnconfirmedAmt 在途总金额
 * @apiSuccess (响应结果) {Number} data.totalDisCurUnconfirmedAmt 展示币种在途总金额
 * @apiSuccess (响应结果) {Number} data.totalUnconfirmedNum 待确认笔数
 * @apiSuccess (响应结果) {Number} data.redeemUnconfirmedNum 赎回待确认笔数
 * @apiSuccess (响应结果) {Number} data.totalCurrentAsset 当前总收益
 * @apiSuccess (响应结果) {String} data.totalIncomCalStat 总收益计算状态: 0-计算中;1-计算成功
 * @apiSuccess (响应结果) {Number} data.totalCashCollection 总回款
 * @apiSuccess (响应结果) {String} data.hasHZProduct 是否持有好臻产品 0:没有,1:有
 * @apiSuccess (响应结果) {String} data.hasHKProduct 是否持有好买香港产品  0:没有,1:有
 * @apiSuccess (响应结果) {Array} data.balanceList 持仓明细列表
 * @apiSuccess (响应结果) {String} data.balanceList.disCode 分销代码
 * @apiSuccess (响应结果) {Array} data.balanceList.disCodeList 分销代码列表
 * @apiSuccess (响应结果) {String} data.balanceList.productCode 产品代码
 * @apiSuccess (响应结果) {String} data.balanceList.subProductCode 子产品代码
 * @apiSuccess (响应结果) {String} data.balanceList.productName 产品名称
 * @apiSuccess (响应结果) {String} data.balanceList.subProductName 子产品名称
 * @apiSuccess (响应结果) {String} data.balanceList.productType 产品类型
 * @apiSuccess (响应结果) {String} data.balanceList.productSubType 产品子类型(好买产品线)
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceVol 总份额
 * @apiSuccess (响应结果) {Number} data.balanceList.unconfirmedVol 待确认份额
 * @apiSuccess (响应结果) {Number} data.balanceList.unconfirmedAmt 待确认金额
 * @apiSuccess (响应结果) {String} data.balanceList.currency 币种
 * @apiSuccess (响应结果) {Number} data.balanceList.nav 净值
 * @apiSuccess (响应结果) {String} data.balanceList.navDt 净值日期
 * @apiSuccess (响应结果) {String} data.balanceList.navDivFlag 净值分红标识 0-否，1-是
 * @apiSuccess (响应结果) {Number} data.balanceList.marketValue 市值
 * @apiSuccess (响应结果) {Number} data.balanceList.currencyMarketValue 当前币种的市值
 * @apiSuccess (响应结果) {Number} data.balanceList.disCurMarketValue 展示币种对应的市值
 * @apiSuccess (响应结果) {String} data.balanceList.scaleType 销售类型: 1-直销;2-代销
 * @apiSuccess (响应结果) {String} data.balanceList.hkSaleFlag 好买香港代销标识: 0-否; 1-是
 * @apiSuccess (响应结果) {String} data.balanceList.StageEstablishFlag 分期成立标识(证券类有此标识:0-否,1-是)
 * @apiSuccess (响应结果) {String} data.balanceList.fractionateCallFlag 分次call标识(股权类有此标识:0-否,1-是)
 * @apiSuccess (响应结果) {String} data.balanceList.fundCXQXStr 产品存续期限(类似于5+3+2这种说明)
 * @apiSuccess (响应结果) {Number} data.balanceList.netBuyAmount 净购买金额(投资成本)
 * @apiSuccess (响应结果) {Number} data.balanceList.currencyNetBuyAmount 净购买金额(投资成本)(当前币种)
 * @apiSuccess (响应结果) {Number} data.balanceList.paidInAmt 认缴金额
 * @apiSuccess (响应结果) {String} data.balanceList.incomeDt 收益日期
 * @apiSuccess (响应结果) {String} data.balanceList.incomeCalStat 0-计算中；1-计算完成
 * @apiSuccess (响应结果) {Number} data.balanceList.currentAsset 当前收益(人民币）
 * @apiSuccess (响应结果) {Number} data.balanceList.currentAssetCurrency 当前收益（当前币种）
 * @apiSuccess (响应结果) {Number} data.balanceList.accumIncome 累计收益
 * @apiSuccess (响应结果) {Number} data.balanceList.accumIncomeRmb 累计收益(人民币)
 * @apiSuccess (响应结果) {Number} data.balanceList.accumRealizedIncome 累计已实现收益
 * @apiSuccess (响应结果) {Number} data.balanceList.accumRealizedIncomeRmb 累计已实现收益人民币
 * @apiSuccess (响应结果) {String} data.balanceList.rePurchaseFlag 是否复构 0-否 1-是
 * @apiSuccess (响应结果) {String} data.balanceList.benchmark 业绩比较基准
 * @apiSuccess (响应结果) {String} data.balanceList.benchmarkType 业绩比较基准类型：0-业绩比较基准（年化） 1-业绩报酬计提基准（年化）
 * @apiSuccess (响应结果) {String} data.balanceList.valueDate 起息日
 * @apiSuccess (响应结果) {String} data.balanceList.dueDate 到期日
 * @apiSuccess (响应结果) {String} data.balanceList.standardFixedIncomeFlag 标准固收标识(固收类有此标识:0-非标准固收，1-标准固收，2-现金管理， 3-券商集合理财 4-纯债产品)
 * @apiSuccess (响应结果) {String} data.balanceList.investmentHorizon
 * @apiSuccess (响应结果) {String} data.balanceList.cooperation
 * @apiSuccess (响应结果) {String} data.balanceList.crisisFlag
 * @apiSuccess (响应结果) {Number} data.balanceList.yieldIncome
 * @apiSuccess (响应结果) {String} data.balanceList.yieldIncomeDt
 * @apiSuccess (响应结果) {Number} data.balanceList.copiesIncome 万份收益
 * @apiSuccess (响应结果) {String} data.balanceList.hwSaleFlag 是否海外产品 0-否 1-是
 * @apiSuccess (响应结果) {String} data.balanceList.regDt 登记日期
 * @apiSuccess (响应结果) {String} data.balanceList.oneStepType 一级监管分类
 * @apiSuccess (响应结果) {String} data.balanceList.twoStepType 二级监管分类
 * @apiSuccess (响应结果) {String} data.balanceList.secondStepType 三级监管分类
 * @apiSuccess (响应结果) {String} data.balanceList.productSaleType 产品销售类型 0-好买 1-海外 2-其他
 * @apiSuccess (响应结果) {String} data.balanceList.naProductFeeType NA产品收费类型 10201-好买收费 0-管理人收费
 * @apiSuccess (响应结果) {Number} data.balanceList.receivManageFee 累计应收管理费
 * @apiSuccess (响应结果) {Number} data.balanceList.receivPreformFee 累计应收业绩报酬
 * @apiSuccess (响应结果) {Number} data.balanceList.currencyMarketValueExFee NA产品费后市值(当前币种， 减去当前累计应收管理费和累计应收业绩报酬)
 * @apiSuccess (响应结果) {Number} data.balanceList.marketValueExFee NA产品费后市值(人民币， 减去当前累计应收管理费和累计应收业绩报酬)
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceIncomeNew 当前收益（股权新算法）不含费
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceIncomeNewRmb 当前收益（股权新算法）不含费-人民币
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceFactor 平衡因子
 * @apiSuccess (响应结果) {String} data.balanceList.convertFinish 平衡因子转换完成 1-是 0-否
 * @apiSuccess (响应结果) {String} data.balanceList.balanceFactorDate 平衡因子日期
 * @apiSuccess (响应结果) {Number} data.balanceList.yieldRate 收益率
 * @apiSuccess (响应结果) {Number} data.balanceList.accumIncomeNew 累计收益（股权固收新算法）不含费
 * @apiSuccess (响应结果) {Number} data.balanceList.accumIncomeNewRmb 累计收益（股权固收新算法）不含费-人民币
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceCost 持仓总成本(人民币）
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceCostCurrency 持仓总成本（当前币种）
 * @apiSuccess (响应结果) {Number} data.balanceList.dailyAsset 日收益(人民币）
 * @apiSuccess (响应结果) {Number} data.balanceList.dailyAssetCurrency 日收益（当前币种）
 * @apiSuccess (响应结果) {Number} data.balanceList.cashCollection 私募股权回款
 * @apiSuccess (响应结果) {Number} data.balanceList.currencyCashCollection 私募股权回款(当前币种)
 * @apiSuccess (响应结果) {Number} data.balanceList.accumYieldRate
 * @apiSuccess (响应结果) {Number} data.balanceList.accumCost
 * @apiSuccess (响应结果) {Number} data.balanceList.accumCostRmb
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceFloatIncome
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceFloatIncomeRmb
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceFloatIncomeRate
 * @apiSuccess (响应结果) {Number} data.balanceList.dayAssetRate
 * @apiSuccess (响应结果) {Number} data.balanceList.dayIncomeGrowthRate
 * @apiSuccess (响应结果) {Number} data.balanceList.accumCostNew
 * @apiSuccess (响应结果) {Number} data.balanceList.accumCostRmbNew
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceAmt
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceAmtRmb
 * @apiSuccess (响应结果) {Number} data.balanceList.accumCollection
 * @apiSuccess (响应结果) {Number} data.balanceList.accumCollectionRmb
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceAmtExFee
 * @apiSuccess (响应结果) {Number} data.balanceList.balanceAmtExFeeRmb
 * @apiSuccess (响应结果) {String} data.balanceList.sxz
 * @apiSuccess (响应结果) {Number} data.balanceList.currentIncome 当前收益(当前币种）
 * @apiSuccess (响应结果) {Number} data.balanceList.currentIncomeRmb 当前收益(人民币）
 * @apiSuccess (响应结果) {Number} data.balanceList.currentAccumIncome 当前累计收益(当前币种）
 * @apiSuccess (响应结果) {Number} data.balanceList.currentAccumIncomeRmb 当前累计收益(人民币）
 * @apiSuccess (响应结果) {String} data.balanceList.stageFlag 是否拆单产品 1-是
 * @apiSuccess (响应结果) {String} data.balanceList.establishDt 产品成立日期
 * @apiSuccess (响应结果) {String} data.balanceList.assetUpdateDate 收益计算日期
 * @apiSuccess (响应结果) {Number} data.balanceList.unitBalanceCostExFee 单位持仓成本去费
 * @apiSuccess (响应结果) {Number} data.balanceList.unitBalanceCostExFeeRmb 单位持仓成本去费(人民币)
 * @apiSuccess (响应结果) {String} data.balanceList.ownershipTransferIdentity 股权转让标识
 * @apiSuccess (响应结果) {String} data.balanceList.sfhwcxg 是否海外储蓄罐(1:是;0:否)
 * @apiSuccess (响应结果) {String} data.balanceList.cpqxsm 股权产品期限说明
 * @apiSuccess (响应结果) {String} data.balanceList.navDisclosureType 净值披露方式(1-净值,2-份额收益)--持仓特殊产品指标控制需求新增 20221122
 * @apiSuccess (响应结果) {String} data.balanceList.abnormalFlag 对于 阳光私募/非货基型券商集合 产品，若【最新净值日期】比第一笔交易的【确认日期】早3个月以上      异常标志(0-否 1-是)--持仓特殊产品指标控制需求新增 20221122
 * @apiSuccess (响应结果) {String} data.balanceList.marketValueCtl 人民币市值-是否控制表人为置空(0-否 1-是)
 * @apiSuccess (响应结果) {String} data.balanceList.currencyMarketValueCtl 当前币种市值-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.currencyMarketValueExFeeCtl NA产品费后市值（当前币种）-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.marketValueExFeeCtl NA产品费后市值（人民币）-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.currentAssetCtl 人民币收益-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.currentAssetCurrencyCtl 当前币种收益-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.dailyAssetCtl 人民币日收益-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.dailyAssetCurrencyCtl 当前币种日收益-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.accumIncomeCtl 累计收益-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.accumIncomeRmbCtl 累计收益人民币-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.accumRealizedIncomeCtl 累计已实现收益-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.accumRealizedIncomeRmbCtl 累计已实现收益人民币-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.balanceIncomeNewCtl 当前收益（股权新算法）不含费-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.balanceIncomeNewRmbCtl 当前收益（股权新算法）不含费人民币-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.accumIncomeNewCtl 累计收益（股权固收新算法）不含费-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.accumIncomeNewRmbCtl 累计收益（股权固收新算法）不含费人民币-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.yieldRateCtl 收益率-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.balanceVolCtl 份额-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.unconfirmedVolCtl 待确认份额-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.navCtl 净值-是否控制表人为置空
 * @apiSuccess (响应结果) {String} data.balanceList.qianXiFlag 是否为千禧年产品 0-否、1-是
 * @apiSuccess (响应结果) {Number} data.balanceList.unPaidInAmt 待投金额（人民币）
 * @apiSuccess (响应结果) {Number} data.balanceList.currencyUnPaidInAmt 待投金额（当前币种）
 * @apiSuccess (响应结果) {Array} data.unconfirmeProducts 在途数据
 * @apiSuccess (响应结果) {String} data.unconfirmeProducts.fundCode 产品代码
 * @apiSuccess (响应结果) {String} data.unconfirmeProducts.productType 产品类型
 * @apiSuccess (响应结果) {String} data.unconfirmeProducts.productSubType 产品子类型
 * @apiSuccess (响应结果) {Number} data.unconfirmeProducts.unconfirmedAmt 待确认金额(人民币)
 * @apiSuccess (响应结果) {String} data.unconfirmeProducts.hkSaleFlag 好买香港代销标识: 0-否; 1-是
 * @apiSuccess (响应结果) {String} data.unconfirmeProducts.disCode 销售渠道
 * @apiSuccessExample 响应结果示例
 * {"code":"VSSnSII","data":{"totalDisCurUnconfirmedAmt":4063.0525954568798,"hasHZProduct":"ZqmR","totalUnconfirmedAmt":5018.1638173891615,"totalUnconfirmedNum":9076,"totalCashCollection":7626.77185900792,"totalMarketValue":4301.4592094170985,"disPlayCurrencyTotalMarketValue":3672.5569526837266,"disCodeList":["UfMj"],"unconfirmeProducts":[{"fundCode":"IIHCi7","unconfirmedAmt":4592.406880213158,"disCode":"Cx7x","hkSaleFlag":"P0o","productSubType":"iBbCos78","productType":"Yx"}],"txAcctNo":"QCz9","balanceList":[{"balanceCostCurrency":381.96603353420676,"accumIncomeRmbCtl":"JRb","dailyAsset":4050.8164205615094,"currencyCashCollection":6068.87006401441,"disCode":"Rg7onzJMCA","dailyAssetCurrency":4238.052237003891,"accumIncomeNewRmbCtl":"sUY5rBrFhZ","balanceIncomeNewRmbCtl":"k","productSubType":"v7HC","accumRealizedIncomeCtl":"ciS5rDA","productName":"N","balanceIncomeNewRmb":760.5559547170337,"currentAsset":1373.8419430457338,"oneStepType":"qGleo5","accumIncomeNewRmb":8902.446855074824,"accumYieldRate":5779.904552888258,"productSaleType":"ehUo5DiA","cashCollection":8133.085194916375,"currentAccumIncome":6261.647378553117,"balanceFloatIncomeRate":5287.198782670222,"cooperation":"ZduJjF","nav":594.1459772333025,"navDt":"NWwndBYe","accumRealizedIncome":9636.161780433526,"abnormalFlag":"KWnK","unconfirmedVolCtl":"cgIjVm3H","dayAssetRate":2642.9841630924043,"benchmark":"Xi9sr9gL","regDt":"aOeDG0u","netBuyAmount":545.6894970268921,"balanceAmtExFeeRmb":5024.541339142027,"balanceFloatIncome":6348.439466100883,"accumIncomeNew":8535.111637425778,"currencyMarketValueExFee":9065.373269858275,"unPaidInAmt":7242.548749645191,"dayIncomeGrowthRate":1845.9554308523118,"marketValueExFeeCtl":"2YLWLSydkF","establishDt":"yGWgn","currencyMarketValue":7757.687819310029,"navDisclosureType":"sPV","assetUpdateDate":"Nbq","currency":"d4r","balanceFactor":9937.009276871111,"accumIncomeRmb":7440.562478911087,"hwSaleFlag":"OAI","standardFixedIncomeFlag":"NG","yieldIncome":9176.955301235512,"balanceVolCtl":"u62n4i","balanceIncomeNew":1900.6171881215882,"currentAccumIncomeRmb":1990.0610629239911,"naProductFeeType":"jc","qianXiFlag":"FeGpnI1u","incomeCalStat":"o4iGGb6D1","yieldRate":4489.676778665661,"accumCollectionRmb":9995.388594222617,"productCode":"4WuE5I5s96","paidInAmt":360.24734776907485,"currentAssetCurrency":3108.336295299168,"accumCollection":5532.878078289207,"copiesIncome":414.81172089351537,"dailyAssetCtl":"gKVWDwFm","accumIncomeNewCtl":"RyzdCjn","crisisFlag":"xX","dueDate":"xw7N","investmentHorizon":"0cOzwbhsn3","marketValueExFee":5853.860342656887,"disCodeList":["9F0FOiAo"],"accumCost":100.01029242108439,"balanceAmtExFee":4052.6441205612487,"StageEstablishFlag":"IXALgs","incomeDt":"MtmJXp1k5","balanceAmtRmb":492.7606937444351,"balanceIncomeNewCtl":"pm","receivPreformFee":2611.361260769087,"accumIncome":5942.474712444806,"yieldRateCtl":"9Duxyl","currencyNetBuyAmount":3261.2377683188356,"currencyUnPaidInAmt":797.2464699154181,"rePurchaseFlag":"mDkMul","ownershipTransferIdentity":"p7ZoUZKk1","unitBalanceCostExFee":7423.688525851164,"sxz":"LaO3H7J","scaleType":"CShATG31m","currentAssetCtl":"786sjvfP","currencyMarketValueCtl":"AFZhwODG7n","subProductName":"mJ8tLOyD","navCtl":"5ZfRnh","benchmarkType":"AAFOwjcp","accumIncomeCtl":"IqyyYf3Pw","balanceFactorDate":"Xq3Vlq","currencyMarketValueExFeeCtl":"K8","secondStepType":"EeQjGFbCY","navDivFlag":"kBF6ZXFe","fundCXQXStr":"IQCsFP","currentIncome":1855.442187597046,"balanceVol":3183.207495630085,"marketValueCtl":"7K1R4SSXXG","disCurMarketValue":1539.21229697067,"balanceAmt":5165.671953923677,"balanceCost":4497.898822126926,"yieldIncomeDt":"4sY","balanceFloatIncomeRmb":466.49739712685266,"hkSaleFlag":"8Zmtq8","sfhwcxg":"Z","accumRealizedIncomeRmbCtl":"fhaDtmbn","productType":"5Hwk","accumCostRmbNew":6466.944510557625,"cpqxsm":"OSjCkrc","subProductCode":"d15rtl9lom","twoStepType":"qJ","accumCostRmb":1266.9449295604684,"dailyAssetCurrencyCtl":"jdfkzbW","marketValue":9781.350012371693,"valueDate":"dh5","receivManageFee":7834.572369870822,"accumRealizedIncomeRmb":5431.63082131027,"currentIncomeRmb":9491.087468798609,"unitBalanceCostExFeeRmb":8674.2276603169,"unconfirmedVol":1837.4571316271392,"unconfirmedAmt":4695.893047370499,"fractionateCallFlag":"JdNzWwdjm","currentAssetCurrencyCtl":"DO","stageFlag":"RDs","convertFinish":"9BBFNkWxOa","accumCostNew":3231.0644799782685}],"hasHKProduct":"U","totalCurrentAsset":2328.73808265176,"redeemUnconfirmedNum":4516,"totalIncomCalStat":"yrdaHF"},"description":"qftDiyvN"}
 */
public interface QueryBalanceFacade extends BaseFacade<QueryBalanceRequest, QueryBalanceResponse> {

}
