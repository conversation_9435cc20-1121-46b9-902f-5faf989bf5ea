/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.sell;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/11/8 16:08
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CounterPiggySellValidateRequest extends BaseRequest {

    private static final long serialVersionUID = 7257952427750697613L;
    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;

    /**
     * 资金账号 赎回方向为1-电汇时必填
     */
    private String cpAcctNo;

    /**
     * 基金交易账号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金交易账号", isRequired = true)
    private String fundTxAcctNo;

    /**
     * 赎回方式 1-按份额赎回；2-按金额赎回
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "赎回方式", isRequired = true)
    private String redeemMethod;

    /**
     * 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "赎回方向", isRequired = true)
    private String redeemDirection;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 预约单号
     */
    private String prebookDealNo;
}
