/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.contractsign;

import lombok.Data;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2023/5/22 17:35
 * @since JDK 1.8
 */
@Data
public class CustContractDtlSignVO implements Serializable {
    private static final long serialVersionUID = 1265507659289559263L;
    /**
     * 文件代码
     */
    private String fileType;
    /**
     * 文件名称
     */
    private String fileName;
    /**
     * 文件类型描述
     */
    private String fileTypeDesc;
    /**
     * 文件路径
     */
    private String filePath;
    /**
     * 协议明细签署状态	 0-未签署；1-已签署
     */
    private String signFlag;
    /**
     * 协议明细签署时间
     */
    private String signDate;



}