/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.buy;

import com.howbuy.commons.validator.MyValidation;
import com.howbuy.commons.validator.util.ValidatorTypeEnum;
import com.howbuy.dtms.order.client.domain.request.BaseRequest;
import com.howbuy.dtms.order.client.domain.request.OrderTxCodes;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 查询买入信息请求
 * <AUTHOR>
 * @date 2024/4/10 10:02
 * @since JDK 1.8
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryBuyInfoRequest extends BaseRequest {

    private static final long serialVersionUID = -3280832751905605785L;

    public QueryBuyInfoRequest() {
        this.setTxCode(OrderTxCodes.HW_ORDER_QUERY_BUY_INFO);
    }

    /**
     * 香港客户号
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "香港客户号", isRequired = true)
    private String hkCustNo;

    /**
     * 基金代码
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "基金代码", isRequired = true)
    private String fundCode;


    /**
     * 业务类型
     */
    @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "业务类型", isRequired = true)
    private String businessType;

    /**
     * 中台业务码
     */
    private String businessCode;

}
