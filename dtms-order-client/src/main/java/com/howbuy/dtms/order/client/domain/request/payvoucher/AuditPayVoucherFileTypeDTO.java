/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/8/2 18:09
 * @since JDK 1.8
 */

@Setter
@Getter
public class AuditPayVoucherFileTypeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 材料类型 dtms-order-展示的是文件类型、crm-trade-展示的文件类型id ftid
     */
    private String fileType;

    /**
     * 材料类型描述
     */
    private String fileTypeDesc;

    /**
     * 材料来源 0-dtms-order 1-crm-trade
     */
    private String fileSource;

    /**
     * 仅当 材料来源 = crm-trade时，有值。crm资料审核时，需要作为crm接口的入参
     */
    private String fileId;


    /**
     * 审核意见
     */
    private String remark;

    /**
     * 是否审核通过
     */
    private boolean passFlag;

}