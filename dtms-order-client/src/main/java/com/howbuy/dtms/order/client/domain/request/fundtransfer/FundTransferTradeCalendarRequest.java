/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.request.fundtransfer;

import com.howbuy.dtms.order.client.domain.request.BaseRequest;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/12/11 12:27
 * @since JDK 1.8
 */
public class FundTransferTradeCalendarRequest  extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 8408648917288589375L;
    /**
     * 转出基金
     */
    private String outFundCode;

    /**
     * 转入基金
     */
    private String inFundCode;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    public String getOutFundCode() {
        return outFundCode;
    }

    public void setOutFundCode(String outFundCode) {
        this.outFundCode = outFundCode;
    }

    public String getInFundCode() {
        return inFundCode;
    }

    public void setInFundCode(String inFundCode) {
        this.inFundCode = inFundCode;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }
}
