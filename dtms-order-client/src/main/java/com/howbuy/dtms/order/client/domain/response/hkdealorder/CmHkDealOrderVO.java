/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.hkdealorder;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/10/27 16:56
 * @since JDK 1.8
 */
@Data
public class CmHkDealOrderVO {

    //MIDDLE_BUSI_CODE hdod 中台业务单号
    private String middleBusiCode;
    //BUSINESS_TYPE hdo 业务类型
    private String businessType;
    //deal_no hdo 订单号
    private Long dealNo;
    //ORDER_STATUS  hdo 预约状态
    private String orderStatus;
    //PREBOOK_DEAL_NO hdo  预约单号
    private String prebookDealNo;
    //APP_STATUS hdod 订单状态
    private String appStatus;
    //认缴订单号
    //hk_custno hdo 香港客户号
    private String hkCustNo;
    //ID_NO_CIPHER  hdo 证件号码密文
    private String idNoCipher;
    //CUST_CHINESE_NAME hdo 中文名称
    private String custChineseName;
    //product_code 产品代码
    private String productCode;
    //PRODUCT_NAME hdo 产品名称
    private String productName;
    //MAIN_FUND_CODE hdod 母基金代码
    private String mainFundCode;
    //currency hdod 币种
    private String currency;
    //APP_AMT hdod 申请金额
    private BigDecimal appAmt;
    //APP_VOL hdod 申请份额
    private BigDecimal appVol;
    //FEE_RATE hdod 手续费率
    private BigDecimal feeRate;
    //DISCOUNT_RATE hdod 折扣费率
    private BigDecimal discountRate;
    //APP_DT  hdo  申请日期
    private String appDt;
    //dpp_tm hdo 申请时间
    private String appTm;
    //open_dt hdo  开放日期
    private String openDt;
    //PAYMENT_TYPE_LIST  hdod 支付方式列表
    private String paymentTypeList;
    //IS_AGREE_CURRENCY_EXCHANGE hdo  是否同意换汇 0-否 1-是
    private String isAgreeCurrencyExchange;
    //PAY_STATUS hdod 付款状态
    private String payStatus;
    //PAY_VOUCHER_STATUS hdod 打款凭证状态  0-无需上传、1-未上传、2-已上传、3-审核通过、4-审核不通过
    private String payVoucherStatus;
    //pay_end_dt  hdod 打款截止日期
    private String payEndDt;
    //pay_end_tm hdod 打款截止时间
    private String payEndTm;
    //ACTUAL_PAY_DT   hdod  实际打款日期
    private String actualPayDt;
    //ACTUAL_PAY_AMT  hdod 实际打款金额
    private BigDecimal actualPayAmt;
    //REDEEM_DIRECTION_LIST    hdod 赎回方向列表
    private String redeemDirectionList;
    //TRADE_CHANNEL hdo 交易渠道
    private String tradeChannel;
    //OUTLET_CODE hdo 网点
    private String outletCode;
    //FUND_DIV_MODE hdod  分红方式 0-红利再投；1-现金红利
    private String fundDivMode;
    //TA_TRADE_DT hdod TA交易日期
    private String taTradeDt;
    //ACK_DT hdod   确认日期
    private String ackDt;
    //ACK_VOL hdod  确认份额
    private BigDecimal ackVol;
    //ACK_amt hdod  确认金额
    private BigDecimal ackAmt;
    //fee hdod 确认手续费
    private BigDecimal fee;
    //ACK_NAV hdod 确认净值
    private BigDecimal ackNav;
    //memo hdo 备注
    private String memo;

}