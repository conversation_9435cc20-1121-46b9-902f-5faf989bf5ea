/**
 * Copyright (c) 2024, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.payvoucher;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2024/7/23 14:34
 * @since JDK 1.8
 */
@Getter
@Setter
public class PayVoucherDetailResponse implements Serializable {


    private static final long serialVersionUID = 3242777652786935458L;
    /**
     * 香港客户号
     */
    private String hkCustNo;


    /**
     * 香港客户号
     */
    private String voucherNo;

    /**
     * 上传打款凭证类型 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证
     */
    private String voucherType;

    /**
     * 交易订单号 类型为1-交易下单凭证时，订单号必传
     */
    private String tradeOrderNo;

    /**
     * 汇款资金账号
     */
    private String remitCpAcctNo;

    /**
     * 汇款币种
     */
    private String remitCurrency;

    /**
     * 汇款金额
     */
    private BigDecimal remitAmt;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行中文名称
     */
    private String bankChineseName;

    /**
     * 银行账户
     */
    private String bankAcct;


    /**
     * SWIFT代码
     */
    private String swiftCode;

    /**
     * 银行账户摘要
     */
    private String bankAcctDigest;

    /**
     * 银行账号掩码
     */
    private String bankAcctMask;

    /**
     * 1-柜台；4-Wap（小程序）；9-CRM-PC；10-CRM移动端；11-APP
     */
    private String tradeChannel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 文件列表
     */
    private List<PayVoucherFileVO> fileList;

    /**
     * 审核不通过原因
     */
    private PayVoucherAuditResultResponse auditReason;

    /**
     * 重复打款凭证
     */
    private String duplicateVoucher;

    /**
     * 材料来源
     */
    private String fileSource;
}