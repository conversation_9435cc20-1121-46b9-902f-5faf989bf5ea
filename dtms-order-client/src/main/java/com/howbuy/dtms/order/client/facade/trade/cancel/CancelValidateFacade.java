/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.facade.trade.cancel;

import com.howbuy.dtms.order.client.domain.request.cancelorder.CancelOrderValidateRequest;
import com.howbuy.dtms.order.client.facade.BaseFacade;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.cancel.CancelValidateFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup CancelValidateFacadeImpl
 * @apiName execute()
 * @apiDescription 撤单校验接口，用于校验基金交易订单是否可以撤单
 * @apiParam (请求体) {String} hkCustNo 香港客户号
 * @apiParam (请求体) {String} dealNo 订单号
 * @apiParam (请求体) {String} txCode 交易码(默认HW0011)
 * @apiParamExample 请求体示例
 * {
 *   "hkCustNo": "HK000001",
 *   "dealNo": "20250427000001",
 *   "txCode": "HW0011"
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":null,"description":"订单可以撤单"}
 * <AUTHOR>
 * @description: (撤单校验接口)
 * @date 2025/4/27 10:52:20
 * @since JDK 1.8
 */
public interface CancelValidateFacade extends BaseFacade<CancelOrderValidateRequest, Void> {

}
