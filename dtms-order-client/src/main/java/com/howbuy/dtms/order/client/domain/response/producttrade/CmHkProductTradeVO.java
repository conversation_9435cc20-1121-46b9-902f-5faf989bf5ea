/**
 * Copyright (c) 2023, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.client.domain.response.producttrade;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2023/10/27 16:56
 * @since JDK 1.8
 */
@Data
public class CmHkProductTradeVO {

    private String id;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 客户编号
     */
    private String custNo;

    /**
     * 香港ebrokeid
     */
    private String hkCustId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品代码
     */
    private String productCode;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 预约状态
     */
    private String orderStatus;

    /**
     * 是否线上签约
     */
    private String isOnlineSign;

    /**
     * 签约状态
     */
    private String signStatus;

    /**
     * 签约时间
     */
    private Date signDt;

    /**
     * 打款状态
     */
    private String payStatus;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 币种
     */
    private String currency;

    /**
     * 申请金额
     */
    private BigDecimal appAmt;

    /**
     * 申请份额
     */
    private BigDecimal appVol;

    /**
     * 确认金额
     */
    private BigDecimal ackAmt;

    /**
     * 确认份额
     */
    private BigDecimal ackVol;

}