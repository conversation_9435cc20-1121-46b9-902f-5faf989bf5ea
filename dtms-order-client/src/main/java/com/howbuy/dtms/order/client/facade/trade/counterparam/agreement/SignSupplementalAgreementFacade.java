package com.howbuy.dtms.order.client.facade.trade.counterparam.agreement;

import com.howbuy.dtms.order.client.facade.BaseFacade;
import com.howbuy.dtms.order.client.domain.request.agreement.SignSupplementalAgreementRequest;
import com.howbuy.dtms.order.client.domain.response.agreement.SignSupplementalAgreementResponse;

/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.counterparam.agreement.SignSupplementalAgreementFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup SignSupplementalAgreementFacade
 * @apiName execute()
 * @apiDescription 线上补签协议接口
 * @apiParam (请求参数) {String} hkCustNo 香港客户号
 * @apiParam (请求参数) {String} fundCode 基金编码
 * @apiParam (请求参数) {Array} agreementIdList 补签协议ID列表
 * @apiParamExample 请求参数示例
 * {
 *     "hkCustNo": "HK10086",
 *     "fundCode": "000001",
 *     "agreementIdList": [
 *         "AGR202403060001",
 *         "AGR202403060002"
 *     ]
 * }
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccess (响应结果) {String} data.signStatus 签署状态
 * @apiSuccessExample 响应结果示例
 * {
 *     "code": "0000",
 *     "data": {
 *         "signStatus": "1"
 *     },
 *     "description": "补签协议成功"
 * }
 */
public interface SignSupplementalAgreementFacade extends BaseFacade<SignSupplementalAgreementRequest, SignSupplementalAgreementResponse> {

} 