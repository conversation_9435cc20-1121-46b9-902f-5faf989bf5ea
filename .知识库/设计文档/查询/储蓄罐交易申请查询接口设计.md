## 储蓄罐交易申请查询接口

- 请求地址

|                          |       |                                                              |                        |
| ------------------------ | ----- | ------------------------------------------------------------ | ---------------------- |
|                          |       |                                                              |                        |
| PiggyTradeAppQueryFacade | Dubbo | com.howbuy.dtms.order.client.facade.query.piggytradeapp.PiggyTradeAppQueryFacade | 储蓄罐交易申请查询接口 |

- 入参

|              |                |       |      |      |
| ------------ | -------------- | ----- | ---- | ---- |
| importAppIds | 导入申请Id列表 | Array | 是   |      |

- 出参

|             |                          |        |                              |
| ----------- | ------------------------ | ------ | ---------------------------- |
| code        | 状态码                   | String |                              |
| description | 描述信息                 | String |                              |
| data        | 数据封装                 | Object |                              |
| list        | PiggyTradeAppVO列表 开始 | array  |                              |
| importAppId | 导入申请Id               | String | 控管表编号                   |
| isGenerated | 是否已生成               | String | 0-未生成 1-已生成 2-生成失败 |
| dealNo      | 订单号                   | String |                              |
| buyAmt      | 购买金额                 | String |                              |
| fee         | 手续费                   | String |                              |
| remark      | 备注                     | String |                              |
| list        | PiggyTradeAppVO列表 结束 | array  |                              |



- 处理逻辑(ImportPiggyTradeAppService.queryListByImportAppId())：
  - 参数校验：
    - importAppIds不等于空
    - importAppIds.size<=200
  - 查询hw_piggy_trade_app_import
    - import_app_id in importAppIds
  - 构建PiggyTradeAppVOList