## 查询交易订单(资金)接口

- 请求地址

|                                |       |                                                              |                        |
| ------------------------------ | ----- | ------------------------------------------------------------ | ---------------------- |
| QueryDealOrderForCaptailFacade | DUBBO | com.howbuy.dtms.order.client.facade.query.order.QueryDealOrderForCaptailFacade.execute() | 查询交易订单(资金)接口 |

- 入参

|         |            |       |      |             |
| ------- | ---------- | ----- | ---- | ----------- |
| dealNos | 订单号列表 | Array | 是   | 限制最大200 |

- 出参

|                           |                           |        |                                                              |
| ------------------------- | ------------------------- | ------ | ------------------------------------------------------------ |
|                           |                           |        |                                                              |
| code                      | 状态码                    | String |                                                              |
| description               | 描述信息                  | String |                                                              |
| data                      | 数据封装                  | Object |                                                              |
| dealOrderForCaptailVOList | 交易订单(资金)VO列表 开始 | Array  |                                                              |
| dealNo                    | 订单号                    | Number |                                                              |
| busiCode                  | 业务代码                  | String | 020 022 02A 02B                                              |
| fundCode                  | 基金代码                  | String |                                                              |
| currency                  | 币种代码                  | String |                                                              |
| productPayEndDt           | 产品打款截止日期          | String | yyyyMMdd                                                     |
| productPayEndTm           | 产品打款截止时间          | String | HHmmss                                                       |
| payEndDt                  | 打款截止日期              | String | yyyyMMdd                                                     |
| payEndTm                  | 打款截止时间              | String | HHmmss                                                       |
| preSubmitTaDt             | 预计上报日                | String | yyyyMMdd                                                     |
| preSubmitTaTm             | 预计上报时间              | String | HHmmss                                                       |
| openDt                    | 开放日期                  | String | yyyyMMdd                                                     |
| paymentType               | 支付方式                  | String | 支付方式 1：电汇；2：支票；3：海外储蓄罐                     |
| payStatus                 | 付款状态                  | String | 0-无需支付；1-未打款；2-已打款；3-到账确认；4-退款           |
| netAppAmt                 | 净申请金额                | Number |                                                              |
| estimateFee               | 预估手续费                | Number |                                                              |
| orderStatus               | 订单状态                  | String | 1-申请成功；2-部分确认；3-确认成功；4-确认失败；5-自行撤销；6-强制取消 |
| cpAcctNo                  | 资金账号                  | String |                                                              |
| receiptSerialNo           | 入账流水号                | String |                                                              |
| dealOrderForCaptailVOList | 交易订单(资金)VO列表 开始 | Array  |                                                              |



- 处理逻辑(QueryOrderService.QueryDealOrderForCaptail())：
  - 参数校验：dealNos不等于空，dealNos.size<=200
  - 数据查询
    - 根据dealNos查询订单表 dealOrderList
    - 根据dealNos查询订单明细表 dealOrderDtlList
    - 根据dealNos查询打款凭证表 payVoucherOrderList
    - 遍历dealOrderForCaptailVOList构建DealOrderForCaptailVO
      - deal_no、busiCode(BusinessCodeEnum.getByMCode)、payment_type(取payment_type_list)、payStatus、orderStatus、cpAcctNo从hw_deal_order获取
      - fundCode、currency、productPayEndDt、productPayEndTm、payEndDt、payEndTm、preSubmitTaDt、preSubmitTaTm、openDt、从hw_deal_order_dtl列表中取最小的productPayEndDt、productPayEndTm的一条
      - netAppAmt、estimateFee从hw_deal_order_dtl.netAppAmt、estimateFee汇总获取，使用com.howbuy.dtms.order.service.commom.utils.DealDtlMergeUtil#mergeEstimateFee、mergeNetAppAmt
      - receiptSerialNo从hw_pay_voucher_order.actual_pay_account中获取