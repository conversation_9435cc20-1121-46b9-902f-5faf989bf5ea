# 异步支付消息处理详细设计

### 1. 接口名称
异步支付消息处理

### 2. 接口说明
接收支付通知MQ消息，处理异步支付逻辑。当认申购、认缴、实缴等订单创建成功后，会触发此消息。本处理器负责根据消息内容，查询关联的订单与支付单，在校验状态合法后，调用三方支付网关发起实际支付，并根据支付结果更新支付订单状态。

### 3. 接口类型
RocketMQ 消息消费者

### 4. 接口地址或方法签名
- **消费主题 (Topic)**: `DTMS_ORDER_PAYMENT_NOTIFY_TOPIC` (示例)
- **消费组 (Group)**: `CID_DTMS_ORDER_PAYMENT_PROCESSOR` (示例)
- **处理类及方法**: `com.howbuy.dtms.order.service.mq.payment.PayNotifyMessageProcessor.process(String message)`

### 5. 请求参数表（MQ消息体-JSON格式）
| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 支付订单号 | pmtDealNo | Long | 是 | 2023031500001 | 支付订单表 `hw_payment_order` 的业务流水号 |

### 6. 响应参数表
无，消息队列消费者无需同步返回。

### 7. 返回码说明
不适用。消费成功则确认消息，消费失败则抛出异常，由MQ进行重试。

### 8. 关键业务逻辑说明
1.  **消息接收与解析**：消费者获取到消息后，将JSON格式的字符串消息体解析为Java对象，提取 `pmtDealNo`。
2.  **参数校验**：校验 `pmtDealNo` 是否存在且格式合法，如果非法则记录错误日志并丢弃消息，防止无效重试。
3.  **查询支付订单**：根据 `pmtDealNo` 查询 `hw_payment_order` 支付订单信息。若订单不存在，则可能为无效消息或数据异常，记录错误日志后丢弃消息。
4.  **支付方式校验 (新增)**: 检查 `hw_payment_order` 的 `payment_type_list` 字段。如果该字段不为空且第三位是'1'，表示此为储蓄罐支付，不通过此消息处理器进行处理。记录提示日志后，正常消费并返回。
5.  **订单类型分支处理与前置条件校验**：根据 `hw_payment_order` 的 `order_type` 字段进行分支处理。
    -   **交易类订单 (`order_type` = '1')**:
        -   **查询关联主订单**：从支付订单信息中获取 `deal_no`，查询 `hw_deal_order` 主订单信息。
        -   **支付前置条件校验**：
            -   校验 `hw_deal_order` 主订单状态是否为“申请成功”。
            -   校验 `hw_payment_order` 支付订单的支付状态 `tx_pmt_flag` 是否为“未支付”（值为`1`）。
            -   若任一条件不满足，说明订单状态已变更或不适合发起支付，记录警告日志并正常消费消息，避免重试。
    -   **充值类订单 (`order_type` = '2')**:
        -   充值类订单没有关联的 `hw_deal_order`。
        -   **支付前置条件校验**：
            -   仅校验 `hw_payment_order` 支付订单的支付状态 `tx_pmt_flag` 是否为“未支付”（值为`1`）。
            -   若条件不满足，说明订单状态已变更或不适合发起支付，记录警告日志并正常消费消息，避免重试。
6.  **更新支付状态（支付前）**：
    - 将 `hw_payment_order` 的支付状态 `tx_pmt_flag` 更新为“支付中”（值为`4`）。
    - 将 `hw_payment_order` 的对账状态 `pmt_comp_flag` 更新为“未对账”（值为`1`）。
    - 此操作需在独立事务中完成，确保状态更新成功。
7.  **调用内部支付系统服务**：根据查询到的 `hw_payment_order` 记录，构建 `PaymentRequestDTO`，调用 `PaymentOuterService.pay()` 方法，该方法内会通过 Dubbo 调用支付系统的 `PaymentFacade.pay()` 接口。
8.  **处理支付结果**：根据 `PaymentOuterService` 返回的 `PaymentResponseDTO`，更新 `hw_payment_order` 状态：
    - 获取 `payStatus`, `outPmtDealNo`, `retCode`, `retDesc`, `pmtOrgCode` 等字段。
    - **支付成功 (`payStatus` 定义为成功状态)**：
        - 更新 `tx_pmt_flag` 为“支付成功”（值为`2`）。
        - 更新 `pmt_complete_dtm` (支付完成时间) 为当前系统时间。
    - **支付失败 (`payStatus` 定义为失败状态)**：
        - 更新 `tx_pmt_flag` 为“支付失败”（值为`3`）。
    - **支付处理中 (`payStatus` 定义为处理中状态)**：
        - `tx_pmt_flag` 保持“支付中”（值为`4`）。
        - 后续由“支付状态刷新定时任务”进行状态跟进。
    - 统一将 `out_pmt_deal_no`, `ret_code`, `ret_desc`, `pmt_org_code` 等返回信息更新到支付订单表中。

### 9. 流程图（使用 PlantUML）
```plantuml
@startuml
title 异步支付消息处理流程
start
:接收并解析MQ消息;
:根据 pmtDealNo 查询支付订单;
if (支付订单不存在?) then (是)
  :记录错误日志，消息不重试;
  stop
endif
if (是储蓄罐支付?) then (是)
  :记录日志"储蓄罐支付不处理"，正常ACK;
  stop
endif

if (是交易类订单?) then (是)
  :查询主订单;
  if (主订单状态!=“申请成功” or 支付单状态!=“未支付”) then (是)
    :记录警告日志，消息不重试;
    stop
  endif
else (是充值类订单)
  if (支付单状态!=“未支付”) then (是)
    :记录警告日志，消息不重试;
    stop
  endif
endif

:更新支付订单状态为“支付中”、对账状态为“未对账”;
:调用内部支付系统服务;
switch (支付服务返回结果?)
case ( 支付成功 )
  :更新支付订单状态为“支付成功”;
case ( 支付失败 )
  :更新支付订单状态为“支付失败”;
case ( 支付中 )
  :更新支付订单时间戳;
endswitch
stop
@enduml
```

### 10. 时序图（使用 PlantUML）
```plantuml
@startuml
title 异步支付消息处理时序
skinparam sequenceMessageAlign center
actor "MQ Broker" as MQ
participant "PayNotifyMessageProcessor" as Processor
participant "HwPaymentOrderRepository" as PmtRepo
participant "HwDealOrderRepository" as OrderRepo
participant "PaymentOuterService" as OuterService
participant "支付系统\n(PaymentFacade)" as PaymentFacade

MQ -> Processor: 投递支付消息
activate Processor

Processor -> Processor: 解析消息，获取 pmtDealNo
Processor -> PmtRepo: findByPmtDealNo(pmtDealNo)
activate PmtRepo
PmtRepo --> Processor: 返回 HwPaymentOrderPO
deactivate PmtRepo

Processor -> Processor: 校验支付订单(是否存在、是否储蓄罐支付)
alt 订单不存在或为储蓄罐支付
    Processor -> Processor: 记录日志，正常ACK
    Processor -> MQ: ACK 消息
    deactivate Processor
end

Processor -> Processor: 根据订单类型(交易/充值)进行前置条件校验
alt 交易类订单
    Processor -> OrderRepo: findByDealNo(dealNo)
    activate OrderRepo
    OrderRepo --> Processor: 返回 HwDealOrderPO
    deactivate OrderRepo
    note right of Processor: 校验主订单和支付单状态
else 充值类订单
    note right of Processor: 仅校验支付单状态
end

alt 前置条件满足
    Processor -> PmtRepo: updateStatusToPaying(pmtDealNo)
    activate PmtRepo
    PmtRepo --> Processor: 更新成功
    deactivate PmtRepo

    Processor -> OuterService: pay(paymentRequestDTO)
    activate OuterService
    OuterService -> PaymentFacade: pay(request)
    activate PaymentFacade
    PaymentFacade --> OuterService: 返回 PaymentResponse
    deactivate PaymentFacade
    OuterService --> Processor: 返回 PaymentResponseDTO
    deactivate OuterService

    Processor -> PmtRepo: updateWithPaymentResult(...)
    activate PmtRepo
    PmtRepo --> Processor: 更新成功
    deactivate PmtRepo
else 前置条件不满足
    Processor -> Processor: 记录日志，正常ACK
end

Processor -> MQ: ACK 消息
deactivate Processor
@enduml
```

### 11. 异常处理机制
- **消息格式错误**：在消息解析阶段捕获异常，记录错误日志，并将消息投递到死信队列（DLQ），避免阻塞正常队列。
- **数据库访问异常**：在查询或更新数据库时，若发生`DataAccessException`等异常，直接抛出运行时异常。MQ消费者框架会捕获此异常并根据配置进行消息重试。
- **内部Dubbo服务调用异常**：调用 `PaymentOuterService` 时，内部已对Dubbo调用进行 `try-catch` 封装，并会抛出自定义的 `BusinessException`。此异常应被捕获，记录详细错误日志，并触发MQ重试机制。
- **幂等性保障**：整个处理流程需要保证幂等性。通过“支付前置条件校验”步骤，可以有效防止已处理或状态不正确的订单被重复支付。查询-更新操作非原子，可考虑在 `update` 语句中加入状态作为 `where` 条件（CAS思想），或引入分布式锁。

### 12. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
|---|---|
| HwPaymentOrderRepository | 支付订单表(`hw_payment_order`)的数据访问层，负责增删改查。 |
| HwDealOrderRepository | 主订单表(`hw_deal_order`)的数据访问层，负责查询订单状态。 |
| PaymentOuterService | 封装了对内部支付系统Dubbo接口调用的服务，是与支付系统交互的统一出口。 |
| 支付系统 (PaymentFacade) | 内部支付系统提供的Dubbo接口，用于执行实际的支付操作。 |
| RocketMQ Client | 用于从 Broker 消费消息。 |

</rewritten_file> 