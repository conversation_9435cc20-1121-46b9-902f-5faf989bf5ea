## 支付对账接口

- 请求地址

  | 类名               | 请求方式 | 接口url                                       | 描述     |
  | :----------------- | :------- | :-------------------------------------------- | :------- |
  | PaymentCheckFacade | dubbo    | com.howbuy.dtms.order.client.facade.trade.payment | 支付对账 |

  - 入参

  | 字段       | 字段注释     | 类型   | 是否必填 | 备注 |
  | :--------- | :----------- | :----- | :------- | :--- |
  | pmtCheckDt | 支付对账日期 | String | 是       |      |

  - 出参

  | 字段        | 字段注释 | 类型   | 备注               |
  | :---------- | :------- | :----- | :----------------- |
  | code        | 状态码   | String | 状态码0000表示成功 |
  | description | 描述信息 | String |                    |

  

- 接口逻辑

  - 初始化pmtCompFlagList为1-未对账
  - 调用paymentCheckService.paymentCheck(pmtCheckDt, List<String> pmtCompFlagList)