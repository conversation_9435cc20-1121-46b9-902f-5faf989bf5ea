# 1. 服务名称
支付对账服务

# 2. 服务说明
该服务用于定期对账支付订单。通过调用外部支付网关接口，核对内部系统的支付订单（`hw_payment_order`）与支付渠道的实际支付结果是否一致，并将对账结果记录到支付对账结果表（`hw_payment_check_result`）中，确保双方数据的一致性。

# 3. 类路径及方法签名
- **类路径**: `com.howbuy.dtms.order.service.business.payment.PaymentCheckService`
- **方法签名**: `paymentCheck(Date pmtCheckDt, List<String> pmtCompFlagList)`

# 4. 方法入参表
| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 对账日期 | `pmtCheckDt` | `java.util.Date` | 否 | `"2023-10-27"` | 指定对账的具体日期。若为空，则默认查询180天内的订单。 |
| 对账状态列表 | `pmtCompFlagList` | `List<String>` | 是 | `["1", "3"]` | 需要进行对账的支付订单状态列表 (例如: 1-未对账, 3-对账不平)。 |

# 5. 方法出参表
该方法无出参（返回类型为 `void`），其执行结果通过更新数据库和发送监控来体现。

# 6. 关键业务逻辑说明
1.  **并发控制**：服务入口增加分布式锁，确保同一时间只有一个对账任务在执行，防止数据并发问题。
2.  **数据查询**：分页查询 `hw_payment_order` 表获取待对账的订单数据，每页500条。
    -   **查询条件**:
        -   支付对账状态 `pmt_comp_flag` 在传入的 `pmtCompFlagList` 列表中。
        -   交易支付标志 `tx_pmt_flag` 为 2-付款成功 或 3-付款失败。
        -   记录状态 `rec_stat` 为 1-有效。
        -   若 `pmtCheckDt` 非空，则 `pmt_check_dt` 等于该入参；否则查询 `app_dtm` 在180天内的订单。
    -   **排序**: 按申请时间 `app_dtm` 升序排列。
3.  **循环处理**：遍历查询到的支付订单列表，对每一笔订单在 `try-catch` 块中执行对账，避免单笔订单异常中断整个批次。
4.  **外部核对**：调用 `PayOuterService.queryPayResult` 查询外部支付渠道的订单详情。
5.  **结果比对**:
    -   **跳过处理**：若外部接口返回的 `pmtDealNo` 非空，但 `pmtCompFlag` 不为 `2-对账完成`，则跳过该订单。
    -   **对账不平**：若支付金额、交易状态或币种任一不匹配，判定为"对账不平"，并记录不一致的具体原因。
    -   **对账不平（对手方无此订单）**：若外部接口未返回 `pmtDealNo`，判定为"对账不平"，备注为"支付侧订单不存在"。
    -   **对账完成**：若所有关键信息（金额、状态、币种）均一致，判定为"对账完成"。
6.  **结果持久化**：
    -   根据对账判定结果，构建 `HwPaymentCheckResultPO` 实体。
    -   在 `hw_payment_check_result` 表中新增或更新对账结果。如果为新增，则发送异常监控告警。
7.  **订单状态更新**：
    -   使用**乐观锁**机制更新 `hw_payment_order` 表的 `pmt_comp_flag` 和 `update_timestamp`。
    -   `UPDATE` 语句的 `WHERE` 条件中必须包含查询时获得的 `update_timestamp` 值。
    -   若更新影响行数不为1，说明发生并发修改冲突，此时记录异常日志、发送监控预警，并抛出异常。
8.  **锁释放**：任务执行完毕或异常退出时，在 `finally` 块中释放分布式锁。

# 7. 流程图 (PlantUML)
```plantuml
@startuml
title 支付对账服务流程图 (简化版)
start
:开始;
if (获取分布式锁?) then (成功)
  repeat
    :分页查询待对账订单;
    if (有待处理订单?) then (是)
      while (遍历订单)
        :核对外部支付结果;
        :记录对账结果 (新增/更新);
        :乐观锁更新订单状态;
        if (更新失败?) then (是)
          :告警并中断任务;
          stop
        endif
      endwhile
    else (否)
      break
    endif
  repeat while (上一批有数据?) is (是)
  :释放分布式锁;
else (失败)
  :记录日志后退出;
endif
:结束;
stop
@enduml
```

# 8. 时序图 (PlantUML)
```plantuml
@startuml
title 支付对账服务时序图 (简化版)
actor "调度器" as scheduler
participant "PaymentCheckService" as service
participant "外部支付服务" as outerSvc
participant "数据库" as db

scheduler -> service: paymentCheck(...)
activate service

service -> service: 获取分布式锁
loop for each page of orders
    service -> db: 查询待对账订单
    activate db
    db --> service: 订单列表
    deactivate db
    
    loop for each order
        service -> outerSvc: 查询支付结果
        activate outerSvc
        outerSvc --> service: 外部支付结果
        deactivate outerSvc
        
        service -> db: 保存对账结果并更新订单(乐观锁)
        activate db
        db --> service: 更新成功
        deactivate db
    end
end
service -> service: 释放分布式锁
deactivate service
@enduml
```

# 9. 异常处理机制
该方法在执行过程中可能会抛出异常，异常处理机制如下：
- 如果发生异常，服务会记录异常日志，并发送监控告警。
- 如果发生乐观锁冲突，服务会记录日志，并抛出异常，中断任务。
- 如果发生单笔订单异常，服务会记录单笔订单异常日志，但不中断整个批次。

# 10. 其他说明
该服务在执行过程中会使用分布式锁来确保同一时间只有一个对账任务在执行，以防止数据并发问题。如果发生乐观锁冲突，服务会记录日志，并抛出异常，中断任务。如果发生单笔订单异常，服务会记录单笔订单异常日志，但不中断整个批次。