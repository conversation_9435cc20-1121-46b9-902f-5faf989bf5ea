## 支付对账结果查询接口

- 请求地址

| 类名                          | 请求方式 | 接口url                                       | 描述             |
| ----------------------------- | -------- | --------------------------------------------- | ---------------- |
| QueryPaymentCheckResultFacade | dubbo    | com.howbuy.dtms.order.client.facade.query.payment | 支付对账结果查询 |

- 入参

| 字段         | 字段注释         | 类型         | 是否必填 | 备注 |
| ------------ | ---------------- | ------------ | -------- | ---- |
| pmtDealNo    | 支付订单号       | String       | 否       |      |
| dealNo       | 订单号           | String       | 否       |      |
| outPmtDealNo | 外部支付订单号   | String       | 否       |      |
| fundCodes    | 基金代码列表     | List<String> | 否       |      |
| pmtCheckDt   | 支付对账日期     | String       | 必填     |      |
| pmtCompFlags | 支付对账标记列表 | List<String> | 否       |      |
| pageNum      | 页码             | Integer      | 是       | 1    |
| pageSize     | 每页大小         | Integer      | 是       | 20   |

- 出参

| 字段            | 字段注释       | 类型    | 备注                                                         |
| --------------- | -------------- | ------- | ------------------------------------------------------------ |
| total           | 总记录数       | Long    |                                                              |
| pageNum         | 当前页码       | Integer |                                                              |
| pageSize        | 每页大小       | Integer |                                                              |
| pages           | 总页数         | Integer |                                                              |
| list            | 数据列表       | Array   |                                                              |
| pmtDealNo       | 支付订单号     | Long    |                                                              |
| dealNo          | 订单号         | Long    |                                                              |
| middleBusiCode  | 中台业务代码   | String  |                                                              |
| paymentTypeList | 支付方式列表   | String  | 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐          |
| hkCustNo        | 香港客户号     | String  |                                                              |
| custName        | 客户姓名       | String  |                                                              |
| cpAcctNo        | 资金账号       | String  |                                                              |
| fundTxAcctNo    | 基金交易账号   | String  |                                                              |
| fundCode        | 基金代码       | String  |                                                              |
| fundAddr        | 基金简称       | String  |                                                              |
| currency        | 币种           | String  |                                                              |
| appDtm          | 申请时间       | String  |                                                              |
| pmtAmt          | 支付金额       | String  |                                                              |
| pmtCheckDt      | 支付对账日期   | String  |                                                              |
| outPmtDealNo    | 外部支付订单号 | String  |                                                              |
| outPmtAmt       | 外部支付金额   | String  |                                                              |
| outCurrency     | 外部币种       | String  |                                                              |
| outPmtFlag      | 外部支付标识   | String  | 1-待支付 2-支付成功 3-支付失败 4-支付中                      |
| txPmtFlag       | 交易支付标识   | String  | 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；5-已退款;6-等待付款；7-撤单成功； |
| pmtCompFlag     | 支付对账标记   | String  | 0-无需对账；1-未对账；2-对账完成；3-对账不平;                |
| memo            | 备注           | String  |                                                              |
| list            | 数据列表       | Array   |                                                              |



- 处理逻辑
  - 根据入参分页查询支付订单表(hw_payment_order)
    - 关联支付对账结果表(hw_payment_check_result)
      - 关联条件：pmt_Deal_No = pmt_Deal_No 
      - 返回字段取值：outPmtDealNo、outPmtAmt、outCurrency、outPmtFlag、memo 取 支付对账结果表(hw_payment_check_result)的out_Pmt_Deal_No、out_Pmt_Amt、out_Currency、out_Pmt_Flag、memo
    - 关联交易订单表(hw_deal_order)
      - 关联条件：deal_No = deal_No
      - 返回字段取值：custName、fundAddr 取交易订单表(hw_deal_order)的 cust_chinese_name、product_abbr 赋值
    - 查询条件为接口入参，根据入参是否必填编写查询条件