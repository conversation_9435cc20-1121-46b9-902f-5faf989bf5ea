### 1. 任务名称
异步支付处理任务

### 2. 任务说明
本任务负责处理异步发起的支付请求。当上游业务系统（如订单创建模块）完成订单创建并需要发起支付时，会发送一条异步消息。本任务消费该消息，调用底层支付服务完成实际的支付操作，并更新订单的支付状态。这种异步处理方式可以避免支付过程中的长时间等待，提升主流程的响应速度和系统吞吐量。

### 3. 消息来源
- **触发方式**: 上游系统异步发送
- **MQ Topic 名称**: `DTMS_ORDER_ASYNC_DO_PAYMENT`
- **消息 Tag（如有）**: 无
- **MQ 消息消费方式**: Push / 集群消费

### 4. 消息格式说明
消息体为JSON格式，包含支付交易的关键信息。

| 中文名 | 字段名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 支付交易号 | `pmt_deal_no` | String | 是 | "P202407261000001" | 支付系统的唯一交易流水号，用于标识一笔支付请求。 |

### 5. 核心业务处理逻辑
1.  **接收消息**：任务从`DTMS_ORDER_ASYNC_DO_PAYMENT` Topic 接收到一条支付处理消息。
2.  **消息校验**：
    -   解析消息体（JSON格式）。
    -   检查 `pmt_deal_no` 字段是否存在且不为空。若无效，记录错误日志并放弃处理该消息（或根据配置移入死信队列），防止无效数据污染系统。
3.  **调用业务服务**：消息校验通过后，直接调用 `AsyncDoPaymentService.process(pmt_deal_no)` 方法。所有核心业务逻辑，包括订单查询、幂等性判断、调用支付网关、更新订单状态等，均由 `AsyncDoPaymentService` 内部封装实现。
4.  **确认消息**：无论 `AsyncDoPaymentService` 处理成功还是发生可预期的业务异常（如支付失败），只要服务本身没有抛出系统级异常，就向MQ发送ACK，确认消息已被成功消费。

### 6. 流程图（使用 PlantUML）
```plantuml
@startuml
title 异步支付处理流程
start
:从MQ接收支付消息;
:解析并校验消息;
if (消息格式是否有效?) then (是)
  :调用 AsyncDoPaymentService.process(pmt_deal_no);
  :向MQ发送ACK;
else (否)
  :记录错误日志;
  :将消息移入死信队列;
endif
stop
@enduml
```

### 7. 时序图（使用 PlantUML）
```plantuml
@startuml
title 异步支付处理时序
participant "MQ" as MQ
participant "异步支付处理器" as Processor
participant "支付业务服务" as AsyncDoPaymentService

MQ -> Processor: 发送支付消息 (pmt_deal_no)
activate Processor

Processor -> Processor: 校验消息
Processor -> AsyncDoPaymentService: process(pmt_deal_no)
activate AsyncDoPaymentService

' ... 内部业务逻辑由AsyncDoPaymentService封装 ...

AsyncDoPaymentService --> Processor: 返回处理结果
deactivate AsyncDoPaymentService

Processor -> MQ: 发送ACK
deactivate Processor
@enduml
```

### 8. 异常处理机制
- **消息字段校验失败**：消息体格式错误、`pmt_deal_no` 缺失或为空，记录错误日志，并将消息发送到死信队列（DLQ）进行人工干预。
- **幂等性保障**：幂等性完全由 `AsyncDoPaymentService` 服务内部保证。消息处理器不处理幂等逻辑。
- **下游系统不可用**：
    -   当调用 `AsyncDoPaymentService.process()` 时，如果服务返回系统级异常（如数据库连接失败、依赖服务超时），则消息处理器应让消息进入MQ的重试流程。
    -   若达到最大重试次数后仍然失败，消息将被自动移入死信队列。
    -   配置针对死信队列的监控告警，通知运维人员处理。
- **消息处理业务失败**：
    -   所有业务层面的失败（如支付失败、订单状态不正确等）均由 `AsyncDoPaymentService` 内部消化，不应向上抛出异常导致MQ重试。服务处理完成后，消息处理器正常ACK消息。

### 9. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|---|---|
| `AsyncDoPaymentService` | 封装了完整的异步支付业务逻辑，包括订单查询、幂等性检查、调用支付网关及更新订单状态。 |
| MQ消息队列 | 接收上游系统的支付指令，实现业务解耦和异步化。 |
| 日志服务 | 记录任务执行过程中的关键信息、错误和异常，用于问题排查。 |
| 告警系统 | 当发生严重异常或消息积压时，通过短信、邮件等方式发送告警。 | 