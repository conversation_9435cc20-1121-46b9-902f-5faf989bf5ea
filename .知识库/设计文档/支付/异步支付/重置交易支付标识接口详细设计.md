# 重置交易支付标识接口详细设计

### 1. 接口名称
重置交易支付标识接口

### 2. 接口说明
用于处理因支付渠道异常或超时导致的支付状态不一致问题。当一笔订单在支付系统实际未支付成功，但在本系统（订单系统）中被标记为"付款中"时，可通过此接口将支付标识和相关交易订单状态重置为"未付款"，以便用户可以重新发起支付。

### 3. 接口类型
Dubbo

### 4. 接口地址或方法签名
- **接口**：`com.howbuy.dtms.order.client.facade.trade.payment.ResetTxPmtFlagFacade`
- **方法**：`execute(ResetTxPmtFlagRequest request)`

### 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 支付订单号 | pmtDealNo | String | 是 | "P2023102700001" | 需要重置状态的支付订单的唯一编号 |

### 6. 响应参数表

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|---|---|---|---|---|
| 状态码 | code | String | "0000" | "0000"表示成功，其他表示失败 |
| 描述信息 | description | String | "成功" | 对返回码的补充说明 |
| 响应数据 | data | Boolean | true | 操作是否成功 |

### 7. 返回码说明

| 返回码 | 说明 | 备注 |
|---|---|---|
| 0000 | 成功 | 操作执行成功 |
| B0001 | 支付订单不存在 | 根据 `pmtDealNo` 未找到对应的支付订单 |
| B0002 | 支付订单状态不正确 | 支付订单的 `txPmtFlag` 不为 "4"（付款中） |
| B0003 | 关联交易订单不存在 | 支付订单有关联交易，但未找到对应的交易订单 |
| B0004 | 关联交易订单状态不正确 | 交易订单的 `payStatus` 不为 "2"（付款中）或 `orderStatus` 不为 "1"（申请成功） |
| C0001 | 支付系统已存在该订单 | 调用支付网关查询发现该订单已支付成功，无法重置 |
| A0001 | 系统处理异常 | 并发修改导致乐观锁更新失败，或其他未知系统错误 |

### 8. 关键业务逻辑说明
1.  接收请求后，根据入参 `pmtDealNo` 查询支付订单（`hw_payment_order`）。
2.  **前置校验-支付订单**：
    *   校验支付订单是否存在，若不存在，则流程中断，返回错误码 `B0001`。
    *   校验支付订单的支付标识 `txPmtFlag` 是否为 "4"（付款中），若不是，则说明订单状态已变更或非目标状态，流程中断，返回错误码 `B0002`。
3.  **前置校验-关联交易订单**：
    *   判断支付订单的 `orderType` 是否为 "1"（交易类型）。
    *   若是，则根据支付订单中的交易订单号查询交易订单（`hw_deal_order`）。
    *   校验交易订单是否存在，若不存在，则流程中断，返回错误码 `B0003`。
    *   校验交易订单的支付状态 `payStatus` 是否为 "2"（付款中）且订单状态 `orderStatus` 是否为 "1"（申请成功），若不满足，则流程中断，返回错误码 `B0004`。
4.  **前置校验-外部支付系统**：
    *   调用支付网关的支付结果查询接口（`PayOuterService.queryPayResult`）。
    *   校验支付网关返回结果，如果能查询到明确的支付成功信息（如返回的 `pmtDealNo` 不为空），则说明订单在支付渠道已成功，不应重置。流程中断，返回错误码 `C0001`。
5.  **核心处理-状态更新**：
    *   开启数据库事务。
    *   **更新支付订单**：将 `hw_payment_order` 表的 `tx_pmt_flag` 字段更新为 "1"（未付款），并更新 `update_timestamp`。此操作使用乐观锁，`WHERE` 条件需包含查询时获得的 `update_timestamp` 和前置状态 `tx_pmt_flag='4'`。
    *   **更新交易订单**：如果 `orderType` 为 "1"，则将 `hw_deal_order` 表的 `pay_status` 字段更新为 "1"（未付款），并更新 `update_timestamp`。此操作同样使用乐观锁，`WHERE` 条件需包含查询时获得的 `update_timestamp` 和前置状态 `order_status='1'`、`pay_status='2'`。
6.  **结果处理**：
    *   校验上述 `UPDATE` 操作的返回影响行数。若任一操作影响行数不为1，则说明数据已被其他线程修改，触发乐观锁失败。此时应回滚事务，记录异常日志，发送监控告警，并向调用方抛出系统异常，返回错误码 `A0001`。
    *   若所有更新成功，提交事务，返回成功响应。

### 9. 流程图

```plantuml
@startuml
title 重置交易支付标识流程图 (简化版)
start
:接收重置请求 (pmtDealNo);
:查询支付订单及关联交易订单;
:执行前置状态校验
(校验本地订单状态与外部支付系统结果);
if (全部校验通过?) then (yes)
  :开启事务;
  :更新支付订单与交易订单状态 (乐观锁);
  if (更新成功?) then (yes)
    :提交事务;
    :返回成功;
  else (no)
    :回滚事务;
    :记录异常并告警;
    :返回系统异常;
  endif
else (no)
  :返回业务校验失败;
endif
stop
@enduml
```

### 10. 时序图

```plantuml
@startuml
title 重置交易支付标识时序图 (简化版)
actor "调用方" as caller
participant "ResetTxPmtFlagFacade" as facade
participant "Repositories" as repos
participant "PayOuterService" as payGateway

caller -> facade: execute(request)
activate facade

facade -> repos: 查询支付订单与交易订单
repos --> facade: 订单数据

facade -> payGateway: 查询外部支付结果
payGateway --> facade: 支付结果

note right of facade: 执行所有前置校验

alt 校验不通过
    facade --> caller: 返回业务错误
    deactivate facade
end

group 事务 [Transaction]
    facade -> repos: 更新订单状态 (乐观锁)
    repos --> facade: 更新结果
end

alt 更新失败 (并发冲突)
    facade -> facade: 回滚事务, 记录日志, 告警
    facade --> caller: 返回系统异常
else 成功
    facade -> facade: 提交事务
    facade --> caller: 返回成功
end

deactivate facade
@enduml
```

### 11. 异常处理机制
*   **参数校验**：对入口参数 `pmtDealNo` 进行非空校验，若为空直接返回参数错误。
*   **业务校验**：在核心逻辑处理前，对支付订单、交易订单的状态进行严格的前置校验，不满足条件的直接中断流程，返回明确的业务错误码。
*   **外部依赖**：调用支付网关接口时，需做好超时和异常捕获。若接口调用失败或超时，可根据业务场景决定是重试还是直接返回未知异常，建议返回系统繁忙，由调用方稍后重试。
*   **并发控制**：在数据更新环节，采用基于 `update_timestamp` 的乐观锁机制。当更新影响行数为0时，判定为并发冲突，应立即回滚事务，向上抛出系统异常，并记录详细日志，触发监控告警，以便人工介入排查。
*   **兜底策略**：所有未预期的 `Exception` 都应在顶层被捕获，防止异常信息泄露给调用方。统一封装为"系统内部错误"或特定错误码返回，并记录完整的错误堆栈日志。

### 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|---|---|
| HwPaymentOrderRepository | 支付订单表（`hw_payment_order`）的数据访问仓库 |
| HwDealOrderRepository | 交易订单表（`hw_deal_order`）的数据访问仓库 |
| PayOuterService | 外部支付网关服务，用于查询第三方支付系统的支付结果 |
| 监控预警模块 | 用于在发生乐观锁冲突或未知系统异常时，发送告警通知 |


</rewritten_file> 