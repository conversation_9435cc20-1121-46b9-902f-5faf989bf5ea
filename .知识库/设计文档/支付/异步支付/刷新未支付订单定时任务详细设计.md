# 刷新未支付订单定时任务详细设计

### 1. 任务名称
刷新未支付订单定时任务

### 2. 任务说明
该任务是一个定时调度任务，旨在定期刷新和处理处于"未支付"状态的订单。其核心业务背景是，部分订单在创建后可能因为用户未及时支付、支付渠道回调延迟或系统间通信异常等原因，长时间停滞在未支付状态。本任务通过轮询这些订单，并触发后续的支付状态检查和更新流程，确保订单状态的最终一致性，避免订单遗漏。

### 3. 消息来源
- **触发方式**: 定时调度。由分布式任务调度中心（如XXL-Job）在预设的cron表达式时间点，向指定MQ Topic发送一条消息来触发任务执行。
- **MQ Topic 名称**: `DTMS_ORDER_EC_TASK_REFRESH_UN_PAY_ORDER`
- **消息 Tag（如有）**: 无
- **MQ 消息消费方式**: Push消费。消费者以集群模式消费，保证每次调度只有一个实例执行任务。

### 4. 消息格式说明
任务由调度器触发，消息体为空，仅作为启动信号，不包含业务参数。核心的处理逻辑数据源于数据库查询。

| 中文名 | 字段名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| N/A    | N/A    | N/A  | N/A      | `{}`   | 消息体为空，仅作触发信号 |

### 5. 核心业务处理逻辑
1. 任务消费者接收到来自 `DTMS_ORDER_EC_TASK_REFRESH_UN_PAY_ORDER` Topic 的消息后，开始执行处理逻辑。
2. 任务启动后，会以分页的方式，循环查询数据库中的支付订单表（`hw_payment_order`）。
3. 查询条件如下：
    - 支付状态（`tx_pmt_flag`）为 "1 - 未付款"。
    - 订单申请时间（`app_dtm`）在当前时间的10分钟之前，且在当前时间的20天之内。这个时间窗口旨在处理近期和中期未支付的订单，避免处理刚创建（用户可能正在支付）或过于陈旧的订单。
    - 记录状态（`rec_stat`）为 "1 - 有效"。
    - 查询结果按订单申请时间（`app_dtm`）升序排序。
4. 对分页查询出的每一批订单数据进行遍历。
5. 在遍历过程中，对每一个订单，通过 `try...catch` 块包裹，调用 `AsyncDoPaymentService` 服务的 `process` 方法，并传入支付订单号（`pmt_deal_no`）作为参数。
6. `process` 方法会异步触发对该笔订单支付结果的查询和后续状态更新。
7. 如果在调用 `process` 方法时发生异常，系统会捕获该异常，记录详细的错误日志（包括订单号和异常信息），然后继续处理下一笔订单，保证单笔订单的失败不影响整个批次任务的执行。
8. 当处理完一个分页的数据后，继续查询下一页，直到查询结果为空，表示所有符合条件的未支付订单都已处理完毕。
9. 任务执行结束。

### 6. 流程图 (PlantUML)
```plantuml
@startuml
title 刷新未支付订单定时任务处理流程
start
:接收MQ触发消息;
repeat
    :分页查询hw_payment_order表;
    if (查询结果是否为空?) then (是)
        break
    else (否)
        while (遍历当前页的订单)
            group 单笔订单处理
                :获取订单号 pmt_deal_no;
                try
                    :调用 AsyncDoPaymentService.process(pmt_deal_no);
                catch (Exception e)
                    :记录异常日志;
                endtry
            endwhile
        :查询下一页;
    endif
repeatwhile (有更多分页?)
:任务结束;
stop
@enduml
```

### 7. 时序图 (PlantUML)
```plantuml
@startuml
title 刷新未支付订单定时任务时序图
autonumber
participant Scheduler as "任务调度中心"
participant RefreshUnPaymentOrderProcessor as "任务处理器"
participant RefreshUnPaymentOrderService as "订单刷新服务"
participant DBMaster as "数据库 (hw_payment_order)"
participant AsyncDoPaymentService as "异步支付服务"

Scheduler -> RefreshUnPaymentOrderProcessor: 发送MQ消息触发任务
RefreshUnPaymentOrderProcessor -> RefreshUnPaymentOrderService: 调用process()
activate RefreshUnPaymentOrderService
RefreshUnPaymentOrderService -> DBMaster: 分页查询未支付订单
activate DBMaster
DBMaster --> RefreshUnPaymentOrderService: 返回订单列表
deactivate DBMaster

loop 遍历订单列表
    RefreshUnPaymentOrderService -> AsyncDoPaymentService: process(pmt_deal_no)
    note right: 异步调用，主流程不等待返回
end

deactivate RefreshUnPaymentOrderService
@enduml
```

### 8. 异常处理机制
- **消息字段缺失或不合法**: 任务由调度器触发，消息体为空，不涉及消息字段校验。
- **消息幂等性问题**: 任务逻辑本身具有幂等性。它处理的是处于"未支付"状态的订单。如果 `AsyncDoPaymentService` 成功处理了订单，订单状态会更新，因此在下一次任务执行时，该订单不会再被查询到，避免了重复处理。
- **下游系统不可用**: 对下游服务 `AsyncDoPaymentService` 的调用被包含在 `try...catch` 块中。如果下游服务不可用或处理失败，会捕获异常并记录日志，任务会继续处理下一个订单，不会中断。失败的订单将在下次任务运行时被重新处理。
- **消息处理失败**:
  - **单笔订单失败**：通过 `try...catch` 机制隔离，记录日志后继续，不影响批次中其他订单。
  - **任务整体失败**：依赖于MQ消费端的重试机制和任务调度中心的失败重试策略。

### 9. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
|----------|----------|
| 任务调度中心 | 负责按预设规则定时触发任务。 |
| 消息队列 (MQ) | 用于接收任务调度中心的触发消息。 |
| 数据库 `hw_payment_order` 表 | 存储支付订单数据，是本任务的核心数据来源。 |
| `AsyncDoPaymentService` | 核心下游服务，负责异步查询和处理单笔订单的支付状态。 |
| 日志服务 | 用于记录任务执行过程中的关键信息及异常情况，便于问题排查。 |
``` 
</rewritten_file>