# 服务详细设计文档 - 异步支付服务

### 1. 服务名称
异步支付服务

### 2. 服务说明
本服务用于处理异步的支付请求。核心逻辑是根据传入的支付流水号，查询并校验支付订单与关联的业务订单（如交易订单），在更新订单状态后，调用外部支付服务完成支付操作，并最终记录支付结果。该服务旨在确保支付流程的数据一致性和状态同步的可靠性，通常由消息队列（MQ）事件触发。

### 4. 类路径及方法签名
- **类路径**：`com.howbuy.dtms.order.service.business.payment.AsyncDoPaymentService.process(pmtDealNo)`

### 5. 方法入参表
| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 支付订单号 | pmtDealNo | String | 是 | "PMT2024010100001" | 支付系统生成的唯一订单号 |

### 6. 方法出参表
| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|---|---|---|---|---|
| - | - | void | - | 该服务无直接返回值，处理结果通过更新数据库状态和调用外部服务体现。 |

### 7. 关键业务逻辑说明
1.  **接收请求**：服务接收一个`pmtDealNo`（支付订单号）作为输入参数。
2.  **支付订单校验**：
    -   根据`pmtDealNo`查询`hw_payment_order`（支付订单表）。
    -   校验支付订单是否存在。若不存在，记录异常日志并发送监控预警，流程终止。
    -   校验支付订单的`txPmtFlag`（交易支付标记）是否为`1`（未付款）。若状态不正确，记录异常日志并发送监控预警，流程终止。
3.  **关联业务订单校验**：
    -   如果支付订单的`orderType`为`1`（交易），则需进一步校验关联的`hw_deal_order`（交易订单表）。
    -   根据支付订单中的订单号查询交易订单。
    -   校验交易订单是否存在。若不存在，记录异常日志并发送监控预警，流程终止。
    -   校验交易订单的`payStatus`（支付状态）是否为`1`（未付款），且`orderStatus`（订单状态）是否为`1`（申请成功）。若状态不满足条件，记录异常日志并发送监控预警，流程终止。
4.  **支付前置处理（事务内）**：
    -   **更新支付订单**：将`hw_payment_order`的`tx_pmt_flag`更新为`4`（付款中），并设置其他相关标记。此操作使用乐观锁（比对`tx_pmt_flag`原状态和`update_timestamp`），若更新失败（影响行数为0），表明数据已被并发修改，记录异常、发送预警、抛出异常并回滚事务。
    -   **更新交易订单**：若为交易类订单，将`hw_deal_order`的`pay_status`更新为`2`（付款中）。同样使用乐观锁（比对`order_status`、`pay_status`原状态和`update_timestamp`），若更新失败，处理方式同上。
5.  **调用外部支付**：
    -   在状态更新成功提交事务后，调用`PayOuterService.callPayment`方法，向外部支付网关发起实际的支付请求。
6.  **支付后置处理**：
    -   根据外部支付服务的返回结果，更新`hw_payment_order`表，记录返回码`ret_code`、返回描述`ret_desc`和外部支付流水号`out_pmt_deal_no`。
    -   此更新操作同样采用乐观锁（比对`tx_pmt_flag`是否为`4`），若更新失败，记录异常并发送预警，可能需要人工介入核查。

### 9. 流程图
```plantuml
@startuml
title 异步支付服务处理流程 (简化版)

start
:接收支付流水号 pmtDealNo;

:查询并校验支付订单与关联业务订单;

if (订单状态校验通过?) then (是)
    :开始事务;
    :【乐观锁】更新订单状态为 "付款中";
    if (更新成功?) then (是)
        :提交事务;
        :调用外部支付服务;
        :【乐观锁】根据支付结果更新支付订单;
        :流程结束;
    else (否)
        :回滚事务;
        :记录异常并告警;
        stop
    endif
else (否)
    :记录异常并告警;
    stop
endif

stop
@enduml
```

### 10. 时序图
```plantuml
@startuml
title 异步支付服务时序图 (简化版)

actor "调用方" as Caller
participant "AsyncDoPaymentService" as Service
database "数据库" as DB
participant "PayOuterService" as PayService

Caller -> Service: process(pmtDealNo)

Service -> DB: 查询支付订单及关联订单
DB --> Service: 订单数据

Service -> Service: 校验订单状态
alt 校验失败
    Service --> Caller: 抛出异常
end

group 事务
    Service -> DB: 【乐观锁】更新订单状态为 "付款中"
    DB --> Service: 更新成功
end

Service -> PayService: callPayment(...)
PayService --> Service: 支付结果

Service -> DB: 【乐观锁】更新支付订单结果
DB --> Service: 更新成功

@enduml
```

### 11. 异常处理机制
- **参数校验失败**：根据`pmt_deal_no`查询不到支付订单，或支付订单状态（`txPmtFlag`）不为"未付款"，打印异常日志并发送监控预警。
- **关联订单校验失败**：对于交易类订单，查询不到关联的交易订单，或交易订单状态（`payStatus`, `orderStatus`）不满足前置条件，打印异常日志并发送监控预警。
- **并发冲突**：在更新支付订单或交易订单状态时，若使用乐观锁更新影响行数不为1，说明数据已被其他线程修改。此时应打印异常日志，发送监控预警，并向上抛出异常，触发事务回滚。
- **外部服务异常**：`PayOuterService.callPayment`调用本身可能失败或超时，需要有相应的`try-catch`处理，记录失败日志，并根据业务需求决定是否需要重试或进行状态回滚。
- **兜底策略**：关键步骤失败后，流程终止。依赖监控预警系统通知运维人员介入处理。对于支付状态不一致的数据，可能需要人工核查和修复。

### 12. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
|---|---|
| `hw_payment_order` (数据库表) | 存储支付订单信息，是服务处理的主要对象。 |
| `hw_deal_order` (数据库表) | 存储交易订单信息，是支付订单关联的上游业务订单。 |
| `PayOuterService` (外部服务) | 封装了对第三方支付网关或支付中心的调用接口。 |
| 监控预警系统 | 用于在发生异常时发送通知，以便及时发现和处理问题。 | 