### 1. 任务名称
支付状态刷新定时任务

### 2. 任务说明
本任务用于定时扫描并更新处于"付款中"状态的支付订单。由于部分支付渠道无法实时返回最终支付结果，此任务通过主动轮询外部支付网关，获取这些订单的最终状态（成功或失败），并同步更新系统内部的支付订单和相关业务订单状态，以保证核心交易数据的最终一致性。

### 3. 消息来源
- **触发方式**: 定时调度。由调度中心（如 XXL-Job）定期触发，向 MQ 发送消息来启动任务。
- **MQ Topic 名称**: `DTMS_ORDER_EC_TASK_REFRESH_PAY_RESULT`
- **消息 Tag（如有）**: 无
- **MQ 消息消费方式**: Push

### 4. 消息格式说明
本任务由定时器触发，消息体不包含业务数据，仅作为批处理任务的启动信号。

| 中文名 | 字段名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 触发参数 | `N/A` | `N/A` | 否 | `{}` | 消息体为空，不使用。 |

### 5. 核心业务处理逻辑
1.  **消息接收**：任务消费者从 `DTMS_ORDER_EC_TASK_REFRESH_PAY_RESULT` Topic 中获取到消息，触发任务执行。
2.  **分页查询支付订单**：任务启动后，从 `hw_payment_order`（支付订单表）中分页批量查询需要处理的记录。查询条件如下：
    -   `tx_pmt_flag` = 4 (付款中)
    -   `rec_stat` = 1 (记录有效)
    -   `update_timestamp` (更新时间) 介于 `当前时间 - 180天` 与 `当前时间 - 10分钟` 之间。
    -   查询结果按 `update_timestamp` 升序排列，确保优先处理最早的订单。
3.  **循环处理单笔支付订单**：遍历查询到的支付订单列表，对每一笔订单执行以下逻辑：
    1.  **查询关联订单**：根据支付订单的订单号，查询关联的 `HwDealOrder`（交易订单表）和 `HwDealOrderDtl`（交易订单明细表）。
    2.  **前置校验**：
        -   判断支付订单的 `order_type` 是否为 `1`（交易类型）。
        -   如果是交易类型，则必须校验关联的 `HwDealOrder` 和 `HwDealOrderDtl` 记录是否存在。
        -   若校验失败（关联订单不存在），则打印异常日志、发送监控预警，并终止当前这笔支付订单的处理，继续处理下一笔。
    3.  **调用内部后续处理**：直接调用内部的 `ProcessPmtResultService.process` 服务，传入当前支付订单信息以及关联的交易订单和明细，以触发后续的业务流程（如更新交易订单状态、份额计算等）。

### 6. 流程图（使用 PlantUML）
```plantuml
@startuml
title 支付状态刷新任务处理流程图

start
:接收MQ触发消息;
:分页查询"付款中"的支付订单;

while (有需要处理的支付订单?) is (是)
    :获取一笔支付订单;
    :查询关联的交易订单和明细;
    if (是交易类型且关联订单不存在?) then (是)
        :打印异常日志;
        :发送监控预警;
        :结束当前订单处理;
        stop
    else (否)
        :调用内部服务处理后续业务逻辑;
    endif
endwhile (否)

:任务执行结束;

stop
@enduml
```

### 7. 时序图（使用 PlantUML）
```plantuml
@startuml
title 支付状态刷新任务时序图

actor Scheduler as "定时调度中心"
participant "MQ (`DTMS_ORDER_EC_TASK_REFRESH_PAY_RESULT`)" as MQ
participant RefreshPaymentResultProcessor as "任务处理器"
database Database as "订单数据库"
participant ProcessPmtResultService as "支付结果处理服务"
participant AlertSystem as "监控告警系统"

Scheduler -> MQ: 发送任务触发消息
MQ -> RefreshPaymentResultProcessor: 推送消息
activate RefreshPaymentResultProcessor

RefreshPaymentResultProcessor -> Database: 分页查询 hw_payment_order (status='付款中')
activate Database
Database --> RefreshPaymentResultProcessor: 返回支付订单列表
deactivate Database

loop 对每笔支付订单
    RefreshPaymentResultProcessor -> Database: 查询 HwDealOrder, HwDealOrderDtl
    activate Database
    Database --> RefreshPaymentResultProcessor: 返回关联订单信息
    deactivate Database
    
    alt 关联订单不存在
        RefreshPaymentResultProcessor -> AlertSystem: 发送异常告警
    else 关联订单存在
        RefreshPaymentResultProcessor -> ProcessPmtResultService: process(...)
        activate ProcessPmtResultService
        ProcessPmtResultService --> RefreshPaymentResultProcessor: 处理完成
        deactivate ProcessPmtResultService
    end
end
deactivate RefreshPaymentResultProcessor

@enduml
```

### 8. 异常处理机制
-   **数据校验异常**：当支付订单所关联的交易订单或明细缺失时，系统会记录详细的错误日志，并通过监控告警系统通知相关人员。该笔订单的处理将中止，任务会继续处理下一笔订单，避免因单笔数据问题阻塞整个批次。
-   **幂等性保障**：本任务通过查询特定状态（`tx_pmt_flag=4`）的订单来筛选处理目标。一旦订单处理成功，其状态会被更新为终态（如成功或失败）。因此，在下一次任务运行时，这些已处理过的订单不会被再次查询到，从而天然地保证了处理的幂等性。
-   **下游系统不可用**：
    -   **数据库**：若数据库连接失败或访问超时，任务将抛出异常。依赖MQ的消费失败重试机制，消息会在一段时间后被重新投递，任务得以重新执行。
-   **消息处理失败与重试**：
    -   若任务在执行过程中发生意外中断（如JVM崩溃），MQ的消费确认机制将确保消息不会丢失，并在消费者恢复后重新投递。
    -   如果因持续的、不可恢复的错误（如代码BUG、数据源永久性故障）导致消息重试多次仍然失败，该消息最终应被投递到**死信队列（DLQ）**。同时，系统需配置相应的监控告警，一旦有消息进入死信队列，立即通知开发或运维人员进行人工干预。

### 9. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
|---|---|
| 订单数据库 | 存储和查询支付订单、交易订单等核心业务数据。 |
| 支付结果处理服务 | 内部公共业务模块，负责支付成功或失败后的所有后续业务逻辑。 |
| 监控告警系统 | 用于在发生关键异常（如数据校验失败、任务执行失败）时，发送通知。 |
| 日志服务 | 记录任务执行过程中的详细日志，用于问题排查。 |
| 消息队列（MQ） | 接收上游调度系统的触发信号，并提供可靠的消息投递与重试机制。 | 