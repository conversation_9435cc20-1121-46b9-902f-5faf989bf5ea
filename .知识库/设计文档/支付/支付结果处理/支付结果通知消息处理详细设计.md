### 1. 任务名称
支付结果通知消息处理

### 2. 任务说明
本任务负责处理上游支付网关发送的支付结果通知。当用户完成支付后，支付网关会通过消息队列（MQ）异步通知本系统。本任务消费该消息，更新内部支付订单和关联的业务订单状态，并触发后续的业务流程。这是确保交易状态最终一致性的关键环节。

### 3. 消息来源
- **触发方式**: 上游支付系统异步发送
- **MQ Topic 名称**: `TOPIC_HK_PAY_PAY_RESULT_NOTIFY`
- **消息 Tag（如有）**: 无
- **MQ 消息消费方式**: Push

### 4. 消息格式说明
消息体为JSON格式，具体字段如下：

| 中文名 | 字段名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 支付订单号 | pmtDealNo | String | 是 | "P2023102700001" | 系统内部生成的支付订单唯一标识 |
| 交易支付状态 | txPmtFlag | String | 是 | "S" | 支付状态，例如：S-成功, F-失败, I-处理中 |
| 支付完成状态 | pmtCompFlag | String | 是 | "C" | 支付完成状态，例如：C-完成, N-未完成 |
| 返回码 | retCode | String | 是 | "0000" | 上游支付网关返回的原始处理码 |
| 返回描述 | retDesc | String | 否 | "交易成功" | 上游支付网关返回的原始描述信息 |
| 外部支付流水号 | outPmtDealNo | String | 否 | "T2023102712345" | 支付网关或银行侧的流水号 |
| 支付对账日期 | pmtCheckDt | String | 否 | "20231027" | 与支付网关的对账日期 |
| 订单类型 | orderType | String | 是 | "1" | 订单类型，例如：1-交易订单 |
| 币种 | currency | String | 是 | "HKD" | 支付币种，遵循ISO 4217标准 |
| 支付金额 | pmtAmt | Number | 是 | 1000.50 | 实际支付的金额 |

### 5. 核心业务处理逻辑
1.  **接收消息**: 任务启动后，从 `TOPIC_HK_PAY_PAY_RESULT_NOTIFY` 主题消费支付结果通知消息。
2.  **消息校验**:
    - 对消息体进行基础校验，确保 `pmtDealNo`、`txPmtFlag`、`pmtCompFlag` 等必填字段存在且格式正确。
    - 若校验失败，记录错误日志，并将消息丢弃或移入死信队列，触发告警。
3.  **查询支付订单**:
    - 使用消息中的 `pmtDealNo` 查询内部的支付订单（`hw_payment_order` 表）。
    - **幂等性判断**：检查该支付订单的状态，若已是最终状态（如已成功或已失败），则判断为重复消息，直接确认消费并记录日志，终止后续处理。
    - 若支付订单不存在，记录严重错误日志，并发送异常监控预警。
4.  **关联业务订单校验 (如果需要)**:
    - 判断消息中的 `orderType` 是否为 `1` (交易订单)。
    - 如果是，则从刚查到的支付订单中获取关联的业务订单号。
    - 使用业务订单号查询主交易订单（`HwDealOrder` 表）及其明细（`HwDealOrderDtl` 表）。
    - 若关联的交易订单或明细不存在，记录严重错误日志，并发送异常监控预警。
5.  **触发后续业务**:
    - 调用通用的支付结果处理服务。
    - 将查询到的支付订单、交易订单（如有）、交易订单明细（如有）以及从消息解析出的支付结果信息作为参数传入。
    - 由该服务封装后续的所有业务逻辑，例如更新支付订单和交易订单状态、通知客户、记账等。

### 6. 流程图（使用 PlantUML）
```plantuml
@startuml
title 支付结果通知处理流程
start
:接收支付结果通知消息;
if (消息格式校验通过?) then (是)
  :根据 pmtDealNo 查询支付订单;
  if (支付订单存在且状态非终态?) then (是)
    if (orderType == '1'?) then (是)
      :查询关联的交易订单及明细;
      if (交易订单及明细存在?) then (是)
        :调用支付结果处理服务;
      else (否)
        :记录异常日志;
        :发送监控告警;
      endif
    else (否)
      :调用支付结果处理服务;
    endif
  else (否)
    :记录日志 (重复消费或订单不存在);
    if (订单不存在?) then (是)
        :发送监控告警;
    endif
  endif
else (否)
  :记录异常日志;
  :消息移入死信队列;
endif
stop
@enduml
```

### 7. 时序图（使用 PlantUML）
```plantuml
@startuml
title 支付结果通知处理时序
actor "支付网关" as Gateway
participant "消息队列 (MQ)" as MQ
participant "支付结果消费者" as Consumer
participant "订单数据库" as DB
participant "支付结果处理服务" as Processor

Gateway -> MQ : 发送支付结果通知
MQ -> Consumer : 推送消息
Consumer -> Consumer : 校验消息格式与内容
Consumer -> DB : 查询支付订单 (hw_payment_order)
DB --> Consumer : 返回支付订单信息
alt 订单类型为交易订单
    Consumer -> DB : 查询交易订单 (HwDealOrder, HwDealOrderDtl)
    DB --> Consumer : 返回交易订单及明细
end
Consumer -> Processor : process(支付订单, 交易订单, ...)
Processor -> DB : 更新支付订单及业务订单状态
DB --> Processor : 更新成功
Processor --> Consumer : 返回处理结果
Consumer -> MQ : 确认消息消费 (ACK)
@enduml
```

### 8. 异常处理机制
- **消息字段校验失败**: 消息格式或关键字段不符合约定。处理方式：记录错误日志，将消息转移到死信队列（DLQ）进行人工排查，并触发告警。
- **消息幂等性问题**: 为防止因网络重试等原因导致重复消费，处理前会检查支付订单的状态。若订单已处于最终状态（成功/失败），则视为重复消息，直接确认（ACK）并忽略，不做任何业务处理。
- **关联数据不存在**: 消费时发现支付订单、或其关联的业务订单不存在。处理方式：记录严重错误日志，立即发送告警通知相关人员排查数据不一致问题。消息可根据策略进入重试队列。
- **下游系统/服务不可用**: 调用的支付结果处理服务、数据库等依赖不可用。处理方式：消费者应集成重试机制（如固定间隔、指数退避），在尝试指定次数后若仍然失败，则将消息转入死信队列，并触发熔断或告警。
- **业务处理失败**: 在支付结果处理服务内部发生可预期的业务异常。处理方式：服务内部应抛出明确的业务异常，消费者捕获后根据异常类型决定是否重试。对于不可逆的错误，记录日志并结束流程。

### 9. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
|---|---|
| 消息队列服务 | 用于接收上游系统异步发送的支付结果通知。 |
| 订单数据库 | 核心数据存储，用于查询和更新支付订单、交易订单的状态和信息。 |
| 支付结果处理服务 | 封装了支付成功或失败后的核心业务逻辑，如更新交易状态、资金处理、通知等。 |
| 统一监控告警平台 | 用于在处理过程中发生关键异常（如数据不存在、服务调用失败）时，发送实时告警。 | 