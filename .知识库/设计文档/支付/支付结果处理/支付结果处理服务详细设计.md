# 1. 服务名称
支付结果处理服务

# 2. 服务说明
本服务用于处理来自支付网关的最终支付结果（成功或失败）。服务会根据支付结果更新系统内的支付订单和交易订单状态，确保数据的一致性。该服务是支付流程闭环的关键环节，处理交易订单的后续状态流转。

# 3. 类路径及方法签名
- **类路径**: `com.howbuy.dtms.order.service.business.payment.ProcessPmtResultBusiness`
- **方法签名**: `void process(HwPaymentOrderPO hwPaymentOrder, HwDealOrderPO hwDealOrder, List<HwDealOrderDtlPO> hwDealOrderDtlList, PaymentResultDTO paymentResult)`

# 4. 方法入参表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 支付订单 | `hwPaymentOrder` | `HwPaymentOrderPO` | 是 | | 待更新的支付订单实体，处理前状态应为"付款中"。 |
| 交易订单 | `hwDealOrder` | `HwDealOrderPO` | 是 | | 关联的交易订单实体，处理前状态应为"付款中"。 |
| 交易订单明细列表 | `hwDealOrderDtlList` | `List<HwDealOrderDtlPO>` | 是 | | 关联的交易订单明细列表。 |
| 支付结果 | `paymentResult` | `PaymentResultDTO` | 是 | | 从外部支付网关接收到的支付结果数据。 |

**`PaymentResultDTO` 关键字段:**

| 中文名 | 英文名 | 类型 | 字段说明 |
|---|---|---|---|
| 支付状态 | `txPmtFlag` | String | 支付结果的标志，例如 "S" 代表成功, "F" 代表失败。 |
| 支付渠道返回码 | `ret_code` | String | 支付渠道返回的原始处理码。 |
| 支付渠道返回描述| `ret_desc`| String | 支付渠道返回的原始处理描述。 |
| 支付渠道流水号 | `out_pmt_deal_no` | String | 支付渠道生成的唯一交易流水号。 |

# 5. 方法出参表
该方法没有返回值(`void`)。处理结果通过业务异常或系统异常向上层调用者传递。

# 6. 关键业务逻辑说明
1.  **并发控制**：服务开始时，根据支付订单号(`payment_order_no`)获取分布式锁，确保同一笔支付订单的结果不会被并发处理。
2.  **事务处理**：整个更新过程在一个数据库事务中执行，保证数据操作的原子性。
3.  **更新支付订单** (`hw_payment_order`)：
    -   根据 `PaymentResultDTO` 的内容，更新支付订单的支付标志 (`tx_pmt_flag`)、返回码 (`ret_code`)、返回描述 (`ret_desc`)、外部流水号 (`out_pmt_deal_no`)、对账日期 (`pmt_check_dt`) 和完成时间 (`pmt_complete_dtm`)。
    -   使用乐观锁进行更新：`UPDATE` 语句的 `WHERE` 条件中必须包含前置状态 `tx_pmt_flag='4'` (付款中) 和查询时获得的 `update_timestamp` 值。
    -   如果更新影响行数不为 1，说明记录状态已被改变，记录异常日志，发送监控预警，并抛出异常中断流程。
4.  **处理交易订单** (`hw_deal_order`) - 仅当订单类型 `order_type` 为 `1` (交易) 时执行：
    -   **支付成功** (`paymentResult.txPmtFlag` 为成功状态):
        -   更新交易订单的支付状态 `pay_status` 为 `4` (成功)。
        -   同样采用乐观锁机制进行更新，前置状态为 `pay_status='2'` (付款中)。
        -   冲突校验逻辑同上，失败则记录日志、预警并抛出异常。
    -   **支付失败** (`paymentResult.txPmtFlag` 为失败状态):
        -   更新交易订单的支付状态 `pay_status` 为 `5` (失败)，同时更新订单状态 `order_status` 为 `6` (强制取消)。
        -   同样采用乐观锁机制，前置状态为 `pay_status='2'` (付款中)。
        -   冲突校验逻辑同上。
        -   **遍历更新订单明细** (`hw_deal_order_dtl`): 将每条明细的申请状态 `app_status` 更新为 `1` (申请失败)，同样采用乐观锁。
5.  **发送消息**：
    -   **订单更新消息**：无论成功或失败，调用 `SendMqService` 发送订单状态变更的MQ消息，通知下游系统。
    -   **打款确认消息**：调用 `ConfirmFundsReceivedService` 的消息发送逻辑，通知相关方资金已到账（或失败）。
6.  **释放锁**：在服务逻辑的最后(`finally` 块中)，释放分布式锁。

# 7. 流程图 (PlantUML)

```plantuml
@startuml
title 支付结果处理服务流程图
start
:获取支付订单锁;
if (获取锁失败?) then (是)
  :处理异常并终止;
  stop
endif

:开始事务;
:更新支付订单(乐观锁);
if (更新失败?) then (是)
  :回滚 & 释放锁;
  :抛出异常并终止;
  stop
endif

if (是交易订单?) then (是)
  if (支付成功?) then (是)
    :更新交易订单为"成功"(乐观锁);
    if (更新失败?) then (是)
      :回滚 & 释放锁;
      :抛出异常并终止;
      stop
    endif
  else (否)
    :更新交易订单为"失败";
    :更新订单明细为"申请失败";
    if (更新失败?) then (是)
      :回滚 & 释放锁;
      :抛出异常并终止;
      stop
    endif
  endif
endif

:提交事务;
:发送下游MQ通知;
:释放锁;
stop
@enduml
```

# 8. 时序图 (PlantUML)

```plantuml
@startuml
title 支付结果处理时序图
actor "调用方" as Caller
participant "ProcessPmtResultBusiness" as Business
participant "Database" as DB
participant "MessageQueue" as MQ

Caller -> Business: process(paymentResult)
activate Business

Business -> Business: 获取分布式锁
note right: 保证处理唯一性

Business -> DB: **在事务中执行** <br> 1. 更新支付订单 <br> 2. 更新交易订单(及明细)
activate DB
note left of DB: 使用乐观锁保证数据一致性
DB --> Business: 更新成功
deactivate DB

Business -> MQ: 发送订单状态变更等消息
activate MQ
MQ --> Business
deactivate MQ

Business -> Business: 释放分布式锁

Business --> Caller: (void)
deactivate Business

@enduml
```

# 9. 异常处理机制

| 异常场景 | 处理方式 |
|---|---|
| 获取分布式锁失败 | 记录错误日志，根据业务要求决定是重试还是直接向上抛出异常，防止并发处理。 |
| 乐观锁更新失败 | `UPDATE` 语句影响行数为 0 或不为 1 时，说明数据已被其他线程修改。此时应立即回滚事务，记录严重的错误日志，并发送系统预警通知。然后向上抛出特定的业务异常（如 `OptimisticLockingFailureException`），中断当前流程。 |
| 数据库连接或执行异常 | 事务将自动回滚。捕获 `SQLException` 或相关 `DataAccessException`，记录错误日志，并包装成系统异常向上抛出。 |
| 消息发送失败 | 消息发送的失败不应影响主干业务（数据库事务）。可采用本地消息表等最终一致性方案，或者记录错误日志由定时任务进行补偿发送。 |


# 10. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|---|---|
| `com.howbuy.dtms.order.service.business.sendmq.SendMqService` | 提供发送订单状态变更MQ消息的公共服务。 |
| `com.howbuy.dtms.order.service.service.trade.fundsreceived.ConfirmFundsReceivedService` | 提供发送打款确认消息的公共服务。 |
| 分布式锁组件 | 用于实现支付订单级别的并发控制。 |
``` 
</rewritten_file>