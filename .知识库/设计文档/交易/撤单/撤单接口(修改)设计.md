## 撤单接口接口(修改)

- 处理逻辑
  - 新增校验器(CancelOrderService、CancelOrderValidateService、AbstractCounterCancelService增加柜台在途撤单校验器)：
    - 柜台在途撤单校验
      - 校验顺序：TradeValidatorEnum.ORDER_UN_SUBMIT
      - 查询柜台订单hw_counter_audit_order关联柜台订单明细hw_counter_audit_order_dtl
        - hw_counter_audit_order_dtl.deal_no=入参订单号
        - 审核状态audit_status = 2-等待复核 或者 8-等待回访
        - counter_biz_type=revoke条件
        - 记录状态rec_stat= 0-未删除
      - 存在则抛出异常
  - com.howbuy.dtms.order.service.service.trade.cancelorder.BaseCancelOrderService#process修改：
    - 数据准备com.howbuy.dtms.order.service.business.orderupdate.OrderUpdateService#cancelOrder
      -  新建CancelOrderUpdateBO 替换OrderUpdateBO，CancelOrderUpdateBO 内部持有CancelOrderUpdateParam、List<CancelOrderDtlUpdateBO>
    - 数据修改com.howbuy.dtms.order.service.repository.HwDealOrderRepository#batchUpdateDealOrder
      - 修改交易订单-hw_deal_order：根据入参cancelType设置orderStatus、payStatus=1-未付款时设置为0-无需付款 update_timestamp为服务器时间
        - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态order_status=1-申请成功、pay_status=原状态和查询时获得的HwDealOrder.update_timestamp值。
        - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
      - 遍历修改交易订单明细-hw_deal_order_dtl：根据入参cancelType设置appStatus、cancelDate=request.appDt+appTm、cancelCause为接口入参、cancelCpAcctNo为接口入参、submitStatus为5-无需上报、update_timestamp为服务器时间
        - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态app_status=0-申请成功、submitStatus=原状态和查询时获得的HwDealOrderDtl.update_timestamp值。
        - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
      - 修改支付订单表-hw_payment_order：设置tx_pmt_flag为0-无需付款 pmt_comp_flag为0-无需对账 update_timestamp为服务器时间
        - 修改条件：UPDATE语句的WHERE条件中必须包含前置状态tx_pmt_flag=1-未付款。