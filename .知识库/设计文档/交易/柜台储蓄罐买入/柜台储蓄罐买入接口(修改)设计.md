## 柜台储蓄罐买入接口(修改)

- 请求地址

| 类名                  | 请求方式 | 接口url                                                      | 描述               |
| --------------------- | -------- | ------------------------------------------------------------ | ------------------ |
| CounterPiggyBuyFacade | Dubbo    | com.howbuy.dtms.order.client.facade.query.counterpiggybuy.CounterPiggyBuyFacade | 柜台储蓄罐买入接口 |

- 入参

| 字段             | 字段注释     | 类型       | 是否必填 | 备注     |
| ---------------- | ------------ | ---------- | -------- | -------- |
| fundTxAcctNo     | 基金交易账号 | String     | 否       | 增加字段 |
| discountRate     | 折扣率       | BigDecimal | 是       | 增加字段 |
| relationalDealNo | 关联订单号   | String     | 否       | 增加字段 |
| preSubmitTaDt    | 预计上报日期 | String     | 是       | 删除字段 |
| payEndDt         | 打款截止日期 | String     | 否       | 删除字段 |
| dealNo           | 订单号       | String     | 否       | 删除字段 |



- 修改逻辑(CounterPiggyBuyService.process())：

  - 参数校验
    - 根据入参的增加字段、删除字段以及是否必填修改tradeValidate

  - 构建买入参数
    - discountType设置为折扣率，com.howbuy.dtms.common.enums.DiscountTypeEnum
    - applyDiscountRate设置为入参-discountRate
    - fundTxAcctNo设置为入参-fundTxAcctNo

  - 构建校验链
    - TradeValidatorEnum.BUY_FEE 替换为TradeValidatorEnum.COUNTER_BUY_FEE

  - com.howbuy.dtms.order.service.business.ordercreate.OrderCreateService#createDealOrder增加分支逻辑，支付方式为支票时
    - 支付状态设置为成功
    - 打款凭证状态为无需上传
    - context.getBusinessCodeEnum()!=_112A的实际打款金额为买入参数的买入金额
    - context.getBusinessCodeEnum()==_112A的实际打款金额为买入参数的预估手续费
    - 实际打款日期=订单打款截止日期
    - 实际打款时间=订单打款截止时间

  - 删除原有的根据tradeChannel来设置支付状态、折扣率的逻辑
    - 打款凭证状态设置为无需上传
    - 入参关联订单号非空时则设置订单的关联订单号字段

  - 出参订单号从orderCreateBO.getHwDealOrderDtl()中获取