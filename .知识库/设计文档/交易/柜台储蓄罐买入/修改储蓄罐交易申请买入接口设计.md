## 修改储蓄罐交易申请买入接口

- 请求地址

| 类名                           | 请求方式 | 接口url                                                      | 描述                       |
|------------------------------| -------- | ------------------------------------------------------------ | -------------------------- |
| UpdatePiggyTradeAppBuyFacade | Dubbo    | com.howbuy.dtms.order.client.facade.trade.piggytradeapp.UpdatePiggyTradeAppBuyFacade | 修改储蓄罐交易申请买入接口 |

- 入参

| 字段         | 字段注释   | 类型   | 是否必填 | 备注 |
| ------------ | ---------- | ------ | -------- | ---- |
| importAppId  | 导入申请id | String | 是       |      |
| buyAmt       | 买入金额   | String | 是       |      |
| discountRate | 折扣率     | String | 是       |      |
| operator     | 操作人     | String | 是       |      |
| remark       | 备注       | String | 是       |      |



- 处理逻辑(UpatePiggyTradeAppBuyService.process())：
  - 参数校验
    - 入参非空校验
    - 校验买入金额的格式以及必须大于0
    - 校验折扣率的格式以及必须大于等于0且小于1
    - 查询储蓄罐交易申请记录HwPiggyTradeAppImportPO，查询条件importAppId为入参importAppId，是否删除为否
    - 校验HwPiggyTradeAppImportPO具体字段
      - 【中台业务码】=申购，否则抛出异常：赎回记录不允许修改
      - 【储蓄罐申请来源】=【可用余额、excel】，否则抛出异常：控管表下发数据不允许修改
      - 【生成状态】=0-未生成，否则抛出异常：当前记录订单生成状态不是未生成
    - 校验客户信息
      - 根据HwPiggyTradeAppImportPO的香港客户号，调用HkCustInfoOuterService#getHkCustInfo查询客户信息，客户信息不存在，则抛出异常
  - 前置查询
    - 调用QueryFundInfoOuterService#queryFundBasicInfo查询基金信息，入参：HwPiggyTradeAppImportPO的基金代码
    - 调用QueryFundInfoOuterService#queryFundFeeRateByAppAmt查询基金费率，入参：客户信息的投资者类型、、业务代码为BusinessCodeEnum.PURCHASE.getCode()、收款方collectRecipient为CollectRecipientEnum.HOWBUY.getCode
  - 计算手续费、申请金额，在TradeUtils中增加公共方法
    - 根据TradeUtils.isJPYCurrency判断根据基金的币种类型进行截位：日元截位精度为Constants.JPY_AMOUNT_SCALE，其他币种截位精度Constants.AMOUNT_SCALE 
    -  基金信息的费用计算方式为0-外扣法：
      1. 手续费（精度处理）=净申购金额*交易费率*折扣率
      2. 申请金额 = 净申购金额 + 手续费 
    - 基金信息的费用计算方式为1-内扣法：
      1. 手续费（精度处理）=净申购金额*交易费率*折扣率
      2. 净申购金额=申请金额
  - 更新HwPiggyTradeAppImportPO，设置买入金额、手续费、折扣率、申请金额、备注、修改时间为服务器时间。
    - UPDATE语句的WHERE条件为是否删除为未删除，是否生成为未生成。
    - 若UPDATE影响行数不为1，则打印异常日志并发送异常监控预警。