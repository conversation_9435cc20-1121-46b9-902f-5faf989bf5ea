## 批量储蓄罐交易申请买入接口

- 请求地址

|                             |       |                                                              |                            |
| --------------------------- | ----- | ------------------------------------------------------------ | -------------------------- |
| BatchPiggyTradeAppBuyFacade | Dubbo | com.howbuy.dtms.order.client.facade.trade.piggytradeapp.BatchPiggyTradeAppBuyFacade | 批量储蓄罐交易申请买入接口 |

- 入参

|                      |                        |        |      |                                                            |
| -------------------- | ---------------------- | ------ | ---- | ---------------------------------------------------------- |
| piggyTradeAppBuyList | 储蓄罐申请买入列表开始 | Array  | 是   |                                                            |
| importAppId          | 导入申请id             | String | 是   | 控管表编号                                                 |
| piggyAppSource       | 储蓄罐申请来源         | String | 是   | 0-excel 1-可用余额、2-客户控管表、3-退款控管表、4-自动赎回 |
| hkCustNo             | 香港客户号             | String | 是   |                                                            |
| fundTxAcctNo         | 基金交易账号           | String | 是   |                                                            |
| appAmt               | 申请金额               | String | 是   |                                                            |
| relationalDealNo     | 关联订单号             | String | 否   |                                                            |
| piggyTradeAppBuyList | 储蓄罐申请买入列表结束 | Array  | 是   |                                                            |

- 出参

|             |          |        |      |
| ----------- | -------- | ------ | ---- |
| code        | 状态码   | String |      |
| description | 描述信息 | String |      |



- 处理逻辑(BatchPiggyTradeAppBuy.process())：

  - 参数校验：

    - piggyTradeAppBuyList不等于空
    - piggyTradeAppBuyList.size<=200
    - 校验importAppId是否已存在未生成、已生成的记录
      - 根据入参piggyTradeAppBuyList得到importAppId列表
      - 根据importAppId列表查询hw_piggy_trade_app_import中是否删除为0-未删除的记录
        - 遍历构建非生成失败列表（是否生成为0-未生成或者1-已生成）和生成失败列表 （是否生成为2-生成失败）
        - 有效记录列表大于0，则抛出异常：申请Id{XXXX,YYYY,...}已存在

  - 前置查询

    - 查询当前可购买的储蓄罐获取基金信息

      - QueryFundInfoOuterService#querySupportBuyPiggyBuyFundList 
      - 查询不到，则抛出异常

    - 去重list的客户号，批量查询客户信息HkCustInfoOuterService#getHkCustInfoList，获取客户信息列表

    - 查询开放日信息，获取

      预计上报日

      - 判断基金代码是否已收市
        -  查询DtmsSettleOuterService#queryCounterCloseStatus，入参：基金代码
        - counterCloseStatusVO.getStat()=YesOrNoEnum.YES.getValue()为已收市，否则未收市
      - 调用QueryFundInfoOuterService#QueryOpenDtInfoRequest，入参：基金代码、业务代码为BusinessCodeEnum.PURCHASE.getCode()、间隔天数为0、申请日期为当前系统日期、申请时间：基金信息的下单结束时间 + （x分-已收市时x为1分钟，未收市时x为-1分钟）
        - 返回为空，则抛出异常：查询基金开放日信息失败
        - 否则返回查询结果的预计上报日期

    - 查询费率，QueryFundInfoOuterService#queryFundFeeRateList，获取费率map，投资者类型为key，费率信息列表为value

      - 调用QueryFundInfoOuterService#queryFundFeeRateList，入参：基金代码、投资者类型列表为客户信息列表的投资者类型去重获取、业务代码为BusinessCodeEnum.PURCHASE.getCode()、收款方collectRecipient为CollectRecipientEnum.HOWBUY.getCode()

    - 批量查询客户申请折扣，QueryFundInfoOuterService#queryCustFeeDiscount，获取客户申请折扣map，香港客户号为key，value为申请折扣信息

    - 调用QueryFundInfoOuterService#queryCustFeeDiscount，入参：申请日期=当前服务日期，FeeTypeEnum.SUB_FEE.getCode()，基金代码，客户号列表

  - 遍历piggyTradeAppBuyList，构建HwPiggyTradeAppImportPO

    - import_app_id取piggyTradeAppBuyList的importAppId
    - 基金交易账号取piggyTradeAppBuyList的基金交易账号
    - 导入日期取服务器系统日期
    - 创建人取值sys
    - relational_deal_no取piggyTradeAppBuyList的relationalDealNo
    - 客户信息相关字段取值客户信息列表
    - 基金信息相关字段取值基金信息
    - 预计上报日字段取值
    - 申请金额取值piggyTradeAppBuyList的申请金额
    - 中台业务码取BusinessCodeEnum.PURCHASE.getMCode()
    - 支付方式取值：储蓄罐申请来源为可用余额、excel时取PayMethodEnum.CHEQUE.getCode，否则取值PayMethodEnum.ELECTRIC_REMITTANCE.getCode
    - 手续费、买入金额、折扣率赋值
      - 根据费率map、客户申请折扣map、piggyTradeAppList的申请金额计算手续费、净申请金额，买入金额为计算净申请金额，手续费为计算手续费
      - 折扣率取Min(客户申请折扣率,1)
      - 交易费率
        - 根据客户信息的投资者类型获取费率列表
        - 过滤费率列表的取费率方式为GetFeeRateMethodEnum.GET_FEE_RATE_METHOD_AMT.getCode()
        - 根据申请金额匹配客户申请折扣列表：申请金额大于等于费率金额下限、小于等于费率金额上限
      - 根据TradeUtils.isJPYCurrency判断根据基金的币种类型进行截位：日元截位精度为Constants.JPY_AMOUNT_SCALE，其他币种截位精度Constants.AMOUNT_SCALE
      - 基金信息的费用计算方式为0-外扣法：
        1. 净申购金额（精度处理）＝申请金额/(1＋交易费率*折扣率)
        2. 手续费=申请金额-净申购金额
      - 基金信息的费用计算方式为1-内扣法：
        1. 净申购金额=申请金额
        2. 手续费（精度处理）=净申购金额*交易费率*折扣率
      - 根据净申请金额<=0校验，校验失败则抛出异常：申请Idxxx的申请金额{}，手续费{}，买入金额小于0。

  - 数据事务处理

    - 批量修改hw_piggy_trade_app_import的是否删除为1-已删除，修改条件：是否生成为2-生成失败，importAppId 在生成失败列表的importAppId列表范围内
      - 修改成功条数!=生成失败列表条数，则抛出异常：逻辑删除生成失败记录失败
    - 批量插入HwPiggyTradeAppImportPO列表