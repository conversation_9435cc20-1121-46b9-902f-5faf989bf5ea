## APP基金购买接口（修改）

- 请求地址

|              |       |                                                              |                 |
| ------------ | ----- | ------------------------------------------------------------ | --------------- |
| BuyWebFacade | DUBBO | com.howbuy.dtms.order.client.facade.trade.buy.BuyWebFacade.execute() | APP基金购买接口 |

- 接口逻辑：

  - 历史代码逻辑不变

  - 改动点1： com.howbuy.dtms.order.service.business.ordercreate.OrderCreateService#createOrder处理

    - 调用com.howbuy.dtms.order.service.commom.utils.TradeUtils#isSubsPurchase判断是否为买入订单且（hw_deal_order_dtl.middle_busi_code!=112A || （hw_deal_order_dtl.middle_busi_code=112A && hw_deal_order_dtl.estimate_fee>0）），如果是调用createPaymentOrder方法（新增方法）

      - 根据

        com.howbuy.dtms.order.dao.bo

        .OrderCreateBO的订单、订单明细创建PaymentOrderPO（hw_payment_order）并进行赋值

        - OrderCreateBO.hwDealOrder、OrderCreateBO.hwDealOrderDtl进行基础字段赋值：
          deal_no、payment_type_list、hk_cust_no、cp_acct_no、fund_tx_acct_no、fund_code、currency、outlet_code、trade_channel
        - pmt_deal_no=SequenceService.getSnowflakeIdDefault()
        - 支付方式为2-支票时，tx_pmt_flag为2-付款成功，pmt_comp_flag为0-无需对账。否则tx_pmt_flag为1-未付款，pmt_comp_flag为1-未对账
        - pmt_amt= hwDealOrder.middle_busi_code!=112A则取值hw_deal_order_dtl.appAmt，否则取值hw_deal_order_dtl.estimate_fee
        - pmt_check_dt=hw_deal_order_dtl.pay_end_dt
        - order_type=1-交易

      - PaymentOrderPO设置到OrderCreateBO中

  - 改动点2：com.howbuy.dtms.order.service.repository.HwDealOrderRepository#saveDealOrder处理

    - 判断OrderCreateBO.PaymentOrderPO非空，则进行PaymentOrderPO新增操作

  - 改动点3：com.howbuy.dtms.order.service.business.ordermessage.OrderMessageService#buyOrderMessage处理

    - 判断OrderCreateBO.PaymentOrderPO非空，则进行

      支付消息发送（mq方式 便于后续应用结构调整）

      - 消息topic：DTMS_ORDER_ASYNC_DO_PAYMENT
      - 消息体：
        - AsyncDoPaymentMessageDTO
        - 属性：pmt_deal_no