### 1. 任务名称
生成资金退款文件调度任务

### 2. 任务说明
本任务由定时调度系统触发，负责处理待退款的资金记录。核心功能是查询数据库中所有未处理的资金退款申请，将其汇总生成一个标准格式的文件，并将文件生成结果通过消息队列通知下游系统。这是一个周期性执行的批量数据处理任务。

### 3. 消息来源
- **触发方式**: 定时调度
- **MQ Topic 名称**: `DTMS_ORDER_EC_TASK_CAPITAL_REFUND_FILE`
- **消息 Tag（如有）**: 无
- **MQ 消息消费方式**: Push

### 4. 消息格式说明
| 中文名 | 字段名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 工作日校验开关 | `checkWorkday` | String | 是 | "1" | 值为"1"时，任务会校验当天是否为香港工作日；为其他值则跳过校验。 |

### 5. 核心业务处理逻辑
1.  **接收并解析消息**：任务启动后，从`DTMS_ORDER_EC_TASK_CAPITAL_REFUND_FILE`主题消费一条消息，并解析消息体。
2.  **工作日校验**：
    *   判断消息中的`checkWorkday`字段是否为"1"。
    *   如果是，则调用外部清算服务判断当天是否为香港工作日。如果不是工作日，则记录日志并直接结束本次任务。
    *   如果否，则跳过工作日校验。
3.  **查询待处理数据**：调用核心业务处理模块，查询`资金退款表(hw_capital_refund)`中所有处理状态为“未处理”的记录。如果查询结果为空，则说明当前没有需要处理的数据，任务正常结束。
4.  **创建文件处理记录**：
    *   在`文件处理记录表(io_file_process_rec)`中插入一条新的记录，文件类型设置为“资金退款文件”，文件状态和处理状态均设置为“未处理”。
5.  **文件生成与状态更新**：
    *   使用乐观锁机制，将文件处理记录的`文件状态`更新为“处理中”，防止并发问题。
    *   调用文件生成服务，开始生成退款文件。
    *   **数据查询**：再次查询`资金退款表(hw_capital_refund)`中所有“未处理”的记录。
    *   **文件写入**：将查询到的数据按照以下格式写入文件：`订单号`、`冻结号`、`香港客户号`、`基金交易账号`、`基金代码`、`基金简称`、`基金币种`、`管理人代码`、`业务类型`、`支付方式`、`回款方向`、`退款金额`、`退款日期`。
    *   **数据落库与状态更新**：文件成功生成后，将文件中的数据批量插入到`资金退款导出流水表(io_capital_refund_export)`，并批量更新`资金退款表(hw_capital_refund)`中对应记录的处理状态为“已处理”。
    *   **更新文件记录**：将`文件处理记录表`中的`文件状态`更新为“生成成功”。
6.  **发送文件通知**：
    *   向`DTMS_ORDER_CAPITAL_REFUND_FILE`主题发送一条通知消息，消息内容包含文件类型、文件路径、文件名、退款日期等信息，供下游系统消费。
7.  **更新最终状态**：将`文件处理记录表`中的`处理状态`更新为“处理成功”，标志着整个流程顺利完成。

### 6. 流程图 (PlantUML)

```plantuml
@startuml
title 生成资金退款文件处理流程
start
:接收调度消息;
if (需要工作日校验?) then (是)
  if (当天是香港工作日?) then (是)
    :继续执行;
  else (否)
    :结束任务;
    stop
  endif
else (否)
  :继续执行;
endif
:查询"未处理"的退款数据;
if (有数据?) then (是)
  :创建文件处理记录 (io_file_process_rec);
  :更新文件记录状态为"处理中";
  :生成退款文件;
  :批量写流水表 (io_capital_refund_export);
  :批量更新退款表状态为"已处理" (hw_capital_refund);
  :更新文件记录状态为"生成成功";
  :发送文件生成成功通知MQ;
  :更新文件记录状态为"处理成功";
else (否)
  :结束任务;
endif
stop
@enduml
```

### 7. 时序图 (PlantUML)

```plantuml
@startuml
title 生成资金退款文件时序图
autonumber

participant "调度系统" as Scheduler
participant "消息消费者" as Consumer
participant "清算服务" as SettleSvc
participant "数据库" as DB
participant "通知消息队列" as NotifyMQ

Scheduler -> Consumer: 发送任务触发消息
Consumer -> SettleSvc: (可选)查询是否为工作日
SettleSvc --> Consumer: 返回工作日判断结果
Consumer -> DB: 查询未处理的退款数据
alt 有退款数据
    Consumer -> DB: 插入文件处理记录
    Consumer -> DB: 更新文件记录状态(处理中)
    Consumer -> DB: 生成文件(查询、写入、更新)
    Consumer -> NotifyMQ: 发送文件生成通知
    Consumer -> DB: 更新文件记录状态(处理成功)
end
@enduml
```

### 8. 异常处理机制
- **消息格式错误**：若接收到的消息体不符合约定（如关键字段缺失），记录错误日志后，消息将被丢弃，任务终止。
- **重复消费**：文件处理记录的创建与状态更新基于数据库的原子操作和乐观锁，可有效防止因消息重复消费导致的数据处理错误。
- **下游系统不可用**：
    - 若**清算服务**调用失败，任务将重试。若达到最大重试次数后仍失败，则记录严重错误日志、发送告警，并终止任务。
    - 若**数据库**连接异常，任务将进行重试。
- **业务处理失败**：
    - 在文件生成过程中若发生任何异常（如数据查询失败、文件写入IO错误），`try-catch`块会捕获异常。
    - 捕获异常后，会将`文件处理记录表`中的`文件状态`更新为“生成失败”，记录详细错误日志并发送告警，以便人工介入。任务不会自动重试核心业务逻辑。

### 9. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|---|---|
| 定时调度中心 | 负责按预设规则周期性地触发本任务。 |
| 清算服务 | 提供查询指定日期是否为香港工作日的外部接口。 |
| 消息队列(MQ) | 用于接收调度任务消息和发送文件处理结果通知。 |
| 数据库(MySQL) | 存储资金退款数据、文件处理记录及导出流水。 |
| 文件存储服务 | 存放最终生成的资金退款文件。 | 