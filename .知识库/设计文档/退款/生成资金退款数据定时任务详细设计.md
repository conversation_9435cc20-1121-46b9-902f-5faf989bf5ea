## 1. 任务名称
生成资金退款数据定时任务

## 2. 任务说明
该任务用于定期扫描系统中需要进行资金退款的订单数据，并生成相应的退款记录。任务主要处理两种业务场景：
1.  客户付款成功后，订单被撤销（包括客户自行撤销或系统强制取消），需要将款项退还给客户。
2.  客户使用“储蓄罐”发起的赎回订单成功，但该笔赎回资金原计划用于支付的另一笔申购订单最终支付失败，因此这笔赎回款项需要退还给客户。

任务通过生成退款数据，为后续的实际退款操作提供数据支持。

## 3. 消息来源
- **触发方式**: 定时调度。由调度中心在预定时间（如每日凌晨）触发，向消息队列（MQ）发送一条任务启动消息。
- **MQ Topic 名称**: `DTMS_ORDER_EC_TASK_CAPITAL_REFUND_DATA`
- **消息 Tag（如有）**: 无
- **MQ 消息消费方式**: Push（推模式），消费者被动接收消息。

## 4. 消息格式说明
消息体为JSON格式，用于传递任务执行的控制参数。

| 中文名 | 字段名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 工作日校验 | workdayCheck | Boolean | 否 | true | 控制是否需要在香港工作日才执行任务。`true`表示需要校验，`false`或不传此字段表示不校验，直接执行。 |

## 5. 核心业务处理逻辑
1.  **任务启动**: 消费者从`DTMS_ORDER_EC_TASK_CAPITAL_REFUND_DATA`主题接收到任务处理消息。
2.  **工作日校验**:
    - 解析消息体，获取`workdayCheck`字段。
    - 如果`workdayCheck`为`true`，则调用外部的**dtmsSettleOuterService.queryWorkday()**，查询当天是否为香港工作日。
    - 如果非工作日，则记录日志并终止本次任务。
3.  **查询待退款数据**: 任务会分两种情况查询需要退款的订单。
    
    **情况一：查询付款成功后已撤单的退款数据**
    - 在数据库中查询同时满足以下条件的订单：
      - 订单支付状态为“成功”。
      - 订单状态为“自行撤销”或“强制取消”。
      - 订单记录为有效状态。
      - 订单的最后更新时间大于当前时间-10天(控制数据查询范围)。
      - 关联的支付订单记录为“付款成功”且“对账完成”。
      - 在资金退款表中不存在此订单的有效退款记录（防止重复处理）。
    - 如果查询到结果，则根据订单号批量查询这些订单的明细信息。
    - 遍历查询结果，将订单信息和订单明细信息聚合成退款数据结构，其中：
      - **退款金额**：汇总所有订单明细的“申购金额”，DealDtlMergeUtil#mergeAppAmt。
      - **退款日期**：取当前服务器日期。
      - **处理状态**：置为“未处理”。
      - **退款去向**：置为“现金余额”。

    **情况二：查询储蓄罐赎回成功且原关联订单支付失败的退款数据**
    - 在数据库中查询同时满足以下条件的赎回订单：
      - 订单状态为“部分确认”或“确认成功”。
      - 中台业务码为“赎回”。
      - 订单记录为有效状态。
      - 订单的最后更新时间大于当前时间-10天(控制数据查询范围)。
      - 此赎回订单所关联的原始申购订单状态为“强制取消”，且支付状态为“失败”。
      - 关联的支付订单记录为“付款失败”且“对账完成”。
      - 在资金退款表中不存在此赎回订单的有效退款记录。
    - 如果查询到结果，则根据订单号批量查询这些订单的明细信息。
    - 遍历查询结果，聚合成退款数据结构，其中：
      - **退款金额**：汇总所有订单明细的“确认金额”，DealDtlMergeUtil#mergeAckAmt。
      - **退款日期**：取当前服务器日期。
      - **处理状态**：置为“未处理”。
      - **退款去向**：置为“现金余额”。
      - **支付方式**：置为空。

4.  **数据入库**:
    - 将上述两种情况查询并构建出的退款数据合并到一个列表中。
    - 如果列表不为空，则将所有新的退款数据分批次批量插入到**资金退款表**中。
5.  **任务结束**: 记录本次任务执行结果日志，如成功插入的退款记录数量。

## 6. 流程图（使用 PlantUML）
```plantuml
@startuml
title 生成资金退款数据定时任务处理流程 (简化版)

start
:接收MQ任务消息;
if (需要工作日校验?) then (是)
  :校验香港工作日;
  if (非工作日?) then (是)
    :终止任务;
    end
  endif
endif

:查询两种场景下的待退款订单
(付款后撤单/赎回成功但原申购失败);

if (存在待退款订单?) then (是)
    :聚合信息，构建退款数据列表;
    :批量插入退款数据到数据库;
    :记录成功日志;
else (否)
    :记录无数据日志;
endif

end
@enduml
```

## 7. 时序图（使用 PlantUML）
```plantuml
@startuml
title 生成资金退款数据任务时序图 (简化版)

participant 调度中心 as Scheduler
participant "消息队列(MQ)" as MQ
participant "订单服务" as OrderService
participant 外部服务 as External
participant 数据库 as DB

Scheduler -> MQ: 触发任务
MQ -> OrderService: 推送任务消息

alt 需要工作日校验
    OrderService -> External: 校验香港工作日
    External --> OrderService: 返回结果
end

OrderService -> DB: 查询两种场景的待退款订单
DB --> OrderService: 返回订单数据列表

alt 存在待退款订单
    OrderService -> OrderService: 构建退款数据
    OrderService -> DB: 批量插入退款数据
    DB --> OrderService: 操作成功
end

OrderService -> OrderService: 记录任务执行日志

@enduml
```

## 8. 异常处理机制
- **消息格式错误**: 如果接收到的MQ消息体JSON格式不正确，任务将捕获解析异常，记录错误日志，并放弃该消息，不进行重试。
- **消息幂等性**: 核心查询逻辑中包含了“在资金退款表中不存在此订单的有效退款记录”的判断条件，天然保证了任务的可重复执行性。即使任务因异常重试或重复触发，也不会对同一笔订单生成重复的退款记录。
- **下游系统不可用**:
  - **结算服务**: 如果调用结算服务失败（如网络超时、服务异常），任务将进行有限次数的重试（例如3次）。若最终仍失败，则记录严重错误日志，发送告警，并终止本次任务。
  - **数据库**: 如果数据库访问失败，Spring事务机制会确保操作回滚。任务处理失败后，MQ的重试机制会将消息重新投递，在数据库恢复后可重新处理。
- **消息处理失败**:
  - 在业务逻辑处理过程中发生的任何预期之外的异常，都会被捕获并记录详细的错误日志。
  - 根据MQ消费者配置，失败的消息会进行重试。若达到最大重试次数后仍然失败，消息将被投递到死信队列（DLQ），等待人工介入排查。

## 9. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
|---|---|
| **调度中心** | 负责按预设规则定时触发任务，向MQ发送消息。 |
| **消息队列 (MQ)** | 用于任务的异步触发和解耦，支持可靠的消息投递和重试机制。 |
| **结算服务** | 外部微服务，提供查询指定日期是否为香港工作日的功能。 |
| **数据库 (MySQL)** | 持久化存储订单数据、支付数据和资金退款数据。 | 