# PaymentCheckService 支付对账服务逻辑校验项脑图

## Mermaid 脑图结构

```mermaid
graph LR
    A[支付对账服务 PaymentCheckService] --> B[并发控制校验]
    A --> C[支付订单查询校验]
    A --> D[支付订单遍历处理]
    A --> E[支付外部服务调用]
    A --> F[对账结果比较校验]
    A --> G[对账结果记录处理]
    A --> H[支付订单状态更新]

    B --> B1[✅ 正例-通过: 获取并发锁成功]
    B --> B2[❌ 反例-不通过: 并发锁获取失败<br/>方法执行被阻塞或跳过]

    C --> C1[查询条件校验]
    C --> C2[分页查询执行]

    C1 --> C11[✅ 正例-通过: pmtCompFlagList非空且有效]
    C1 --> C12[❌ 反例-不通过: pmtCompFlagList为空<br/>查询条件不完整]

    C2 --> C21[✅ 正例-通过: 分页查询500条记录成功<br/>条件: pmt_comp_flag in list, tx_pmt_flag=2或3, rec_stat=1]
    C2 --> C22[❌ 反例-不通过: 数据库查询异常<br/>SQL执行失败或超时]

    D --> D1[支付订单存在性校验]
    D --> D2[异常处理机制]

    D1 --> D11[✅ 正例-通过: 支付订单列表非空]
    D1 --> D12[❌ 反例-不通过: 支付订单列表为空<br/>无需处理的对账数据]

    D2 --> D21[✅ 正例-通过: try-catch正常执行]
    D2 --> D22[❌ 反例-不通过: 异常被捕获<br/>记录异常日志并继续处理下一条]

    E --> E1[支付外部服务调用]
    E --> E2[服务返回结果校验]

    E1 --> E11[✅ 正例-通过: PayOuterService.queryPayResult调用成功]
    E1 --> E12[❌ 反例-不通过: 外部服务调用失败<br/>网络异常或服务不可用]

    E2 --> E21[✅ 正例-通过: 服务返回pmtDealNo非空]
    E2 --> E22[❌ 反例-不通过: 服务返回pmtDealNo为空<br/>支付侧订单不存在]
    E2 --> E23[✅ 正例-通过: 服务返回pmtCompFlag=2对账完成]
    E2 --> E24[❌ 反例-不通过: 服务返回pmtCompFlag≠2<br/>返回空，无需处理]

    F --> F1[支付金额比较]
    F --> F2[交易支付标记比较]
    F --> F3[币种比较]
    F --> F4[对账结果判定]

    F1 --> F11[✅ 正例-通过: 服务返回pmtAmt = 订单pmtAmt]
    F1 --> F12[❌ 反例-不通过: 支付金额不一致<br/>构建对账不平结果]

    F2 --> F21[✅ 正例-通过: 服务返回tx_pmt_flag = 订单tx_pmt_flag]
    F2 --> F22[❌ 反例-不通过: 交易支付标记不一致<br/>构建对账不平结果]

    F3 --> F31[✅ 正例-通过: 服务返回currency = 订单currency]
    F3 --> F32[❌ 反例-不通过: 币种不一致<br/>构建对账不平结果]

    F4 --> F41[✅ 正例-通过: 所有字段一致<br/>pmt_comp_flag=2对账完成]
    F4 --> F42[❌ 反例-不通过: 存在字段不一致<br/>pmt_comp_flag=3对账不平]

    G --> G1[对账结果存在性校验]
    G --> G2[对账结果记录操作]
    G --> G3[异常监控告警]

    G1 --> G11[✅ 正例-通过: 根据pmt_deal_no查询到已存在记录]
    G1 --> G12[❌ 反例-不通过: 对账结果记录不存在<br/>需要新增记录]

    G2 --> G21[✅ 正例-通过: 新增hw_payment_check_result成功]
    G2 --> G22[✅ 正例-通过: 更新hw_payment_check_result成功]
    G2 --> G23[❌ 反例-不通过: 数据库操作失败<br/>INSERT或UPDATE异常]

    G3 --> G31[✅ 正例-通过: 异常监控告警发送成功]
    G3 --> G32[❌ 反例-不通过: 监控告警发送失败<br/>告警系统异常]

    H --> H1[乐观锁更新校验]
    H --> H2[更新结果校验]
    H --> H3[异常处理]

    H1 --> H11[✅ 正例-通过: 乐观锁更新成功<br/>WHERE条件包含update_timestamp]
    H1 --> H12[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1]

    H2 --> H21[✅ 正例-通过: 支付订单状态更新成功<br/>pmt_comp_flag和update_timestamp已更新]
    H2 --> H22[❌ 反例-不通过: 支付订单状态更新失败<br/>数据库更新异常]

    H3 --> H31[✅ 正例-通过: 正常处理完成]
    H3 --> H32[❌ 反例-不通过: 乐观锁冲突处理<br/>异常日志+监控预警+抛异常]
```

## 详细校验项说明

### 支付对账服务 PaymentCheckService

#### 1. 并发控制校验
- **正例-通过**: 获取并发锁成功，方法正常执行
- **反例-不通过**: 
  - 并发锁获取失败，方法执行被阻塞或跳过
  - 多个线程同时执行对账任务导致数据冲突

#### 2. 支付订单查询校验
- **查询条件校验**
  - **正例-通过**: pmtCompFlagList参数非空且包含有效的对账标记值
  - **反例-不通过**: pmtCompFlagList为空或null，查询条件不完整
- **分页查询执行**
  - **正例-通过**: 分页查询500条记录成功，查询条件包括pmt_comp_flag in list、tx_pmt_flag=2或3、rec_stat=1、时间范围等
  - **反例-不通过**: 数据库查询异常，SQL执行失败或超时

#### 3. 支付订单遍历处理
- **支付订单存在性校验**
  - **正例-通过**: 查询到的支付订单列表非空，有待处理的对账数据
  - **反例-不通过**: 支付订单列表为空，无需处理的对账数据
- **异常处理机制**
  - **正例-通过**: try-catch机制正常工作，单条记录异常不影响整体处理
  - **反例-不通过**: 异常被捕获，记录异常日志并继续处理下一条记录

#### 4. 支付外部服务调用
- **支付外部服务调用**
  - **正例-通过**: PayOuterService.queryPayResult调用成功，返回支付结果数据
  - **反例-不通过**: 外部服务调用失败，网络异常或支付服务不可用
- **服务返回结果校验**
  - **正例-通过**: 服务返回pmtDealNo非空且pmtCompFlag=2对账完成
  - **反例-不通过**: 
    - 服务返回pmtDealNo为空，支付侧订单不存在
    - 服务返回pmtCompFlag≠2，返回空无需处理

#### 5. 对账结果比较校验
- **支付金额比较**
  - **正例-通过**: 外部服务返回的pmtAmt等于订单表中的pmtAmt
  - **反例-不通过**: 支付金额不一致，构建对账不平结果
- **交易支付标记比较**
  - **正例-通过**: 外部服务返回的tx_pmt_flag等于订单表中的tx_pmt_flag
  - **反例-不通过**: 交易支付标记不一致，构建对账不平结果
- **币种比较**
  - **正例-通过**: 外部服务返回的currency等于订单表中的currency
  - **反例-不通过**: 币种不一致，构建对账不平结果
- **对账结果判定**
  - **正例-通过**: 所有字段一致，设置pmt_comp_flag=2对账完成
  - **反例-不通过**: 存在字段不一致，设置pmt_comp_flag=3对账不平

#### 6. 对账结果记录处理
- **对账结果存在性校验**
  - **正例-通过**: 根据pmt_deal_no查询到已存在的对账结果记录，执行更新操作
  - **反例-不通过**: 对账结果记录不存在，需要执行新增操作
- **对账结果记录操作**
  - **正例-通过**: 新增或更新hw_payment_check_result表记录成功
  - **反例-不通过**: 数据库操作失败，INSERT或UPDATE异常
- **异常监控告警**
  - **正例-通过**: 异常监控告警发送成功，及时通知相关人员
  - **反例-不通过**: 监控告警发送失败，告警系统异常

#### 7. 支付订单状态更新
- **乐观锁更新校验**
  - **正例-通过**: 乐观锁更新成功，WHERE条件包含查询时的update_timestamp值
  - **反例-不通过**: 乐观锁冲突，UPDATE语句影响行数不等于1
- **更新结果校验**
  - **正例-通过**: 支付订单状态更新成功，pmt_comp_flag和update_timestamp字段已更新
  - **反例-不通过**: 支付订单状态更新失败，数据库更新异常
- **异常处理**
  - **正例-通过**: 正常处理完成，无异常情况
  - **反例-不通过**: 乐观锁冲突处理，打印异常日志+发送监控预警+抛出异常
