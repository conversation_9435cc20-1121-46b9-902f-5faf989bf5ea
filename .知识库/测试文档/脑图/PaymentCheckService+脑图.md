# 支付对账服务校验项脑图

## 支付结果消息处理 PaymentResultNotifyProcessor

```mermaid
graph LR
    A[支付结果消息处理 PaymentResultNotifyProcessor] --> B[支付订单存在性校验]
    A --> C[订单类型校验]
    A --> D[交易订单校验]
    A --> E[支付订单信息回填]
    A --> F[支付结果处理调用]

    B --> B1[✅ 正例-通过: 支付订单存在]
    B --> B2[❌ 反例-不通过: 支付订单不存在<br/>异常日志+监控预警]

    C --> C1[✅ 正例-通过: order_type=1-交易]
    C --> C2[✅ 正例-通过: order_type≠1-非交易，跳过订单校验]

    D --> D1[交易订单存在性校验]
    D --> D2[订单明细存在性校验]

    D1 --> D11[✅ 正例-通过: HwDealOrder存在]
    D1 --> D12[❌ 反例-不通过: HwDealOrder不存在<br/>异常日志+监控预警]

    D2 --> D21[✅ 正例-通过: HwDealOrderDtl存在]
    D2 --> D22[❌ 反例-不通过: HwDealOrderDtl不存在<br/>异常日志+监控预警]

    E --> E1[✅ 正例-通过: 支付订单信息回填成功<br/>更新tx_pmt_flag, ret_code, ret_desc, out_pmt_deal_no, pmt_check_dt, pmt_complete_dtm]
    E --> E2[❌ 反例-不通过: 支付订单信息回填失败<br/>数据库更新异常]

    F --> F1[✅ 正例-通过: ProcessPmtResultService.process调用成功]
    F --> F2[❌ 反例-不通过: ProcessPmtResultService.process调用失败<br/>业务处理异常]
```

## 支付对账服务 PaymentCheckService

```mermaid
graph LR
    G[支付对账服务 PaymentCheckService] --> H[并发控制校验]
    G --> I[支付订单查询校验]
    G --> J[外部支付服务查询校验]
    G --> K[对账结果比较校验]
    G --> L[对账结果处理校验]
    G --> M[支付订单状态更新校验]

    H --> H1[✅ 正例-通过: 获取并发锁成功]
    H --> H2[❌ 反例-不通过: 并发冲突<br/>等待或拒绝执行]

    I --> I1[查询条件校验]
    I --> I2[分页查询校验]

    I1 --> I11[✅ 正例-通过: pmt_comp_flag in pmtCompFlagList<br/>tx_pmt_flag=2或3, rec_stat=1]
    I1 --> I12[✅ 正例-通过: pmtCheckDt非空时按日期查询]
    I1 --> I13[✅ 正例-通过: pmtCheckDt为空时按时间范围查询<br/>当前时间>app_dtm>180天前]

    I2 --> I21[✅ 正例-通过: 分页500条查询成功]
    I2 --> I22[❌ 反例-不通过: 查询异常<br/>数据库连接或SQL异常]

    J --> J1[✅ 正例-通过: PayOuterService.queryPayResult调用成功]
    J --> J2[❌ 反例-不通过: 外部服务调用失败<br/>网络异常或服务异常]

    K --> K1[支付订单号校验]
    K --> K2[对账状态校验]
    K --> K3[金额一致性校验]
    K --> K4[支付标记一致性校验]
    K --> K5[币种一致性校验]

    K1 --> K11[✅ 正例-通过: 服务返回.pmtDealNo非空]
    K1 --> K12[❌ 反例-不通过: 服务返回.pmtDealNo为空<br/>构建对账结果pmt_comp_flag=3, 备注:支付侧订单不存在]

    K2 --> K21[✅ 正例-通过: 服务返回.pmtCompFlag=2-对账完成]
    K2 --> K22[❌ 反例-不通过: 服务返回.pmtCompFlag≠2<br/>返回空，不处理]

    K3 --> K31[✅ 正例-通过: 服务返回.pmtAmt=HwPaymentOrderPO.pmtAmt]
    K3 --> K32[❌ 反例-不通过: 金额不一致<br/>构建对账结果pmt_comp_flag=3, 备注:支付金额不一致]

    K4 --> K41[✅ 正例-通过: 服务返回.tx_pmt_flag=HwPaymentOrderPO.tx_pmt_flag]
    K4 --> K42[❌ 反例-不通过: 支付标记不一致<br/>构建对账结果pmt_comp_flag=3, 备注:交易支付标记不一致]

    K5 --> K51[✅ 正例-通过: 服务返回.currency=HwPaymentOrderPO.currency]
    K5 --> K52[❌ 反例-不通过: 币种不一致<br/>构建对账结果pmt_comp_flag=3, 备注:币种不一致]

    L --> L1[对账结果存在性校验]
    L --> L2[对账结果操作校验]

    L1 --> L11[✅ 正例-通过: hw_payment_check_result不存在，执行新增]
    L1 --> L12[✅ 正例-通过: hw_payment_check_result存在，执行更新]

    L2 --> L21[✅ 正例-通过: 对账结果新增成功+异常监控告警]
    L2 --> L22[✅ 正例-通过: 对账结果更新成功]
    L2 --> L23[❌ 反例-不通过: 对账结果操作失败<br/>数据库操作异常]

    M --> M1[乐观锁更新校验]
    M --> M2[更新结果校验]

    M1 --> M11[✅ 正例-通过: 乐观锁更新成功<br/>WHERE条件包含update_timestamp]
    M1 --> M12[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1, 异常日志+监控预警+抛异常]

    M2 --> M21[✅ 正例-通过: 支付订单状态更新成功<br/>pmt_comp_flag更新, update_timestamp更新为当前时间]
    M2 --> M22[❌ 反例-不通过: 支付订单状态更新失败<br/>数据库更新异常]
```

## 详细校验项说明

### 支付结果消息处理校验项

**支付订单存在性校验**
- ✅ 正例-通过：根据pmtDealNo查询到支付订单hw_payment_order
- ❌ 反例-不通过：支付订单不存在，打印异常日志并发送异常监控预警

**订单类型校验**  
- ✅ 正例-通过：order_type=1-交易，需要校验交易订单
- ✅ 正例-通过：order_type≠1-非交易，跳过交易订单校验

**交易订单校验**
- ✅ 正例-通过：HwDealOrder和HwDealOrderDtl都存在
- ❌ 反例-不通过：订单或订单明细不存在，打印异常日志并发送异常监控预警

**支付订单信息回填**
- ✅ 正例-通过：成功更新tx_pmt_flag、ret_code、ret_desc、out_pmt_deal_no、pmt_check_dt、pmt_complete_dtm
- ❌ 反例-不通过：数据库更新失败

### 支付对账服务校验项

**并发控制校验**
- ✅ 正例-通过：成功获取并发控制锁，避免重复执行
- ❌ 反例-不通过：并发冲突，等待或拒绝执行

**支付订单查询校验**
- ✅ 正例-通过：按条件分页查询500条支付订单成功
  - pmt_comp_flag in pmtCompFlagList
  - tx_pmt_flag = 2-付款成功 or 3-付款失败  
  - rec_stat=1
  - 时间条件满足
- ❌ 反例-不通过：查询异常，数据库连接或SQL异常

**外部支付服务查询校验**
- ✅ 正例-通过：PayOuterService.queryPayResult调用成功返回结果
- ❌ 反例-不通过：外部服务调用失败，网络异常或服务异常

**对账结果比较校验**
- ✅ 正例-通过：支付订单号非空且对账状态=2，金额、支付标记、币种都一致
- ❌ 反例-不通过：
  - 支付侧订单不存在：pmtDealNo为空，pmt_comp_flag=3
  - 数据不一致：金额、支付标记或币种不一致，pmt_comp_flag=3
  - 对账未完成：pmtCompFlag≠2，返回空不处理

**乐观锁更新校验**
- ✅ 正例-通过：WHERE条件包含update_timestamp，UPDATE影响行数=1
- ❌ 反例-不通过：乐观锁冲突，UPDATE影响行数≠1，异常日志+监控预警+抛异常

**对账结果处理校验**
- ✅ 正例-通过：根据是否存在选择新增或更新hw_payment_check_result
- ❌ 反例-不通过：数据库操作失败