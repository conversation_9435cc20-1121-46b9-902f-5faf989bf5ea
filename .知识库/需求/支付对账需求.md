## 支付结果消息处理

- 类：PaymentResultNotifyProcessor extends MessageProcessor
- MQ：
  - TOPIC：TOPIC_HK_PAY_PAY_RESULT_NOTIFY
  - 消息体：PaymentResultNotifyMessageDTO
    - pmtDealNo、txPmtFlag、pmtCompFlag、retCode、retDesc、outPmtDealNo、pmtCheckDt、orderType、、currency、pmtAmt
- 处理逻辑(PaymentResultNotifyService)：
  - 根据支付通知消息的支付订单号查询支付订单hw_payment_order
  - 校验支付订单是否存在，校验失败，则打印异常日志并发送异常监控预警
  - 如果order_type=1-交易
    - 根据支付订单的订单号查询订单HwDealOrder、订单明细HwDealOrderDtl
      - 校验订单及订单明细
      - 校验订单、订单明细是否存在
    - 校验失败
      - 打印异常日志并发送异常监控预警
  - 根据支付通知消息的信息回填支付订单
    - tx_pmt_flag=接口返回 
    - ret_code=接口返回
    - ret_desc=接口返回
    - out_pmt_deal_no=接口返回
    - pmt_check_dt=接口返回
    - pmt_complete_dtm=当前时间
  - 调用支付结果处理（com.howbuy.dtms.order.service.business.payment.ProcessPmtResultService.process(HwPaymentOrderPO,HwDealOrder,List<HwDealOrderDtl>,PaymentResultDTO)） 



## 支付对账服务（PaymentCheckService）

- 处理逻辑paymentCheckService.paymentCheck(pmtCheckDt, List<String> pmtCompFlagList)：
  -  添加paymentCheck方法的并发控制
  -  分页500条查询支付订单表-hw_payment_order，条件
    - pmt_comp_flag in pmtCompFlagList
    - tx_pmt_flag = 2-付款成功 or 3-付款失败
    - rec_stat=1
    - 入参 pmtCheckDt 非空时
      - pmt_check_dt= 入参pmtCheckDt   
    - 否则
      - 结束时间（当前时间）>申请时间app_dtm> 开始时间（180天前）
    - 申请时间app_dtm 排升序
  - 遍历未对账支付订单列表（HwPaymentOrderPO）
    - try...catch调用check方法
      - 调用支付外部服务(PayOuterService.queryPayResult)
      - 服务返回.pmtDealNo非空 且 服务返回.pmtCompFlag!=2-对账完成，则返回空
      - 服务返回.pmtAmt !=HwPaymentOrderPO.pmtAmt 或者 服务返回.tx_pmt_flag!=HwPaymentOrderPO.tx_pmt_flag 或者 服务返回.currency!=HwPaymentOrderPO.currency，则构建支付对账结果HwPaymentCheckResultPO，pmt_comp_flag：3-对账不平，备注：支付金额、交易支付标记、币种 不一致
      - 服务返回.pmtDealNo为空时，则构建支付对账结果HwPaymentCheckResultPO，pmt_comp_flag：3-对账不平，备注：支付侧订单不存在
      - 服务返回.pmtAmt =HwPaymentOrderPO.pmtAmt 并且 服务返回.tx_pmt_flag=HwPaymentOrderPO.tx_pmt_flag 并且 服务返回.currency=HwPaymentOrderPO.currency，则构建支付对账结果HwPaymentCheckResultPO，pmt_comp_flag：2-对账完成
    - 根据返回的HwPaymentCheckResultPO处理
      - HwPaymentCheckResultPO==null，则不处理
      - 根据支付订单号pmt_deal_no查询支付对账结果hw_payment_check_result判断是否存在
        - 不存在则新增hw_payment_check_result并发送异常监控告警
        - 否则则更新
      - 进行hw_payment_order更新：设置pmt_comp_flag为HwPaymentCheckResultPO.pmt_comp_flag，update_timestamp为当前服务器时间
        - 乐观锁实现：UPDATE语句的WHERE条件中必须包含查询时获得的HwPaymentOrderPO.update_timestamp值。
        - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。