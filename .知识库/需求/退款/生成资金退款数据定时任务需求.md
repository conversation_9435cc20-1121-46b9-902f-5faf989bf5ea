## 生成资金退款数据定时任务

- 类：GenerateCapitalRefundDataProcessor extends BatchMessageProcessor
- MQ：DTMS_ORDER_EC_TASK_CAPITAL_REFUND_DATA
- 消息体：是否工作日校验
- 处理逻辑：
  - 判断当前是否香港工作日
    - 是否工作日校验!=1-是，则跳过判断
    - 否则，dtmsSettleOuterService.queryWorkday()来判断当前日期与工作日日期是否相等，如果不相等则结束任务。
  - 调用资金退款文件处理器（CapitalRefundDataProcess）
    -  查询待退款数据，不存在则返回
      - 查询付款成功已撤单的退款数据
        - 查询订单表hw_deal_order关联支付订单表hw_payment_order（关联字段：hw_deal_order.deal_no = hw_payment_order.deal_no），条件
          - hw_deal_order.pay_status=4-成功
          - hw_deal_order.order_status=5-自行撤销 or 6-强制取消
          - hw_deal_order.rec_stat=0
          - hw_payment_order.tx_pmt_flag=2-付款成功
          - hw_payment_order.pmt_comp_flag=2-对账完成
          - hw_payment_order.rec_stat=1
          - hw_deal_order.update_timestamp>10天前
          - 在资金退款表hw_capital_refund中不存在
            - 关联字段 hw_deal_order.deal_no = hw_capital_refund.deal_no
            - hw_capital_refund.rec_stat=1
        - 根据订单表的订单号列表Lists.partition分批查询订单明细列表
        - 根据查询的订单明细列表、订单列表按订单维度合并后构建HwCapitalRefundPO
          - deal_no、relational_deal_no、hk_cust_no、middle_busi_code、payment_type(取payment_type_list)从hw_deal_order获取
          - fund_tx_acct_no、fund_abbr、fund_currency、fund_man_code从hw_deal_order_dtl列表中取第一条
          - refund_amt从hw_deal_order_dtl.app_amt汇总获取，使用com.howbuy.dtms.order.service.commom.utils.DealDtlMergeUtil#mergeAppAmt
          - refund_dt取当前服务器日期、handle_status为0-未处理、refund_direction为2-现金余额
      - 查询储蓄罐赎回成功原关联订单已支付失败的退款数据
        - 查询订单表hw_deal_order，条件
          - hw_deal_order.order_status=2-部分确认 or 3- 确认成功
          - hw_deal_order.middle_busi_code=1124
          - hw_deal_order.rec_stat=0
          - hw_deal_order.update_timestamp>10天前
          - 买入hw_deal_order关联hw_payment_order，（关联字段：买入hw_deal_order.deal_no = hw_payment_order.deal_no）
            - 关联字段 hw_deal_order.deal_no = 买入hw_deal_order.deal_no
            - 买入hw_deal_order.pay_status=5-失败
            - 买入hw_deal_order.order_status=6-强制取消
            - 买入hw_deal_order.rec_stat=0
            - hw_payment_order.tx_pmt_flag=3-付款失败
            - hw_payment_order.pmt_comp_flag=2-对账完成
            - hw_payment_order.rec_stat=1
          - 在hw_capital_refund中不存在
            - 关联字段 hw_deal_order.deal_no = hw_capital_refund.deal_no
            - hw_capital_refund.rec_stat=1
        - 根据订单表的订单号列表Lists.partition分批查询订单明细列表
        - 根据查询的订单明细列表、订单列表按订单维度合并后构建HwCapitalRefundPO
          - deal_no、relational_deal_no、hk_cust_no、middle_busi_code从hw_deal_order获取
          - fund_tx_acct_no、fund_abbr、fund_currency、fund_man_code从hw_deal_order_dtl列表中取第一条
          - refund_amt从hw_deal_order_dtl.ack_amt汇总获取，使用com.howbuy.dtms.order.service.commom.utils.DealDtlMergeUtil#mergeAckAmt
          - refund_dt取当前服务器日期、handle_status为0-未处理、refund_direction为2-现金余额、payment_type为空
    - 两个HwCapitalRefundPO汇总，通过ListSplitter实现分批插入