## 生成资金退款文件调度任务

- 类：GenerateCapitalRefundFileProcessor extends BatchMessageProcessor
- MQ：DTMS_ORDER_EC_TASK_CAPITAL_REFUND_FILE
- 消息体：是否工作日校验
- 处理逻辑：
  - 判断当前是否香港工作日
    - 是否工作日校验!=1-是，则跳过判断
    - 否则，dtmsSettleOuterService.queryWorkday()来判断当前日期与工作日日期是否相等，如果不相等则结束任务。
  - 调用资金退款文件处理器（CapitalRefundFileProcess，参考CapitalAckFileProcess）
    - 查询hw_capital_refund的处理状态为0-未处理的数据，不存在则返回
    - 构建IoFileProcessRecPO（文件类型IoFileTypeEnum-11-资金退款文件、file_status=0-未处理 process_status=0未处理 ）并插入io_file_process_rec
    - try..catch处理
      - 乐观锁 修改io_file_process_rec文件状态file_status=5-处理中
      - 调用CapitalRefundFileGenerateService(CapitalAckFileGenerateService)的generateFile
        - queryIoRecExportList：查询hw_capital_refund的处理状态为0-未处理的数据
        - 文件格式：订单号、冻结号、香港客户号、基金交易账号、基金代码、基金简称、基金币种、管理人代码、业务类型、支付方式、回款方向、退款金额、退款日期
        - afterSuccessWrite：
          - 根据list批量插入io_capital_refund_export
          - 根据list批量更新hw_capital_refund的处理状态为1-已处理
      - 修改文件记录io_file_process_rec的文件状态file_status=3-生成成功
    - 发送文件通知消息
      - 消息-topic：DTMS_ORDER_CAPITAL_REFUND_FILE
      - 消息-内容：fileType=11、filePath、fileName、refundDt
    - 修改文件记录io_file_process_rec的处理状态process_status=2-处理成功