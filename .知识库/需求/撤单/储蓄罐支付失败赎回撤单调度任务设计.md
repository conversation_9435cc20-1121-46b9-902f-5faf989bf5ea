## 储蓄罐支付失败赎回撤单调度任务

- 类：PiggyPayFailRedeemCancelProcessor extends BatchMessageProcessor
- MQ：DTMS_ORDER_EC_TASK_PIGGY_PAY_FAIL_REDEEM_CANCEL
- 消息体：是否工作日校验
- 处理逻辑：
  - 判断当前是否香港工作日
    - 是否工作日校验!=1-是，则跳过判断
    - 否则，dtmsSettleOuterService.queryWorkday()来判断当前日期与工作日日期是否相等，如果不相等则结束任务。
  - 调用储蓄罐支付失败赎回撤单处理器(PiggyPayFailRedeemCancelProcess)
    - 查询买入订单表hw_deal_order关联支付订单表hw_payment_order（关联字段：买入hw_deal_order.deal_no = hw_payment_order.deal_no），返回赎回订单的去重订单号
      - 买入hw_deal_order.pay_status=5-失败
      - 买入hw_deal_order.order_status=5-自行撤销、6-强制取消
      - 买入hw_deal_order.rec_stat=0
      - 买入hw_deal_order.update_timestamp>当前时间-10天
      - hw_payment_order.tx_pmt_flag=3-付款失败
      - hw_payment_order.pmt_comp_flag=2-对账完成
      - hw_payment_order.rec_stat=1
      - 在hw_deal_order表关联订单明细表hw_deal_order_dtl表中存在（关联字段：hw_deal_order.deal_no = hw_deal_order_dtl.deal_no）
        - 关联字段 hw_deal_order.relational_deal_no= 买入hw_deal_order.deal_no
        - hw_deal_order.middle_busi_code=1124
        - hw_deal_order.order_status=1-申请成功
        - hw_deal_order_dtl.submit_status=0-未上报 or 3-待重新上报
    - 遍历订单号列表
      - try...catch 调用柜台撤单接口-counterCancelService.process，接口调用失败则打印异常日志发送监控告警