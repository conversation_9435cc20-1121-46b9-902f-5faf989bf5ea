## 重置交易支付标识接口

- 请求地址

  | 类名                 | 请求方式 | 接口url                                       | 描述             |
  | :------------------- | :------- | :-------------------------------------------- | :--------------- |
  | ResetTxPmtFlagFacade | dubbo    | com.howbuy.dtms.order.client.facade.trade.payment | 重置支付标识接口 |

  | 字段      | 字段注释   | 类型   | 是否必填 | 备注 |
  | :-------- | :--------- | :----- | :------- | :--- |
  | pmtDealNo | 支付订单号 | String | 是       |      |

  - 出参

  | 字段        | 字段注释 | 类型   | 备注               |
  | :---------- | :------- | :----- | :----------------- |
  | code        | 状态码   | String | 状态码0000表示成功 |
  | description | 描述信息 | String |                    |

- 接口逻辑

  - 根据入参pmt_deal_no查询支付订单表-hw_payment_order
    - 校验支付订单PO-paymentOrderPO是否存在
    - 校验交易支付标识txPmtFlag是否等于4-付款中
    - 校验失败，则返回校验失败信息
  - 如果orderType=1-交易，则进行交易订单-hw_deal_order校验
    - 根据支付订单的订单号查询交易订单-hw_deal_order
      - 校验交易订单是否存在
      - 校验支付状态payStatus是否等于2-付款中、订单状态orderStatus是否为 1-申请成功
    - 校验失败，则返回校验失败信息
  - 校验支付系统是否存在订单
    - 调用支付外部服务(PayOuterService.queryPayResult)
    - 校验支付返回.pmtDealNo为空
    - 校验失败，则返回校验失败信息
  - 事务修改支付订单表-hw_payment_order、交易订单-hw_deal_order
    - 修改支付订单hw_payment_order：设置tx_pmt_flag为1-未付款、update_timestamp为服务器时间
      - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态tx_pmt_flag='4'和查询时获得的update_timestamp值。
      - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
    - order_type=1-交易
      - 修改交易订单hw_deal_order：设置pay_status为1-未付款、update_timestamp为服务器时间。
        - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态order_status='1'、pay_status='2'和查询时获得的update_timestamp值。
        - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。