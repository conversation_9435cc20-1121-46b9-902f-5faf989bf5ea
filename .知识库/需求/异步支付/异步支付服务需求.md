## 异步支付服务（AsyncDoPaymentService）

- 处理逻辑（com.howbuy.dtms.order.service.business.payment.AsyncDoPaymentService.process(pmtDealNo)）
  - 根据事件属性pmt_deal_no查询支付订单表-hw_payment_order
    - 校验支付订单PO-paymentOrderPO是否存在
    - 校验交易支付标记txPmtFlag是否为1-未付款
    - 校验失败，则打印异常日志并发送异常监控预警
  - 如果orderType=1-交易，则进行交易订单-hw_deal_order校验
    - 根据支付订单的订单号查询交易订单-hw_deal_order
      - 校验交易订单是否存在
      - 校验支付状态payStatus是否为1-未付款、订单状态orderStatus是否为 1-申请成功
    - 校验失败，则打印异常日志并发送异常监控预警
  - 校验成功
    - 支付发起前操作(事务一致处理)
      - 修改支付订单hw_payment_order：设置tx_pmt_flag为4-付款中、pmt_comp_flag为1-未对账、app_dtm为数据库服务器时间。
        - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态tx_pmt_flag='1'和查询时获得的update_timestamp值。
        - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
      - order_type=1-交易
        - 修改交易订单hw_deal_order：设置pay_status为2-付款中、update_timestamp为数据库服务器时间。
          - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态order_status='1'、pay_status='1'和查询时获得的update_timestamp值。
          - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
    - 支付发起操作
      - 调用支付外部服务(PayOuterService.callPayment)
    - 支付发起后操作
      - 根据返回结果修改支付订单hw_payment_order：设置ret_code、ret_desc、out_pmt_deal_no、update_timestamp为数据库服务器时间。
        - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态tx_pmt_flag='4'和发起支付前查询时获得的update_timestamp值。
        - 冲突校验：若UPDATE影响行数不为1，则打印异常日志并发送异常监控预警。