## 支付状态刷新---新增定时任务

- 类：RefreshPaymentResultProcessor extends BatchMessageProcessor

- MQ：DTMS_ORDER_EC_TASK_REFRESH_PAY_RESULT

- 处理逻辑(RefreshPaymentResultService)：

  - 分页查询支付中的支付订单表-hw_payment_order，条件

    - tx_pmt_flag=4-付款中
    - 结束时间（当前时间 - 10分钟）>修改时间update_timestamp > 开始时间（180天前）
    - rec_stat=1
    - update_timestamp 排升序

  - 遍历处理

    - 根据支付订单hw_payment_order的订单号查询订单HwDealOrder、订单明细HwDealOrderDtl

    - hw_payment_order.order_type=1-交易

      -  校验订单及订单明细
        - 校验订单、订单明细是否存在
      - 校验失败
        - 打印异常日志并发送异常监控预警

    - 调用支付外部服务(

      PayOuterService.queryPayResult

      )

      - 服务返回空，则不处理
      - 否则，根据支付结果回填支付订单HwPaymentOrderPO
        - tx_pmt_flag=接口返回 
        - ret_code=接口返回
        - ret_desc=接口返回
        - out_pmt_deal_no=接口返回
        - pmt_check_dt=接口返回
        - pmt_complete_dtm=当前时间

    - 调用支付结果处理（com.howbuy.dtms.order.service.business.payment.ProcessPmtResultService.process(HwPaymentOrderPO,HwDealOrder,List<HwDealOrderDtl>,PaymentResultDTO)） 