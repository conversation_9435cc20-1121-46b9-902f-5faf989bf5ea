## 支付结果消息通知处理

- 类：PaymentResultNotifyProcessor extends MessageProcessor
- MQ：
  - TOPIC：TOPIC_HK_PAY_PAY_RESULT_NOTIFY
  - 消息体：PaymentResultNotifyMessageDTO
    - pmtDealNo、txPmtFlag、pmtCompFlag、retCode、retDesc、outPmtDealNo、pmtCheckDt、orderType、、currency、pmtAmt
- 处理逻辑(PaymentResultNotifyService)：
  - 根据支付通知消息的支付订单号查询支付订单hw_payment_order
  - 校验支付订单是否存在，校验失败，则打印异常日志并发送异常监控预警
  - 如果order_type=1-交易
    - 根据支付订单的订单号查询订单HwDealOrder、订单明细HwDealOrderDtl
      - 校验订单及订单明细
      - 校验订单、订单明细是否存在
    - 校验失败
      - 打印异常日志并发送异常监控预警
  - 根据支付通知消息的信息回填支付订单
    - tx_pmt_flag=接口返回 
    - ret_code=接口返回
    - ret_desc=接口返回
    - out_pmt_deal_no=接口返回
    - pmt_check_dt=接口返回
    - pmt_complete_dtm=当前时间
  - 调用支付结果处理（com.howbuy.dtms.order.service.business.payment.ProcessPmtResultService.process(HwPaymentOrderPO,HwDealOrder,List<HwDealOrderDtl>,PaymentResultDTO)） 