## 支付结果处理服务（ProcessPmtResultService）

- 处理逻辑（com.howbuy.dtms.order.service.business.payment.ProcessPmtResultBusiness.process(HwPaymentOrderPO,HwDealOrder,List<HwDealOrderDtl>,PaymentResultDTO)） 
  - 根据支付订单号加并发锁
  - 事务更新支付结果
    - 修改支付订单-hw_payment_order：根据PaymentResultDTO设置tx_pmt_flag、ret_code、ret_desc、out_pmt_deal_no、pmt_check_dt、pmt_complete_dtm、update_timestamp为数据库服务器时间
      - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态tx_pmt_flag='4'和查询时获得的HwPaymentOrderPO.update_timestamp值。
      - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
    - order_type=1-交易
      - PaymentResultDTO.txPmtFlag=付款成功
        - 修改交易订单-hw_deal_order：设置pay_status为4-成功、 update_timestamp为数据库服务器时间
          - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态pay_status=2-付款中和查询时获得的HwDealOrder.update_timestamp值。
          - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
      - PaymentResultDTO.txPmtFlag=付款失败
        - 修改交易订单-hw_deal_order：设置pay_status为5-失败、order_status为6-强制取消、 update_timestamp为数据库服务器时间 
          - 乐观锁实现：UPDATE语句的WHERE条件中必须包含前置状态pay_status=2-付款中和查询时获得的HwDealOrder.update_timestamp值。
          - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
        - 遍历修改订单明细-hw_deal_order_dtl：设置app_status为1-申请失败、update_timestamp为数据库服务器时间 
          - 乐观锁实现：UPDATE语句的WHERE条件中必须包含查询时获得的HwDealOrderDtl.update_timestamp值。
          - 冲突校验：若UPDATE影响行数不为1，则打印异常日志，发送异常监控预警，并抛出异常。
  - order_type=1-交易 且 PaymentResultDTO.txPmtFlag!=付款中，则订单修改消息发送
    - com.howbuy.dtms.order.service.business.sendmq.SendMqService#sendOrderUpdateMessage
  - order_type=1-交易 且 PaymentResultDTO.txPmtFlag=付款成功，则发送打款确认消息，参考com.howbuy.dtms.order.service.service.trade.fundsreceived.ConfirmFundsReceivedService#sendMsg