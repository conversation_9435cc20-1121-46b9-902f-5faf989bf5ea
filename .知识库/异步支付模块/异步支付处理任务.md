# 异步支付处理任务详细设计文档

### 1. 任务名称
异步支付处理任务

### 2. 任务说明
本任务用于处理异步发起的支付请求。它消费消息队列中的支付指令，根据支付订单号完成支付状态校验、交易订单关联校验、调用外部支付网关、并最终更新支付结果等一系列操作。该任务是订单支付流程核心的一环，确保支付请求的最终一致性。

### 3. 消息来源
- **触发方式**：上游业务系统完成订单创建后，异步发送MQ消息触发。
- **MQ Topic 名称**：`DTMS_ORDER_PAYMENT_TOPIC`
- **消息 Tag**：`ASYNC_PAY`
- **MQ 消息消费方式**：Push消费

### 4. 消息格式说明
消息体为JSON格式，包含需要处理的支付订单号。

| 中文名     | 字段名      | 类型   | 是否必填 | 示例值                | 字段说明               |
|------------|-------------|--------|----------|-----------------------|------------------------|
| 支付订单号 | `pmtDealNo` | Long   | 是       | `20240101123456789`   | 待处理的支付订单唯一标识 |

### 5. 核心业务处理逻辑
1.  从消息队列接收到支付处理消息，并解析获取 `pmtDealNo`。
2.  **参数校验**：判断 `pmtDealNo` 是否为空，如果为空，则记录错误日志，流程终止。
3.  **查询支付订单**：根据 `pmtDealNo` 查询 `hw_payment_order`（海外支付订单表），并确认记录状态 `rec_stat` 为 `1` (正常)。
4.  **支付订单校验**：
    -   如果查询结果为空，说明订单不存在，记录错误日志，流程终止。
    -   如果查询结果不为空，则判断支付标识 `tx_pmt_flag` 是否为 `1` (未付款)。若不是，则说明订单状态已被改变（可能已支付或支付中），为避免重复处理，记录错误日志，流程终止。
5.  **关联交易订单校验**：判断支付订单中的订单类型 `order_type` 是否为 `1` (交易类)。
    -   如果是交易类，则使用支付订单中的 `deal_no` 查询 `hw_deal_order` (海外订单表)，并确认记录状态 `rec_stat` 为 `1` (正常)。
    -   校验该交易订单的申请状态是否为 `0` (申请成功)。若不是，则说明前置交易未成功，无法支付，记录错误日志，流程终止。
6.  **更新订单为付款中**：使用乐观锁机制更新 `hw_payment_order` 表。
    -   将支付标识 `tx_pmt_flag` 设置为 `4` (付款中)。
    -   将支付对账标记 `pmt_comp_flag` 设置为 `1` (未对账)。
    -   更新 `update_timestamp` 为当前系统时间。
    -   更新条件为 `pmt_deal_no`、原始 `tx_pmt_flag` ('1') 及原始 `update_timestamp`。
    -   如果更新影响行数为0，说明订单状态已被其他线程修改，记录并发冲突日志，流程终止。
7.  **调用外部支付**：调用已封装的支付网关服务（com.howbuy.dtms.order.service.outerservice.payment.PaymentOuterService），发起实际支付请求。
8.  **更新支付结果**：根据 `pmt_deal_no`，将支付网关返回的 `返回码`、`返回描述`、`外部支付订单号` 以及最新的 `update_timestamp` 更新回 `hw_payment_order` 表，并打印更新日志。

### 6. 流程图 (PlantUML)
```plantuml
@startuml
title 异步支付处理流程
start
:接收异步支付消息;
:解析消息获取支付订单号 (pmtDealNo);
if (支付订单号为空?) then (是)
  :记录异常日志;
  stop
endif
:根据支付订单号查询支付订单 (hw_payment_order);
if (支付订单不存在 或 记录无效?) then (是)
  :记录异常日志;
  stop
endif
if (支付标识 (tx_pmt_flag) 不为 "1-未付款"?) then (是)
  :记录异常日志;
  stop
endif
if (订单类型 (order_type) 为 "1-交易类"?) then (是)
  :根据订单号查询交易订单 (hw_deal_order);
  if (交易订单不存在 或 交易状态不为 "0-申请成功"?) then (是)
    :记录异常日志;
    stop
  endif
endif
:乐观锁更新支付订单状态为 "4-付款中";
if (更新失败 (影响行数为0)?) then (是)
  :记录并发更新失败日志;
  stop
endif
:调用外部支付网关发起支付;
:根据支付订单号更新支付结果 (外部返回码、描述、外部订单号);
:记录更新日志;
stop
@enduml
```

### 7. 时序图 (PlantUML)
```plantuml
@startuml
title 异步支付处理时序图
actor "消息队列 (MQ)" as MQ
participant "支付消费者服务" as Consumer
database "订单数据库" as DB
participant "外部支付网关" as PaymentGateway
MQ -> Consumer: 发送支付消息 (pmtDealNo)
activate Consumer
Consumer -> Consumer: 校验消息参数 (pmtDealNo)
alt 参数无效
    Consumer -> Consumer: 记录错误日志
    destroy Consumer
end
Consumer -> DB: 查询支付订单 (hw_payment_order)
activate DB
DB --> Consumer: 返回支付订单信息
deactivate DB
Consumer -> Consumer: 校验支付订单状态
alt 状态不合法
    Consumer -> Consumer: 记录错误日志
    destroy Consumer
end
alt 订单类型为 "交易类"
    Consumer -> DB: 查询交易订单 (hw_deal_order)
    activate DB
    DB --> Consumer: 返回交易订单信息
    deactivate DB
    Consumer -> Consumer: 校验交易订单状态
    alt 状态不合法
        Consumer -> Consumer: 记录错误日志
        destroy Consumer
    end
end
Consumer -> DB: 更新支付订单状态为 "付款中" (乐观锁)
activate DB
DB --> Consumer: 返回更新结果
deactivate DB
alt 更新失败
    Consumer -> Consumer: 记录并发异常日志
    destroy Consumer
end
Consumer -> PaymentGateway: 调用支付接口
activate PaymentGateway
PaymentGateway --> Consumer: 返回支付结果
deactivate PaymentGateway
Consumer -> DB: 更新支付订单支付结果
activate DB
DB --> Consumer: 返回更新结果
deactivate DB
Consumer -> Consumer: 记录操作日志
deactivate Consumer
@enduml
```

### 8. 异常处理机制
- **消息格式错误**：若消息体无法解析或关键字段 `pmtDealNo` 缺失，记录错误日志，消息将被丢弃，不进行重试。
- **业务校验失败**：
    - 支付订单不存在、状态不正确、或关联的交易订单状态不正确，均视为业务逻辑错误。记录详细错误日志后，流程终止，不进行重试。
- **幂等性处理**：通过校验支付订单状态 `tx_pmt_flag` 是否为 `1` (未付款) 来保证幂等性。一旦订单状态被更新，重复的消息将会被此检查拦截，防止重复支付。
- **并发更新失败**：采用乐观锁更新支付状态，若更新失败，说明有其他实例正在处理该订单。此时应记录日志并正常结束，不视为错误。
- **下游系统依赖**：
    - **数据库**：数据库连接或操作异常，应触发重试机制。
    - **外部支付网关**：调用支付网关超时或失败（如网络异常、对方服务不可用），应触发重试。若达到最大重试次数后依然失败，需将消息投递至死信队列（DLQ），并触发告警，由人工介入处理。

### 9. 调用的公共模块或外部依赖
| 模块名称         | 功能简述                     |
|------------------|------------------------------|
| 消息队列 (MQ)    | 接收异步支付指令             |
| 订单数据库 (MySQL) | 存储和更新支付订单、交易订单信息 |
| 外部支付网关     | 发起实际的支付操作           |
| 日志服务         | 记录处理过程中的信息、警告和错误 |
| 告警系统         | 在发生关键错误时（如进入死信队列）发送通知 | 