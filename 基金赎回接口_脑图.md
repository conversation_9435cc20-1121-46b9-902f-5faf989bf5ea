# 基金赎回接口测试脑图

```plantuml
@startmindmap
* 基金赎回接口(FundSellWebFacade.execute)
** 基础参数校验
*** 正例：必填参数完整(appDt,appTm,tradeChannel,outletCode,ipAddress,externalDealNo)
*** 反例：参数为空 - 报错C020002参数错误
** 并发控制校验
*** 正例：外部订单号未被锁定
*** 反例：外部订单号已被锁定 - 报错C020003并发异常
** 账户状态校验(ACCOUNT_STATUS)
*** 正例：客户状态正常(custState=1)
*** 反例：客户状态异常 - 报错C021002客户状态异常
** 交易密码校验(TRADE_PASSWORD)
*** 正例：交易密码正确
*** 反例：交易密码错误 - 报错C021008交易密码错误
*** 反例：交易密码状态异常 - 报错C023064交易密码状态异常
** 资金账号校验(CP_ACCT_NO)
*** 正例：资金账号存在且有效
*** 反例：资金账号不存在 - 报错C021009银行卡不存在
** 基金交易账号状态校验(FUND_TX_ACCT_STAT)
*** 正例：基金交易账号状态正常
*** 反例：基金交易账号状态异常 - 报错C021083基金交易账户状态异常
*** 反例：基金交易账号不存在 - 报错C021084基金交易账户不存在
*** 反例：基金交易账号类型错误 - 报错C023065基金交易账户类型错误
** 预约单号校验(PREBOOK_DEAL_NO_VALID)
*** 正例：预约单号有效或为空
*** 反例：预约单号错误 - 报错C021036预约单号错误
*** 反例：预约单号已被使用 - 报错C021050预约单号已被使用
** 订单号校验(DEAL_NO_VALID)
*** 正例：订单号有效且未被使用
*** 反例：订单号已被使用 - 报错C021055订单号已被使用
** 外部订单号校验(EXTERNAL_ORDER_NO)
*** 正例：外部订单号未被使用
*** 反例：外部订单号已被使用 - 报错C021056外部订单号已被使用
** 产品渠道校验(PRODUCT_CHANNEL)
*** 正例：渠道允许该业务
*** 反例：渠道不允许该业务 - 报错C021011渠道不允许该业务
** 产品业务校验(PRODUCT_BUSINESS)
*** 正例：产品已开通赎回业务
*** 反例：产品未开通该业务 - 报错C021012产品未开通该业务
** 开放日校验(OPEN_DT)
*** 正例：当前日期为产品开放日
*** 反例：产品不在开放期内 - 报错C021048产品不在开放期内
** 产品净值状态校验(PRODUCT_NAV_STATUS)
*** 正例：产品净值状态正常
*** 反例：产品净值状态不正常 - 报错C021017产品的净值状态不正常
*** 反例：基金净值为空 - 报错C023074基金净值为空
** 赎回方式校验(REDEMPTION_METHOD)
*** 正例：赎回方式匹配产品配置
**** 产品支持全部赎回方式
**** 产品支持按金额赎回且请求为按金额赎回
**** 产品支持按份额赎回且请求为按份额赎回
*** 反例：赎回方式不匹配 - 报错C021024赎回方式错误
** 赎回方向校验(REDEMPTION_DIRECTION)
*** 正例：普通赎回方向
*** 正例：储蓄罐赎回且已签署储蓄罐协议
*** 反例：储蓄罐赎回但未签署协议 - 报错C021040客户未签署海外储蓄罐协议或协议未生效
** 可用份额校验(AVAILABLE_SHARE)
*** 按金额赎回
**** 正例：申请金额<=可用份额*净值*折价比例
**** 反例：申请金额>可用份额*净值*折价比例 - 报错C021028客户份额不足
**** 反例：卖出金额为空 - 报错C020002参数错误
*** 按份额赎回
**** 正例：申请份额<=可用份额
**** 反例：申请份额>可用份额 - 报错C021028客户份额不足
**** 反例：卖出份额为空 - 报错C020002参数错误
**** 全部赎回判断：剩余份额=0时设置全部赎回标识
** 卖出最低持有校验(SELL_MIN_HOLD)
*** 客户层面最低持有份额校验
**** 正例：剩余份额>=客户最低持有份额
**** 反例：剩余份额<客户最低持有份额 - 报错C021025不满足客户的最低持有份额
*** 基金层面最低持有份额校验
**** 正例：基金剩余份额>=基金最低持有份额
**** 反例：基金剩余份额<基金最低持有份额 - 报错C021026不满足基金的最低持有份额
** 卖出交易限额校验(SELL_TRADE_LIMIT)
*** 按金额赎回限额校验
**** 正例：申请金额在最小最大限额范围内
**** 反例：申请金额<最小限额 - 报错C021051申请金额低于下限
**** 反例：申请金额>最大限额 - 报错C021052申请金额超过上限
*** 按份额赎回限额校验
**** 正例：申请份额在最小最大限额范围内(非全部赎回)
**** 反例：申请份额<最小限额 - 报错C021053申请份额低于下限
**** 反例：申请份额>最大限额 - 报错C021054申请份额超过上限
**** 特殊情况：全部赎回时跳过最小限额校验
** 订单创建
*** 正例：成功创建订单和订单明细
*** 反例：订单创建失败 - 报错C020996数据库异常
** 订单保存
*** 正例：成功保存到数据库
*** 反例：保存失败 - 报错C020996数据库异常
** 订单消息发送
*** 正例：成功发送赎回订单消息
*** 反例：消息发送失败 - 系统日志记录
** 返回结果
*** 正例：返回成功响应包含订单号
*** 反例：系统异常 - 报错C020001系统异常
** 赎回方式枚举校验
*** 正例：赎回方式枚举存在(BY_AMOUNT/BY_SHARE)
*** 反例：赎回方式不存在 - 报错C020002参数错误
** 赎回方向枚举校验
*** 正例：赎回方向枚举存在
*** 反例：赎回方向不存在 - 报错C020002参数错误
** 边界值测试
*** 金额精度：最多2位小数
*** 份额精度：根据基金配置的份额精度
*** 最小金额：0.01
*** 最大金额：根据基金限额配置
*** 最小份额：根据基金限额配置
*** 最大份额：根据基金限额配置
@endmindmap
```
