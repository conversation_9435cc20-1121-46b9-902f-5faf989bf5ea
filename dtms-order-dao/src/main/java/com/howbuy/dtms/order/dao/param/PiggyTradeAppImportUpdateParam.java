package com.howbuy.dtms.order.dao.param;

import lombok.Getter;
import lombok.Setter;
import java.io.Serializable;
import java.util.Date;

/**
 * @description: 储蓄罐申请单更新参数
 * <AUTHOR>
 * @date 2025/07/17 16:28
 * @since JDK 1.8
 */
@Getter
@Setter
public class PiggyTradeAppImportUpdateParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 储蓄罐申请Id
     */
    private String piggyAppId;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 是否生成（1-已生成，0-未生成）
     */
    private String isGenerated;

    /**
     * 订单号
     */
    private String dealNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 旧是否生成状态（0-未生成）
     */
    private String oldIsGenerated;
} 