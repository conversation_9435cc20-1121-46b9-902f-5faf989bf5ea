package com.howbuy.dtms.order.dao.mapper.mysql.order;

import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/7/23 16:35
 * @since JDK 1.8
 */
public interface HwPaymentOrderPOMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(HwPaymentOrderPO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(HwPaymentOrderPO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    HwPaymentOrderPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(HwPaymentOrderPO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(HwPaymentOrderPO record);
}