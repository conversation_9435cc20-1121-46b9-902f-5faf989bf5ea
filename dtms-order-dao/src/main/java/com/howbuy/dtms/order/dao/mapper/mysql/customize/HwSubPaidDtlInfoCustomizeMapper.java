package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.po.HwSubPaidDtlInfoPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description: 认缴实缴明细信息自定义Mapper
 * @date 2025-04-07 14:15:06
 * @since JDK 1.8
 */
public interface HwSubPaidDtlInfoCustomizeMapper {

    /**
     * @description: 根据订单号、基金代码、基金交易账号和香港客户号查询认缴实缴明细信息
     * @param dealNo	 订单号
     * @param fundCode	 基金代码
     * @param fundTxAcctNo	 基金交易账号
     * @param hkCustNo 	香港客户号
     * @return com.howbuy.dtms.order.dao.po.HwSubPaidDtlInfoPO
     * @author: jinqing.rao
     * @date: 2025/6/12 19:51
     * @since JDK 1.8
     */
    HwSubPaidDtlInfoPO selectByDealNoAndFundCodeAndFundTxAcctNo(@Param("dealNo") Long dealNo, @Param("fundCode") String fundCode,
                                                                @Param("fundTxAcctNo")  String fundTxAcctNo, @Param("hkCustNo") String hkCustNo);
} 