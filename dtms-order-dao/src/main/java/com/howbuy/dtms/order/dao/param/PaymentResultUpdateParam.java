package com.howbuy.dtms.order.dao.param;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 支付结果更新参数对象
 * @author: shaoyang.li
 * @date: 2025-07-08 11:20:37
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentResultUpdateParam implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 支付订单号
     */
    private Long pmtDealNo;

    /**
     * 支付状态标志
     */
    private String txPmtFlag;

    /**
     * 返回码
     */
    private String retCode;

    /**
     * 返回描述
     */
    private String retDesc;

    /**
     * 外部支付订单号
     */
    private String outPmtDealNo;

    /**
     * 对账日期
     */
    private String pmtCheckDt;

    /**
     * 原始支付状态标志
     */
    private String originalTxPmtFlag;

    /**
     * 原始更新时间戳
     */
    private Date originalUpdateTimestamp;

    /**
     * 当前时间戳
     */
    private Date currentTimestamp;

    /**
     * 支付完成时间戳
     */
    private Date pmtCompleteTimestamp;

    /**
     * @description: 构建器模式创建参数对象
     * @return PaymentResultUpdateParamBuilder
     * @author: shaoyang.li
     * @date: 2025-07-08 11:20:37
     * @since JDK 1.8
     */
    public static PaymentResultUpdateParamBuilder builder() {
        return new PaymentResultUpdateParamBuilder();
    }

    /**
     * @description: 构建器类
     * @author: shaoyang.li
     * @date: 2025-07-08 11:20:37
     * @since JDK 1.8
     */
    public static class PaymentResultUpdateParamBuilder {
        private PaymentResultUpdateParam param = new PaymentResultUpdateParam();

        public PaymentResultUpdateParamBuilder pmtDealNo(Long pmtDealNo) {
            param.setPmtDealNo(pmtDealNo);
            return this;
        }

        public PaymentResultUpdateParamBuilder txPmtFlag(String txPmtFlag) {
            param.setTxPmtFlag(txPmtFlag);
            return this;
        }

        public PaymentResultUpdateParamBuilder retCode(String retCode) {
            param.setRetCode(retCode);
            return this;
        }

        public PaymentResultUpdateParamBuilder retDesc(String retDesc) {
            param.setRetDesc(retDesc);
            return this;
        }

        public PaymentResultUpdateParamBuilder outPmtDealNo(String outPmtDealNo) {
            param.setOutPmtDealNo(outPmtDealNo);
            return this;
        }

        public PaymentResultUpdateParamBuilder pmtCheckDt(String pmtCheckDt) {
            param.setPmtCheckDt(pmtCheckDt);
            return this;
        }

        public PaymentResultUpdateParamBuilder originalTxPmtFlag(String originalTxPmtFlag) {
            param.setOriginalTxPmtFlag(originalTxPmtFlag);
            return this;
        }

        public PaymentResultUpdateParamBuilder originalUpdateTimestamp(Date originalUpdateTimestamp) {
            param.setOriginalUpdateTimestamp(originalUpdateTimestamp);
            return this;
        }

        public PaymentResultUpdateParamBuilder currentTimestamp(Date currentTimestamp) {
            param.setCurrentTimestamp(currentTimestamp);
            return this;
        }

        public PaymentResultUpdateParamBuilder pmtCompleteTimestamp(Date pmtCompleteTimestamp) {
            param.setPmtCompleteTimestamp(pmtCompleteTimestamp);
            return this;
        }

        public PaymentResultUpdateParam build() {
            return param;
        }
    }
}
