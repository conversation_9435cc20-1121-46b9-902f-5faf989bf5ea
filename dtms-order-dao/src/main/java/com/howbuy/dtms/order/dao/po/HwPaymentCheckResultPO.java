package com.howbuy.dtms.order.dao.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 
 * @date 2025/7/23 15:46
 * @since JDK 1.8
 */
/**
    * 海外支付对账结果
    */
public class HwPaymentCheckResultPO {
    /**
    * 主键id
    */
    private Long id;

    /**
    * 支付订单号
    */
    private Long pmtDealNo;

    /**
    * 订单号
    */
    private Long dealNo;

    /**
    * 香港客户号
    */
    private String hkCustNo;

    /**
    * 基金代码
    */
    private String fundCode;

    /**
    * 币种
    */
    private String currency;

    /**
    * 支付金额
    */
    private BigDecimal pmtAmt;

    /**
    * 支付对账日期
    */
    private String pmtCheckDt;

    /**
    * 外部支付订单号
    */
    private String outPmtDealNo;

    /**
    * 外部支付金额
    */
    private BigDecimal outPmtAmt;

    /**
    * 外部币种
    */
    private String outCurrency;

    /**
    * 外部支付标识 1-待支付 2-支付成功 3-支付失败 4-支付中
    */
    private String outPmtFlag;

    /**
    * 交易支付标识 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中
    */
    private String txPmtFlag;

    /**
    * 支付对账标记0-无需对账；1-未对账；2-对账完成；3-对账不平;
    */
    private String pmtCompFlag;

    /**
    * 备注
    */
    private String memo;

    /**
    * 记录状态 0-无效 1-正常
    */
    private String recStat;

    /**
    * 版本号
    */
    private Integer version;

    /**
    * 创建时间戳
    */
    private Date createTimestamp;

    /**
    * 更新时间戳
    */
    private Date updateTimestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPmtDealNo() {
        return pmtDealNo;
    }

    public void setPmtDealNo(Long pmtDealNo) {
        this.pmtDealNo = pmtDealNo;
    }

    public Long getDealNo() {
        return dealNo;
    }

    public void setDealNo(Long dealNo) {
        this.dealNo = dealNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public BigDecimal getPmtAmt() {
        return pmtAmt;
    }

    public void setPmtAmt(BigDecimal pmtAmt) {
        this.pmtAmt = pmtAmt;
    }

    public String getPmtCheckDt() {
        return pmtCheckDt;
    }

    public void setPmtCheckDt(String pmtCheckDt) {
        this.pmtCheckDt = pmtCheckDt;
    }

    public String getOutPmtDealNo() {
        return outPmtDealNo;
    }

    public void setOutPmtDealNo(String outPmtDealNo) {
        this.outPmtDealNo = outPmtDealNo;
    }

    public BigDecimal getOutPmtAmt() {
        return outPmtAmt;
    }

    public void setOutPmtAmt(BigDecimal outPmtAmt) {
        this.outPmtAmt = outPmtAmt;
    }

    public String getOutCurrency() {
        return outCurrency;
    }

    public void setOutCurrency(String outCurrency) {
        this.outCurrency = outCurrency;
    }

    public String getOutPmtFlag() {
        return outPmtFlag;
    }

    public void setOutPmtFlag(String outPmtFlag) {
        this.outPmtFlag = outPmtFlag;
    }

    public String getTxPmtFlag() {
        return txPmtFlag;
    }

    public void setTxPmtFlag(String txPmtFlag) {
        this.txPmtFlag = txPmtFlag;
    }

    public String getPmtCompFlag() {
        return pmtCompFlag;
    }

    public void setPmtCompFlag(String pmtCompFlag) {
        this.pmtCompFlag = pmtCompFlag;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }
}