package com.howbuy.dtms.order.dao.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 
 * @date 2025/7/23 16:35
 * @since JDK 1.8
 */
/**
    * 海外支付订单
    */
public class HwPaymentOrderPO {
    /**
    * 主键id
    */
    private Long id;

    /**
    * 支付订单号
    */
    private Long pmtDealNo;

    /**
    * 订单号
    */
    private Long dealNo;

    /**
    * 支付方式列表 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
    */
    private String paymentTypeList;

    /**
    * 香港客户号
    */
    private String hkCustNo;

    /**
    * 资金账号
    */
    private String cpAcctNo;

    /**
    * 基金交易账号
    */
    private String fundTxAcctNo;

    /**
    * 基金代码
    */
    private String fundCode;

    /**
    * 币种
    */
    private String currency;

    /**
    * 申请时间
    */
    private Date appDtm;

    /**
    * 支付金额
    */
    private BigDecimal pmtAmt;

    /**
    * 产品打款截止日期
    */
    private String productPayEndDt;

    /**
    * 产品打款截止时间
    */
    private String productPayEndDm;

    /**
    * 交易支付标记 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；
    */
    private String txPmtFlag;

    /**
    * 支付对账标记0-无需对账；1-未对账；2-对账完成；3-对账不平;
    */
    private String pmtCompFlag;

    /**
    * 支付对账日期
    */
    private String pmtCheckDt;

    /**
    * 外部支付订单号
    */
    private String outPmtDealNo;

    /**
    * 支付机构代码
    */
    private String pmtOrgCode;

    /**
    * 支付返回码
    */
    private String retCode;

    /**
    * 支付返回描述
    */
    private String retDesc;

    /**
    * 支付完成日期时间
    */
    private Date pmtCompleteDtm;

    /**
    * 网点号
    */
    private String outletCode;

    /**
    * 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
    */
    private String tradeChannel;

    /**
    * 备注
    */
    private String memo;

    /**
    * 订单类型 1-交易 2-edda入金
    */
    private String orderType;

    /**
    * 记录状态 0-无效 1-正常
    */
    private String recStat;

    /**
    * 版本号
    */
    private Integer version;

    /**
    * 创建时间戳
    */
    private Date createTimestamp;

    /**
    * 更新时间戳
    */
    private Date updateTimestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getPmtDealNo() {
        return pmtDealNo;
    }

    public void setPmtDealNo(Long pmtDealNo) {
        this.pmtDealNo = pmtDealNo;
    }

    public Long getDealNo() {
        return dealNo;
    }

    public void setDealNo(Long dealNo) {
        this.dealNo = dealNo;
    }

    public String getPaymentTypeList() {
        return paymentTypeList;
    }

    public void setPaymentTypeList(String paymentTypeList) {
        this.paymentTypeList = paymentTypeList;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getCpAcctNo() {
        return cpAcctNo;
    }

    public void setCpAcctNo(String cpAcctNo) {
        this.cpAcctNo = cpAcctNo;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Date getAppDtm() {
        return appDtm;
    }

    public void setAppDtm(Date appDtm) {
        this.appDtm = appDtm;
    }

    public BigDecimal getPmtAmt() {
        return pmtAmt;
    }

    public void setPmtAmt(BigDecimal pmtAmt) {
        this.pmtAmt = pmtAmt;
    }

    public String getProductPayEndDt() {
        return productPayEndDt;
    }

    public void setProductPayEndDt(String productPayEndDt) {
        this.productPayEndDt = productPayEndDt;
    }

    public String getProductPayEndDm() {
        return productPayEndDm;
    }

    public void setProductPayEndDm(String productPayEndDm) {
        this.productPayEndDm = productPayEndDm;
    }

    public String getTxPmtFlag() {
        return txPmtFlag;
    }

    public void setTxPmtFlag(String txPmtFlag) {
        this.txPmtFlag = txPmtFlag;
    }

    public String getPmtCompFlag() {
        return pmtCompFlag;
    }

    public void setPmtCompFlag(String pmtCompFlag) {
        this.pmtCompFlag = pmtCompFlag;
    }

    public String getPmtCheckDt() {
        return pmtCheckDt;
    }

    public void setPmtCheckDt(String pmtCheckDt) {
        this.pmtCheckDt = pmtCheckDt;
    }

    public String getOutPmtDealNo() {
        return outPmtDealNo;
    }

    public void setOutPmtDealNo(String outPmtDealNo) {
        this.outPmtDealNo = outPmtDealNo;
    }

    public String getPmtOrgCode() {
        return pmtOrgCode;
    }

    public void setPmtOrgCode(String pmtOrgCode) {
        this.pmtOrgCode = pmtOrgCode;
    }

    public String getRetCode() {
        return retCode;
    }

    public void setRetCode(String retCode) {
        this.retCode = retCode;
    }

    public String getRetDesc() {
        return retDesc;
    }

    public void setRetDesc(String retDesc) {
        this.retDesc = retDesc;
    }

    public Date getPmtCompleteDtm() {
        return pmtCompleteDtm;
    }

    public void setPmtCompleteDtm(Date pmtCompleteDtm) {
        this.pmtCompleteDtm = pmtCompleteDtm;
    }

    public String getOutletCode() {
        return outletCode;
    }

    public void setOutletCode(String outletCode) {
        this.outletCode = outletCode;
    }

    public String getTradeChannel() {
        return tradeChannel;
    }

    public void setTradeChannel(String tradeChannel) {
        this.tradeChannel = tradeChannel;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getRecStat() {
        return recStat;
    }

    public void setRecStat(String recStat) {
        this.recStat = recStat;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }
}