package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.param.PaymentResultUpdateParam;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.dao.query.PaymentCheckQuery;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description: 海外支付订单自定义Mapper
 * <AUTHOR>
 * @date 2025-07-02 16:04:22
 * @since JDK 1.8
 */
public interface CustomizeHwPaymentOrderPOMapper {

    /**
     * @description: 根据支付订单号查询支付订单
     * @param pmtDealNo 支付订单号
     * @return 支付订单信息
     * @author: shaoyang.li
     * @date: 2025-07-08 11:20:37
     * @since JDK 1.8
     */
    HwPaymentOrderPO selectByPmtDealNo(@Param("pmtDealNo") Long pmtDealNo);

    /**
     * @description:根据订单号查询支付订单
     * @param dealNo
     * @return com.howbuy.dtms.order.dao.po.HwPaymentOrderPO
     * <AUTHOR>
     * @date 2025/7/29 9:53
     * @since JDK 1.8
     */
    HwPaymentOrderPO selectByDealNo(@Param("dealNo") Long dealNo);

    /**
     * @description: 使用乐观锁更新支付订单状态为付款中
     * @param pmtDealNo 支付订单号
     * @param originalTxPmtFlag 原始交易支付标记
     * @param originalUpdateTimestamp 原始更新时间戳
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    int updateToPayingWithOptimisticLock(@Param("pmtDealNo") Long pmtDealNo,
                                       @Param("originalTxPmtFlag") String originalTxPmtFlag,
                                       @Param("originalUpdateTimestamp") Date originalUpdateTimestamp,
                                       @Param("currentTimestamp") Date currentTimestamp);

    /**
     * @description: 更新支付结果
     * @param pmtDealNo 支付订单号
     * @param retCode 返回码
     * @param retDesc 返回描述
     * @param outPmtDealNo 外部支付订单号
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    int updatePaymentResultWithOptimisticLock(@Param("pmtDealNo") Long pmtDealNo,
                                            @Param("retCode") String retCode,
                                            @Param("retDesc") String retDesc,
                                            @Param("outPmtDealNo") String outPmtDealNo,
                                            @Param("currentTimestamp") Date currentTimestamp);

    /**
     * @description: 使用乐观锁更新支付结果（完整版本）
     * @param param 支付结果更新参数对象
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-08 11:20:37
     * @since JDK 1.8
     */
    int updatePaymentResultWithOptimisticLockFull(@Param("param") PaymentResultUpdateParam param);

    /**
     * @description: 查询未支付订单（配合PageHelper使用）
     * @param txPmtFlag 支付状态
     * @param recStat 记录状态
     * @param updateTimeStart 更新时间开始
     * @param updateTimeEnd 更新时间结束
     * @return 未支付订单列表
     * @author: shaoyang.li
     * @date: 2025-07-03 11:34:43
     * @since JDK 1.8
     */
    List<HwPaymentOrderPO> selectUnPaymentOrders(@Param("txPmtFlag") String txPmtFlag,
                                                @Param("recStat") String recStat,
                                                @Param("updateTimeStart") Date updateTimeStart,
                                                @Param("updateTimeEnd") Date updateTimeEnd);

    /**
     * @description: 查询支付中状态的支付订单（配合PageHelper使用）
     * @param txPmtFlag 支付状态
     * @param recStat 记录状态
     * @param updateTimeStart 更新时间开始
     * @param updateTimeEnd 更新时间结束
     * @return 支付中状态的支付订单列表
     * @author: shaoyang.li
     * @date: 2025-07-04 15:08:30
     * @since JDK 1.8
     */
    List<HwPaymentOrderPO> selectPayingOrders(@Param("txPmtFlag") String txPmtFlag,
                                             @Param("recStat") String recStat,
                                             @Param("updateTimeStart") Date updateTimeStart,
                                             @Param("updateTimeEnd") Date updateTimeEnd);

    /**
     * @description: 查询待对账的支付订单（配合PageHelper使用）
     * @param query 查询条件
     * @return 待对账的支付订单列表
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    List<HwPaymentOrderPO> selectPaymentOrdersForCheck(PaymentCheckQuery query);

    /**
     * @description: 使用乐观锁更新支付订单对账状态
     * @param pmtDealNo 支付订单号
     * @param pmtCompFlag 支付对账标记
     * @param originalPmtCompFlag 原始支付对账标记
     * @param originalUpdateTimestamp 原始更新时间戳
     * @param currentTimestamp 当前时间戳
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    int updatePaymentCheckStatusWithOptimisticLock(@Param("pmtDealNo") Long pmtDealNo,
                                                  @Param("pmtCompFlag") String pmtCompFlag,
                                                  @Param("originalPmtCompFlag") String originalPmtCompFlag,
                                                  @Param("originalUpdateTimestamp") Date originalUpdateTimestamp,
                                                  @Param("currentTimestamp") Date currentTimestamp);

    /**
     * @description: 使用乐观锁重置支付订单交易支付标识
     * @param pmtDealNo 支付订单号
     * @param txPmtFlag 新的交易支付标记
     * @param originalTxPmtFlag 原始交易支付标记
     * @param originalUpdateTimestamp 原始更新时间戳
     * @param currentTimestamp 当前时间戳
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    int resetTxPmtFlagWithOptimisticLock(@Param("pmtDealNo") Long pmtDealNo,
                                       @Param("txPmtFlag") String txPmtFlag,
                                       @Param("originalTxPmtFlag") String originalTxPmtFlag,
                                       @Param("originalUpdateTimestamp") Date originalUpdateTimestamp,
                                       @Param("currentTimestamp") Date currentTimestamp);

    /**
     * @description: 撤单时更新支付订单状态
     * @param dealNo 订单号
     * @param txPmtFlag 交易支付标记
     * @param pmtCompFlag 支付对账标记
     * @param originalTxPmtFlag 原始交易支付标记（更新条件）
     * @param currentTimestamp 当前时间戳
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-08 20:01:30
     * @since JDK 1.8
     */
    int updateForCancel(@Param("dealNo") Long dealNo,
                       @Param("txPmtFlag") String txPmtFlag,
                       @Param("pmtCompFlag") String pmtCompFlag,
                       @Param("originalTxPmtFlag") String originalTxPmtFlag,
                       @Param("currentTimestamp") Date currentTimestamp);

    /**
     * @description:(根据订单号修改支付订单)
     * @param updatePaymentOrder
     * @return int
     * <AUTHOR>
     * @date 2025/7/29 10:19
     * @since JDK 1.8
     */
    int updateByDealNo(HwPaymentOrderPO updatePaymentOrder);
}
