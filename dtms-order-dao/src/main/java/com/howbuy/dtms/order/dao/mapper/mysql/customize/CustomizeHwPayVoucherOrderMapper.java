/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.po.HwPayVoucherOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 打款凭证订单自定义Mapper
 * <AUTHOR>
 * @date 2025-07-11 13:35:30
 * @since JDK 1.8
 */
public interface CustomizeHwPayVoucherOrderMapper {

    /**
     * @description: 根据订单号列表查询审核成功打款凭证订单
     * @param dealNos 订单号列表
     * @return List<HwPayVoucherOrder> 打款凭证订单列表
     * <AUTHOR>
     * @date 2025-07-11 13:35:30
     * @since JDK 1.8
     */
    List<HwPayVoucherOrder> selectAuditPassByDealNos(@Param("dealNos") List<Long> dealNos);
}
