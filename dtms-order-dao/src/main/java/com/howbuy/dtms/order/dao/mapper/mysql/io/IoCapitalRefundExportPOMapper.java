package com.howbuy.dtms.order.dao.mapper.mysql.io;

import com.howbuy.dtms.order.dao.po.IoCapitalRefundExportPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/7/10 15:27
 * @since JDK 1.8
 */
public interface IoCapitalRefundExportPOMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(IoCapitalRefundExportPO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(IoCapitalRefundExportPO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    IoCapitalRefundExportPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(IoCapitalRefundExportPO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(IoCapitalRefundExportPO record);

    /**
     * 批量插入资金退款导出记录
     *
     * @param list 导出记录列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<IoCapitalRefundExportPO> list);
}