/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.bo.CapitalRefundOrderBO;
import com.howbuy.dtms.order.dao.po.HwCapitalRefundPO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @description: 海外资金退款表自定义Mapper
 * <AUTHOR>
 * @date 2025-07-10 13:47:32
 * @since JDK 1.8
 */
public interface CustomizeHwCapitalRefundPOMapper {

    /**
     * 查询支付成功撤单退款数据
     *
     * @param updateTimeStart 更新时间开始
     * @return 待退款订单列表
     */
    List<CapitalRefundOrderBO> selectPaidCancelRefundOrders(@Param("updateTimeStart") Date updateTimeStart);

    /**
     * 查询储蓄罐赎回成功退款数据
     *
     * @param updateTimeStart 更新时间开始
     * @return 待退款订单列表
     */
    List<CapitalRefundOrderBO> selectPiggyRedeemRefundOrders(@Param("updateTimeStart") Date updateTimeStart);

    /**
     * 检查订单是否已存在退款记录
     *
     * @param dealNo 订单号
     * @param recStat 记录状态
     * @return 退款记录数量
     */
    int countRefundRecordByDealNo(@Param("dealNo") Long dealNo, @Param("recStat") String recStat);

    /**
     * 查询未处理的退款数据
     *
     * @return 未处理的退款数据列表
     */
    List<HwCapitalRefundPO> selectUnprocessedRefunds();

    /**
     * 批量插入退款记录
     *
     * @param refundList 退款记录列表
     * @return 插入成功的记录数
     */
    int batchInsert(@Param("list") List<HwCapitalRefundPO> refundList);

    /**
     * 批量更新退款记录处理状态为已处理
     *
     * @param dealNoList 订单号列表
     * @return 更新成功的记录数
     */
    int batchUpdateProcessStatusToProcessed(@Param("dealNoList") List<Long> dealNoList);
}
