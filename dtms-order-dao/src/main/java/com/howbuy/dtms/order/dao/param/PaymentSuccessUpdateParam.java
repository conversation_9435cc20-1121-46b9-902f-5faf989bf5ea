/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.param;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 支付成功更新参数
 * <AUTHOR>
 * @date 2025-07-15 14:07:46
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentSuccessUpdateParam {

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 实际支付日期
     */
    private String actualPayDt;

    /**
     * 实际支付时间
     */
    private String actualPayTm;

    /**
     * 实际支付金额
     */
    private BigDecimal actualPayAmt;

    /**
     * 原始支付状态
     */
    private String originalPayStatus;

    /**
     * 原始更新时间戳
     */
    private Date originalUpdateTimestamp;

    /**
     * 当前更新时间戳
     */
    private Date currentUpdateTimestamp;
} 