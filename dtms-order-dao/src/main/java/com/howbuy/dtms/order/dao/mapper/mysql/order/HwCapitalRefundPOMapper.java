package com.howbuy.dtms.order.dao.mapper.mysql.order;

import com.howbuy.dtms.order.dao.po.HwCapitalRefundPO;

/**
 * <AUTHOR>
 * @description: 
 * @date 2025/7/10 10:51
 * @since JDK 1.8
 */
public interface HwCapitalRefundPOMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(HwCapitalRefundPO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(HwCapitalRefundPO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    HwCapitalRefundPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(HwCapitalRefundPO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(HwCapitalRefundPO record);
}