/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.dto.PaymentCheckResultDTO;
import com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO;
import com.howbuy.dtms.order.dao.query.PaymentCheckResultQuery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 海外支付对账结果自定义Mapper
 * <AUTHOR>
 * @date 2025-07-04 16:20:10
 * @since JDK 1.8
 */
public interface CustomizeHwPaymentCheckResultPOMapper {

    /**
     * @description: 根据支付订单号查询对账结果
     * @param pmtDealNo 支付订单号
     * @return 对账结果信息
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    HwPaymentCheckResultPO selectByPmtDealNo(@Param("pmtDealNo") Long pmtDealNo);

    /**
     * @description: 分页查询支付对账结果
     * @param query 查询条件
     * @return 支付对账结果列表
     * @author: shaoyang.li
     * @date: 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    List<PaymentCheckResultDTO> selectPaymentCheckResultPage(@Param("query") PaymentCheckResultQuery query);
}
