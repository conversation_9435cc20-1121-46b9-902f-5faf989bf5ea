/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.bo;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 储蓄罐支付失败赎回订单BO
 * @author: shaoyang.li
 * @date: 2025-07-10 19:59:33
 * @since JDK 1.8
 */
@Getter
@Setter
public class PiggyPayFailRedeemOrderBO {
    
    /**
     * 香港客户号
     */
    private String hkCustNo;
    
    /**
     * 订单号
     */
    private Long dealNo;
} 