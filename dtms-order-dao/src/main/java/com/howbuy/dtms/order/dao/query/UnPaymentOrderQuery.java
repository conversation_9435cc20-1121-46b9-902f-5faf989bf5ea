/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.query;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @description: 未支付订单查询条件
 * <AUTHOR>
 * @date 2025-07-03 11:34:43
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UnPaymentOrderQuery extends PageQuer<PERSON> {
    
    /**
     * 支付状态（tx_pmt_flag）- 1:未付款
     */
    private String txPmtFlag;
    
    /**
     * 记录状态（rec_stat）- 1:有效
     */
    private String recStat;
    
    /**
     * 更新时间开始（update_timestamp >= ?）
     */
    private Date updateTimeStart;
    
    /**
     * 更新时间结束（update_timestamp < ?）
     */
    private Date updateTimeEnd;
}
