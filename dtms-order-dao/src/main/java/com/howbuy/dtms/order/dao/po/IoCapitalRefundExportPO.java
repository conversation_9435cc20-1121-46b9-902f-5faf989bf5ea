package com.howbuy.dtms.order.dao.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 
 * @date 2025/7/10 10:52
 * @since JDK 1.8
 */
/**
    * io资金退款导出表
    */
public class IoCapitalRefundExportPO {
    /**
    * 主键id
    */
    private Long id;

    /**
    * 导出日期
    */
    private String exportDt;

    /**
    * 文件id
    */
    private Long fileId;

    /**
    * 订单号
    */
    private Long dealNo;

    /**
    * 关联订单号(订单表的订单号)
    */
    private Long relationalDealNo;

    /**
    * 香港客户号
    */
    private String hkCustNo;

    /**
    * 基金交易账号
    */
    private String fundTxAcctNo;

    /**
    * 基金代码
    */
    private String fundCode;

    /**
    * 基金简称
    */
    private String fundAbbr;

    /**
    * 基金币种
    */
    private String fundCurrency;

    /**
    * 管理人代码
    */
    private String fundManCode;

    /**
    * 业务码
    */
    private String busiCode;

    /**
    * 支付方式 1：电汇；2：支票；3：海外储蓄罐
    */
    private String paymentType;

    /**
    * 退款方向 2-现金余额
    */
    private String refundDirection;

    /**
    * 退款金额
    */
    private BigDecimal refundAmt;

    /**
    * 退款日期 YYYYMMDD
    */
    private String refundDt;

    /**
    * 创建时间戳
    */
    private Date createTimestamp;

    /**
    * 更新时间戳
    */
    private Date updateTimestamp;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getExportDt() {
        return exportDt;
    }

    public void setExportDt(String exportDt) {
        this.exportDt = exportDt;
    }

    public Long getFileId() {
        return fileId;
    }

    public void setFileId(Long fileId) {
        this.fileId = fileId;
    }

    public Long getDealNo() {
        return dealNo;
    }

    public void setDealNo(Long dealNo) {
        this.dealNo = dealNo;
    }

    public Long getRelationalDealNo() {
        return relationalDealNo;
    }

    public void setRelationalDealNo(Long relationalDealNo) {
        this.relationalDealNo = relationalDealNo;
    }

    public String getHkCustNo() {
        return hkCustNo;
    }

    public void setHkCustNo(String hkCustNo) {
        this.hkCustNo = hkCustNo;
    }

    public String getFundTxAcctNo() {
        return fundTxAcctNo;
    }

    public void setFundTxAcctNo(String fundTxAcctNo) {
        this.fundTxAcctNo = fundTxAcctNo;
    }

    public String getFundCode() {
        return fundCode;
    }

    public void setFundCode(String fundCode) {
        this.fundCode = fundCode;
    }

    public String getFundAbbr() {
        return fundAbbr;
    }

    public void setFundAbbr(String fundAbbr) {
        this.fundAbbr = fundAbbr;
    }

    public String getFundCurrency() {
        return fundCurrency;
    }

    public void setFundCurrency(String fundCurrency) {
        this.fundCurrency = fundCurrency;
    }

    public String getFundManCode() {
        return fundManCode;
    }

    public void setFundManCode(String fundManCode) {
        this.fundManCode = fundManCode;
    }

    public String getBusiCode() {
        return busiCode;
    }

    public void setBusiCode(String busiCode) {
        this.busiCode = busiCode;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getRefundDirection() {
        return refundDirection;
    }

    public void setRefundDirection(String refundDirection) {
        this.refundDirection = refundDirection;
    }

    public BigDecimal getRefundAmt() {
        return refundAmt;
    }

    public void setRefundAmt(BigDecimal refundAmt) {
        this.refundAmt = refundAmt;
    }

    public String getRefundDt() {
        return refundDt;
    }

    public void setRefundDt(String refundDt) {
        this.refundDt = refundDt;
    }

    public Date getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Date createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

    public Date getUpdateTimestamp() {
        return updateTimestamp;
    }

    public void setUpdateTimestamp(Date updateTimestamp) {
        this.updateTimestamp = updateTimestamp;
    }
}