/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.param;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @description: 撤单更新参数
 * <AUTHOR>
 * @date 2025-07-08 20:01:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class CancelOrderUpdateParam {

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 订单状态
     */
    private String orderStatus;

    /**
     * 支付状态
     */
    private String payStatus;

    /**
     * 原始订单状态
     */
    private String originalOrderStatus;

    /**
     * 原始支付状态
     */
    private String originalPayStatus;

    /**
     * 原始更新时间戳
     */
    private Date originalUpdateTimestamp;

    /**
     * 当前更新时间戳
     */
    private Date currentUpdateTimestamp;
}
