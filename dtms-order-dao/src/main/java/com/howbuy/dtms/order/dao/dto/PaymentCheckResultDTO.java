package com.howbuy.dtms.order.dao.dto;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付对账结果查询DTO
 * 
 * <AUTHOR>
 * @date 2025-07-07 17:13:51
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentCheckResultDTO {

    /**
     * 支付订单号
     */
    private Long pmtDealNo;

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 中台业务代码
     */
    private String middleBusiCode;

    /**
     * 支付方式列表
     * 111；第一位：电汇；第二位：支票；第三位：海外储蓄罐
     */
    private String paymentTypeList;

    /**
     * 香港客户号
     */
    private String hkCustNo;

    /**
     * 客户姓名
     */
    private String custName;

    /**
     * 资金账号
     */
    private String cpAcctNo;

    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;

    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 基金简称
     */
    private String fundAddr;

    /**
     * 币种
     */
    private String currency;

    /**
     * 申请时间
     */
    private Date appDtm;

    /**
     * 支付金额
     */
    private BigDecimal pmtAmt;

    /**
     * 支付对账日期
     */
    private String pmtCheckDt;

    /**
     * 外部支付订单号
     */
    private String outPmtDealNo;

    /**
     * 外部支付金额
     */
    private BigDecimal outPmtAmt;

    /**
     * 外部币种
     */
    private String outCurrency;

    /**
     * 外部支付标识
     * 1-待支付 2-支付成功 3-支付失败 4-支付中
     */
    private String outPmtFlag;

    /**
     * 交易支付标识
     * 0-无需付款；1-未付款；2-付款成功；3-付款失败；4-付款中；5-已退款;6-等待付款；7-撤单成功；
     */
    private String txPmtFlag;

    /**
     * 支付对账标记
     * 0-无需对账；1-未对账；2-对账完成；3-对账不平;
     */
    private String pmtCompFlag;

    /**
     * 备注
     */
    private String memo;
}
