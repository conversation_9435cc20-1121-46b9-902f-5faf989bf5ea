/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.param;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * @description: 撤单订单明细更新参数
 * <AUTHOR>
 * @date 2025-07-08 20:01:30
 * @since JDK 1.8
 */
@Getter
@Setter
public class CancelOrderDtlUpdateParam {

    /**
     * 明细ID
     */
    private Long id;

    /**
     * 订单号
     */
    private Long dealNo;

    /**
     * 申请状态
     */
    private String appStatus;

    /**
     * 撤单时间
     */
    private String cancelDate;

    /**
     * 撤单原因
     */
    private String cancelCause;

    /**
     * 撤单柜台账号
     */
    private String cancelCpAcctNo;

    /**
     * 上报状态
     */
    private String submitStatus;

    /**
     * 原始申请状态
     */
    private String originalAppStatus;

    /**
     * 原始上报状态
     */
    private String originalSubmitStatus;

    /**
     * 当前更新时间戳
     */
    private Date currentUpdateTimestamp;
}
