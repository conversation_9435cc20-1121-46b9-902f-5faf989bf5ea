/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.bo;

import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/25 11:15
 * @since JDK 1.8
 */
@Getter
@Setter
public class HwDealOrderDtlCounterBO extends HwDealOrderDtl {
    /**
     * 客户名称
     */
    private String custName;

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }
}
