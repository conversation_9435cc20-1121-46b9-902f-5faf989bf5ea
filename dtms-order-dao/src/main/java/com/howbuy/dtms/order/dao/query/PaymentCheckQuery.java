/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.query;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * @description: 支付对账查询条件
 * <AUTHOR>
 * @date 2025-07-04 16:20:10
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentCheckQuery {

    /**
     * 支付对账标记列表
     */
    private List<String> pmtCompFlagList;

    /**
     * 交易支付标记列表
     */
    private List<String> txPmtFlagList;

    /**
     * 记录状态
     */
    private String recStat;

    /**
     * 支付对账日期
     */
    private String pmtCheckDt;

    /**
     * 更新时间开始
     */
    private Date updateTimeStart;

    /**
     * 更新时间结束
     */
    private Date updateTimeEnd;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向
     */
    private String orderDirection;
}
