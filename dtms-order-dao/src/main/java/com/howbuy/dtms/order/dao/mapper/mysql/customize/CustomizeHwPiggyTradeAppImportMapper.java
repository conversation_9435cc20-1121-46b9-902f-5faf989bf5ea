/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.po.HwPiggyTradeAppImport;
import com.howbuy.dtms.order.dao.param.PiggyTradeAppImportUpdateParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description: 海外储蓄罐导入申请表自定义查询Mapper
 * <AUTHOR>
 * @date 2025-07-14 19:54:54
 * @since JDK 1.8
 */
public interface CustomizeHwPiggyTradeAppImportMapper {

    /**
     * @description: 批量插入
     * @param entities 海外储蓄罐导入申请记录列表
     * @return int 插入成功记录数
     * @author: shaoyang.li
     * @date: 2025-07-14 19:54:54
     * @since JDK 1.8
     */
    int insertBatch(@Param("entities") List<HwPiggyTradeAppImport> entities);

    /**
     * @description: 根据关联交易单号查询
     * @param dealNo 关联交易单号
     * @return java.util.List<com.howbuy.dtms.order.dao.po.HwPiggyTradeAppImport> 海外储蓄罐导入申请记录列表
     * @author: shaoyang.li
     * @date: 2025-07-14 19:54:54
     * @since JDK 1.8
     */
    List<HwPiggyTradeAppImport> queryTradeAppImportByRelationalDealNo(Long dealNo);

    /**
     * @description: 根据导入申请Id列表查询
     * @param importAppIds 导入申请Id列表
     * @return java.util.List<com.howbuy.dtms.order.dao.po.HwPiggyTradeAppImport> 海外储蓄罐导入申请记录列表
     * @author: shaoyang.li
     * @date: 2025-07-15 19:32:38
     * @since JDK 1.8
     */
    List<HwPiggyTradeAppImport> queryByImportAppIds(@Param("importAppIds") List<String> importAppIds);

    /**
     * @description: 批量逻辑删除生成失败的记录
     * @param importAppIds 导入申请Id列表
     * @return int 删除记录数
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    int batchLogicalDeleteByImportAppIds(@Param("importAppIds") List<String> importAppIds);

    /**
     * @description: 批量插入储蓄罐交易申请记录
     * @param records 储蓄罐交易申请记录列表
     * @return int 插入记录数
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    int batchInsert(@Param("records") List<HwPiggyTradeAppImport> records);

    /**
     * @description: 根据储蓄罐申请Id和旧是否生成状态乐观锁更新生成状态及相关字段
     * @param param 储蓄罐申请Id
     * @return int 更新行数
     * <AUTHOR>
     * @date 2025/07/17 16:28
     * @since JDK 1.8
     */
    int updateIsGeneratedByAppIdAndOldStatus(PiggyTradeAppImportUpdateParam param);

    /**
     * @description: 根据导入申请Id查询储蓄罐交易申请记录
     * @param importAppId 导入申请Id
     * @return HwPiggyTradeAppImport 海外储蓄罐导入申请记录
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    HwPiggyTradeAppImport queryByImportAppId(@Param("importAppId") String importAppId);

    /**
     * @description: 根据导入申请Id更新储蓄罐交易申请记录
     * @param piggyTradeAppImport 储蓄罐交易申请记录
     * @return int 更新行数
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    int updateByImportAppId(HwPiggyTradeAppImport piggyTradeAppImport);
}