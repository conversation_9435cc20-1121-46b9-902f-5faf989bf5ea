package com.howbuy.dtms.order.dao.mapper.mysql.order;

import com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/7/23 15:46
 * @since JDK 1.8
 */
public interface HwPaymentCheckResultPOMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(HwPaymentCheckResultPO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(HwPaymentCheckResultPO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    HwPaymentCheckResultPO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(HwPaymentCheckResultPO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(HwPaymentCheckResultPO record);
}