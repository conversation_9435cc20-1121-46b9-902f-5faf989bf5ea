<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPaymentOrderPOMapper">

    <!-- 根据支付订单号查询支付订单 -->
    <select id="selectByPmtDealNo" parameterType="java.lang.Long"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.Base_Column_List" />
        from hw_payment_order
        where pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT}
        and rec_stat = '1'
    </select>

    <!-- 根据订单号查询支付订单 -->
    <select id="selectByDealNo" parameterType="java.lang.Long"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.Base_Column_List" />
        from hw_payment_order
        where deal_no = #{dealNo,jdbcType=BIGINT}
        and rec_stat = '1'
    </select>

    <!-- 使用乐观锁更新支付订单状态为付款中 -->
    <update id="updateToPayingWithOptimisticLock">
        update hw_payment_order
        set tx_pmt_flag = '4',
            update_timestamp = #{currentTimestamp,jdbcType=TIMESTAMP}
        where pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT}
          and tx_pmt_flag = #{originalTxPmtFlag,jdbcType=VARCHAR}
          and update_timestamp = #{originalUpdateTimestamp,jdbcType=TIMESTAMP}
          and rec_stat = '1'
    </update>

    <!-- 更新支付结果 -->
    <update id="updatePaymentResultWithOptimisticLock">
        update hw_payment_order
        set ret_code = #{retCode,jdbcType=VARCHAR},
            ret_desc = #{retDesc,jdbcType=VARCHAR},
            out_pmt_deal_no = #{outPmtDealNo,jdbcType=VARCHAR},
            update_timestamp = #{currentTimestamp,jdbcType=TIMESTAMP}
        where pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT}
          and rec_stat = '1'
    </update>

    <!-- 使用乐观锁更新支付结果（完整版本） -->
    <update id="updatePaymentResultWithOptimisticLockFull">
        update hw_payment_order
        set tx_pmt_flag = #{param.txPmtFlag,jdbcType=VARCHAR},
            ret_code = #{param.retCode,jdbcType=VARCHAR},
            ret_desc = #{param.retDesc,jdbcType=VARCHAR},
            out_pmt_deal_no = #{param.outPmtDealNo,jdbcType=VARCHAR},
            <if test="param.pmtCheckDt != null and param.pmtCheckDt != ''">
            pmt_check_dt = #{param.pmtCheckDt,jdbcType=VARCHAR},
            </if>
            pmt_complete_dtm = #{param.pmtCompleteTimestamp,jdbcType=TIMESTAMP},
            update_timestamp = #{param.currentTimestamp,jdbcType=TIMESTAMP}
        where pmt_deal_no = #{param.pmtDealNo,jdbcType=BIGINT}
          and tx_pmt_flag = #{param.originalTxPmtFlag,jdbcType=VARCHAR}
          and update_timestamp = #{param.originalUpdateTimestamp,jdbcType=TIMESTAMP}
          and rec_stat = '1'
    </update>

    <!-- 查询未支付订单（配合PageHelper使用） -->
    <select id="selectUnPaymentOrders"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.Base_Column_List"/>
        from hw_payment_order
        where tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR}
          and rec_stat = #{recStat,jdbcType=VARCHAR}
          and update_timestamp >= #{updateTimeStart,jdbcType=TIMESTAMP}
          and update_timestamp &lt; #{updateTimeEnd,jdbcType=TIMESTAMP}
        order by update_timestamp asc
    </select>

    <!-- 查询支付中状态的支付订单（配合PageHelper使用） -->
    <select id="selectPayingOrders"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.Base_Column_List" />
        from hw_payment_order
        where tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR}
          and rec_stat = #{recStat,jdbcType=VARCHAR}
          and update_timestamp >= #{updateTimeStart,jdbcType=TIMESTAMP}
          and update_timestamp &lt; #{updateTimeEnd,jdbcType=TIMESTAMP}
        order by update_timestamp asc
    </select>

    <!-- 查询待对账的支付订单（配合PageHelper使用） -->
    <select id="selectPaymentOrdersForCheck" parameterType="com.howbuy.dtms.order.dao.query.PaymentCheckQuery"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper.Base_Column_List" />
        from hw_payment_order
        where rec_stat = #{recStat,jdbcType=VARCHAR}
        <if test="pmtCompFlagList != null and pmtCompFlagList.size() > 0">
            and pmt_comp_flag in
            <foreach collection="pmtCompFlagList" item="pmtCompFlag" open="(" separator="," close=")">
                #{pmtCompFlag,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="txPmtFlagList != null and txPmtFlagList.size() > 0">
            and tx_pmt_flag in
            <foreach collection="txPmtFlagList" item="txPmtFlag" open="(" separator="," close=")">
                #{txPmtFlag,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="pmtCheckDt != null and pmtCheckDt != ''">
            and pmt_check_dt = #{pmtCheckDt,jdbcType=VARCHAR}
        </if>
        <if test="updateTimeStart != null">
            and update_timestamp >= #{updateTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTimeEnd != null">
            and update_timestamp &lt; #{updateTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="orderBy != null and orderBy != ''">
            order by ${orderBy}
            <if test="orderDirection != null and orderDirection != ''">
                ${orderDirection}
            </if>
        </if>
    </select>

    <!-- 使用乐观锁更新支付订单对账状态 -->
    <update id="updatePaymentCheckStatusWithOptimisticLock">
        update hw_payment_order
        set pmt_comp_flag    = #{pmtCompFlag,jdbcType=VARCHAR},
            update_timestamp = #{currentTimestamp,jdbcType=TIMESTAMP}
        where pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT}
          and pmt_comp_flag = #{originalPmtCompFlag,jdbcType=VARCHAR}
          and update_timestamp = #{originalUpdateTimestamp,jdbcType=TIMESTAMP}
          and rec_stat = '1'
    </update>

    <!-- 使用乐观锁重置支付订单交易支付标识 -->
    <update id="resetTxPmtFlagWithOptimisticLock">
        update hw_payment_order
        set tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR},
            update_timestamp = #{currentTimestamp,jdbcType=TIMESTAMP}
        where pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT}
          and tx_pmt_flag = #{originalTxPmtFlag,jdbcType=VARCHAR}
          and update_timestamp = #{originalUpdateTimestamp,jdbcType=TIMESTAMP}
          and rec_stat = '1'
    </update>

    <!-- 撤单时更新支付订单状态 -->
    <update id="updateForCancel">
        update hw_payment_order
        set tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR},
            pmt_comp_flag = #{pmtCompFlag,jdbcType=VARCHAR},
            update_timestamp = #{currentTimestamp,jdbcType=TIMESTAMP}
        where deal_no = #{dealNo,jdbcType=BIGINT}
          and tx_pmt_flag = #{originalTxPmtFlag,jdbcType=VARCHAR}
          and rec_stat = '1'
    </update>

    <update id="updateByDealNo" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentOrderPO">
    <!--@mbg.generated-->
    update hw_payment_order
    <set>
        <if test="pmtDealNo != null">
            pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT},
        </if>
        <if test="paymentTypeList != null">
            payment_type_list = #{paymentTypeList,jdbcType=VARCHAR},
        </if>
        <if test="hkCustNo != null">
            hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
        </if>
        <if test="cpAcctNo != null">
            cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR},
        </if>
        <if test="fundTxAcctNo != null">
            fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
        </if>
        <if test="fundCode != null">
            fund_code = #{fundCode,jdbcType=VARCHAR},
        </if>
        <if test="currency != null">
            currency = #{currency,jdbcType=VARCHAR},
        </if>
        <if test="appDtm != null">
            app_dtm = #{appDtm,jdbcType=TIMESTAMP},
        </if>
        <if test="pmtAmt != null">
            pmt_amt = #{pmtAmt,jdbcType=DECIMAL},
        </if>
        <if test="productPayEndDt != null">
            product_pay_end_dt = #{productPayEndDt,jdbcType=VARCHAR},
        </if>
        <if test="productPayEndDm != null">
            product_pay_end_dm = #{productPayEndDm,jdbcType=VARCHAR},
        </if>
        <if test="txPmtFlag != null">
            tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR},
        </if>
        <if test="pmtCompFlag != null">
            pmt_comp_flag = #{pmtCompFlag,jdbcType=CHAR},
        </if>
        <if test="pmtCheckDt != null">
            pmt_check_dt = #{pmtCheckDt,jdbcType=VARCHAR},
        </if>
        <if test="outPmtDealNo != null">
            out_pmt_deal_no = #{outPmtDealNo,jdbcType=VARCHAR},
        </if>
        <if test="pmtOrgCode != null">
            pmt_org_code = #{pmtOrgCode,jdbcType=VARCHAR},
        </if>
        <if test="retCode != null">
            ret_code = #{retCode,jdbcType=VARCHAR},
        </if>
        <if test="retDesc != null">
            ret_desc = #{retDesc,jdbcType=VARCHAR},
        </if>
        <if test="pmtCompleteDtm != null">
            pmt_complete_dtm = #{pmtCompleteDtm,jdbcType=TIMESTAMP},
        </if>
        <if test="outletCode != null">
            outlet_code = #{outletCode,jdbcType=VARCHAR},
        </if>
        <if test="tradeChannel != null">
            trade_channel = #{tradeChannel,jdbcType=VARCHAR},
        </if>
        <if test="memo != null">
            memo = #{memo,jdbcType=VARCHAR},
        </if>
        <if test="orderType != null">
            order_type = #{orderType,jdbcType=VARCHAR},
        </if>
        <if test="recStat != null">
            rec_stat = #{recStat,jdbcType=CHAR},
        </if>
        <if test="version != null">
            version = #{version,jdbcType=INTEGER},
        </if>
        <if test="createTimestamp != null">
            create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
        </if>
        <if test="updateTimestamp != null">
            update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
        </if>
    </set>
    where deal_no = #{dealNo,jdbcType=BIGINT}
    </update>

</mapper>
