<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPaymentCheckResultPOMapper">

    <!-- 根据支付订单号查询对账结果 -->
    <select id="selectByPmtDealNo" parameterType="java.lang.Long"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentCheckResultPOMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentCheckResultPOMapper.Base_Column_List" />
        from hw_payment_check_result
        where pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT}
        and rec_stat = '1'
        order by id desc
        limit 1
    </select>

    <!-- 分页查询支付对账结果 -->
    <resultMap id="PaymentCheckResultMap" type="com.howbuy.dtms.order.dao.dto.PaymentCheckResultDTO">
        <result column="pmtDealNo" jdbcType="BIGINT" property="pmtDealNo" />
        <result column="dealNo" jdbcType="BIGINT" property="dealNo" />
        <result column="middleBusiCode" jdbcType="VARCHAR" property="middleBusiCode" />
        <result column="paymentTypeList" jdbcType="VARCHAR" property="paymentTypeList" />
        <result column="hkCustNo" jdbcType="VARCHAR" property="hkCustNo" />
        <result column="custName" jdbcType="VARCHAR" property="custName" />
        <result column="cpAcctNo" jdbcType="VARCHAR" property="cpAcctNo" />
        <result column="fundTxAcctNo" jdbcType="VARCHAR" property="fundTxAcctNo" />
        <result column="fundCode" jdbcType="VARCHAR" property="fundCode" />
        <result column="fundAddr" jdbcType="VARCHAR" property="fundAddr" />
        <result column="currency" jdbcType="VARCHAR" property="currency" />
        <result column="appDtm" jdbcType="TIMESTAMP" property="appDtm" />
        <result column="pmtAmt" jdbcType="DECIMAL" property="pmtAmt" />
        <result column="pmtCheckDt" jdbcType="VARCHAR" property="pmtCheckDt" />
        <result column="outPmtDealNo" jdbcType="VARCHAR" property="outPmtDealNo" />
        <result column="outPmtAmt" jdbcType="DECIMAL" property="outPmtAmt" />
        <result column="outCurrency" jdbcType="VARCHAR" property="outCurrency" />
        <result column="outPmtFlag" jdbcType="VARCHAR" property="outPmtFlag" />
        <result column="txPmtFlag" jdbcType="VARCHAR" property="txPmtFlag" />
        <result column="pmtCompFlag" jdbcType="VARCHAR" property="pmtCompFlag" />
        <result column="memo" jdbcType="VARCHAR" property="memo" />
    </resultMap>

    <select id="selectPaymentCheckResultPage"
            resultMap="PaymentCheckResultMap">
        SELECT
            po.pmt_deal_no AS pmtDealNo,
            po.deal_no AS dealNo,
            do.middle_busi_code AS middleBusiCode,
            po.payment_type_list AS paymentTypeList,
            po.hk_cust_no AS hkCustNo,
            do.cust_chinese_name AS custName,
            po.cp_acct_no AS cpAcctNo,
            po.fund_tx_acct_no AS fundTxAcctNo,
            po.fund_code AS fundCode,
            do.product_abbr AS fundAddr,
            po.currency AS currency,
            po.app_dtm AS appDtm,
            po.pmt_amt AS pmtAmt,
            po.pmt_check_dt AS pmtCheckDt,
            COALESCE(cr.out_pmt_deal_no, po.out_pmt_deal_no) AS outPmtDealNo,
            COALESCE(cr.out_pmt_amt, NULL) AS outPmtAmt,
            COALESCE(cr.out_currency, '') AS outCurrency,
            COALESCE(cr.out_pmt_flag, '') AS outPmtFlag,
            po.tx_pmt_flag AS txPmtFlag,
            po.pmt_comp_flag AS pmtCompFlag,
            COALESCE(cr.memo, '') AS memo
        FROM hw_payment_order po
        LEFT JOIN hw_payment_check_result cr ON po.pmt_deal_no = cr.pmt_deal_no AND cr.rec_stat = '1'
        LEFT JOIN hw_deal_order do ON po.deal_no = do.deal_no AND do.rec_stat = '0'
        WHERE po.rec_stat = '1'
        AND po.pmt_check_dt = #{query.pmtCheckDt,jdbcType=VARCHAR}
        <if test="query.pmtDealNo != null and query.pmtDealNo != ''">
            AND po.pmt_deal_no = #{query.pmtDealNo,jdbcType=VARCHAR}
        </if>
        <if test="query.dealNo != null and query.dealNo != ''">
            AND po.deal_no = #{query.dealNo,jdbcType=VARCHAR}
        </if>
        <if test="query.orderType != null and query.orderType != ''">
            AND po.order_type = #{query.orderType,jdbcType=VARCHAR}
        </if>
        <if test="query.outPmtDealNo != null and query.outPmtDealNo != ''">
            AND (po.out_pmt_deal_no = #{query.outPmtDealNo,jdbcType=VARCHAR} OR cr.out_pmt_deal_no = #{query.outPmtDealNo,jdbcType=VARCHAR})
        </if>
        <if test="query.fundCodes != null and query.fundCodes.size() > 0">
            AND po.fund_code IN
            <foreach collection="query.fundCodes" item="fundCode" open="(" separator="," close=")">
                #{fundCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="query.pmtCompFlags != null and query.pmtCompFlags.size() > 0">
            AND po.pmt_comp_flag IN
            <foreach collection="query.pmtCompFlags" item="pmtCompFlag" open="(" separator="," close=")">
                #{pmtCompFlag,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY po.pmt_deal_no ASC
    </select>

</mapper>
