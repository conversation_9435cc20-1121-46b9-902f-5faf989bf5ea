<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPayVoucherOrderMapper">


    <select id="selectAuditPassByDealNos" resultMap="com.howbuy.dtms.order.dao.mapper.mysql.payvoucher.HwPayVoucherOrderMapper.BaseResultMap">
        SELECT
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.payvoucher.HwPayVoucherOrderMapper.Base_Column_List" />
        FROM hw_pay_voucher_order
        WHERE rec_stat = '0'
        AND audit_status = '3'
        <if test="dealNos != null and dealNos.size() > 0">
            AND trade_order_no IN
            <foreach collection="dealNos" item="dealNo" open="(" separator="," close=")">
                #{dealNo,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY create_timestamp DESC
    </select>
</mapper>
