<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.order.dao.po.HwPaymentOrderPO">
    <!--@mbg.generated-->
    <!--@Table hw_payment_order-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pmt_deal_no" jdbcType="BIGINT" property="pmtDealNo" />
    <result column="deal_no" jdbcType="BIGINT" property="dealNo" />
    <result column="payment_type_list" jdbcType="VARCHAR" property="paymentTypeList" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="cp_acct_no" jdbcType="VARCHAR" property="cpAcctNo" />
    <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="app_dtm" jdbcType="TIMESTAMP" property="appDtm" />
    <result column="pmt_amt" jdbcType="DECIMAL" property="pmtAmt" />
    <result column="product_pay_end_dt" jdbcType="VARCHAR" property="productPayEndDt" />
    <result column="product_pay_end_dm" jdbcType="VARCHAR" property="productPayEndDm" />
    <result column="tx_pmt_flag" jdbcType="VARCHAR" property="txPmtFlag" />
    <result column="pmt_comp_flag" jdbcType="CHAR" property="pmtCompFlag" />
    <result column="pmt_check_dt" jdbcType="VARCHAR" property="pmtCheckDt" />
    <result column="out_pmt_deal_no" jdbcType="VARCHAR" property="outPmtDealNo" />
    <result column="pmt_org_code" jdbcType="VARCHAR" property="pmtOrgCode" />
    <result column="ret_code" jdbcType="VARCHAR" property="retCode" />
    <result column="ret_desc" jdbcType="VARCHAR" property="retDesc" />
    <result column="pmt_complete_dtm" jdbcType="TIMESTAMP" property="pmtCompleteDtm" />
    <result column="outlet_code" jdbcType="VARCHAR" property="outletCode" />
    <result column="trade_channel" jdbcType="VARCHAR" property="tradeChannel" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="rec_stat" jdbcType="CHAR" property="recStat" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, pmt_deal_no, deal_no, payment_type_list, hk_cust_no, cp_acct_no, fund_tx_acct_no, 
    fund_code, currency, app_dtm, pmt_amt, product_pay_end_dt, product_pay_end_dm, tx_pmt_flag, 
    pmt_comp_flag, pmt_check_dt, out_pmt_deal_no, pmt_org_code, ret_code, ret_desc, pmt_complete_dtm, 
    outlet_code, trade_channel, memo, order_type, rec_stat, version, create_timestamp, 
    update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_payment_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_payment_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentOrderPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_payment_order (pmt_deal_no, deal_no, payment_type_list, 
      hk_cust_no, cp_acct_no, fund_tx_acct_no, 
      fund_code, currency, app_dtm, 
      pmt_amt, product_pay_end_dt, product_pay_end_dm, 
      tx_pmt_flag, pmt_comp_flag, pmt_check_dt, 
      out_pmt_deal_no, pmt_org_code, ret_code, 
      ret_desc, pmt_complete_dtm, outlet_code, 
      trade_channel, memo, order_type, 
      rec_stat, version, create_timestamp, 
      update_timestamp)
    values (#{pmtDealNo,jdbcType=BIGINT}, #{dealNo,jdbcType=BIGINT}, #{paymentTypeList,jdbcType=VARCHAR}, 
      #{hkCustNo,jdbcType=VARCHAR}, #{cpAcctNo,jdbcType=VARCHAR}, #{fundTxAcctNo,jdbcType=VARCHAR}, 
      #{fundCode,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{appDtm,jdbcType=TIMESTAMP}, 
      #{pmtAmt,jdbcType=DECIMAL}, #{productPayEndDt,jdbcType=VARCHAR}, #{productPayEndDm,jdbcType=VARCHAR}, 
      #{txPmtFlag,jdbcType=VARCHAR}, #{pmtCompFlag,jdbcType=CHAR}, #{pmtCheckDt,jdbcType=VARCHAR}, 
      #{outPmtDealNo,jdbcType=VARCHAR}, #{pmtOrgCode,jdbcType=VARCHAR}, #{retCode,jdbcType=VARCHAR}, 
      #{retDesc,jdbcType=VARCHAR}, #{pmtCompleteDtm,jdbcType=TIMESTAMP}, #{outletCode,jdbcType=VARCHAR}, 
      #{tradeChannel,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR}, 
      #{recStat,jdbcType=CHAR}, #{version,jdbcType=INTEGER}, #{createTimestamp,jdbcType=TIMESTAMP}, 
      #{updateTimestamp,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentOrderPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_payment_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pmtDealNo != null">
        pmt_deal_no,
      </if>
      <if test="dealNo != null">
        deal_no,
      </if>
      <if test="paymentTypeList != null">
        payment_type_list,
      </if>
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="cpAcctNo != null">
        cp_acct_no,
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="appDtm != null">
        app_dtm,
      </if>
      <if test="pmtAmt != null">
        pmt_amt,
      </if>
      <if test="productPayEndDt != null">
        product_pay_end_dt,
      </if>
      <if test="productPayEndDm != null">
        product_pay_end_dm,
      </if>
      <if test="txPmtFlag != null">
        tx_pmt_flag,
      </if>
      <if test="pmtCompFlag != null">
        pmt_comp_flag,
      </if>
      <if test="pmtCheckDt != null">
        pmt_check_dt,
      </if>
      <if test="outPmtDealNo != null">
        out_pmt_deal_no,
      </if>
      <if test="pmtOrgCode != null">
        pmt_org_code,
      </if>
      <if test="retCode != null">
        ret_code,
      </if>
      <if test="retDesc != null">
        ret_desc,
      </if>
      <if test="pmtCompleteDtm != null">
        pmt_complete_dtm,
      </if>
      <if test="outletCode != null">
        outlet_code,
      </if>
      <if test="tradeChannel != null">
        trade_channel,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pmtDealNo != null">
        #{pmtDealNo,jdbcType=BIGINT},
      </if>
      <if test="dealNo != null">
        #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="paymentTypeList != null">
        #{paymentTypeList,jdbcType=VARCHAR},
      </if>
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="cpAcctNo != null">
        #{cpAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="appDtm != null">
        #{appDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="pmtAmt != null">
        #{pmtAmt,jdbcType=DECIMAL},
      </if>
      <if test="productPayEndDt != null">
        #{productPayEndDt,jdbcType=VARCHAR},
      </if>
      <if test="productPayEndDm != null">
        #{productPayEndDm,jdbcType=VARCHAR},
      </if>
      <if test="txPmtFlag != null">
        #{txPmtFlag,jdbcType=VARCHAR},
      </if>
      <if test="pmtCompFlag != null">
        #{pmtCompFlag,jdbcType=CHAR},
      </if>
      <if test="pmtCheckDt != null">
        #{pmtCheckDt,jdbcType=VARCHAR},
      </if>
      <if test="outPmtDealNo != null">
        #{outPmtDealNo,jdbcType=VARCHAR},
      </if>
      <if test="pmtOrgCode != null">
        #{pmtOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="retCode != null">
        #{retCode,jdbcType=VARCHAR},
      </if>
      <if test="retDesc != null">
        #{retDesc,jdbcType=VARCHAR},
      </if>
      <if test="pmtCompleteDtm != null">
        #{pmtCompleteDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="outletCode != null">
        #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=CHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentOrderPO">
    <!--@mbg.generated-->
    update hw_payment_order
    <set>
      <if test="pmtDealNo != null">
        pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT},
      </if>
      <if test="dealNo != null">
        deal_no = #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="paymentTypeList != null">
        payment_type_list = #{paymentTypeList,jdbcType=VARCHAR},
      </if>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="cpAcctNo != null">
        cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="appDtm != null">
        app_dtm = #{appDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="pmtAmt != null">
        pmt_amt = #{pmtAmt,jdbcType=DECIMAL},
      </if>
      <if test="productPayEndDt != null">
        product_pay_end_dt = #{productPayEndDt,jdbcType=VARCHAR},
      </if>
      <if test="productPayEndDm != null">
        product_pay_end_dm = #{productPayEndDm,jdbcType=VARCHAR},
      </if>
      <if test="txPmtFlag != null">
        tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR},
      </if>
      <if test="pmtCompFlag != null">
        pmt_comp_flag = #{pmtCompFlag,jdbcType=CHAR},
      </if>
      <if test="pmtCheckDt != null">
        pmt_check_dt = #{pmtCheckDt,jdbcType=VARCHAR},
      </if>
      <if test="outPmtDealNo != null">
        out_pmt_deal_no = #{outPmtDealNo,jdbcType=VARCHAR},
      </if>
      <if test="pmtOrgCode != null">
        pmt_org_code = #{pmtOrgCode,jdbcType=VARCHAR},
      </if>
      <if test="retCode != null">
        ret_code = #{retCode,jdbcType=VARCHAR},
      </if>
      <if test="retDesc != null">
        ret_desc = #{retDesc,jdbcType=VARCHAR},
      </if>
      <if test="pmtCompleteDtm != null">
        pmt_complete_dtm = #{pmtCompleteDtm,jdbcType=TIMESTAMP},
      </if>
      <if test="outletCode != null">
        outlet_code = #{outletCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeChannel != null">
        trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=CHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentOrderPO">
    <!--@mbg.generated-->
    update hw_payment_order
    set pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT},
      deal_no = #{dealNo,jdbcType=BIGINT},
      payment_type_list = #{paymentTypeList,jdbcType=VARCHAR},
      hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      cp_acct_no = #{cpAcctNo,jdbcType=VARCHAR},
      fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      app_dtm = #{appDtm,jdbcType=TIMESTAMP},
      pmt_amt = #{pmtAmt,jdbcType=DECIMAL},
      product_pay_end_dt = #{productPayEndDt,jdbcType=VARCHAR},
      product_pay_end_dm = #{productPayEndDm,jdbcType=VARCHAR},
      tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR},
      pmt_comp_flag = #{pmtCompFlag,jdbcType=CHAR},
      pmt_check_dt = #{pmtCheckDt,jdbcType=VARCHAR},
      out_pmt_deal_no = #{outPmtDealNo,jdbcType=VARCHAR},
      pmt_org_code = #{pmtOrgCode,jdbcType=VARCHAR},
      ret_code = #{retCode,jdbcType=VARCHAR},
      ret_desc = #{retDesc,jdbcType=VARCHAR},
      pmt_complete_dtm = #{pmtCompleteDtm,jdbcType=TIMESTAMP},
      outlet_code = #{outletCode,jdbcType=VARCHAR},
      trade_channel = #{tradeChannel,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR},
      rec_stat = #{recStat,jdbcType=CHAR},
      version = #{version,jdbcType=INTEGER},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>