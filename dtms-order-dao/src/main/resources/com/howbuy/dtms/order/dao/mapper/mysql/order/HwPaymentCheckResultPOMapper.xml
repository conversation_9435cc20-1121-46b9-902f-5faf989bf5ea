<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentCheckResultPOMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO">
    <!--@mbg.generated-->
    <!--@Table hw_payment_check_result-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pmt_deal_no" jdbcType="BIGINT" property="pmtDealNo" />
    <result column="deal_no" jdbcType="BIGINT" property="dealNo" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="pmt_amt" jdbcType="DECIMAL" property="pmtAmt" />
    <result column="pmt_check_dt" jdbcType="VARCHAR" property="pmtCheckDt" />
    <result column="out_pmt_deal_no" jdbcType="VARCHAR" property="outPmtDealNo" />
    <result column="out_pmt_amt" jdbcType="DECIMAL" property="outPmtAmt" />
    <result column="out_currency" jdbcType="VARCHAR" property="outCurrency" />
    <result column="out_pmt_flag" jdbcType="VARCHAR" property="outPmtFlag" />
    <result column="tx_pmt_flag" jdbcType="VARCHAR" property="txPmtFlag" />
    <result column="pmt_comp_flag" jdbcType="CHAR" property="pmtCompFlag" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="rec_stat" jdbcType="CHAR" property="recStat" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, pmt_deal_no, deal_no, hk_cust_no, fund_code, currency, pmt_amt, pmt_check_dt, 
    out_pmt_deal_no, out_pmt_amt, out_currency, out_pmt_flag, tx_pmt_flag, pmt_comp_flag, 
    memo, rec_stat, version, create_timestamp, update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_payment_check_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_payment_check_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_payment_check_result (pmt_deal_no, deal_no, hk_cust_no, 
      fund_code, currency, pmt_amt, 
      pmt_check_dt, out_pmt_deal_no, out_pmt_amt, 
      out_currency, out_pmt_flag, tx_pmt_flag, 
      pmt_comp_flag, memo, rec_stat, 
      version, create_timestamp, update_timestamp
      )
    values (#{pmtDealNo,jdbcType=BIGINT}, #{dealNo,jdbcType=BIGINT}, #{hkCustNo,jdbcType=VARCHAR}, 
      #{fundCode,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{pmtAmt,jdbcType=DECIMAL}, 
      #{pmtCheckDt,jdbcType=VARCHAR}, #{outPmtDealNo,jdbcType=VARCHAR}, #{outPmtAmt,jdbcType=DECIMAL}, 
      #{outCurrency,jdbcType=VARCHAR}, #{outPmtFlag,jdbcType=VARCHAR}, #{txPmtFlag,jdbcType=VARCHAR}, 
      #{pmtCompFlag,jdbcType=CHAR}, #{memo,jdbcType=VARCHAR}, #{recStat,jdbcType=CHAR}, 
      #{version,jdbcType=INTEGER}, #{createTimestamp,jdbcType=TIMESTAMP}, #{updateTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_payment_check_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pmtDealNo != null">
        pmt_deal_no,
      </if>
      <if test="dealNo != null">
        deal_no,
      </if>
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="pmtAmt != null">
        pmt_amt,
      </if>
      <if test="pmtCheckDt != null">
        pmt_check_dt,
      </if>
      <if test="outPmtDealNo != null">
        out_pmt_deal_no,
      </if>
      <if test="outPmtAmt != null">
        out_pmt_amt,
      </if>
      <if test="outCurrency != null">
        out_currency,
      </if>
      <if test="outPmtFlag != null">
        out_pmt_flag,
      </if>
      <if test="txPmtFlag != null">
        tx_pmt_flag,
      </if>
      <if test="pmtCompFlag != null">
        pmt_comp_flag,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pmtDealNo != null">
        #{pmtDealNo,jdbcType=BIGINT},
      </if>
      <if test="dealNo != null">
        #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="pmtAmt != null">
        #{pmtAmt,jdbcType=DECIMAL},
      </if>
      <if test="pmtCheckDt != null">
        #{pmtCheckDt,jdbcType=VARCHAR},
      </if>
      <if test="outPmtDealNo != null">
        #{outPmtDealNo,jdbcType=VARCHAR},
      </if>
      <if test="outPmtAmt != null">
        #{outPmtAmt,jdbcType=DECIMAL},
      </if>
      <if test="outCurrency != null">
        #{outCurrency,jdbcType=VARCHAR},
      </if>
      <if test="outPmtFlag != null">
        #{outPmtFlag,jdbcType=VARCHAR},
      </if>
      <if test="txPmtFlag != null">
        #{txPmtFlag,jdbcType=VARCHAR},
      </if>
      <if test="pmtCompFlag != null">
        #{pmtCompFlag,jdbcType=CHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=CHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO">
    <!--@mbg.generated-->
    update hw_payment_check_result
    <set>
      <if test="pmtDealNo != null">
        pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT},
      </if>
      <if test="dealNo != null">
        deal_no = #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="pmtAmt != null">
        pmt_amt = #{pmtAmt,jdbcType=DECIMAL},
      </if>
      <if test="pmtCheckDt != null">
        pmt_check_dt = #{pmtCheckDt,jdbcType=VARCHAR},
      </if>
      <if test="outPmtDealNo != null">
        out_pmt_deal_no = #{outPmtDealNo,jdbcType=VARCHAR},
      </if>
      <if test="outPmtAmt != null">
        out_pmt_amt = #{outPmtAmt,jdbcType=DECIMAL},
      </if>
      <if test="outCurrency != null">
        out_currency = #{outCurrency,jdbcType=VARCHAR},
      </if>
      <if test="outPmtFlag != null">
        out_pmt_flag = #{outPmtFlag,jdbcType=VARCHAR},
      </if>
      <if test="txPmtFlag != null">
        tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR},
      </if>
      <if test="pmtCompFlag != null">
        pmt_comp_flag = #{pmtCompFlag,jdbcType=CHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=CHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO">
    <!--@mbg.generated-->
    update hw_payment_check_result
    set pmt_deal_no = #{pmtDealNo,jdbcType=BIGINT},
      deal_no = #{dealNo,jdbcType=BIGINT},
      hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      pmt_amt = #{pmtAmt,jdbcType=DECIMAL},
      pmt_check_dt = #{pmtCheckDt,jdbcType=VARCHAR},
      out_pmt_deal_no = #{outPmtDealNo,jdbcType=VARCHAR},
      out_pmt_amt = #{outPmtAmt,jdbcType=DECIMAL},
      out_currency = #{outCurrency,jdbcType=VARCHAR},
      out_pmt_flag = #{outPmtFlag,jdbcType=VARCHAR},
      tx_pmt_flag = #{txPmtFlag,jdbcType=VARCHAR},
      pmt_comp_flag = #{pmtCompFlag,jdbcType=CHAR},
      memo = #{memo,jdbcType=VARCHAR},
      rec_stat = #{recStat,jdbcType=CHAR},
      version = #{version,jdbcType=INTEGER},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>