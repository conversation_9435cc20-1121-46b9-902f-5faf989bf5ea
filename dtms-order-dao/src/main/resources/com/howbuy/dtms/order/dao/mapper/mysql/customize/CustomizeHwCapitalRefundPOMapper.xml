<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwCapitalRefundPOMapper">

    <resultMap id="CapitalRefundOrderResultMap" type="com.howbuy.dtms.order.dao.bo.CapitalRefundOrderBO">
        <result column="deal_no" jdbcType="BIGINT" property="dealNo"/>
        <result column="relational_deal_no" jdbcType="BIGINT" property="relationalDealNo"/>
        <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo"/>
        <result column="middle_busi_code" jdbcType="VARCHAR" property="middleBusiCode"/>
        <result column="payment_type_list" jdbcType="VARCHAR" property="paymentTypeList"/>
    </resultMap>

    <!-- 查询支付成功撤单退款数据 -->
    <select id="selectPaidCancelRefundOrders" parameterType="map"
            resultMap="CapitalRefundOrderResultMap">
        SELECT
            o.deal_no,
            o.relational_deal_no,
            o.hk_cust_no,
            o.middle_busi_code,
            o.payment_type_list
        FROM hw_deal_order o
        INNER JOIN hw_payment_order p ON o.deal_no = p.deal_no
        WHERE o.pay_status = '4'
          AND o.order_status IN ('5', '6')
          AND o.rec_stat = '0'
          AND p.tx_pmt_flag = '2'
          AND p.pmt_comp_flag = '2'
          AND p.rec_stat = '1'
          AND o.update_timestamp >= #{updateTimeStart,jdbcType=TIMESTAMP}
          AND NOT EXISTS (
              SELECT 1 FROM hw_capital_refund r
              WHERE r.deal_no = o.deal_no
                AND r.rec_stat = '1'
          )
        ORDER BY o.deal_no
    </select>

    <!-- 查询储蓄罐赎回成功退款数据 -->
    <select id="selectPiggyRedeemRefundOrders" parameterType="map"
            resultMap="CapitalRefundOrderResultMap">
        SELECT o.deal_no,
               o.relational_deal_no,
               o.hk_cust_no,
               o.middle_busi_code,
               NULL as payment_type_list
        FROM hw_deal_order buy_order
                 INNER JOIN hw_deal_order o ON o.relational_deal_no = buy_order.deal_no
                 INNER JOIN hw_payment_order p ON buy_order.deal_no = p.deal_no
        WHERE buy_order.pay_status = '5'
          AND buy_order.order_status = '6'
          AND buy_order.rec_stat = '0'
          AND buy_order.update_timestamp >= #{updateTimeStart,jdbcType=TIMESTAMP}
          AND o.order_status IN ('2', '3')
          AND o.middle_busi_code = '1124'
          AND o.rec_stat = '0'
          AND p.tx_pmt_flag = '3'
          AND p.pmt_comp_flag = '2'
          AND p.rec_stat = '1'
          AND NOT EXISTS (SELECT 1
                          FROM hw_capital_refund r
                          WHERE r.deal_no = o.deal_no
                            AND r.rec_stat = '1')
        ORDER BY o.deal_no
    </select>

    <!-- 检查订单是否已存在退款记录 -->
    <select id="countRefundRecordByDealNo" parameterType="map" resultType="int">
        SELECT COUNT(1)
        FROM hw_capital_refund
        WHERE deal_no = #{dealNo,jdbcType=BIGINT}
          AND rec_stat = #{recStat,jdbcType=CHAR}
    </select>

    <!-- 查询未处理的退款数据 -->
    <select id="selectUnprocessedRefunds" resultMap="com.howbuy.dtms.order.dao.mapper.mysql.order.HwCapitalRefundPOMapper.BaseResultMap">
        SELECT
            <include refid="com.howbuy.dtms.order.dao.mapper.mysql.order.HwCapitalRefundPOMapper.Base_Column_List" />
        FROM hw_capital_refund
        WHERE handle_status = '0'
          AND rec_stat = '1'
        ORDER BY create_timestamp ASC
    </select>

    <!-- 批量插入退款记录 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO hw_capital_refund (
            deal_no, relational_deal_no, hk_cust_no, fund_tx_acct_no, fund_code, fund_abbr,
            fund_currency, fund_man_code, middle_busi_code, payment_type, refund_direction,
            refund_amt, refund_dt, handle_status, rec_stat, version, create_timestamp, update_timestamp
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.dealNo,jdbcType=BIGINT},
                #{item.relationalDealNo,jdbcType=BIGINT},
                #{item.hkCustNo,jdbcType=VARCHAR},
                #{item.fundTxAcctNo,jdbcType=VARCHAR},
                #{item.fundCode,jdbcType=VARCHAR},
                #{item.fundAbbr,jdbcType=VARCHAR},
                #{item.fundCurrency,jdbcType=VARCHAR},
                #{item.fundManCode,jdbcType=VARCHAR},
                #{item.middleBusiCode,jdbcType=VARCHAR},
                #{item.paymentType,jdbcType=VARCHAR},
                #{item.refundDirection,jdbcType=VARCHAR},
                #{item.refundAmt,jdbcType=DECIMAL},
                #{item.refundDt,jdbcType=VARCHAR},
                #{item.handleStatus,jdbcType=VARCHAR},
                #{item.recStat,jdbcType=CHAR},
                #{item.version,jdbcType=INTEGER},
                #{item.createTimestamp,jdbcType=TIMESTAMP},
                #{item.updateTimestamp,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!-- 批量更新退款记录处理状态为已处理 -->
    <update id="batchUpdateProcessStatusToProcessed" parameterType="map">
        UPDATE hw_capital_refund
        SET handle_status = '1',
            update_timestamp = NOW()
        WHERE deal_no IN
        <foreach collection="dealNoList" item="dealNo" open="(" separator="," close=")">
            #{dealNo,jdbcType=BIGINT}
        </foreach>
          AND handle_status = '0'
          AND rec_stat = '1'
    </update>

</mapper>
