<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPiggyTradeAppImportMapper">

    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into hw_piggy_trade_app_import(hk_cust_no, fund_tx_acct_no, cust_name, product_code, product_name, currency,
        middle_busi_code, pre_submit_ta_dt, buy_amt, fee, discount_rate, payment_type, redeem_type, app_amt, app_vol,
        redeem_direction, remark, import_dt, is_generated, deal_no, piggy_app_source, creator,
        modifier, create_time, update_time, is_deleted, version, import_app_id, relational_deal_no)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.hkCustNo}, #{entity.fundTxAcctNo}, #{entity.custName}, #{entity.productCode}, #{entity.productName}, #{entity.currency},
            #{entity.middleBusiCode}, #{entity.preSubmitTaDt}, #{entity.buyAmt}, #{entity.fee}, #{entity.discountRate},
            #{entity.paymentType}, #{entity.redeemType}, #{entity.appAmt}, #{entity.appVol}, #{entity.redeemDirection},
            #{entity.remark}, #{entity.importDt}, #{entity.isGenerated}, #{entity.dealNo}, #{entity.piggyAppSource},
            #{entity.creator}, #{entity.modifier}, #{entity.createTime}, #{entity.updateTime}, #{entity.isDeleted},
            #{entity.version}, #{entity.importAppId}, #{entity.relationalDealNo})
        </foreach>
    </insert>

    <!-- 根据关联交易单号查询 -->
    <select id="queryTradeAppImportByRelationalDealNo"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.counter.HwPiggyTradeAppImportMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.counter.HwPiggyTradeAppImportMapper.Base_Column_List" />
        from hw_piggy_trade_app_import
        where is_deleted = 0 and relational_deal_no = #{dealNo,jdbcType=BIGINT}
    </select>

    <!-- 根据导入申请Id列表查询 -->
    <select id="queryByImportAppIds"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.counter.HwPiggyTradeAppImportMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.counter.HwPiggyTradeAppImportMapper.Base_Column_List" />
        from hw_piggy_trade_app_import
        where import_app_id in
        <foreach collection="importAppIds" item="importAppId" open="(" close=")" separator=",">
            #{importAppId,jdbcType=VARCHAR}
        </foreach>
        and is_deleted = 0
        order by create_time desc
    </select>

    <!-- 批量逻辑删除生成失败的记录 -->
    <update id="batchLogicalDeleteByImportAppIds">
        update hw_piggy_trade_app_import
        set is_deleted = 1,
            update_time = now()
        where is_generated = '2'
        and import_app_id in
        <foreach collection="importAppIds" item="importAppId" open="(" close=")" separator=",">
            #{importAppId,jdbcType=VARCHAR}
        </foreach>
    </update>

    <!-- 批量插入储蓄罐交易申请记录 -->
    <insert id="batchInsert" keyProperty="id" useGeneratedKeys="true">
        insert into hw_piggy_trade_app_import(hk_cust_no, fund_tx_acct_no, cust_name, product_code, product_name, currency,
        middle_busi_code, pre_submit_ta_dt, buy_amt, fee, discount_rate, payment_type, redeem_type, app_amt, app_vol,
        redeem_direction, remark, import_dt, is_generated, deal_no, piggy_app_source, creator,
        modifier, create_time, update_time, is_deleted, version, import_app_id, relational_deal_no)
        values
        <foreach collection="records" item="record" separator=",">
            (#{record.hkCustNo,jdbcType=VARCHAR}, #{record.fundTxAcctNo,jdbcType=VARCHAR}, #{record.custName,jdbcType=VARCHAR},
            #{record.productCode,jdbcType=VARCHAR}, #{record.productName,jdbcType=VARCHAR}, #{record.currency,jdbcType=VARCHAR},
            #{record.middleBusiCode,jdbcType=VARCHAR}, #{record.preSubmitTaDt,jdbcType=VARCHAR}, #{record.buyAmt,jdbcType=DECIMAL},
            #{record.fee,jdbcType=DECIMAL}, #{record.discountRate,jdbcType=DECIMAL}, #{record.paymentType,jdbcType=VARCHAR},
            #{record.redeemType,jdbcType=VARCHAR}, #{record.appAmt,jdbcType=DECIMAL}, #{record.appVol,jdbcType=DECIMAL},
            #{record.redeemDirection,jdbcType=VARCHAR}, #{record.remark,jdbcType=VARCHAR}, #{record.importDt,jdbcType=VARCHAR},
            '0', #{record.dealNo,jdbcType=VARCHAR}, #{record.piggyAppSource,jdbcType=VARCHAR}, #{record.creator,jdbcType=VARCHAR},
            #{record.modifier,jdbcType=VARCHAR}, now(), now(), 0, 0, #{record.importAppId,jdbcType=VARCHAR},
            #{record.relationalDealNo,jdbcType=BIGINT})
        </foreach>
    </insert>

    <!-- 乐观锁更新是否生成及相关字段 -->
    <update id="updateIsGeneratedByAppIdAndOldStatus" parameterType="com.howbuy.dtms.order.dao.param.PiggyTradeAppImportUpdateParam">
        update hw_piggy_trade_app_import
        set is_generated = #{isGenerated,jdbcType=VARCHAR},
            deal_no = #{dealNo,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
            modifier = #{modifier,jdbcType=VARCHAR},
            update_time = #{modifyTime,jdbcType=TIMESTAMP}
        where import_app_id = #{piggyAppId,jdbcType=VARCHAR}
          and is_generated = #{oldIsGenerated,jdbcType=VARCHAR}
          and is_deleted = 0
    </update>

    <!-- 根据导入申请Id查询 -->
    <select id="queryByImportAppId"
            resultMap="com.howbuy.dtms.order.dao.mapper.mysql.counter.HwPiggyTradeAppImportMapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.counter.HwPiggyTradeAppImportMapper.Base_Column_List" />
        from hw_piggy_trade_app_import
        where import_app_id = #{importAppId,jdbcType=VARCHAR}
          and is_deleted = 0
    </select>

    <!-- 根据导入申请Id更新 -->
    <update id="updateByImportAppId" parameterType="com.howbuy.dtms.order.dao.po.HwPiggyTradeAppImport">
        update hw_piggy_trade_app_import
        set buy_amt = #{buyAmt,jdbcType=DECIMAL},
            fee = #{fee,jdbcType=DECIMAL},
            discount_rate = #{discountRate,jdbcType=DECIMAL},
            app_amt = #{appAmt,jdbcType=DECIMAL},
            remark = #{remark,jdbcType=VARCHAR},
            modifier = #{modifier,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where import_app_id = #{importAppId,jdbcType=VARCHAR}
          and is_deleted = 0
          and is_generated = '0'
    </update>

</mapper>