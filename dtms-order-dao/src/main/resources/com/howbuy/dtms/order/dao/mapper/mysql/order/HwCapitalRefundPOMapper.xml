<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.order.HwCapitalRefundPOMapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.order.dao.po.HwCapitalRefundPO">
    <!--@mbg.generated-->
    <!--@Table hw_capital_refund-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deal_no" jdbcType="BIGINT" property="dealNo" />
    <result column="relational_deal_no" jdbcType="BIGINT" property="relationalDealNo" />
    <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
    <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
    <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
    <result column="fund_abbr" jdbcType="VARCHAR" property="fundAbbr" />
    <result column="fund_currency" jdbcType="VARCHAR" property="fundCurrency" />
    <result column="fund_man_code" jdbcType="VARCHAR" property="fundManCode" />
    <result column="middle_busi_code" jdbcType="VARCHAR" property="middleBusiCode" />
    <result column="payment_type" jdbcType="VARCHAR" property="paymentType" />
    <result column="refund_direction" jdbcType="CHAR" property="refundDirection" />
    <result column="refund_amt" jdbcType="DECIMAL" property="refundAmt" />
    <result column="refund_dt" jdbcType="VARCHAR" property="refundDt" />
    <result column="handle_status" jdbcType="VARCHAR" property="handleStatus" />
    <result column="rec_stat" jdbcType="CHAR" property="recStat" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
    <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, deal_no, relational_deal_no, hk_cust_no, fund_tx_acct_no, fund_code, fund_abbr, 
    fund_currency, fund_man_code, middle_busi_code, payment_type, refund_direction, refund_amt, 
    refund_dt, handle_status, rec_stat, version, create_timestamp, update_timestamp
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from hw_capital_refund
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from hw_capital_refund
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.order.dao.po.HwCapitalRefundPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_capital_refund (deal_no, relational_deal_no, hk_cust_no, 
      fund_tx_acct_no, fund_code, fund_abbr, 
      fund_currency, fund_man_code, middle_busi_code, 
      payment_type, refund_direction, refund_amt, 
      refund_dt, handle_status, rec_stat, 
      version, create_timestamp, update_timestamp
      )
    values (#{dealNo,jdbcType=BIGINT}, #{relationalDealNo,jdbcType=BIGINT}, #{hkCustNo,jdbcType=VARCHAR}, 
      #{fundTxAcctNo,jdbcType=VARCHAR}, #{fundCode,jdbcType=VARCHAR}, #{fundAbbr,jdbcType=VARCHAR}, 
      #{fundCurrency,jdbcType=VARCHAR}, #{fundManCode,jdbcType=VARCHAR}, #{middleBusiCode,jdbcType=VARCHAR}, 
      #{paymentType,jdbcType=VARCHAR}, #{refundDirection,jdbcType=CHAR}, #{refundAmt,jdbcType=DECIMAL}, 
      #{refundDt,jdbcType=VARCHAR}, #{handleStatus,jdbcType=VARCHAR}, #{recStat,jdbcType=CHAR}, 
      #{version,jdbcType=INTEGER}, #{createTimestamp,jdbcType=TIMESTAMP}, #{updateTimestamp,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.howbuy.dtms.order.dao.po.HwCapitalRefundPO" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into hw_capital_refund
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dealNo != null">
        deal_no,
      </if>
      <if test="relationalDealNo != null">
        relational_deal_no,
      </if>
      <if test="hkCustNo != null">
        hk_cust_no,
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no,
      </if>
      <if test="fundCode != null">
        fund_code,
      </if>
      <if test="fundAbbr != null">
        fund_abbr,
      </if>
      <if test="fundCurrency != null">
        fund_currency,
      </if>
      <if test="fundManCode != null">
        fund_man_code,
      </if>
      <if test="middleBusiCode != null">
        middle_busi_code,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="refundDirection != null">
        refund_direction,
      </if>
      <if test="refundAmt != null">
        refund_amt,
      </if>
      <if test="refundDt != null">
        refund_dt,
      </if>
      <if test="handleStatus != null">
        handle_status,
      </if>
      <if test="recStat != null">
        rec_stat,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createTimestamp != null">
        create_timestamp,
      </if>
      <if test="updateTimestamp != null">
        update_timestamp,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dealNo != null">
        #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="relationalDealNo != null">
        #{relationalDealNo,jdbcType=BIGINT},
      </if>
      <if test="hkCustNo != null">
        #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAbbr != null">
        #{fundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="fundCurrency != null">
        #{fundCurrency,jdbcType=VARCHAR},
      </if>
      <if test="fundManCode != null">
        #{fundManCode,jdbcType=VARCHAR},
      </if>
      <if test="middleBusiCode != null">
        #{middleBusiCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="refundDirection != null">
        #{refundDirection,jdbcType=CHAR},
      </if>
      <if test="refundAmt != null">
        #{refundAmt,jdbcType=DECIMAL},
      </if>
      <if test="refundDt != null">
        #{refundDt,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null">
        #{handleStatus,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        #{recStat,jdbcType=CHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.order.dao.po.HwCapitalRefundPO">
    <!--@mbg.generated-->
    update hw_capital_refund
    <set>
      <if test="dealNo != null">
        deal_no = #{dealNo,jdbcType=BIGINT},
      </if>
      <if test="relationalDealNo != null">
        relational_deal_no = #{relationalDealNo,jdbcType=BIGINT},
      </if>
      <if test="hkCustNo != null">
        hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      </if>
      <if test="fundTxAcctNo != null">
        fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      </if>
      <if test="fundCode != null">
        fund_code = #{fundCode,jdbcType=VARCHAR},
      </if>
      <if test="fundAbbr != null">
        fund_abbr = #{fundAbbr,jdbcType=VARCHAR},
      </if>
      <if test="fundCurrency != null">
        fund_currency = #{fundCurrency,jdbcType=VARCHAR},
      </if>
      <if test="fundManCode != null">
        fund_man_code = #{fundManCode,jdbcType=VARCHAR},
      </if>
      <if test="middleBusiCode != null">
        middle_busi_code = #{middleBusiCode,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=VARCHAR},
      </if>
      <if test="refundDirection != null">
        refund_direction = #{refundDirection,jdbcType=CHAR},
      </if>
      <if test="refundAmt != null">
        refund_amt = #{refundAmt,jdbcType=DECIMAL},
      </if>
      <if test="refundDt != null">
        refund_dt = #{refundDt,jdbcType=VARCHAR},
      </if>
      <if test="handleStatus != null">
        handle_status = #{handleStatus,jdbcType=VARCHAR},
      </if>
      <if test="recStat != null">
        rec_stat = #{recStat,jdbcType=CHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="createTimestamp != null">
        create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTimestamp != null">
        update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.order.dao.po.HwCapitalRefundPO">
    <!--@mbg.generated-->
    update hw_capital_refund
    set deal_no = #{dealNo,jdbcType=BIGINT},
      relational_deal_no = #{relationalDealNo,jdbcType=BIGINT},
      hk_cust_no = #{hkCustNo,jdbcType=VARCHAR},
      fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR},
      fund_code = #{fundCode,jdbcType=VARCHAR},
      fund_abbr = #{fundAbbr,jdbcType=VARCHAR},
      fund_currency = #{fundCurrency,jdbcType=VARCHAR},
      fund_man_code = #{fundManCode,jdbcType=VARCHAR},
      middle_busi_code = #{middleBusiCode,jdbcType=VARCHAR},
      payment_type = #{paymentType,jdbcType=VARCHAR},
      refund_direction = #{refundDirection,jdbcType=CHAR},
      refund_amt = #{refundAmt,jdbcType=DECIMAL},
      refund_dt = #{refundDt,jdbcType=VARCHAR},
      handle_status = #{handleStatus,jdbcType=VARCHAR},
      rec_stat = #{recStat,jdbcType=CHAR},
      version = #{version,jdbcType=INTEGER},
      create_timestamp = #{createTimestamp,jdbcType=TIMESTAMP},
      update_timestamp = #{updateTimestamp,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>