<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.customize.HwSubPaidDtlInfoCustomizeMapper">

    <resultMap id="BaseResultMap" type="com.howbuy.dtms.order.dao.po.HwSubPaidDtlInfoPO">
        <!--@mbg.generated-->
        <!--@Table hw_sub_paid_dtl_info-->
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="dtl_serial_no" jdbcType="BIGINT" property="dtlSerialNo" />
        <result column="serial_no" jdbcType="BIGINT" property="serialNo" />
        <result column="deal_no" jdbcType="BIGINT" property="dealNo" />
        <result column="hk_cust_no" jdbcType="VARCHAR" property="hkCustNo" />
        <result column="fund_tx_acct_no" jdbcType="VARCHAR" property="fundTxAcctNo" />
        <result column="fund_code" jdbcType="VARCHAR" property="fundCode" />
        <result column="middle_busi_code" jdbcType="VARCHAR" property="middleBusiCode" />
        <result column="sub_amt" jdbcType="DECIMAL" property="subAmt" />
        <result column="paid_amt" jdbcType="DECIMAL" property="paidAmt" />
        <result column="rec_stat" jdbcType="VARCHAR" property="recStat" />
        <result column="version" jdbcType="INTEGER" property="version" />
        <result column="create_timestamp" jdbcType="TIMESTAMP" property="createTimestamp" />
        <result column="update_timestamp" jdbcType="TIMESTAMP" property="updateTimestamp" />
    </resultMap>

    <sql id="Base_Column_List">
        id, dtl_serial_no, serial_no, deal_no, hk_cust_no, fund_tx_acct_no, fund_code, middle_busi_code,
        sub_amt, paid_amt, rec_stat, version, create_timestamp, update_timestamp
    </sql>
    <select id="selectByDealNoAndFundCodeAndFundTxAcctNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM hw_sub_paid_dtl_info
        WHERE deal_no = #{dealNo,jdbcType=BIGINT}
        and fund_code = #{fundCode,jdbcType=VARCHAR}
        and fund_tx_acct_no = #{fundTxAcctNo,jdbcType=VARCHAR}
        and hk_cust_no = #{hkCustNo,jdbcType=VARCHAR}
        and rec_stat = '1'
    </select>
</mapper> 