# 项目结构

## 模块划分
项目采用Maven多模块架构，按照分层架构原则组织：

```
dtms-order/                    # 父项目
├── dtms-order-client/         # 接口公共参数层
├── dtms-order-dao/           # 数据访问层 (DAO)
├── dtms-order-service/       # 业务逻辑层 (Service)
├── dtms-order-remote/        # 启动模块 (Web/RPC入口)
├── pom.xml                   # 父POM配置
└── start.sh                  # 启动脚本
```

## 核心模块职责

### dtms-order-client
- 定义对外接口
- 公共DTO和VO对象
- 接口参数和返回值定义

### dtms-order-dao  
- 数据库实体类 (PO)
- MyBatis Mapper接口
- SQL映射文件
- 数据访问逻辑

### dtms-order-service
- 业务逻辑实现
- 事务管理
- 外部服务调用
- 消息处理

### dtms-order-remote
- Spring Boot启动类
- Web Controller
- Dubbo Provider配置
- 应用配置文件

## Service层内部结构

```
service/
├── aspect/              # AOP切面
├── business/            # 业务处理器
│   ├── ordercreate/    # 订单创建
│   ├── orderupdate/    # 订单更新  
│   ├── feecompute/     # 费用计算
│   ├── fundshare/      # 基金份额
│   └── sendmq/         # 消息发送
├── cacheservice/       # 缓存服务
├── config/             # 配置类
├── controller/         # 控制器
├── job/                # 定时任务
├── mq/                 # 消息处理
├── outerservice/       # 外部服务调用
├── provider/           # Dubbo服务提供者
├── repository/         # 数据仓库
├── service/            # 核心业务服务
└── validate/           # 参数校验
```

## 包命名规范
- 基础包名: `com.howbuy.dtms.order`
- 按功能模块划分子包
- 使用小写字母和点分隔符
- 避免使用缩写，保持语义清晰

## 配置文件组织
- `application.yml`: 主配置文件
- `bootstrap.properties`: 启动配置
- `dubbo.properties`: Dubbo配置
- `logback-spring.xml`: 日志配置

## 事务管理规范
- 类级别: `@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
- 方法级别: `@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
- 查询方法使用SUPPORTS传播机制
- 增删改方法使用REQUIRED传播机制