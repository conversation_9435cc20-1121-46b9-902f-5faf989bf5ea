# 技术栈

## 核心框架
- **Java版本**: JDK 1.8
- **Spring Boot**: 2.3.7.RELEASE
- **Spring Cloud**: Hoxton.SR9
- **Spring Cloud Alibaba**: 2.2.6.RELEASE
- **Dubbo**: 3.2.12 (RPC框架)

## 数据库与持久化
- **MyBatis**: 2.2.2 (ORM框架)
- **Druid**: 1.2.8 (数据库连接池)
- **PageHelper**: 5.3.0 (分页插件)

## 消息中间件
- **RocketMQ**: 消息队列
- **ActiveMQ**: 消息队列备选

## 缓存与存储
- **Redis**: 分布式缓存
- **Jedis**: 4.3.2 (Redis客户端)

## 注册中心与配置
- **Zookeeper**: 3.4.13 (服务注册发现)
- **Nacos**: 配置中心

## 工具库
- **FastJSON**: 1.2.80 (JSON处理)
- **EasyExcel**: 3.0.5 (Excel处理)
- **Commons Collections**: 4.2
- **Lombok**: 代码生成

## 测试框架
- **PowerMock**: 2.0.2
- **TestNG**: 单元测试
- **JUnit4**: 单元测试

## 构建工具
- **Maven**: 项目构建和依赖管理

## 常用命令

### 构建命令
```bash
# 编译项目
mvn clean compile

# 打包项目
mvn clean package

# 跳过测试打包
mvn clean package -DskipTests

# 安装到本地仓库
mvn clean install
```

### 部署命令
```bash
# 启动应用
./start.sh

# 调试模式启动
./start.sh debug

# JMX监控模式启动
./start.sh jmx
```

### 测试命令
```bash
# 运行所有测试
mvn test

# 运行指定测试类
mvn test -Dtest=TestClassName
```

## JVM配置
- **生产环境**: -Xmx4096m -Xms4096m -Xmn800m
- **开发环境**: -Xms1g -Xmx1g
- **GC策略**: CMS垃圾收集器