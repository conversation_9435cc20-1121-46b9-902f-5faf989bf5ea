```plantuml
@startmindmap
* BuyWebFacadeImpl 认申购下单接口
** 客户账户状态校验
*** 正例：账户状态正常
*** 反例：账户状态异常 (ACCOUNT_STATUS_EXCEPTION)
** 交易密码校验
*** 正例：交易密码正确
*** 反例：交易密码错误 (E0001)
** 基金交易账户状态校验
*** 基金交易账户不存在
**** 正例：自动创建非全权委托账户成功
**** 反例：创建失败
*** 基金交易账户存在
**** 正例：账户状态正常
**** 反例：账户状态非正常 (FUND_TX_ACCT_STAT_ERROR)
**** 反例：全权委托产品使用非全权委托账户 (FUND_TX_ACCT_TYPE_ERROR)
** 资金账号校验 (CpAcctNo)
*** 申购业务 (电汇)
**** 正例：资金账号存在且有效
**** 反例：资金账号不存在
*** 赎回业务 (电汇)
**** 正例：资金账号存在且有效
**** 反例：资金账号不存在
** 预约单号校验 (PrebookDealNo)
*** 预约单号不为空
**** 正例：预约单有效且未使用
**** 反例：预约单号错误 (PREBOOK_DEAL_NO_ERROR)
**** 反例：预约单号已被使用 (PREBOOK_DEAL_NO_USED)
*** 预约单号为空 (认缴业务除外)
**** 正例：成功生成预约单号
**** 反例：生成预约单号失败 (PREBOOK_DEAL_NO_ERROR)
** 订单号校验 (DealNo)
*** 正例：订单号有效且未使用
*** 反例：订单号错误 (DEAL_NO_ERROR)
*** 反例：订单号已被使用 (DEAL_NO_USED)
** 外部订单号校验
*** 正例：外部订单号唯一
*** 反例：外部订单号不能为空 (PARAMS_ERROR)
*** 反例：外部订单号已被使用 (EXTERNAL_DEAL_NO_USED)
** 风险等级校验
*** 实缴业务(112B)
**** 正例：不校验，直接通过
*** 非实缴业务
**** 客户风险等级 >= 产品风险等级
***** 正例：校验通过
**** 客户风险等级 < 产品风险等级
***** 反例：风险等级不匹配 (RISK_LEVEL_NOT_MATCH)
** 衍生品投资经验校验
*** 实缴业务(112B)
**** 正例：不校验，直接通过
*** 投资衍生品产品
**** 客户有衍生品投资经验
***** 正例：校验通过
**** 客户无衍生品投资经验
***** 反例：无衍生品投资经验 (DERIVATIVE_EXPERIENCE_NO)
** 投资者资质校验
*** 实缴业务(112B)
**** 正例：不校验，直接通过
*** 虚拟资产或私募产品
**** 专业投资者
***** 正例：校验通过
**** 非专业投资者
***** 反例：非专业投资者 (NOT_PROFESSIONAL_INVESTOR)
** 产品年龄限制校验
*** 开启年龄限制
**** 个人投资者
***** 年龄在限制范围内
****** 正例：校验通过
***** 年龄不在限制范围内
****** 反例：年龄不符 (AGE_NOT_MATCH)
**** 机构/产品投资者
***** 正例：不校验，直接通过
*** 未开启年龄限制
**** 正例：不校验，直接通过
** 产品渠道校验
*** 正例：产品渠道有效
*** 反例：产品渠道无效或关闭 (由 `queryProductControlInfo` 抛出异常)
** 产品业务开通校验
*** 正例：产品业务已开通
*** 反例：产品业务未开通 (由 `queryFundTxOpenCfg` 抛出异常)
** 支付方式校验
*** 储蓄罐支付
**** 已签署储蓄罐协议
***** 正例：校验通过
**** 未签署储蓄罐协议
***** 反例：未签署储蓄罐协议 (NOT_SIGN_CXG_AGREEMENT_ERROR)
*** 其他支付方式
**** 正例：校验通过
** 开放日与打款时间校验
*** 申请时间 < 打款截止时间
**** 正例：校验通过
*** 申请时间 >= 打款截止时间
**** 反例：超过打款截止时间 (PAYMENT_DEADLINE_NOT_SATISFY)
** 产品净值状态校验
*** 交易日基金状态可交易
**** 正例：校验通过
*** 交易日基金状态不可交易
**** 反例：产品状态异常 (PRODUCT_NAV_STATUS_ABNORMAL)
** 本期购买人数及金额校验 (支持预约)
*** 人数校验
**** 当前总人数 < 本期限制人数
***** 正例：校验通过
**** 当前总人数 >= 本期限制人数
***** 反例：超过本期购买人数 (CURRENT_PURCHASE_NUMBER_EXCEEDED)
*** 金额校验
**** 当前总金额 < 本期限制金额
***** 正例：校验通过
**** 当前总金额 >= 本期限制金额
***** 反例：超过本期购买额度 (CURRENT_PURCHASE_AMOUNT_EXCEEDED)
** 本期单人购买笔数及金额校验 (支持预约)
*** 笔数校验 (配置了限制)
**** 当前笔数 < 限制笔数
***** 正例：校验通过
**** 当前笔数 >= 限制笔数
***** 反例：超过单人购买笔数 (CURRENT_PERIOD_ORDER_NUMBER_EXCEEDED)
*** 金额校验 (配置了限制)
**** 当前金额 < 限制金额
***** 正例：校验通过
**** 当前金额 >= 限制金额
***** 反例：超过单人购买金额 (CURRENT_PERIOD_CURRENT_ORDER_AMOUNT_EXCEEDED)
** 交易限额校验
*** 最大申购金额
**** 申请金额 <= 最大申购金额
***** 正例：校验通过
**** 申请金额 > 最大申购金额
***** 反例：超过最大申购金额 (NET_APP_AMT_MORE_THAN_MAX)
*** 最小申购金额 (首次)
**** 申请金额 >= 最小申购金额
***** 正例：校验通过
**** 申请金额 < 最小申购金额
***** 反例：低于最小申购金额 (NET_APP_AMT_LESS_THAN_MIN)
*** 最小申购金额 (追加)
**** 申请金额 >= 最小追加金额
***** 正例：校验通过
**** 申请金额 < 最小追加金额
***** 反例：低于最小追加金额 (NET_APP_AMT_LESS_THAN_ADD_MIN)
** 交易级差校验
*** 首次购买
**** (申请金额 - 最小金额) % 级差 == 0
***** 正例：校验通过
**** (申请金额 - 最小金额) % 级差 != 0
***** 反例：不满足首次购买级差 (NOT_SATISFY_FIRST_PURCHASE_DIFFERENTIAL)
*** 追加购买
**** (申请金额 - 最小追加) % 级差 == 0
***** 正例：校验通过
**** (申请金额 - 最小追加) % 级差 != 0
***** 反例：不满足追加购买级差 (NOT_SATISFY_ADD_PURCHASE_DIFFERENTIAL)
** 手续费校验
*** JPY币种
**** 金额小数位为0
***** 正例：校验通过
**** 金额小数位 > 0
***** 反例：JPY金额不能有小数 (JPY_AMT_SCALE_ERROR)
*** 手续费计算
**** 前端传入手续费 == 后端计算手续费
***** 正例：校验通过
**** 前端传入手续费 != 后端计算手续费
***** 反例：手续费校验失败 (FEE_ERROR)
@endmindmap 
```