# APP认申购下单接口测试脑图

```plantuml
@startmindmap
* APP认申购下单接口
** 基础参数校验
*** 正例：appDt、appTm、tradeChannel、outletCode、ipAddress、externalDealNo全部传入
*** 反例：参数错误C020002 - 必填参数缺失
** 并发控制校验
*** 正例：外部订单号未被锁定
*** 反例：并发异常C020003 - 外部订单号正在处理中
** 账户状态校验
*** 正例：客户状态正常
*** 反例：C021002 - 客户状态异常
** 交易密码校验
*** 正例：交易密码正确
*** 反例：C021008 - 交易密码错误
*** 反例：C023064 - 交易密码状态异常
** 基金交易账户状态校验
*** 正例：基金交易账户状态正常
*** 反例：C021083 - 基金交易账户状态异常
*** 反例：C021084 - 基金交易账户不存在
** 资金账号校验
*** 正例：银行卡存在且有效
*** 反例：C021009 - 银行卡不存在
** 预约单号校验
*** 正例：预约单号有效或为空
*** 反例：C021036 - 预约单号错误
*** 反例：C021050 - 预约单号已被使用
** 订单号校验
*** 正例：订单号唯一
*** 反例：C021055 - 订单号已被使用
** 外部订单号校验
*** 正例：外部订单号唯一
*** 反例：C021056 - 外部订单号已被使用
** 风险等级校验
*** 正例：客户风测等级与产品风险等级匹配
*** 反例：C021004 - 客户风测等级与产品风险等级不匹配
** 衍生品经验校验
*** 正例：客户具备衍生品知识
*** 反例：C021005 - 客户不具备衍生品知识
** 投资者资质校验
*** 正例：客户为专业投资者且资产证明有效
*** 反例：C021006 - 客户非专业投资者
*** 反例：C021007 - 客户非专业投资者或资产证明已过期
** 产品年龄校验
*** 正例：年龄符合渠道交易限制
*** 反例：C021010 - 年龄不符合渠道交易限制
** 产品渠道校验
*** 正例：渠道允许该业务
*** 反例：C021011 - 渠道不允许该业务
** 产品业务校验
*** 正例：产品已开通该业务
*** 反例：C021012 - 产品未开通该业务
** 支付方式校验
*** 正例：支付方式有效
*** 反例：参数错误 - 支付方式不存在
*** 储蓄罐支付校验
**** 正例：已签署储蓄罐协议且协议有效
**** 反例：C023011 - 未签署海外储蓄罐协议或协议未生效
**** 反例：C023012 - 签署的储蓄罐协议非当前储蓄罐基金
** 根据支付方式开放日校验
*** 正例：满足支付方式对应的开放日要求
*** 反例：C021013 - 产品不在预约期内
*** 反例：C021014 - 打款截止日期、时间不满足
*** 反例：C021048 - 产品不在开放期内
** 产品净值状态校验
*** 正例：产品净值状态正常
*** 反例：C021017 - 产品的净值状态不正常
*** 反例：C023074 - 基金净值为空
** 本期人数金额校验
*** 正例：本期购买人数和金额未超额
*** 反例：C021015 - 本期购买人数已超额
*** 反例：C021016 - 本期购买金额已超额
** 本期单人订单校验
*** 正例：客户本期订单笔数和金额未超额
*** 反例：C023069 - 客户本期订单笔数已超额
*** 反例：C023070 - 客户本期订单总购买金额已超额
** 买入交易限额校验
*** 正例：申请金额在限额范围内
*** 反例：C021020 - 净申请金额低于下限
*** 反例：C021021 - 净申请金额超过上限
*** 反例：C021022 - 净申请金额低于追加下限
*** 反例：C021095 - 申请金额低于下限
*** 反例：C021096 - 申请金额超过上限
** 交易级差校验
*** 正例：净申请满足级差要求
*** 反例：C021018 - 净申请不满足首次购买级差
*** 反例：C021019 - 净申请不满足追加购买级差
** 买入手续费校验
*** 正例：手续费计算正确
*** 反例：C021023 - 手续费不正确
*** 反例：C023068 - 预估手续费小于0
*** 反例：C021096 - 日元金额不允许有小数
** 柜台收市校验
*** 正例：基金未柜台收市或满足预计上报日要求
*** 反例：C021082 - 当前基金已柜台收市，不允许柜台交易
** 测试账号校验
*** 正例：非测试账号或测试开关关闭
*** 反例：C023028 - 香港客户号是测试账号不允许交易
** 订单创建
*** 正例：订单及订单明细创建成功
*** 反例：C020996 - 数据库异常
** 订单保存
*** 正例：订单数据保存成功
*** 反例：C020996 - 数据库异常
** 订单消息发送
*** 正例：买入订单消息发送成功
*** 反例：C020001 - 系统异常
** 返回结果
*** 正例：返回订单号
*** 反例：C020001 - 系统异常
@endmindmap
```
