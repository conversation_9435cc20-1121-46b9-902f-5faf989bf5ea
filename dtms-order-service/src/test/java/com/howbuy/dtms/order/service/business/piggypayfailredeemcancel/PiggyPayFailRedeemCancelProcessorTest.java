/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.piggypayfailredeemcancel;

import com.howbuy.dtms.order.client.domain.request.cannel.CounterCancelRequest;
import com.howbuy.dtms.order.dao.bo.PiggyPayFailRedeemOrderBO;

import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.service.trade.cancelorder.CounterCancelService;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description: 储蓄罐支付失败赎回撤单处理器测试类
 * @author: shaoyang.li
 * @date: 2025-07-10 19:59:33
 * @since JDK 1.8
 */
@ExtendWith(MockitoExtension.class)
class PiggyPayFailRedeemCancelProcessorTest {

    @Mock
    private HwDealOrderRepository hwDealOrderRepository;

    @Mock
    private CounterCancelService counterCancelService;

    @InjectMocks
    private PiggyPayFailRedeemCancelProcessor processor;

    @Test
    void testProcess_NoOrdersFound() {
        // Given
        when(hwDealOrderRepository.selectPiggyPayFailRedeemOrders(any())).thenReturn(Collections.emptyList());

        // When
        processor.process();

        // Then
        verify(counterCancelService, never()).process(any(CounterCancelRequest.class));
    }

    @Test
    void testProcess_SuccessfulCancellation() {
        // Given
        PiggyPayFailRedeemOrderBO order1 = new PiggyPayFailRedeemOrderBO();
        order1.setHkCustNo("HK001");
        order1.setDealNo(1001L);
        
        PiggyPayFailRedeemOrderBO order2 = new PiggyPayFailRedeemOrderBO();
        order2.setHkCustNo("HK002");
        order2.setDealNo(1002L);
        
        List<PiggyPayFailRedeemOrderBO> orders = Arrays.asList(order1, order2);
        when(hwDealOrderRepository.selectPiggyPayFailRedeemOrders(any())).thenReturn(orders);

        // When
        processor.process();

        // Then
        verify(counterCancelService, times(2)).process(any(CounterCancelRequest.class));
    }

    @Test
    void testProcess_CancellationException() {
        // Given
        PiggyPayFailRedeemOrderBO order1 = new PiggyPayFailRedeemOrderBO();
        order1.setHkCustNo("HK001");
        order1.setDealNo(1001L);
        
        List<PiggyPayFailRedeemOrderBO> orders = Arrays.asList(order1);
        when(hwDealOrderRepository.selectPiggyPayFailRedeemOrders(any())).thenReturn(orders);
        
        doThrow(new RuntimeException("撤单失败")).when(counterCancelService).process(any(CounterCancelRequest.class));

        // When
        processor.process();

        // Then
        verify(counterCancelService, times(1)).process(any(CounterCancelRequest.class));
        // 异常被捕获，不会影响整体流程
    }
}
