/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.payment;

import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.service.business.sendmq.SendMqService;
import com.howbuy.dtms.order.service.cacheservice.lock.LockService;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description: 支付结果处理业务类测试
 * <AUTHOR>
 * @date 2025-07-03 16:02:08
 * @since JDK 1.8
 */
@ExtendWith(MockitoExtension.class)
class ProcessPmtResultBusinessTest {

    @InjectMocks
    private ProcessPmtResultBusiness processPmtResultBusiness;

    @Mock
    private LockService lockService;

    @Mock
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Mock
    private SendMqService sendMqService;

    private HwPaymentOrderPO mockPaymentOrder;
    private HwDealOrder mockDealOrder;
    private List<HwDealOrderDtl> mockDealOrderDtlList;
    private PaymentResultDTO mockPaymentResult;

    @BeforeEach
    void setUp() {
        // 初始化模拟支付订单
        mockPaymentOrder = new HwPaymentOrderPO();
        mockPaymentOrder.setPmtDealNo(123456789L);
        mockPaymentOrder.setDealNo(987654321L);
        mockPaymentOrder.setOrderType(PaymentOrderTypeEnum.TRADE.getCode());
        mockPaymentOrder.setTxPmtFlag(TxPmtFlagEnum.PAYING.getCode());
        mockPaymentOrder.setHkCustNo("HK001");
        mockPaymentOrder.setFundCode("F001");
        mockPaymentOrder.setCurrency("USD");
        mockPaymentOrder.setPmtAmt(new BigDecimal("1000.00"));
        mockPaymentOrder.setUpdateTimestamp(new Date());

        // 初始化模拟交易订单
        mockDealOrder = new HwDealOrder();
        mockDealOrder.setDealNo(987654321L);
        mockDealOrder.setHkCustNo("HK001");
        mockDealOrder.setOrderStatus("1");
        mockDealOrder.setPayStatus("2");
        mockDealOrder.setUpdateTimestamp(new Date());

        // 初始化模拟订单明细
        HwDealOrderDtl dtl1 = new HwDealOrderDtl();
        dtl1.setId(1L);
        dtl1.setDealNo(987654321L);
        dtl1.setAppStatus("0");
        dtl1.setUpdateTimestamp(new Date());

        HwDealOrderDtl dtl2 = new HwDealOrderDtl();
        dtl2.setId(2L);
        dtl2.setDealNo(987654321L);
        dtl2.setAppStatus("0");
        dtl2.setUpdateTimestamp(new Date());

        mockDealOrderDtlList = Arrays.asList(dtl1, dtl2);

        // 初始化模拟支付结果
        mockPaymentResult = new PaymentResultDTO();
        mockPaymentResult.setTxPmtFlag(TxPmtFlagEnum.PAY_SUCCESS.getCode());
        mockPaymentResult.setRetCode("0000");
        mockPaymentResult.setRetDesc("支付成功");
        mockPaymentResult.setOutPmtDealNo("EXT123456");
        mockPaymentResult.setPmtCheckDt("20250703");
        mockPaymentResult.setPmtAmt(new BigDecimal("1000.00"));
    }

    @Test
    void testProcessPaymentSuccess() {
        // 模拟获取锁成功
        when(lockService.getLock(anyString(), anyInt())).thenReturn(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            processPmtResultBusiness.process(mockPaymentOrder, mockDealOrder, mockDealOrderDtlList, mockPaymentResult);
        });

        // 验证方法调用
        verify(lockService).getLock(anyString(), anyInt());
        verify(hwPaymentOrderRepository).updatePaymentResultWithTransaction(
                any(HwPaymentOrderPO.class), any(HwDealOrder.class), anyList(), any(PaymentResultDTO.class));
        verify(sendMqService).sendOrderUpdateMessage(any(HwDealOrder.class));
        verify(lockService).releaseLock(anyString());
    }

    @Test
    void testProcessPaymentFailed() {
        // 设置支付失败结果
        mockPaymentResult.setTxPmtFlag(TxPmtFlagEnum.PAY_FAIL.getCode());

        // 模拟获取锁成功
        when(lockService.getLock(anyString(), anyInt())).thenReturn(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            processPmtResultBusiness.process(mockPaymentOrder, mockDealOrder, mockDealOrderDtlList, mockPaymentResult);
        });

        // 验证方法调用
        verify(hwPaymentOrderRepository).updatePaymentResultWithTransaction(
                any(HwPaymentOrderPO.class), any(HwDealOrder.class), anyList(), any(PaymentResultDTO.class));
        verify(sendMqService).sendOrderUpdateMessage(any(HwDealOrder.class));
    }

    @Test
    void testProcessWithLockFailure() {
        // 模拟获取锁失败
        when(lockService.getLock(anyString(), anyInt())).thenReturn(false);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            processPmtResultBusiness.process(mockPaymentOrder, mockDealOrder, mockDealOrderDtlList, mockPaymentResult);
        });

        assertEquals(ExceptionEnum.CONCURRENT_ERROR.getCode(), exception.getExceptionCode());
        verify(lockService, never()).releaseLock(anyString());
    }

    @Test
    void testProcessWithInvalidPaymentOrderStatus() {
        // 设置无效的支付订单状态
        mockPaymentOrder.setTxPmtFlag(TxPmtFlagEnum.PAY_SUCCESS.getCode());

        // 执行测试，应该跳过处理而不抛出异常
        assertDoesNotThrow(() -> {
            processPmtResultBusiness.process(mockPaymentOrder, mockDealOrder, mockDealOrderDtlList, mockPaymentResult);
        });

        // 验证没有调用更新方法
        verify(hwPaymentOrderRepository, never()).updatePaymentResultWithTransaction(
                any(HwPaymentOrderPO.class), any(HwDealOrder.class), anyList(), any(PaymentResultDTO.class));
    }

    @Test
    void testProcessWithPaymentResultPaying() {
        // 设置支付结果为支付中
        mockPaymentResult.setTxPmtFlag(TxPmtFlagEnum.PAYING.getCode());

        // 执行测试，应该跳过处理而不抛出异常
        assertDoesNotThrow(() -> {
            processPmtResultBusiness.process(mockPaymentOrder, mockDealOrder, mockDealOrderDtlList, mockPaymentResult);
        });

        // 验证没有调用更新方法
        verify(hwPaymentOrderRepository, never()).updatePaymentResultWithTransaction(
                any(HwPaymentOrderPO.class), any(HwDealOrder.class), anyList(), any(PaymentResultDTO.class));
        // 验证没有发送消息
        verify(sendMqService, never()).sendOrderUpdateMessage(any(HwDealOrder.class));
    }

    @Test
    void testProcessWithOptimisticLockFailure() {
        // 模拟获取锁成功
        when(lockService.getLock(anyString(), anyInt())).thenReturn(true);

        // 模拟事务方法抛出异常
        doThrow(new BusinessException(ExceptionEnum.CONCURRENT_ERROR))
                .when(hwPaymentOrderRepository).updatePaymentResultWithTransaction(
                        any(HwPaymentOrderPO.class), any(HwDealOrder.class), anyList(), any(PaymentResultDTO.class));

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, () -> {
            processPmtResultBusiness.process(mockPaymentOrder, mockDealOrder, mockDealOrderDtlList, mockPaymentResult);
        });

        assertEquals(ExceptionEnum.CONCURRENT_ERROR.getCode(), exception.getExceptionCode());
        verify(lockService).releaseLock(anyString());
    }

    @Test
    void testProcessNonTradeOrder() {
        // 设置为非交易订单
        mockPaymentOrder.setOrderType(PaymentOrderTypeEnum.EDDA_DEPOSIT.getCode());

        // 模拟获取锁成功
        when(lockService.getLock(anyString(), anyInt())).thenReturn(true);

        // 执行测试
        assertDoesNotThrow(() -> {
            processPmtResultBusiness.process(mockPaymentOrder, null, null, mockPaymentResult);
        });

        // 验证调用了事务方法
        verify(hwPaymentOrderRepository).updatePaymentResultWithTransaction(
                any(HwPaymentOrderPO.class), eq(null), eq(null), any(PaymentResultDTO.class));
        // 非交易订单不发送MQ消息
        verify(sendMqService, never()).sendOrderUpdateMessage(any(HwDealOrder.class));
    }
}
