package com.howbuy.dtms.order.service.service.query.payment;

import com.howbuy.dtms.order.client.domain.request.payment.QueryPaymentCheckResultRequest;
import com.howbuy.dtms.order.client.domain.response.payment.QueryPaymentCheckResultResponse;
import com.howbuy.dtms.order.dao.dto.PaymentCheckResultDTO;
import com.howbuy.dtms.order.dao.query.PaymentCheckResultQuery;
import com.howbuy.dtms.order.service.repository.HwPaymentCheckResultRepository;
import com.howbuy.dtms.order.service.service.query.pay.QueryPaymentCheckResultService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 支付对账结果查询服务测试类
 * 
 * <AUTHOR>
 * @date 2025-07-07 17:13:51
 * @since JDK 1.8
 */
@ExtendWith(MockitoExtension.class)
class QueryPaymentCheckResultServiceTest {

    @Mock
    private HwPaymentCheckResultRepository hwPaymentCheckResultRepository;

    @InjectMocks
    private QueryPaymentCheckResultService queryPaymentCheckResultService;

    private QueryPaymentCheckResultRequest mockRequest;
    private List<PaymentCheckResultDTO> mockDtoList;

    @BeforeEach
    void setUp() {
        // 准备测试请求数据
        mockRequest = new QueryPaymentCheckResultRequest();
        mockRequest.setPmtCheckDt("20250707");
        mockRequest.setPage(1);
        mockRequest.setSize(20);
        mockRequest.setFundCodes(Arrays.asList("HK001", "HK002"));
        mockRequest.setPmtCompFlags(Arrays.asList("1", "3"));

        // 准备测试响应数据
        mockDtoList = createMockDtoList();
    }

    @Test
    void testQueryPaymentCheckResultSuccess() {
        // 模拟Repository返回数据
        when(hwPaymentCheckResultRepository.selectPaymentCheckResultPage(any(PaymentCheckResultQuery.class)))
                .thenReturn(mockDtoList);

        // 执行测试
        QueryPaymentCheckResultResponse response = queryPaymentCheckResultService.queryPaymentCheckResult(mockRequest);

        // 验证结果
        assertNotNull(response);
        assertEquals(2, response.getList().size());
        assertEquals(2L, response.getTotal());
        assertEquals(1, response.getPages());

        // 验证Repository方法被调用
        verify(hwPaymentCheckResultRepository, times(1)).selectPaymentCheckResultPage(any(PaymentCheckResultQuery.class));
    }

    @Test
    void testQueryPaymentCheckResultEmpty() {
        // 模拟Repository返回空数据
        when(hwPaymentCheckResultRepository.selectPaymentCheckResultPage(any(PaymentCheckResultQuery.class)))
                .thenReturn(new ArrayList<>());

        // 执行测试
        QueryPaymentCheckResultResponse response = queryPaymentCheckResultService.queryPaymentCheckResult(mockRequest);

        // 验证结果
        assertNotNull(response);
        assertEquals(0, response.getList().size());
        assertEquals(0L, response.getTotal());
        assertEquals(0, response.getPages());
    }

    /**
     * 创建模拟DTO列表
     */
    private List<PaymentCheckResultDTO> createMockDtoList() {
        List<PaymentCheckResultDTO> list = new ArrayList<>();

        PaymentCheckResultDTO dto1 = new PaymentCheckResultDTO();
        dto1.setPmtDealNo(123456789L);
        dto1.setDealNo(987654321L);
        dto1.setHkCustNo("HK001");
        dto1.setFundCode("HK001");
        dto1.setPmtCheckDt("20250707");
        dto1.setPmtCompFlag("1");
        dto1.setAppDtm(new Date());
        dto1.setPmtAmt(new BigDecimal("1000.00"));
        list.add(dto1);

        PaymentCheckResultDTO dto2 = new PaymentCheckResultDTO();
        dto2.setPmtDealNo(123456790L);
        dto2.setDealNo(987654322L);
        dto2.setHkCustNo("HK002");
        dto2.setFundCode("HK002");
        dto2.setPmtCheckDt("20250707");
        dto2.setPmtCompFlag("3");
        dto2.setAppDtm(new Date());
        dto2.setPmtAmt(new BigDecimal("2000.00"));
        list.add(dto2);

        return list;
    }
}
