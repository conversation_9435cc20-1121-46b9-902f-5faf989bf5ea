package com.howbuy.dtms.order.service.service.trade.payment;

import com.howbuy.dtms.order.client.domain.request.payment.PaymentCheckRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.PaymentCheckResponse;
import com.howbuy.dtms.order.service.business.payment.PaymentCheckBusiness;
import com.howbuy.dtms.order.service.service.trade.payment.PaymentCheckService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doThrow;

/**
 * @description: 支付对账服务测试类
 * <AUTHOR>
 * @date 2025-07-07
 * @since JDK 1.8
 */
@RunWith(MockitoJUnitRunner.class)
public class PaymentCheckServiceTest {

    @Mock
    private PaymentCheckBusiness paymentCheckBusiness;

    @InjectMocks
    private PaymentCheckService paymentCheckService;

    /**
     * 测试支付对账执行成功
     */
    @Test
    public void testExecuteSuccess() {
        // 准备测试数据
        PaymentCheckRequest request = new PaymentCheckRequest();
        request.setPmtCheckDt("20250707");

        // Mock业务处理
        doNothing().when(paymentCheckBusiness).paymentCheck(any(String.class), anyList());

        // 执行测试
        Response<PaymentCheckResponse> response = paymentCheckService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals("支付对账执行成功", response.getDescription());
        assertNotNull(response.getData());
    }

    /**
     * 测试支付对账执行失败
     */
    @Test
    public void testExecuteFailure() {
        // 准备测试数据
        PaymentCheckRequest request = new PaymentCheckRequest();
        request.setPmtCheckDt("20250707");

        // Mock业务处理抛出异常
        RuntimeException exception = new RuntimeException("测试异常");
        doThrow(exception).when(paymentCheckBusiness).paymentCheck(any(String.class), anyList());

        // 执行测试
        Response<PaymentCheckResponse> response = paymentCheckService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertTrue(response.getDescription().contains("支付对账执行失败"));
    }

    /**
     * 测试空日期参数
     */
    @Test
    public void testExecuteWithEmptyDate() {
        // 准备测试数据
        PaymentCheckRequest request = new PaymentCheckRequest();
        // 不设置pmtCheckDt，测试空值情况

        // 执行测试
        Response<PaymentCheckResponse> response = paymentCheckService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertTrue(response.getDescription().contains("支付对账日期不能为空"));
    }

    /**
     * 测试日期格式错误
     */
    @Test
    public void testExecuteWithInvalidDateFormat() {
        // 准备测试数据
        PaymentCheckRequest request = new PaymentCheckRequest();
        request.setPmtCheckDt("2025-07-07"); // 错误格式

        // 执行测试
        Response<PaymentCheckResponse> response = paymentCheckService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertTrue(response.getDescription().contains("日期格式错误"));
    }

    /**
     * 测试空请求对象
     */
    @Test
    public void testExecuteWithNullRequest() {
        // 执行测试
        Response<PaymentCheckResponse> response = paymentCheckService.execute(null);

        // 验证结果
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertTrue(response.getDescription().contains("请求参数不能为空"));
    }

}
