package com.howbuy.dtms.order.service.service.trade.payment;

import com.howbuy.dtms.common.enums.OrderStatusEnum;
import com.howbuy.dtms.common.enums.PayStatusEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.client.domain.request.payment.ResetTxPmtFlagRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.ResetTxPmtFlagResponse;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.order.service.outerservice.pay.PayOuterService;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description: 重置交易支付标识服务测试
 * <AUTHOR>
 * @date 2025-07-07 19:02:55
 * @since JDK 1.8
 */
@ExtendWith(MockitoExtension.class)
class ResetTxPmtFlagServiceTest {

    @Mock
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Mock
    private HwDealOrderRepository hwDealOrderRepository;

    @Mock
    private PayOuterService payOuterService;

    @InjectMocks
    private ResetTxPmtFlagService resetTxPmtFlagService;

    private ResetTxPmtFlagRequest request;
    private HwPaymentOrderPO mockPaymentOrder;
    private HwDealOrder mockDealOrder;

    @BeforeEach
    void setUp() {
        // 初始化请求参数
        request = new ResetTxPmtFlagRequest();
        request.setPmtDealNo("2023102700001");

        // 初始化模拟支付订单
        mockPaymentOrder = new HwPaymentOrderPO();
        mockPaymentOrder.setPmtDealNo(Long.valueOf("2023102700001"));
        mockPaymentOrder.setDealNo(123456789L);
        mockPaymentOrder.setOrderType(PaymentOrderTypeEnum.TRADE.getCode());
        mockPaymentOrder.setTxPmtFlag(TxPmtFlagEnum.PAYING.getCode());
        mockPaymentOrder.setHkCustNo("HK001");
        mockPaymentOrder.setFundCode("F001");
        mockPaymentOrder.setCurrency("USD");
        mockPaymentOrder.setPmtAmt(new BigDecimal("1000.00"));
        mockPaymentOrder.setUpdateTimestamp(new Date());

        // 初始化模拟交易订单
        mockDealOrder = new HwDealOrder();
        mockDealOrder.setDealNo(123456789L);
        mockDealOrder.setHkCustNo("HK001");
        mockDealOrder.setOrderStatus(OrderStatusEnum.APPLY_SUCCESS.getCode());
        mockDealOrder.setPayStatus(PayStatusEnum.PAYING.getCode()); // 付款中
        mockDealOrder.setUpdateTimestamp(new Date());
    }

    @Test
    void testExecuteSuccess() {
        // 准备测试数据
        PaymentResultDTO paymentResult = new PaymentResultDTO();
        paymentResult.setSuccess(true);

        when(hwPaymentOrderRepository.selectByPmtDealNo(anyLong())).thenReturn(mockPaymentOrder);
        when(hwDealOrderRepository.selectByDealNo(anyLong())).thenReturn(mockDealOrder);
        when(payOuterService.queryPayResult(anyString())).thenReturn(paymentResult);
        doNothing().when(hwPaymentOrderRepository).resetTxPmtFlagWithTransaction(any(), any());

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("0000", response.getCode());
        // 验证方法调用
        verify(hwPaymentOrderRepository).selectByPmtDealNo(2023102700001L);
        verify(hwDealOrderRepository).selectByDealNo(123456789L);
        verify(payOuterService).queryPayResult("2023102700001");
        verify(hwPaymentOrderRepository).resetTxPmtFlagWithTransaction(mockPaymentOrder, mockDealOrder);
    }

    @Test
    void testExecutePaymentOrderNotFound() {
        // 准备测试数据
        when(hwPaymentOrderRepository.selectByPmtDealNo(anyLong())).thenReturn(null);

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("C023075", response.getCode());
        assertEquals("支付订单不存在", response.getDescription());
    }

    @Test
    void testExecutePaymentOrderStatusIncorrect() {
        // 准备测试数据
        mockPaymentOrder.setTxPmtFlag(TxPmtFlagEnum.PAY_SUCCESS.getCode());
        when(hwPaymentOrderRepository.selectByPmtDealNo(anyLong())).thenReturn(mockPaymentOrder);

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("C023076", response.getCode());
        assertTrue(response.getDescription().contains("支付订单状态不正确"));
    }

    @Test
    void testExecuteDealOrderNotFound() {
        // 准备测试数据
        when(hwPaymentOrderRepository.selectByPmtDealNo(anyLong())).thenReturn(mockPaymentOrder);
        when(hwDealOrderRepository.selectByDealNo(anyLong())).thenReturn(null);

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("C023077", response.getCode());
        assertEquals("关联交易订单不存在", response.getDescription());
    }

    @Test
    void testExecuteExternalPaymentExists() {
        // 准备测试数据
        PaymentResultDTO paymentResult = new PaymentResultDTO();
        paymentResult.setOutPmtDealNo("EXT123456");
        paymentResult.setSuccess(true);
        
        when(hwPaymentOrderRepository.selectByPmtDealNo(anyLong())).thenReturn(mockPaymentOrder);
        when(hwDealOrderRepository.selectByDealNo(anyLong())).thenReturn(mockDealOrder);
        when(payOuterService.queryPayResult(anyString())).thenReturn(paymentResult);

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("C023079", response.getCode());
        assertEquals("支付系统已存在该订单，无法重置", response.getDescription());
    }

    @Test
    void testExecuteEmptyPmtDealNo() {
        // 准备测试数据
        request.setPmtDealNo("");

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("C020002", response.getCode());
        assertEquals("参数错误", response.getDescription());
    }

    @Test
    void testExecuteInvalidPmtDealNoFormat() {
        // 准备测试数据
        request.setPmtDealNo("P2023102700001"); // 包含字母P，无法转换为Long

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("C023080", response.getCode());
        assertEquals("支付订单号格式不正确", response.getDescription());
    }

    @Test
    void testExecuteDealOrderStatusIncorrect() {
        // 准备测试数据
        mockDealOrder.setPayStatus(PayStatusEnum.UN_PAY.getCode()); // 未付款状态，应该是"2"（付款中）
        when(hwPaymentOrderRepository.selectByPmtDealNo(anyLong())).thenReturn(mockPaymentOrder);
        when(hwDealOrderRepository.selectByDealNo(anyLong())).thenReturn(mockDealOrder);

        // 执行测试
        Response<ResetTxPmtFlagResponse> response = resetTxPmtFlagService.execute(request);

        // 验证结果
        assertNotNull(response);
        assertEquals("C023078", response.getCode());
        assertTrue(response.getDescription().contains("关联交易订单状态不正确"));
    }
}
