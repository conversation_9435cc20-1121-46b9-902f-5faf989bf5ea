/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.payment;

import com.howbuy.dtms.common.enums.OrderStatusEnum;
import com.howbuy.dtms.common.enums.PayStatusEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.outerservice.pay.PayOuterService;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentCallContext;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @description: 异步支付业务测试
 * <AUTHOR>
 * @date 2025-07-02 16:04:22
 * @since JDK 1.8
 */
@ExtendWith(MockitoExtension.class)
class AsyncDoPaymentBusinessTest {

    @Mock
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Mock
    private HwDealOrderRepository hwDealOrderRepository;

    @Mock
    private PayOuterService payOuterService;

    @InjectMocks
    private AsyncDoPaymentBusiness asyncDoPaymentBusiness;

    private HwPaymentOrderPO mockPaymentOrder;
    private HwDealOrder mockDealOrder;
    private PaymentResultDTO mockPaymentResult;

    @BeforeEach
    void setUp() {
        // 初始化模拟支付订单
        mockPaymentOrder = new HwPaymentOrderPO();
        mockPaymentOrder.setPmtDealNo(123456789L);
        mockPaymentOrder.setDealNo(987654321L);
        mockPaymentOrder.setOrderType(PaymentOrderTypeEnum.TRADE.getCode());
        mockPaymentOrder.setTxPmtFlag(TxPmtFlagEnum.UN_PAY.getCode());
        mockPaymentOrder.setHkCustNo("HK001");
        mockPaymentOrder.setFundCode("F001");
        mockPaymentOrder.setCurrency("USD");
        mockPaymentOrder.setPmtAmt(new BigDecimal("1000.00"));
        mockPaymentOrder.setUpdateTimestamp(new Date());

        // 初始化模拟交易订单
        mockDealOrder = new HwDealOrder();
        mockDealOrder.setDealNo(987654321L);
        mockDealOrder.setOrderStatus(OrderStatusEnum.APPLY_SUCCESS.getCode());
        mockDealOrder.setPayStatus(PayStatusEnum.UN_PAY.getCode());
        mockDealOrder.setUpdateTimestamp(new Date());

        // 初始化模拟支付结果
        mockPaymentResult = new PaymentResultDTO();
        mockPaymentResult.setRetCode("0000");
        mockPaymentResult.setRetDesc("支付成功");
        mockPaymentResult.setOutPmtDealNo("OUT123456789");
    }

    @Test
    void testProcessSuccess() {
        // 准备测试数据
        String pmtDealNo = "123456789";

        // 模拟方法调用
        when(hwPaymentOrderRepository.selectByPmtDealNo(Long.valueOf(pmtDealNo))).thenReturn(mockPaymentOrder);
        when(hwDealOrderRepository.selectByDealNo(987654321L)).thenReturn(mockDealOrder);
        doNothing().when(hwPaymentOrderRepository).updateToPayingStatusWithTransaction(any(HwPaymentOrderPO.class), any(HwDealOrder.class));
        when(payOuterService.callPayment(any(PaymentCallContext.class))).thenReturn(mockPaymentResult);
        when(hwPaymentOrderRepository.updatePaymentResultWithOptimisticLock(anyLong(), anyString(), anyString(), anyString())).thenReturn(1);

        // 执行测试
        assertDoesNotThrow(() -> asyncDoPaymentBusiness.process(pmtDealNo));

        // 验证方法调用
        verify(hwPaymentOrderRepository).selectByPmtDealNo(Long.valueOf(pmtDealNo));
        verify(hwDealOrderRepository).selectByDealNo(987654321L);
        verify(hwPaymentOrderRepository).updateToPayingStatusWithTransaction(any(HwPaymentOrderPO.class), any(HwDealOrder.class));
        verify(payOuterService).callPayment(any(PaymentCallContext.class));
        verify(hwPaymentOrderRepository).updatePaymentResultWithOptimisticLock(anyLong(), anyString(), anyString(), anyString());
    }

    @Test
    void testProcessWithNullPmtDealNo() {
        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> asyncDoPaymentBusiness.process(null));
        
        assertEquals("支付订单号不能为空", exception.getExceptionDesc());
    }

    @Test
    void testProcessWithPaymentOrderNotFound() {
        // 准备测试数据
        String pmtDealNo = "123456789";

        // 模拟方法调用
        when(hwPaymentOrderRepository.selectByPmtDealNo(Long.valueOf(pmtDealNo))).thenReturn(null);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> asyncDoPaymentBusiness.process(pmtDealNo));
        
        assertEquals("支付订单不存在", exception.getExceptionDesc());
    }

    @Test
    void testProcessWithInvalidPaymentOrderStatus() {
        // 准备测试数据
        String pmtDealNo = "123456789";
        mockPaymentOrder.setTxPmtFlag(TxPmtFlagEnum.PAY_SUCCESS.getCode()); // 设置为已支付状态

        // 模拟方法调用
        when(hwPaymentOrderRepository.selectByPmtDealNo(Long.valueOf(pmtDealNo))).thenReturn(mockPaymentOrder);

        // 执行测试并验证异常
        BusinessException exception = assertThrows(BusinessException.class, 
                () -> asyncDoPaymentBusiness.process(pmtDealNo));
        
        assertEquals("支付订单状态不正确", exception.getExceptionDesc());
    }
}
