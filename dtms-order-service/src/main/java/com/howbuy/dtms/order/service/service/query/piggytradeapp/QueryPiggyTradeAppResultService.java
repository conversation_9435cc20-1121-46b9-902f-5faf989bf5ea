/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.service.query.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.piggytradeapp.QueryPiggyTradeAppResultRequest;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.QueryPiggyTradeAppResultResponse;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.PiggyTradeAppResultVO;
import com.howbuy.dtms.order.dao.po.HwPiggyTradeAppImport;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.ValidateException;
import com.howbuy.dtms.order.service.repository.HwPiggyTradeAppImportRepository;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 查询储蓄罐交易申请结果服务
 * <AUTHOR>
 * @date 2025-07-15 19:32:38
 * @since JDK 1.8
 */
@Slf4j
@Service
public class QueryPiggyTradeAppResultService {

    @Resource
    private HwPiggyTradeAppImportRepository hwPiggyTradeAppImportRepository;

    /**
     * @description: 根据导入申请Id列表查询储蓄罐交易申请结果信息
     * @param request 查询请求参数
     * @return QueryPiggyTradeAppResultResponse 查询结果
     * <AUTHOR>
     * @date 2025-07-15 19:32:38
     * @since JDK 1.8
     */
    public QueryPiggyTradeAppResultResponse queryListByImportAppId(QueryPiggyTradeAppResultRequest request) {
        // 参数校验
        ParamsValidator.validate(request, "importAppIds");
        
        if (CollectionUtils.isEmpty(request.getImportAppIds())) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "导入申请Id列表不能为空");
        }
        
        if (request.getImportAppIds().size() > 200) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "导入申请Id列表最大支持200个");
        }

        log.info("查询储蓄罐交易申请结果信息，导入申请Id列表：{}", request.getImportAppIds());

        // 查询数据库
        List<HwPiggyTradeAppImport> importList = hwPiggyTradeAppImportRepository.queryByImportAppIds(request.getImportAppIds());
        
        // 构建响应结果
        QueryPiggyTradeAppResultResponse response = new QueryPiggyTradeAppResultResponse();
        if (CollectionUtils.isEmpty(importList)) {
            response.setList(new ArrayList<>());
            return response;
        }

        // 转换为VO对象
        List<PiggyTradeAppResultVO> voList = importList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
        
        response.setList(voList);
        
        log.info("查询储蓄罐交易申请结果信息完成，返回记录数：{}", voList.size());
        
        return response;
    }

    /**
     * @description: 将PO对象转换为VO对象
     * @param po 数据库实体对象
     * @return PiggyTradeAppResultVO 视图对象
     * <AUTHOR>
     * @date 2025-07-15 19:32:38
     * @since JDK 1.8
     */
    private PiggyTradeAppResultVO convertToVO(HwPiggyTradeAppImport po) {
        PiggyTradeAppResultVO vo = new PiggyTradeAppResultVO();
        vo.setImportAppId(po.getImportAppId());
        vo.setIsGenerated(po.getIsGenerated());
        vo.setDealNo(po.getDealNo());
        
        // 金额字段转换为字符串
        if (po.getBuyAmt() != null) {
            vo.setBuyAmt(po.getBuyAmt().toString());
        }
        if (po.getFee() != null) {
            vo.setFee(po.getFee().toString());
        }
        
        vo.setRemark(po.getRemark());
        
        return vo;
    }
}
