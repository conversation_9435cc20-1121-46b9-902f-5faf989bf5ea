/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/03/19 20:00
 * @since JDK 1.8
 */
@Getter
@Setter
public class CustFeeDiscountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 费用类别 1-购买手续费、2-管理费计提、3-业绩报酬计提、4-管理费实收、5-业绩报酬实收
     */
    private String feeType;
    /**
     * 基金代码
     */
    private String fundCode;
    /**
     * 香港客户号
     */
    private String hkCustNo;
    /**
     * 折扣率
     */
    private BigDecimal discountRate;
    /**
     * 活动开始日期
     */
    private String startDt;
    /**
     * 活动结束日期
     */
    private String endDt;
} 