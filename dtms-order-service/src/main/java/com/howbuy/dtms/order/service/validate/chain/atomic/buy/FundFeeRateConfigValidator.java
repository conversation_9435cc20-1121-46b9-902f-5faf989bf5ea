/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.validate.chain.atomic.buy;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.common.enums.YesOrNoEnum;
import com.howbuy.dtms.order.service.commom.annotaion.TradeValidatorAnnotation;
import com.howbuy.dtms.order.service.commom.constant.Constants;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.enums.TradeValidatorEnum;
import com.howbuy.dtms.order.service.commom.exception.ValidateException;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.QueryFundInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.FundBasicInfoDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.FundFeeRateDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.QueryFundFeeRateRequestDTO;
import com.howbuy.dtms.order.service.validate.chain.TradeValidator;
import com.howbuy.dtms.order.service.validate.context.TradeContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (首次实缴比例校验)
 * @date 2025/4/7 15:22
 * @since JDK 1.8
 */
@Slf4j
@Service
@TradeValidatorAnnotation(TradeValidatorEnum.FUND_FEE_RATE_CONFIG)
public class FundFeeRateConfigValidator implements TradeValidator {


    @Resource
    private QueryFundInfoOuterService queryFundInfoOuterService;

    @Override
    public void validate(TradeContext context) {
        try {
            // 如果是分次Call产品,校验认缴和实缴费率
            FundBasicInfoDTO fundBasicInfoDTO = context.getFundBasicInfoDTO();
            if (YesOrNoEnum.YES.getValue().equals(fundBasicInfoDTO.getGradationCall())) {
                checkGradationCallFeeRateConfig(context);
                return;
            }


            List<FundFeeRateDTO> fundFeeRateDTOS = getFundFeeRateDTO(context, context.getProductBusinessCode());
            if (CollectionUtils.isEmpty(fundFeeRateDTOS)) {
                throw new ValidateException(ExceptionEnum.TRADE_RATE_NOT_CONFIGURED);
            }
            // 不存在一条 大于等于的费率
            boolean checked = checkFundFeeRateDTO(fundFeeRateDTOS);
            if(!checked){
                throw new ValidateException(ExceptionEnum.TRADE_RATE_NOT_CONFIGURED);
            }
        } catch (Exception e) {
            throw new ValidateException(ExceptionEnum.TRADE_RATE_NOT_CONFIGURED);
        }
    }

    /**
     * @description: 检查分次CALL  产品费率配置
     * @param context
     * @return void
     * @author: jinqing.rao
     * @date: 2025/5/28 18:08
     * @since JDK 1.8
     */
    private void checkGradationCallFeeRateConfig(TradeContext context) {
        // 认缴手续费
        List<FundFeeRateDTO> subFundFeeRateDTOS = getFundFeeRateDTO(context, BusinessCodeEnum._112A.getCode());
        // 实缴手续费
        List<FundFeeRateDTO> paidFundFeeRateDTOS = getFundFeeRateDTO(context, BusinessCodeEnum._112B.getCode());
        if (CollectionUtils.isEmpty(subFundFeeRateDTOS) && CollectionUtils.isEmpty(paidFundFeeRateDTOS)) {
            throw new ValidateException(ExceptionEnum.TRADE_RATE_NOT_CONFIGURED);
        }
        boolean subFeeRateConfigured = false;
        boolean paidFeeRateConfigured = false;

        if (CollectionUtils.isNotEmpty(subFundFeeRateDTOS)) {
            subFeeRateConfigured = checkFundFeeRateDTO(subFundFeeRateDTOS);
        }
        if (CollectionUtils.isNotEmpty(paidFundFeeRateDTOS)) {
            paidFeeRateConfigured = checkFundFeeRateDTO(paidFundFeeRateDTOS);

        }
        if (!subFeeRateConfigured && !paidFeeRateConfigured) {
            throw new ValidateException(ExceptionEnum.TRADE_RATE_NOT_CONFIGURED);
        }
    }

    /**
     * @param fundFeeRateDTOS
     * @return void
     * @description: 检查大于等于0 的费率
     * @author: jinqing.rao
     * @date: 2025/5/28 14:20
     * @since JDK 1.8
     */
    private static boolean checkFundFeeRateDTO(List<FundFeeRateDTO> fundFeeRateDTOS) {
        return fundFeeRateDTOS.stream()
                .anyMatch(fundFeeRateDTO -> fundFeeRateDTO.getFeeRate().compareTo(BigDecimal.ZERO) >= 0);
    }

    /**
     * 查询费率
     *
     * @param context
     * @param busiCode
     * @return
     */
    private List<FundFeeRateDTO> getFundFeeRateDTO(TradeContext context, String busiCode) {
        QueryFundFeeRateRequestDTO requestDTO = new QueryFundFeeRateRequestDTO();
        requestDTO.setFundCode(context.getFundCode());
        requestDTO.setBusiCode(busiCode);
        requestDTO.setInvstType(context.getHkCustInfoDTO().getInvstType());
        requestDTO.setCollectRecipient(Constants.COLLECT_RECIPIENT_HM);
        return queryFundInfoOuterService.queryFundFeeRate(requestDTO);
    }
}
