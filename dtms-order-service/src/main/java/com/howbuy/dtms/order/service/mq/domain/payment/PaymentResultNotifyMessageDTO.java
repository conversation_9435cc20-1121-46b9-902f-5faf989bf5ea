/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.mq.domain.payment;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 支付结果通知消息DTO
 * @author: shaoyang.li
 * @date: 2025-07-04 14:14:52
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentResultNotifyMessageDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 支付订单号
     */
    private String pmtDealNo;
    
    /**
     * 交易支付状态
     */
    private String txPmtFlag;
    
    /**
     * 支付完成状态
     */
    private String pmtCompFlag;
    
    /**
     * 返回码
     */
    private String retCode;
    
    /**
     * 返回描述
     */
    private String retDesc;
    
    /**
     * 外部支付流水号
     */
    private String outPmtDealNo;
    
    /**
     * 支付对账日期
     */
    private String pmtCheckDt;
    
    /**
     * 订单类型
     */
    private String orderType;
    
    /**
     * 币种（遵循ISO 4217标准）
     */
    private String currency;
    
    /**
     * 支付金额
     */
    private String pmtAmt;
} 