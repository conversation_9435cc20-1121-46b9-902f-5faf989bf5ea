/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.payment;

import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import com.howbuy.dtms.order.dao.po.HwFundTxAcctPO;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.service.business.sendmessage.SendMessageService;
import com.howbuy.dtms.order.service.business.sendmq.SendMqService;
import com.howbuy.dtms.order.service.cacheservice.CacheKeyPrefix;
import com.howbuy.dtms.order.service.cacheservice.lock.LockService;
import com.howbuy.dtms.order.service.commom.constant.Constants;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.DealDtlMergeUtil;
import com.howbuy.dtms.order.service.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.hkacc.domain.HkCustInfoDTO;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwFundTxAcctRepository;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 支付结果处理业务类
 * @date 2025-07-03 16:02:08
 * @since JDK 1.8
 */
@Component
@Slf4j
public class ProcessPmtResultBusiness {

    /**
     * 分布式锁默认过期时间（秒）
     */
    private static final int DEFAULT_LOCK_EXPIRE_SECONDS = 300;

    /**
     * 支付结果处理锁前缀
     */
    private static final String PAYMENT_RESULT_LOCK_PREFIX = "payment_result_";

    @Resource
    private LockService lockService;

    @Resource
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Resource
    private SendMqService sendMqService;

    @Resource
    private SendMessageService sendMessageService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    private HwFundTxAcctRepository hwFundTxAcctRepository;

    /**
     * @param hwPaymentOrder     支付订单实体
     * @param hwDealOrder        交易订单实体
     * @param hwDealOrderDtlList 交易订单明细列表
     * @param paymentResult      支付结果数据
     * @description: 处理支付结果
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    public void process(HwPaymentOrderPO hwPaymentOrder, HwDealOrder hwDealOrder,
                        List<HwDealOrderDtl> hwDealOrderDtlList, PaymentResultDTO paymentResult) {
        log.info("开始处理支付结果，支付订单号：{}，交易订单号：{}，支付结果：{}",
                hwPaymentOrder.getPmtDealNo(),
                hwDealOrder != null ? hwDealOrder.getDealNo() : null,
                paymentResult.getTxPmtFlag());

        // 参数校验
        if (!validateProcessParams(hwPaymentOrder, paymentResult)) {
            log.info("参数校验不通过，跳过处理，支付订单号：{}", hwPaymentOrder.getPmtDealNo());
            return;
        }

        String lockKey = buildLockKey(hwPaymentOrder.getPmtDealNo().toString());

        // 获取分布式锁
        if (!lockService.getLock(lockKey, DEFAULT_LOCK_EXPIRE_SECONDS)) {
            log.warn("获取支付结果处理锁失败，支付订单号：{}，可能存在并发处理", hwPaymentOrder.getPmtDealNo());
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        try {
            // 执行支付结果处理业务逻辑
            executePaymentResultProcess(hwPaymentOrder, hwDealOrder, hwDealOrderDtlList, paymentResult);

            log.info("支付结果处理完成，支付订单号：{}", hwPaymentOrder.getPmtDealNo());
        } catch (BusinessException e) {
            log.error("支付结果处理业务异常，支付订单号：{}，异常信息：{}",
                    hwPaymentOrder.getPmtDealNo(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("支付结果处理系统异常，支付订单号：{}，异常信息：{}",
                    hwPaymentOrder.getPmtDealNo(), e.getMessage(), e);
            AlertLogUtil.alert(ProcessPmtResultBusiness.class.getName(),
                    String.format("支付结果处理系统异常，支付订单号：%s，异常：%s",
                            hwPaymentOrder.getPmtDealNo(), e.getMessage()));
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR);
        } finally {
            // 释放分布式锁
            lockService.releaseLock(lockKey);
            log.info("释放支付结果处理锁，支付订单号：{}", hwPaymentOrder.getPmtDealNo());
        }
    }

    /**
     * @param hwPaymentOrder     支付订单实体
     * @param hwDealOrder        交易订单实体
     * @param hwDealOrderDtlList 交易订单明细列表
     * @param paymentResult      支付结果数据
     * @description: 执行支付结果处理业务逻辑
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void executePaymentResultProcess(HwPaymentOrderPO hwPaymentOrder, HwDealOrder hwDealOrder,
                                             List<HwDealOrderDtl> hwDealOrderDtlList, PaymentResultDTO paymentResult) {

        // 1. 在事务中更新所有相关订单状态（由HwPaymentOrderRepository控制事务）
        hwPaymentOrderRepository.updatePaymentResultWithTransaction(
                hwPaymentOrder, hwDealOrder, hwDealOrderDtlList, paymentResult);

        // 2. 发送消息通知
        sendNotificationMessages(hwPaymentOrder, hwDealOrder, hwDealOrderDtlList, paymentResult);
    }


    /**
     * @param hwPaymentOrder 支付订单实体
     * @param hwDealOrder    交易订单实体
     * @param paymentResult  支付结果数据
     * @description: 发送通知消息
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void sendNotificationMessages(HwPaymentOrderPO hwPaymentOrder, HwDealOrder hwDealOrder,
                                          List<HwDealOrderDtl> hwDealOrderDtlList, PaymentResultDTO paymentResult) {
        try {
            // 发送订单状态变更MQ消息（仅交易订单）
            if (isTradeOrder(hwPaymentOrder.getOrderType()) && hwDealOrder != null) {

                log.info("发送订单状态变更MQ消息，订单号：{}", hwDealOrder.getDealNo());
                sendMqService.sendOrderUpdateMessage(hwDealOrder);

                // 发送打款确认消息（仅交易订单且支付成功）
                if (isPaymentSuccess(paymentResult.getTxPmtFlag())) {
                    log.info("发送打款确认消息，订单号：{}", hwDealOrder.getDealNo());
                    sendConfirmFundsReceivedMessage(hwDealOrder, hwDealOrderDtlList);
                }
            }

        } catch (Exception e) {
            // 消息发送失败不影响主业务流程，记录错误日志
            log.error("发送通知消息失败，支付订单号：{}，异常信息：{}",
                    hwPaymentOrder.getPmtDealNo(), e.getMessage(), e);
            AlertLogUtil.alert(ProcessPmtResultBusiness.class.getName(),
                    String.format("发送通知消息失败，支付订单号：%s，异常：%s",
                            hwPaymentOrder.getPmtDealNo(), e.getMessage()));
        }
    }

    /**
     * @param hwPaymentOrder 支付订单实体
     * @param paymentResult  支付结果数据
     * @return 是否需要处理（支付订单状态为付款中时才处理）
     * @description: 参数校验
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private boolean validateProcessParams(HwPaymentOrderPO hwPaymentOrder, PaymentResultDTO paymentResult) {
        if (hwPaymentOrder == null) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付订单不能为空");
        }

        if (hwPaymentOrder.getPmtDealNo() == null) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付订单号不能为空");
        }

        if (paymentResult == null) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付结果不能为空");
        }

        if (StringUtils.isBlank(paymentResult.getTxPmtFlag())) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付状态标志不能为空");
        }

        // 校验支付订单前置状态，不等于付款中的不处理（支付系统可能重复通知）
        if (!TxPmtFlagEnum.PAYING.getCode().equals(hwPaymentOrder.getTxPmtFlag())) {
            log.info("支付订单状态不是付款中，跳过处理，支付订单号：{}，当前状态：{}，期望状态：{}",
                    hwPaymentOrder.getPmtDealNo(), TxPmtFlagEnum.getDescByCode(hwPaymentOrder.getTxPmtFlag()), TxPmtFlagEnum.PAYING.getDesc());
            return false;
        }

        // 校验支付结果状态，如果返回支付中，则不处理
        if (TxPmtFlagEnum.PAYING.getCode().equals(paymentResult.getTxPmtFlag())) {
            log.info("支付结果状态为支付中，跳过处理，支付订单号：{}，支付结果状态：{}",
                    hwPaymentOrder.getPmtDealNo(), TxPmtFlagEnum.PAYING.getDesc());
            return false;
        }

        return true;
    }

    /**
     * @param pmtDealNo 支付订单号
     * @return 锁Key
     * @description: 构建分布式锁Key
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private String buildLockKey(String pmtDealNo) {
        return CacheKeyPrefix.LOCK_KEY_PREFIX + PAYMENT_RESULT_LOCK_PREFIX + pmtDealNo;
    }

    /**
     * @param orderType 订单类型
     * @return 是否为交易订单
     * @description: 判断是否为交易订单
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private boolean isTradeOrder(String orderType) {
        return PaymentOrderTypeEnum.TRADE.getCode().equals(orderType);
    }

    /**
     * @param txPmtFlag 支付状态标志
     * @return 是否支付成功
     * @description: 判断是否支付成功
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private boolean isPaymentSuccess(String txPmtFlag) {
        return TxPmtFlagEnum.PAY_SUCCESS.getCode().equals(txPmtFlag);
    }

    /**
     * @param txPmtFlag 支付状态标志
     * @return 是否支付失败
     * @description: 判断是否支付失败
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private boolean isPaymentFailed(String txPmtFlag) {
        return TxPmtFlagEnum.PAY_FAIL.getCode().equals(txPmtFlag);
    }

    /**
     * @param hwDealOrder        交易订单实体
     * @param hwDealOrderDtlList 交易订单明细列表
     * @description: 发送打款确认消息
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void sendConfirmFundsReceivedMessage(HwDealOrder hwDealOrder, List<HwDealOrderDtl> hwDealOrderDtlList) {
        try {
            // 准备基础数据
            HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hwDealOrder.getHkCustNo());

            if (CollectionUtils.isEmpty(hwDealOrderDtlList)
                    || StringUtils.isEmpty(hwDealOrderDtlList.get(0).getFundTxAcctNo())
                    || Objects.isNull(hkCustInfo)) {
                log.error("发送打款确认消息异常，订单明细或客户信息异常，订单号：{}", hwDealOrder.getDealNo());
                AlertLogUtil.alert(ProcessPmtResultBusiness.class.getName(),
                        String.format("发送打款确认消息异常，订单明细或客户信息异常:deal_no=%s", hwDealOrder.getDealNo()));
                return;
            }

            // 计算预估手续费
            BigDecimal estimateFee = hwDealOrderDtlList.stream()
                    .filter(it -> Objects.nonNull(it.getEstimateFee()))
                    .map(HwDealOrderDtl::getEstimateFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 净申请金额（转换为万元单位）
            String netAppAmt = DealDtlMergeUtil.mergeNetAppAmt(hwDealOrderDtlList)
                    .divide(Constants.AMOUNT_TEN_THOUSAND_UNITS, Constants.AMOUNT_SCALE, BigDecimal.ROUND_HALF_UP)
                    .toEngineeringString();

            // 发送投顾到账确认消息
            try {
                log.info("发送投顾到账确认消息，订单号：{}，客户：{}，净申请金额：{}万，预估手续费：{}元",
                        hwDealOrder.getDealNo(), hkCustInfo.getHkCustNo(), netAppAmt, estimateFee.toEngineeringString());
                sendMessageService.sendConfirmFundReceivedNotifyToAdvisor(hwDealOrder, hkCustInfo, estimateFee, netAppAmt);
            } catch (Exception e) {
                log.error("发送投顾到账确认消息失败，订单号：{}，异常信息：{}", hwDealOrder.getDealNo(), e.getMessage(), e);
                AlertLogUtil.alert(ProcessPmtResultBusiness.class.getName(),
                        String.format("发送投顾到账确认消息异常:deal_no=%s", hwDealOrder.getDealNo()));
            }

            // 获取基金交易账号类型
            HwFundTxAcctPO hwFundTxAcctPO = hwFundTxAcctRepository.selectByFundTxAcctNo(hwDealOrderDtlList.get(0).getFundTxAcctNo());
            if (Objects.isNull(hwFundTxAcctPO)) {
                log.error("发送打款确认消息异常，基金交易账号不存在，订单号：{}，基金交易账号：{}",
                        hwDealOrder.getDealNo(), hwDealOrderDtlList.get(0).getFundTxAcctNo());
                AlertLogUtil.alert(ProcessPmtResultBusiness.class.getName(),
                        String.format("发送打款确认消息异常，基金交易账号不存在:deal_no=%s", hwDealOrder.getDealNo()));
                return;
            }

            // 非全委基金交易账号则发送客户到账确认消息
            if (FundTxAcctTypeEnum.NON_FULL.getCode().equals(hwFundTxAcctPO.getFundTxAccType())) {
                try {
                    log.info("发送客户到账确认消息，订单号：{}，客户：{}，基金交易账号类型：非全委",
                            hwDealOrder.getDealNo(), hkCustInfo.getHkCustNo());
                    sendMessageService.sendConfirmFundReceivedNotify(hwDealOrder, hkCustInfo);
                } catch (Exception e) {
                    log.error("发送客户到账确认消息失败，订单号：{}，异常信息：{}", hwDealOrder.getDealNo(), e.getMessage(), e);
                    AlertLogUtil.alert(ProcessPmtResultBusiness.class.getName(),
                            String.format("发送客户到账确认消息异常:deal_no=%s", hwDealOrder.getDealNo()));
                }
            } else {
                log.info("基金交易账号为全委类型，跳过发送客户到账确认消息，订单号：{}，账号类型：{}",
                        hwDealOrder.getDealNo(), hwFundTxAcctPO.getFundTxAccType());
            }

        } catch (Exception e) {
            log.error("发送打款确认消息系统异常，订单号：{}，异常信息：{}", hwDealOrder.getDealNo(), e.getMessage(), e);
            AlertLogUtil.alert(ProcessPmtResultBusiness.class.getName(),
                    String.format("发送打款确认消息系统异常:deal_no=%s，异常：%s", hwDealOrder.getDealNo(), e.getMessage()));
        }
    }
}
