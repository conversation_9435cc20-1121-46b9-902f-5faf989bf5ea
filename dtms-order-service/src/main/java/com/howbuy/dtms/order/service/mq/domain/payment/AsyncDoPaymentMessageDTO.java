/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.mq.domain.payment;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: 异步支付处理消息DTO
 * @author: shaoyang.li
 * @date: 2025-07-03 11:07:45
 * @since JDK 1.8
 */
@Getter
@Setter
public class AsyncDoPaymentMessageDTO implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 支付交易号
     */
    private String pmtDealNo;
}
