/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.capitalrefund;

import com.google.common.collect.Lists;
import com.howbuy.dtms.common.enums.NewRecStatEnum;
import com.howbuy.dtms.common.enums.YesOrNoEnum;
import com.howbuy.dtms.order.client.domain.response.PageVo;
import com.howbuy.dtms.order.dao.bo.CapitalRefundOrderBO;
import com.howbuy.dtms.order.dao.po.HwCapitalRefundPO;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;

import com.howbuy.dtms.order.service.commom.utils.DateUtils;
import com.howbuy.dtms.order.service.commom.utils.DealDtlMergeUtil;
import com.howbuy.dtms.order.service.repository.HwCapitalRefundRepository;
import com.howbuy.dtms.order.service.repository.HwDealOrderDtlRepository;

import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 资金退款数据处理器
 * <AUTHOR>
 * @date 2025-07-10 13:47:32
 * @since JDK 1.8
 */
@Slf4j
@Component
public class CapitalRefundDataProcessor {

    @Resource
    private HwCapitalRefundRepository hwCapitalRefundRepository;

    @Resource
    private HwDealOrderDtlRepository hwDealOrderDtlRepository;





    /**
     * 退款方向：现金余额
     */
    private static final String REFUND_DIRECTION_CASH = "2";



    /**
     * 查询时间窗口：10天前
     */
    private static final int QUERY_DAYS_BEFORE = 10;

    /**
     * 处理资金退款数据生成
     * 
     * @return 生成的退款记录数量
     */
    public int processCapitalRefundData() {
        log.info("开始处理资金退款数据生成");
        
        List<HwCapitalRefundPO> allRefundList = new ArrayList<>();
        
        // 处理付款成功后已撤单的退款数据
        List<HwCapitalRefundPO> paidCancelledRefunds = processPaidCancelledOrders();
        if (CollectionUtils.isNotEmpty(paidCancelledRefunds)) {
            allRefundList.addAll(paidCancelledRefunds);
            log.info("付款成功后已撤单的退款数据处理完成，生成退款记录：{} 条", paidCancelledRefunds.size());
        }
        
        // 处理储蓄罐赎回成功且原关联订单支付失败的退款数据
        List<HwCapitalRefundPO> piggyRedeemRefunds = processPiggyRedeemOrders();
        if (CollectionUtils.isNotEmpty(piggyRedeemRefunds)) {
            allRefundList.addAll(piggyRedeemRefunds);
            log.info("储蓄罐赎回成功且原关联订单支付失败的退款数据处理完成，生成退款记录：{} 条", piggyRedeemRefunds.size());
        }
        
        // 批量插入退款记录
        int totalInserted = 0;
        if (CollectionUtils.isNotEmpty(allRefundList)) {
            totalInserted = batchInsertRefundRecords(allRefundList);
            log.info("资金退款数据生成完成，共生成退款记录：{} 条", totalInserted);
        } else {
            log.info("未找到需要生成退款记录的订单数据");
        }
        
        return totalInserted;
    }

    /**
     * 处理支付成功撤单的退款数据
     *
     * @return 退款记录列表
     */
    private List<HwCapitalRefundPO> processPaidCancelledOrders() {
        log.info("开始处理支付成功撤单的退款数据");

        Date updateTimeStart = getUpdateTimeStart();
        List<HwCapitalRefundPO> refundList = new ArrayList<>();

        int page = 1;
        int size = 200;
        int totalProcessed = 0;

        while (true) {
            PageVo<CapitalRefundOrderBO> pageResult = hwCapitalRefundRepository.selectPaidCancelRefundOrdersPage(updateTimeStart, page, size);

            if (CollectionUtils.isEmpty(pageResult.getList())) {
                break;
            }

            List<HwCapitalRefundPO> batchRefunds = buildRefundRecordsForPaidCancelled(pageResult.getList());
            if (CollectionUtils.isNotEmpty(batchRefunds)) {
                refundList.addAll(batchRefunds);
            }

            totalProcessed += pageResult.getList().size();

            // 如果当前页数据少于每页大小，说明已经是最后一页
            if (pageResult.getList().size() < size) {
                break;
            }

            page++;
        }

        log.info("支付成功撤单的退款数据处理完成，处理订单数：{}，生成退款记录数：{}",
                totalProcessed, refundList.size());

        return refundList;
    }

    /**
     * 处理储蓄罐赎回成功退款数据
     *
     * @return 退款记录列表
     */
    private List<HwCapitalRefundPO> processPiggyRedeemOrders() {
        log.info("开始处理储蓄罐赎回成功退款数据");

        Date updateTimeStart = getUpdateTimeStart();
        List<HwCapitalRefundPO> refundList = new ArrayList<>();

        int page = 1;
        int size = 200;
        int totalProcessed = 0;

        while (true) {
            PageVo<CapitalRefundOrderBO> pageResult = hwCapitalRefundRepository.selectPiggyRedeemRefundOrdersPage(updateTimeStart, page, size);

            if (CollectionUtils.isEmpty(pageResult.getList())) {
                break;
            }

            List<HwCapitalRefundPO> batchRefunds = buildRefundRecordsForPiggyRedeem(pageResult.getList());
            if (CollectionUtils.isNotEmpty(batchRefunds)) {
                refundList.addAll(batchRefunds);
            }

            totalProcessed += pageResult.getList().size();

            // 如果当前页数据少于每页大小，说明已经是最后一页
            if (pageResult.getList().size() < size) {
                break;
            }

            page++;
        }

        log.info("储蓄罐赎回成功退款数据处理完成，处理订单数：{}，生成退款记录数：{}",
                totalProcessed, refundList.size());

        return refundList;
    }

    /**
     * 获取查询开始时间（10天前）
     *
     * @return 查询开始时间
     */
    private Date getUpdateTimeStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -QUERY_DAYS_BEFORE);
        return calendar.getTime();
    }

    /**
     * 为付款成功后已撤单的订单构建退款记录
     * 
     * @param orderList 订单列表
     * @return 退款记录列表
     */
    private List<HwCapitalRefundPO> buildRefundRecordsForPaidCancelled(List<CapitalRefundOrderBO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        
        List<HwCapitalRefundPO> refundList = new ArrayList<>();
        
        // 按订单号分批查询订单明细
        List<Long> dealNoList = orderList.stream()
                .map(CapitalRefundOrderBO::getDealNo)
                .collect(Collectors.toList());
        
        List<List<Long>> partitionedDealNos = Lists.partition(dealNoList, 200);
        
        for (List<Long> batchDealNos : partitionedDealNos) {
            List<HwDealOrderDtl> orderDtlList = hwDealOrderDtlRepository.selectHwDtlByDealNoList(batchDealNos);
            
            // 按订单号分组
            Map<Long, List<HwDealOrderDtl>> dtlGroupMap = orderDtlList.stream()
                    .collect(Collectors.groupingBy(HwDealOrderDtl::getDealNo));
            
            // 构建退款记录
            for (CapitalRefundOrderBO order : orderList) {
                if (batchDealNos.contains(order.getDealNo())) {
                    List<HwDealOrderDtl> dtlList = dtlGroupMap.get(order.getDealNo());
                    if (CollectionUtils.isNotEmpty(dtlList)) {
                        HwCapitalRefundPO refund = buildRefundRecord(order, dtlList, true);
                        refundList.add(refund);
                    }
                }
            }
        }
        
        return refundList;
    }

    /**
     * 为储蓄罐赎回成功且原关联订单支付失败的订单构建退款记录
     * 
     * @param orderList 订单列表
     * @return 退款记录列表
     */
    private List<HwCapitalRefundPO> buildRefundRecordsForPiggyRedeem(List<CapitalRefundOrderBO> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return Collections.emptyList();
        }
        
        List<HwCapitalRefundPO> refundList = new ArrayList<>();
        
        // 按订单号分批查询订单明细
        List<Long> dealNoList = orderList.stream()
                .map(CapitalRefundOrderBO::getDealNo)
                .collect(Collectors.toList());
        
        List<List<Long>> partitionedDealNos = Lists.partition(dealNoList, 200);
        
        for (List<Long> batchDealNos : partitionedDealNos) {
            List<HwDealOrderDtl> orderDtlList = hwDealOrderDtlRepository.selectHwDtlByDealNoList(batchDealNos);
            
            // 按订单号分组
            Map<Long, List<HwDealOrderDtl>> dtlGroupMap = orderDtlList.stream()
                    .collect(Collectors.groupingBy(HwDealOrderDtl::getDealNo));
            
            // 构建退款记录
            for (CapitalRefundOrderBO order : orderList) {
                if (batchDealNos.contains(order.getDealNo())) {
                    List<HwDealOrderDtl> dtlList = dtlGroupMap.get(order.getDealNo());
                    if (CollectionUtils.isNotEmpty(dtlList)) {
                        HwCapitalRefundPO refund = buildRefundRecord(order, dtlList, false);
                        refundList.add(refund);
                    }
                }
            }
        }
        
        return refundList;
    }

    /**
     * 构建退款记录
     * 
     * @param order 订单信息
     * @param dtlList 订单明细列表
     * @param isPaidCancelled 是否为付款成功后撤单的场景
     * @return 退款记录
     */
    private HwCapitalRefundPO buildRefundRecord(CapitalRefundOrderBO order, List<HwDealOrderDtl> dtlList, 
                                               boolean isPaidCancelled) {
        HwCapitalRefundPO refund = new HwCapitalRefundPO();
        
        // 从订单信息设置字段
        refund.setDealNo(order.getDealNo());
        refund.setRelationalDealNo(order.getRelationalDealNo());
        refund.setHkCustNo(order.getHkCustNo());
        refund.setMiddleBusiCode(order.getMiddleBusiCode());
        
        // 从订单明细设置字段（取第一条明细的基金信息）
        HwDealOrderDtl firstDtl = dtlList.get(0);
        refund.setFundTxAcctNo(firstDtl.getFundTxAcctNo());
        refund.setFundCode(firstDtl.getFundCode());
        refund.setFundAbbr(firstDtl.getFundAbbr());
        refund.setFundCurrency(firstDtl.getCurrency());
        refund.setFundManCode(firstDtl.getFundManCode());
        
        // 设置退款金额
        BigDecimal refundAmt;
        if (isPaidCancelled) {
            // 付款成功后撤单：使用申购金额
            refundAmt = DealDtlMergeUtil.mergeAppAmt(dtlList);
            refund.setPaymentType(order.getPaymentTypeList());
        } else {
            // 储蓄罐赎回：使用确认金额
            refundAmt = DealDtlMergeUtil.mergeAckAmt(dtlList);
            refund.setPaymentType(null);
        }
        refund.setRefundAmt(refundAmt);
        
        // 设置其他字段
        refund.setRefundDt(DateUtils.getCurrentDate());
        refund.setHandleStatus(YesOrNoEnum.NO.getValue()); // 处理状态：0-未处理
        refund.setRefundDirection(REFUND_DIRECTION_CASH);
        refund.setRecStat(NewRecStatEnum.NORMAL.getValue()); // 记录状态：1-有效
        refund.setVersion(0);
        
        Date currentTime = new Date();
        refund.setCreateTimestamp(currentTime);
        refund.setUpdateTimestamp(currentTime);
        
        return refund;
    }

    /**
     * 批量插入退款记录
     * 
     * @param refundList 退款记录列表
     * @return 插入成功的记录数
     */
    private int batchInsertRefundRecords(List<HwCapitalRefundPO> refundList) {
        if (CollectionUtils.isEmpty(refundList)) {
            return 0;
        }
        
        log.info("开始批量插入退款记录，总记录数：{}", refundList.size());
        
        int totalInserted = 0;
        List<List<HwCapitalRefundPO>> partitionedList = Lists.partition(refundList, 200);
        
        for (List<HwCapitalRefundPO> batch : partitionedList) {
            int batchResult = hwCapitalRefundRepository.batchInsert(batch);
            totalInserted += batchResult;
            log.info("批量插入退款记录，本批次插入：{} 条", batchResult);
        }
        
        log.info("批量插入退款记录完成，总共插入：{} 条", totalInserted);
        return totalInserted;
    }
}
