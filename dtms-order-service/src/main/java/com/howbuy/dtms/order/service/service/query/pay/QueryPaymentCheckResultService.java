package com.howbuy.dtms.order.service.service.query.pay;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.howbuy.dtms.order.client.domain.request.payment.QueryPaymentCheckResultRequest;
import com.howbuy.dtms.order.client.domain.response.payment.QueryPaymentCheckResultResponse;
import com.howbuy.dtms.order.dao.dto.PaymentCheckResultDTO;
import com.howbuy.dtms.order.dao.query.PaymentCheckResultQuery;
import com.howbuy.dtms.order.service.commom.utils.DateUtils;
import com.howbuy.dtms.order.service.repository.HwPaymentCheckResultRepository;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 支付对账结果查询服务
 * 
 * <AUTHOR>
 * @date 2025-07-07 17:13:51
 * @since JDK 1.8
 */
@Slf4j
@Service
public class QueryPaymentCheckResultService {

    @Resource
    private HwPaymentCheckResultRepository hwPaymentCheckResultRepository;

    /**
     * 查询支付对账结果
     * 
     * @param request 查询请求参数
     * @return 查询结果
     * @description 根据条件分页查询支付对账结果，关联支付订单表、对账结果表和交易订单表
     * <AUTHOR>
     * @date 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    public QueryPaymentCheckResultResponse queryPaymentCheckResult(QueryPaymentCheckResultRequest request) {
        log.info("开始查询支付对账结果，查询条件：{}", request);
        ParamsValidator.validate(request, "pmtCheckDt", "page", "size");
        // 构建查询条件
        PaymentCheckResultQuery query = buildQuery(request);

        // 分页查询
        PageHelper.startPage(request.getPage(), request.getSize());
        List<PaymentCheckResultDTO> dtoList =
            hwPaymentCheckResultRepository.selectPaymentCheckResultPage(query);

        // 转换DTO为VO
        List<QueryPaymentCheckResultResponse.PaymentCheckResultVO> resultList =
            convertDtoToVo(dtoList);

        // 构建分页响应
        QueryPaymentCheckResultResponse response = buildResponse(resultList);
        
        log.info("支付对账结果查询完成，总记录数：{}", response.getTotal());
        return response;
    }

    /**
     * 构建查询条件
     * 
     * @param request 请求参数
     * @return 查询条件
     * <AUTHOR>
     * @date 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    private PaymentCheckResultQuery buildQuery(QueryPaymentCheckResultRequest request) {
        PaymentCheckResultQuery query = new PaymentCheckResultQuery();
        query.setPmtDealNo(request.getPmtDealNo());
        query.setDealNo(request.getDealNo());
        query.setOutPmtDealNo(request.getOutPmtDealNo());
        query.setFundCodes(request.getFundCodes());
        query.setPmtCheckDt(request.getPmtCheckDt());
        query.setPmtCompFlags(request.getPmtCompFlags());
        query.setOrderType(request.getOrderType());
        return query;
    }

    /**
     * 构建分页响应
     *
     * @param resultList 查询结果列表
     * @return 分页响应
     * <AUTHOR>
     * @date 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    private QueryPaymentCheckResultResponse buildResponse(List<QueryPaymentCheckResultResponse.PaymentCheckResultVO> resultList) {
        QueryPaymentCheckResultResponse response = new QueryPaymentCheckResultResponse();

        if (CollectionUtils.isEmpty(resultList)) {
            response.setTotal(0L);
            response.setPages(0);
            response.setList(new ArrayList<>());
        } else {
            PageInfo<QueryPaymentCheckResultResponse.PaymentCheckResultVO> pageInfo = new PageInfo<>(resultList);
            response.setTotal(pageInfo.getTotal());
            response.setPages(pageInfo.getPages());
            response.setList(resultList);
        }

        return response;
    }

    /**
     * 转换DTO为VO
     *
     * @param dtoList DTO列表
     * @return VO列表
     * <AUTHOR>
     * @date 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    private List<QueryPaymentCheckResultResponse.PaymentCheckResultVO> convertDtoToVo(List<PaymentCheckResultDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }

        return dtoList.stream().map(this::convertSingleDtoToVo).collect(Collectors.toList());
    }

    /**
     * 转换单个DTO为VO
     *
     * @param dto DTO对象
     * @return VO对象
     * <AUTHOR>
     * @date 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    private QueryPaymentCheckResultResponse.PaymentCheckResultVO convertSingleDtoToVo(PaymentCheckResultDTO dto) {
        QueryPaymentCheckResultResponse.PaymentCheckResultVO vo = new QueryPaymentCheckResultResponse.PaymentCheckResultVO();

        vo.setPmtDealNo(dto.getPmtDealNo());
        vo.setDealNo(dto.getDealNo());
        vo.setMiddleBusiCode(dto.getMiddleBusiCode());
        vo.setPaymentTypeList(dto.getPaymentTypeList());
        vo.setHkCustNo(dto.getHkCustNo());
        vo.setCustName(dto.getCustName());
        vo.setCpAcctNo(dto.getCpAcctNo());
        vo.setFundTxAcctNo(dto.getFundTxAcctNo());
        vo.setFundCode(dto.getFundCode());
        vo.setFundAddr(dto.getFundAddr());
        vo.setCurrency(dto.getCurrency());

        // 日期格式化由前端处理，这里直接转换为字符串
        if (dto.getAppDtm() != null) {
            vo.setAppDtm(DateUtils.dateFormatToString(dto.getAppDtm(), DateUtils.YYYYMMDDHHMMSS));
        }

        // 金额转换为字符串
        vo.setPmtAmt(dto.getPmtAmt() != null ? dto.getPmtAmt().toString() : "");
        vo.setPmtCheckDt(dto.getPmtCheckDt());
        vo.setOutPmtDealNo(dto.getOutPmtDealNo());
        vo.setOutPmtAmt(dto.getOutPmtAmt() != null ? dto.getOutPmtAmt().toString() : "");
        vo.setOutCurrency(dto.getOutCurrency());
        vo.setOutPmtFlag(dto.getOutPmtFlag());
        vo.setTxPmtFlag(dto.getTxPmtFlag());
        vo.setPmtCompFlag(dto.getPmtCompFlag());
        vo.setMemo(dto.getMemo());

        return vo;
    }
}
