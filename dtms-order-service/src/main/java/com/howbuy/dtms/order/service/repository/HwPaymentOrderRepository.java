/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.repository;

import com.howbuy.dtms.common.annotation.DataSource;
import com.howbuy.dtms.common.enums.*;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwDealOrderDtlMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwDealOrderMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPaymentOrderPOMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper;
import com.howbuy.dtms.order.dao.param.PaymentFailureDtlUpdateParam;
import com.howbuy.dtms.order.dao.param.PaymentFailureUpdateParam;
import com.howbuy.dtms.order.dao.param.PaymentResultUpdateParam;
import com.howbuy.dtms.order.dao.param.PaymentSuccessUpdateParam;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.DateUtils;
import com.howbuy.dtms.order.service.commom.utils.DealDtlMergeUtil;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 海外支付订单数据仓库
 * <AUTHOR>
 * @date 2025-07-02 16:04:22
 * @since JDK 1.8
 */
@Slf4j
@Repository
@DataSource(value = "mysql")
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class HwPaymentOrderRepository {

    @Resource
    private HwPaymentOrderPOMapper hwPaymentOrderPOMapper;

    @Resource
    private CustomizeHwPaymentOrderPOMapper customizeHwPaymentOrderPOMapper;

    @Resource
    private CustomizeHwDealOrderMapper customizeHwDealOrderMapper;

    @Resource
    private CustomizeHwDealOrderDtlMapper customizeHwDealOrderDtlMapper;

    /**
     * @description: 根据支付订单号查询支付订单
     * @param pmtDealNo 支付订单号
     * @return 支付订单信息
     * @author: shaoyang.li
     * @date: 2025-07-08 11:20:37
     * @since JDK 1.8
     */
    public HwPaymentOrderPO selectByPmtDealNo(Long pmtDealNo) {
        log.info("查询支付订单，支付订单号：{}", pmtDealNo);
        return customizeHwPaymentOrderPOMapper.selectByPmtDealNo(pmtDealNo);
    }

    /**
     * @description:根据订单号查询支付订单
     * @param dealNo
     * @return com.howbuy.dtms.order.dao.po.HwPaymentOrderPO
     * <AUTHOR>
     * @date 2025/7/29 9:54
     * @since JDK 1.8
     */
    public HwPaymentOrderPO selectByDealNo(Long dealNo) {
        log.info("查询支付订单，订单号：{}", dealNo);
        return customizeHwPaymentOrderPOMapper.selectByDealNo(dealNo);
    }

    /**
     * @description: 根据主键查询支付订单
     * @param id 主键ID
     * @return 支付订单信息
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    public HwPaymentOrderPO selectByPrimaryKey(Long id) {
        log.info("根据主键查询支付订单，主键：{}", id);
        return hwPaymentOrderPOMapper.selectByPrimaryKey(id);
    }

    /**
     * @description: 使用乐观锁更新支付订单状态为付款中
     * @param pmtDealNo 支付订单号
     * @param originalTxPmtFlag 原始交易支付标记
     * @param originalUpdateTimestamp 原始更新时间戳
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateToPayingWithOptimisticLock(Long pmtDealNo, String originalTxPmtFlag, Date originalUpdateTimestamp) {
        log.info("使用乐观锁更新支付订单状态为付款中，支付订单号：{}，原始状态：{}，原始更新时间：{}",
                pmtDealNo, originalTxPmtFlag, originalUpdateTimestamp);

        Date currentTimestamp = new Date();
        int result = customizeHwPaymentOrderPOMapper.updateToPayingWithOptimisticLock(
                pmtDealNo, originalTxPmtFlag, originalUpdateTimestamp, currentTimestamp);

        log.info("更新支付订单状态为付款中结果，影响行数：{}", result);
        return result;
    }

    /**
     * @description: 更新支付结果
     * @param pmtDealNo 支付订单号
     * @param retCode 返回码
     * @param retDesc 返回描述
     * @param outPmtDealNo 外部支付订单号
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updatePaymentResultWithOptimisticLock(Long pmtDealNo, String retCode, String retDesc,
                                                   String outPmtDealNo) {
        log.info("更新支付结果，支付订单号：{}，返回码：{}，返回描述：{}，外部订单号：{}",
                pmtDealNo, retCode, retDesc, outPmtDealNo);

        Date currentTimestamp = new Date();
        int result = customizeHwPaymentOrderPOMapper.updatePaymentResultWithOptimisticLock(
                pmtDealNo, retCode, retDesc, outPmtDealNo, currentTimestamp);

        log.info("更新支付结果完成，影响行数：{}", result);
        return result;
    }

    /**
     * @description: 插入支付订单
     * @param hwPaymentOrderPO 支付订单信息
     * @return 插入影响行数
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int insert(HwPaymentOrderPO hwPaymentOrderPO) {
        log.info("插入支付订单，支付订单号：{}", hwPaymentOrderPO.getPmtDealNo());
        return hwPaymentOrderPOMapper.insertSelective(hwPaymentOrderPO);
    }

    /**
     * @description: 支付前置处理 - 更新支付订单和交易订单状态为付款中（事务控制）
     * @param paymentOrder 支付订单信息
     * @param dealOrder 交易订单信息（可为空）
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateToPayingStatusWithTransaction(HwPaymentOrderPO paymentOrder, HwDealOrder dealOrder) {
        log.info("支付前置处理开始，支付订单号：{}，交易订单号：{}",
                paymentOrder.getPmtDealNo(),
                dealOrder != null ? dealOrder.getDealNo() : null);

        // 1. 更新支付订单状态为付款中
        int paymentUpdateResult = updateToPayingWithOptimisticLock(
                paymentOrder.getPmtDealNo(),
                paymentOrder.getTxPmtFlag(),
                paymentOrder.getUpdateTimestamp());

        if (paymentUpdateResult != 1) {
            log.error("更新支付订单状态失败，支付订单号：{}，影响行数：{}", paymentOrder.getPmtDealNo(), paymentUpdateResult);
            throw new RuntimeException("更新支付订单状态失败，可能存在并发冲突");
        }

        // 2. 如果是交易订单，更新交易订单支付状态为付款中
        if (dealOrder != null) {
            Date currentTimestamp = new Date();
            int dealOrderUpdateResult = customizeHwDealOrderMapper.updatePayStatusToPayingWithOptimisticLock(
                    dealOrder.getDealNo(),
                    dealOrder.getOrderStatus(),
                    dealOrder.getPayStatus(),
                    dealOrder.getUpdateTimestamp(),
                    currentTimestamp);

            if (dealOrderUpdateResult != 1) {
                log.error("更新交易订单支付状态失败，订单号：{}，影响行数：{}", dealOrder.getDealNo(), dealOrderUpdateResult);
                throw new RuntimeException("更新交易订单支付状态失败，可能存在并发冲突");
            }
        }

        log.info("支付前置处理完成，支付订单号：{}，交易订单号：{}",
                paymentOrder.getPmtDealNo(),
                dealOrder != null ? dealOrder.getDealNo() : null);
    }

    /**
     * @description: 查询未支付订单（不分页）
     * @param txPmtFlag 支付状态
     * @param recStat 记录状态
     * @param updateTimeStart 更新时间开始
     * @param updateTimeEnd 更新时间结束
     * @return 未支付订单列表
     * @author: shaoyang.li
     * @date: 2025-07-03 11:34:43
     * @since JDK 1.8
     */
    public List<HwPaymentOrderPO> selectUnPaymentOrders(String txPmtFlag, String recStat,
                                                       Date updateTimeStart, Date updateTimeEnd) {
        log.info("查询未支付订单，支付状态：{}，记录状态：{}，更新时间范围：{} - {}",
                txPmtFlag, recStat, updateTimeStart, updateTimeEnd);

        return customizeHwPaymentOrderPOMapper.selectUnPaymentOrders(
                txPmtFlag, recStat, updateTimeStart, updateTimeEnd);
    }

    /**
     * @description: 查询支付中状态的支付订单（不分页）
     * @param txPmtFlag 支付状态
     * @param recStat 记录状态
     * @param updateTimeStart 更新时间开始
     * @param updateTimeEnd 更新时间结束
     * @return 支付中状态的支付订单列表
     * @author: shaoyang.li
     * @date: 2025-07-04 15:08:30
     * @since JDK 1.8
     */
    public List<HwPaymentOrderPO> selectPayingOrders(String txPmtFlag, String recStat,
                                                    Date updateTimeStart, Date updateTimeEnd) {
        log.info("查询支付中状态的支付订单，支付状态：{}，记录状态：{}，更新时间范围：{} - {}",
                txPmtFlag, recStat, updateTimeStart, updateTimeEnd);

        return customizeHwPaymentOrderPOMapper.selectPayingOrders(
                txPmtFlag, recStat, updateTimeStart, updateTimeEnd);
    }

    /**
     * @description: 在事务中更新支付结果及相关订单状态
     * @param hwPaymentOrder 支付订单实体
     * @param hwDealOrder 交易订单实体
     * @param hwDealOrderDtlList 交易订单明细列表
     * @param paymentResult 支付结果数据
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updatePaymentResultWithTransaction(HwPaymentOrderPO hwPaymentOrder, HwDealOrder hwDealOrder,
                                                 List<HwDealOrderDtl> hwDealOrderDtlList, PaymentResultDTO paymentResult) {
        log.info("开始在事务中更新支付结果及相关订单状态，支付订单号：{}，支付结果：{}",
                hwPaymentOrder.getPmtDealNo(), paymentResult.getTxPmtFlag());

        // 1. 更新支付订单状态
        updatePaymentOrderInTransaction(hwPaymentOrder, paymentResult);

        // 2. 处理交易订单（仅当订单类型为交易时）
        if (isTradeOrder(hwPaymentOrder.getOrderType()) && hwDealOrder != null) {
            processTradeOrderInTransaction(hwDealOrder, hwDealOrderDtlList, paymentResult);
        }

        log.info("在事务中更新支付结果及相关订单状态完成，支付订单号：{}", hwPaymentOrder.getPmtDealNo());
    }

    /**
     * @description: 在事务中更新支付订单状态
     * @param hwPaymentOrder 支付订单实体
     * @param paymentResult 支付结果数据
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void updatePaymentOrderInTransaction(HwPaymentOrderPO hwPaymentOrder, PaymentResultDTO paymentResult) {
        log.info("开始更新支付订单状态，支付订单号：{}，目标状态：{}",
                hwPaymentOrder.getPmtDealNo(), paymentResult.getTxPmtFlag());

        // 使用乐观锁更新支付订单状态
        Date currentTimestamp = new Date();
        Date pmtCompleteTimestamp = new Date();

        PaymentResultUpdateParam param = PaymentResultUpdateParam.builder()
                .pmtDealNo(hwPaymentOrder.getPmtDealNo())
                .txPmtFlag(paymentResult.getTxPmtFlag())
                .retCode(paymentResult.getRetCode())
                .retDesc(paymentResult.getRetDesc())
                .outPmtDealNo(paymentResult.getOutPmtDealNo())
                .pmtCheckDt(paymentResult.getPmtCheckDt())
                .originalTxPmtFlag(TxPmtFlagEnum.PAYING.getCode()) // 前置状态：付款中
                .originalUpdateTimestamp(hwPaymentOrder.getUpdateTimestamp())
                .currentTimestamp(currentTimestamp)
                .pmtCompleteTimestamp(pmtCompleteTimestamp)
                .build();

        int updateResult = customizeHwPaymentOrderPOMapper.updatePaymentResultWithOptimisticLockFull(param);

        // 校验更新结果
        if (updateResult != 1) {
            String errorMsg = String.format("更新支付订单状态失败，可能存在并发冲突，支付订单号：%s，影响行数：%d",
                    hwPaymentOrder.getPmtDealNo(), updateResult);
            log.error(errorMsg);
            AlertLogUtil.alert(HwPaymentOrderRepository.class.getName(), errorMsg);
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        log.info("更新支付订单状态成功，支付订单号：{}", hwPaymentOrder.getPmtDealNo());
    }

    /**
     * @description: 在事务中处理交易订单
     * @param hwDealOrder 交易订单实体
     * @param hwDealOrderDtlList 交易订单明细列表
     * @param paymentResult 支付结果数据
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void processTradeOrderInTransaction(HwDealOrder hwDealOrder, List<HwDealOrderDtl> hwDealOrderDtlList,
                                              PaymentResultDTO paymentResult) {

        if (isPaymentSuccess(paymentResult.getTxPmtFlag())) {
            // 支付成功处理
            processPaymentSuccessInTransaction(hwDealOrder, hwDealOrderDtlList);
        } else if (isPaymentFailed(paymentResult.getTxPmtFlag())) {
            // 支付失败处理
            processPaymentFailedInTransaction(hwDealOrder, hwDealOrderDtlList);
        }
    }

    /**
     * @description: 在事务中处理支付成功情况
     * @param hwDealOrder 交易订单实体
     * @param hwDealOrderDtlList 交易订单明细列表
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void processPaymentSuccessInTransaction(HwDealOrder hwDealOrder, List<HwDealOrderDtl> hwDealOrderDtlList) {
        log.info("处理支付成功，交易订单号：{}", hwDealOrder.getDealNo());

        // 获取当前服务器时间
        Date currentTimestamp = new Date();
        
        // 计算实际支付金额（订单明细的net_app_amt汇总）
        BigDecimal actualPayAmt = DealDtlMergeUtil.mergeNetAppAmt(hwDealOrderDtlList);
        if (actualPayAmt == null) {
            actualPayAmt = BigDecimal.ZERO;
        }

        // 构建支付成功更新参数
        PaymentSuccessUpdateParam param = new PaymentSuccessUpdateParam();
        param.setDealNo(hwDealOrder.getDealNo());
        param.setPayStatus(PayStatusEnum.PAY_SUCCESS.getCode()); // 支付成功
        param.setActualPayDt(DateUtils.getCurrentDate()); // 当前日期 yyyyMMdd
        param.setActualPayTm(DateUtils.getCurrentDateTime()); // 当前时间 HHmmss
        param.setActualPayAmt(actualPayAmt); // 实际支付金额
        param.setOriginalPayStatus(PayStatusEnum.PAYING.getCode()); // 前置状态：付款中
        param.setOriginalUpdateTimestamp(hwDealOrder.getUpdateTimestamp());
        param.setCurrentUpdateTimestamp(currentTimestamp);

        // 使用乐观锁更新交易订单支付状态为成功及实际支付信息
        int updateResult = customizeHwDealOrderMapper.updatePayStatusSuccessWithOptimisticLock(param);

        // 校验更新结果
        if (updateResult != 1) {
            String errorMsg = String.format("更新交易订单支付状态为成功失败，可能存在并发冲突，订单号：%s，影响行数：%d",
                    hwDealOrder.getDealNo(), updateResult);
            log.error(errorMsg);
            AlertLogUtil.alert(HwPaymentOrderRepository.class.getName(), errorMsg);
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        log.info("更新交易订单支付状态为成功完成，订单号：{}，实际支付金额：{}",
                hwDealOrder.getDealNo(), actualPayAmt);
    }

    /**
     * @description: 在事务中处理支付失败情况
     * @param hwDealOrder 交易订单实体
     * @param hwDealOrderDtlList 交易订单明细列表
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void processPaymentFailedInTransaction(HwDealOrder hwDealOrder, List<HwDealOrderDtl> hwDealOrderDtlList) {
        log.info("处理支付失败，交易订单号：{}", hwDealOrder.getDealNo());

        // 使用乐观锁更新交易订单状态为失败和强制取消（支付结果场景，限制旧时间戳）
        Date currentTimestamp = new Date();
        PaymentFailureUpdateParam updateParam = new PaymentFailureUpdateParam();
        updateParam.setDealNo(hwDealOrder.getDealNo());
        updateParam.setPayStatus(PayStatusEnum.PAY_FAIL.getCode()); // 支付失败
        updateParam.setOrderStatus(OrderStatusEnum.FORCE_CANCEL.getCode()); // 强制取消
        updateParam.setOriginalPayStatus(PayStatusEnum.PAYING.getCode()); // 前置支付状态：付款中
        updateParam.setOriginalOrderStatus(hwDealOrder.getOrderStatus()); // 原始订单状态
        updateParam.setOriginalUpdateTimestamp(hwDealOrder.getUpdateTimestamp()); // 原始更新时间戳
        updateParam.setCurrentUpdateTimestamp(currentTimestamp);

        int updateResult = customizeHwDealOrderMapper.updatePayStatusAndOrderStatusWithOptimisticLock(updateParam);

        // 校验更新结果
        if (updateResult != 1) {
            String errorMsg = String.format("更新交易订单状态为失败失败，可能存在并发冲突，订单号：%s，影响行数：%d",
                    hwDealOrder.getDealNo(), updateResult);
            log.error(errorMsg);
            AlertLogUtil.alert(HwPaymentOrderRepository.class.getName(), errorMsg);
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        // 更新订单明细状态为申请失败
        updateOrderDetailsToFailedInTransaction(hwDealOrderDtlList);

        log.info("更新交易订单状态为失败完成，订单号：{}", hwDealOrder.getDealNo());
    }

    /**
     * @description: 在事务中更新订单明细状态为申请失败
     * @param hwDealOrderDtlList 交易订单明细列表
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private void updateOrderDetailsToFailedInTransaction(List<HwDealOrderDtl> hwDealOrderDtlList) {
        if (CollectionUtils.isEmpty(hwDealOrderDtlList)) {
            log.info("订单明细列表为空，跳过更新");
            return;
        }

        log.info("开始更新订单明细状态为申请失败，明细数量：{}", hwDealOrderDtlList.size());

        for (HwDealOrderDtl orderDtl : hwDealOrderDtlList) {
            // 判断申请状态是否为申请成功，只有申请成功的才进行修改
            if (!AppStatusEnum.APPLY_SUCCESS.getCode().equals(orderDtl.getAppStatus())) {
                log.info("订单明细申请状态不是申请成功，跳过更新，明细ID：{}，当前申请状态：{}",
                        orderDtl.getId(), orderDtl.getAppStatus());
                continue;
            }

            // 使用乐观锁更新订单明细申请状态为失败和上报状态为无需上报（支付结果场景，限制旧时间戳）
            Date currentTimestamp = new Date();
            PaymentFailureDtlUpdateParam updateParam = new PaymentFailureDtlUpdateParam();
            updateParam.setDtlId(orderDtl.getId());
            updateParam.setAppStatus(AppStatusEnum.APPLY_FAIL.getCode()); // 申请失败
            updateParam.setSubmitStatus(SubmitStatusEnum.NOT_NEED_SUBMIT.getCode()); // 无需上报
            updateParam.setOriginalAppStatus(orderDtl.getAppStatus()); // 原始申请状态
            updateParam.setOriginalUpdateTimestamp(orderDtl.getUpdateTimestamp()); // 原始更新时间戳
            updateParam.setCurrentUpdateTimestamp(currentTimestamp);

            int updateResult = customizeHwDealOrderDtlMapper.updateAppStatusWithOptimisticLock(updateParam);

            if (updateResult != 1) {
                String errorMsg = String.format("更新订单明细申请状态失败，可能存在并发冲突，明细ID：%s，影响行数：%d",
                        orderDtl.getId(), updateResult);
                log.error(errorMsg);
                AlertLogUtil.alert(HwPaymentOrderRepository.class.getName(), errorMsg);
                throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
            }
        }

        log.info("更新订单明细状态为申请失败完成，明细数量：{}", hwDealOrderDtlList.size());
    }

    /**
     * @description: 判断是否为交易订单
     * @param orderType 订单类型
     * @return 是否为交易订单
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private boolean isTradeOrder(String orderType) {
        return PaymentOrderTypeEnum.TRADE.getCode().equals(orderType);
    }

    /**
     * @description: 判断是否支付成功
     * @param txPmtFlag 支付状态标志
     * @return 是否支付成功
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private boolean isPaymentSuccess(String txPmtFlag) {
        return TxPmtFlagEnum.PAY_SUCCESS.getCode().equals(txPmtFlag);
    }

    /**
     * @description: 判断是否支付失败
     * @param txPmtFlag 支付状态标志
     * @return 是否支付失败
     * @author: shaoyang.li
     * @date: 2025-07-03 16:02:08
     * @since JDK 1.8
     */
    private boolean isPaymentFailed(String txPmtFlag) {
        return TxPmtFlagEnum.PAY_FAIL.getCode().equals(txPmtFlag);
    }

    /**
     * @description: 重置交易支付标识（事务控制）
     * @param paymentOrder 支付订单信息
     * @param dealOrder 交易订单信息（可为空）
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void resetTxPmtFlagWithTransaction(HwPaymentOrderPO paymentOrder, HwDealOrder dealOrder) {
        log.info("重置交易支付标识开始，支付订单号：{}，交易订单号：{}",
                paymentOrder.getPmtDealNo(),
                dealOrder != null ? dealOrder.getDealNo() : null);

        // 1. 重置支付订单状态为未付款
        int paymentUpdateResult = resetPaymentOrderTxPmtFlagWithOptimisticLock(
                paymentOrder.getPmtDealNo(),
                paymentOrder.getTxPmtFlag(),
                paymentOrder.getUpdateTimestamp());

        if (paymentUpdateResult != 1) {
            String errorMsg = String.format("重置支付订单状态失败，可能存在并发冲突，支付订单号：%s，影响行数：%d",
                    paymentOrder.getPmtDealNo(), paymentUpdateResult);
            log.error(errorMsg);
            AlertLogUtil.alert("重置支付订单状态失败", errorMsg);
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        // 2. 如果存在关联交易订单，重置交易订单支付状态为未付款
        if (dealOrder != null) {
            int dealOrderUpdateResult = resetDealOrderPayStatusWithOptimisticLock(
                    dealOrder.getDealNo(),
                    dealOrder.getOrderStatus(),
                    dealOrder.getPayStatus(),
                    dealOrder.getUpdateTimestamp());

            if (dealOrderUpdateResult != 1) {
                String errorMsg = String.format("重置交易订单支付状态失败，可能存在并发冲突，交易订单号：%s，影响行数：%d",
                        dealOrder.getDealNo(), dealOrderUpdateResult);
                log.error(errorMsg);
                AlertLogUtil.alert("重置交易订单支付状态失败", errorMsg);
                throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
            }
        }

        log.info("重置交易支付标识完成，支付订单号：{}，交易订单号：{}",
                paymentOrder.getPmtDealNo(),
                dealOrder != null ? dealOrder.getDealNo() : null);
    }

    /**
     * @description: 使用乐观锁重置支付订单交易支付标识为未付款
     * @param pmtDealNo 支付订单号
     * @param originalTxPmtFlag 原始交易支付标记
     * @param originalUpdateTimestamp 原始更新时间戳
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    private int resetPaymentOrderTxPmtFlagWithOptimisticLock(Long pmtDealNo, String originalTxPmtFlag, Date originalUpdateTimestamp) {
        log.info("使用乐观锁重置支付订单交易支付标识，支付订单号：{}，原始状态：{}，原始更新时间：{}",
                pmtDealNo, originalTxPmtFlag, originalUpdateTimestamp);

        Date currentTimestamp = new Date();
        int result = customizeHwPaymentOrderPOMapper.resetTxPmtFlagWithOptimisticLock(
                pmtDealNo, TxPmtFlagEnum.UN_PAY.getCode(), originalTxPmtFlag, originalUpdateTimestamp, currentTimestamp);

        log.info("重置支付订单交易支付标识完成，影响行数：{}", result);
        return result;
    }

    /**
     * @description: 使用乐观锁重置交易订单支付状态为未付款
     * @param dealNo 交易订单号
     * @param originalOrderStatus 原始订单状态
     * @param originalPayStatus 原始支付状态
     * @param originalUpdateTimestamp 原始更新时间戳
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    private int resetDealOrderPayStatusWithOptimisticLock(Long dealNo, String originalOrderStatus, String originalPayStatus, Date originalUpdateTimestamp) {
        log.info("使用乐观锁重置交易订单支付状态，交易订单号：{}，原始订单状态：{}，原始支付状态：{}，原始更新时间：{}",
                dealNo, originalOrderStatus, originalPayStatus, originalUpdateTimestamp);

        Date currentTimestamp = new Date();
        int result = customizeHwDealOrderMapper.resetPayStatusWithOptimisticLock(
                dealNo, PayStatusEnum.UN_PAY.getCode(), originalOrderStatus, originalPayStatus, originalUpdateTimestamp, currentTimestamp);

        log.info("重置交易订单支付状态完成，影响行数：{}", result);
        return result;
    }
}
