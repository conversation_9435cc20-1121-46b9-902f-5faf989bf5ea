/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.base.Throwables;
import com.howbuy.dtms.common.enums.NewRecStatEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.client.domain.response.PageVo;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.service.business.batch.BatchOperateExecutor;
import com.howbuy.dtms.order.service.business.batch.QueryExecuteService;
import com.howbuy.dtms.order.service.business.payment.AsyncDoPaymentBusiness;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.DateUtils;
import com.howbuy.dtms.order.dao.query.UnPaymentOrderQuery;
import com.howbuy.dtms.order.service.job.domain.RefreshUnPaymentOrderTaskVO;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @description: 刷新未支付订单定时任务
 * <AUTHOR>
 * @date 2025-07-03 11:34:43
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RefreshUnPaymentOrderJob extends AbstractBatchMessageJob<RefreshUnPaymentOrderTaskVO> {

    @Value("${TOPIC.DTMS_ORDER_EC_TASK_REFRESH_UN_PAY_ORDER}")
    private String topic;

    @Resource
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Resource
    private AsyncDoPaymentBusiness asyncDoPaymentBusiness;

    @Resource
    private BatchOperateExecutor batchOperateExecutor;

    /**
     * 查询时间窗口：10分钟前到20天前
     */
    private static final int QUERY_START_MINUTES = 10;
    private static final int QUERY_END_DAYS = 20;

    @Override
    protected String getTopicName() {
        return topic;
    }

    /**
     * @description: 执行刷新未支付订单任务
     * @param message 任务消息
     * @author: shaoyang.li
     * @date: 2025-07-03 11:34:43
     * @since JDK 1.8
     */
    @Override
    protected void doProcessJob(RefreshUnPaymentOrderTaskVO message) {
        log.info("刷新未支付订单定时任务开始执行");

        // 构建查询条件
        UnPaymentOrderQuery query = buildQueryCondition();

        // 使用批处理执行器进行分页处理
        int totalProcessed = batchOperateExecutor.queryBeforeExecute(query,
                new QueryExecuteService<UnPaymentOrderQuery, HwPaymentOrderPO>() {

                    @Override
                    public PageVo<HwPaymentOrderPO> queryPage(UnPaymentOrderQuery queryCondition) {
                        return queryUnPaymentOrdersPage(queryCondition);
                    }

                    @Override
                    public int execute(List<HwPaymentOrderPO> paymentOrders) {
                        return processPaymentOrders(paymentOrders);
                    }
                });

        log.info("刷新未支付订单定时任务执行完成，共处理订单数量：{}", totalProcessed);
    }

    /**
     * @description: 构建查询条件
     * @return 查询条件
     * @author: shaoyang.li
     * @date: 2025-07-03 11:34:43
     * @since JDK 1.8
     */
    private UnPaymentOrderQuery buildQueryCondition() {
        UnPaymentOrderQuery query = new UnPaymentOrderQuery();
        
        // 设置支付状态为未付款
        query.setTxPmtFlag(TxPmtFlagEnum.UN_PAY.getCode());
        
        // 设置记录状态为有效
        query.setRecStat(NewRecStatEnum.NORMAL.getValue());
        
        // 设置时间窗口：当前时间10分钟前到20天前
        Date currentTime = new Date();
        
        // 结束时间：当前时间10分钟前
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(currentTime);
        endCalendar.add(Calendar.MINUTE, -QUERY_START_MINUTES);
        query.setUpdateTimeEnd(endCalendar.getTime());
        
        // 开始时间：当前时间20天前
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(currentTime);
        startCalendar.add(Calendar.DAY_OF_MONTH, -QUERY_END_DAYS);
        query.setUpdateTimeStart(startCalendar.getTime());
        
        log.info("构建查询条件完成，支付状态：{}，记录状态：{}，更新时间范围：{} - {}",
                query.getTxPmtFlag(), query.getRecStat(),
                DateUtils.dateFormatToString(query.getUpdateTimeStart(), DateUtils.YYYYMMDDHHMMSS),
                DateUtils.dateFormatToString(query.getUpdateTimeEnd(), DateUtils.YYYYMMDDHHMMSS));
        
        return query;
    }

    /**
     * @description: 分页查询未支付订单（使用PageHelper）
     * @param query 查询条件
     * @return 分页结果
     * @author: shaoyang.li
     * @date: 2025-07-03 11:34:43
     * @since JDK 1.8
     */
    private PageVo<HwPaymentOrderPO> queryUnPaymentOrdersPage(UnPaymentOrderQuery query) {
        log.info("分页查询未支付订单，页号：{}，每页大小：{}", query.getPage(), query.getSize());

        // 使用PageHelper进行分页
        PageHelper.startPage(query.getPage(), query.getSize());

        List<HwPaymentOrderPO> paymentOrders = hwPaymentOrderRepository.selectUnPaymentOrders(
                query.getTxPmtFlag(),
                query.getRecStat(),
                query.getUpdateTimeStart(),
                query.getUpdateTimeEnd());

        // 获取分页信息
        Page<HwPaymentOrderPO> pageInfo = (Page<HwPaymentOrderPO>) paymentOrders;

        // 构建分页结果
        PageVo<HwPaymentOrderPO> pageVo = new PageVo<>();
        pageVo.setList(paymentOrders);
        pageVo.setTotal(pageInfo.getTotal());
        pageVo.setPages(pageInfo.getPages());

        log.info("分页查询未支付订单完成，查询到订单数量：{}，总数量：{}，总页数：{}",
                paymentOrders.size(), pageInfo.getTotal(), pageInfo.getPages());

        return pageVo;
    }

    /**
     * @description: 处理支付订单列表
     * @param paymentOrders 支付订单列表
     * @return 处理成功的订单数量
     * @author: shaoyang.li
     * @date: 2025-07-03 11:34:43
     * @since JDK 1.8
     */
    private int processPaymentOrders(List<HwPaymentOrderPO> paymentOrders) {
        log.info("开始处理支付订单列表，订单数量：{}", paymentOrders.size());
        
        int successCount = 0;
        
        for (HwPaymentOrderPO paymentOrder : paymentOrders) {
            try {
                // 调用异步支付服务处理单笔订单
                asyncDoPaymentBusiness.process(paymentOrder.getPmtDealNo().toString());
                successCount++;
                
                log.info("处理支付订单成功，支付订单号：{}", paymentOrder.getPmtDealNo());
                
            } catch (Exception e) {
                // 单笔订单处理失败，记录日志但不影响其他订单处理
                log.error("处理支付订单失败，支付订单号：{}，异常信息：{}", 
                        paymentOrder.getPmtDealNo(), e.getMessage(), e);
                
                // 发送告警
                AlertLogUtil.alert(this.getClass().getName(), 
                    String.format("处理支付订单失败，支付订单号：%s，异常信息：%s", 
                            paymentOrder.getPmtDealNo(), e.getMessage()));
            }
        }
        
        log.info("处理支付订单列表完成，总数量：{}，成功数量：{}", paymentOrders.size(), successCount);
        
        return successCount;
    }
}
