/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.query.order;

import com.howbuy.dtms.order.client.domain.request.order.QueryDealOrderForCapitalRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.order.QueryDealOrderForCapitalResponse;
import com.howbuy.dtms.order.client.facade.query.order.QueryDealOrderForCapitalFacade;
import com.howbuy.dtms.order.service.service.query.order.QueryOrderService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 查询交易订单(资金)接口实现
 * <AUTHOR>
 * @date 2025-07-11 13:35:30
 * @since JDK 1.8
 */
@DubboService
public class QueryDealOrderForCapitalFacadeImpl implements QueryDealOrderForCapitalFacade {

    @Resource
    private QueryOrderService queryOrderService;

    /**
     * @description: 查询交易订单(资金)
     * @param request 请求参数
     * @return Response<QueryDealOrderForCapitalResponse> 响应结果
     * <AUTHOR>
     * @date 2025-07-11 13:35:30
     * @since JDK 1.8
     */
    @Override
    public Response<QueryDealOrderForCapitalResponse> execute(QueryDealOrderForCapitalRequest request) {
        return Response.ok(queryOrderService.queryDealOrderForCapital(request));
    }
}
