/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.repository;

import com.howbuy.dtms.order.dao.dto.PaymentCheckResultDTO;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPaymentCheckResultPOMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPaymentOrderPOMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentCheckResultPOMapper;
import com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.dao.query.PaymentCheckResultQuery;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description: 海外支付对账结果数据仓库
 * <AUTHOR>
 * @date 2025-07-04 16:20:10
 * @since JDK 1.8
 */
@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class HwPaymentCheckResultRepository {

    @Resource
    private HwPaymentCheckResultPOMapper hwPaymentCheckResultPOMapper;

    @Resource
    private CustomizeHwPaymentCheckResultPOMapper customizeHwPaymentCheckResultPOMapper;

    @Resource
    private CustomizeHwPaymentOrderPOMapper customizeHwPaymentOrderPOMapper;

    /**
     * @description: 根据支付订单号查询对账结果
     * @param pmtDealNo 支付订单号
     * @return 对账结果信息
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    public HwPaymentCheckResultPO selectByPmtDealNo(Long pmtDealNo) {
        log.info("查询支付对账结果，支付订单号：{}", pmtDealNo);
        return customizeHwPaymentCheckResultPOMapper.selectByPmtDealNo(pmtDealNo);
    }

    /**
     * @description: 根据主键查询对账结果
     * @param id 主键ID
     * @return 对账结果信息
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    public HwPaymentCheckResultPO selectByPrimaryKey(Long id) {
        log.info("根据主键查询支付对账结果，主键：{}", id);
        return hwPaymentCheckResultPOMapper.selectByPrimaryKey(id);
    }

    /**
     * @description: 插入对账结果
     * @param hwPaymentCheckResultPO 对账结果信息
     * @return 插入影响行数
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int insert(HwPaymentCheckResultPO hwPaymentCheckResultPO) {
        log.info("插入支付对账结果，支付订单号：{}", hwPaymentCheckResultPO.getPmtDealNo());
        
        // 设置创建和更新时间
        Date currentTime = new Date();
        hwPaymentCheckResultPO.setVersion(0);
        hwPaymentCheckResultPO.setCreateTimestamp(currentTime);
        hwPaymentCheckResultPO.setUpdateTimestamp(currentTime);
        
        return hwPaymentCheckResultPOMapper.insertSelective(hwPaymentCheckResultPO);
    }

    /**
     * @description: 更新对账结果
     * @param hwPaymentCheckResultPO 对账结果信息
     * @return 更新影响行数
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateByPrimaryKeySelective(HwPaymentCheckResultPO hwPaymentCheckResultPO) {
        log.info("更新支付对账结果，主键：{}", hwPaymentCheckResultPO.getId());
        
        // 设置更新时间
        hwPaymentCheckResultPO.setUpdateTimestamp(new Date());
        
        return hwPaymentCheckResultPOMapper.updateByPrimaryKeySelective(hwPaymentCheckResultPO);
    }

    /**
     * @description: 新增或更新对账结果（根据支付订单号判断）
     * @param hwPaymentCheckResultPO 对账结果信息
     * @return 操作影响行数
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int insertOrUpdate(HwPaymentCheckResultPO hwPaymentCheckResultPO) {
        log.info("新增或更新支付对账结果，支付订单号：{}", hwPaymentCheckResultPO.getPmtDealNo());
        
        // 先查询是否存在
        HwPaymentCheckResultPO existingResult = selectByPmtDealNo(hwPaymentCheckResultPO.getPmtDealNo());
        
        if (existingResult == null) {
            // 不存在则新增
            return insert(hwPaymentCheckResultPO);
        } else {
            // 存在则更新
            hwPaymentCheckResultPO.setId(existingResult.getId());
            return updateByPrimaryKeySelective(hwPaymentCheckResultPO);
        }
    }

    /**
     * @description: 在事务中保存支付对账结果并更新订单状态
     * @param checkResultPO 对账结果
     * @param paymentOrder 支付订单
     * @param pmtCompFlag 对账状态
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void savePaymentCheckResultAndUpdateOrderStatus(HwPaymentCheckResultPO checkResultPO,
                                                          HwPaymentOrderPO paymentOrder,
                                                          String pmtCompFlag) {
        log.info("开始在事务中保存对账结果并更新订单状态，支付订单号：{}", checkResultPO.getPmtDealNo());

        try {
            // 1. 保存对账结果
            int saveResult = insertOrUpdate(checkResultPO);
            if (saveResult <= 0) {
                log.error("保存支付对账结果失败，支付订单号：{}", checkResultPO.getPmtDealNo());
                throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "保存对账结果失败");
            }

            log.info("保存支付对账结果成功，支付订单号：{}，对账状态：{}",
                    checkResultPO.getPmtDealNo(), checkResultPO.getPmtCompFlag());

            // 2. 更新支付订单对账状态
            Date currentTimestamp = new Date();
            int updateResult = customizeHwPaymentOrderPOMapper.updatePaymentCheckStatusWithOptimisticLock(
                    paymentOrder.getPmtDealNo(),
                    pmtCompFlag,
                    paymentOrder.getPmtCompFlag(),
                    paymentOrder.getUpdateTimestamp(),
                    currentTimestamp);

            if (updateResult != 1) {
                log.error("更新支付订单对账状态失败，支付订单号：{}，影响行数：{}",
                        paymentOrder.getPmtDealNo(), updateResult);
                throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR.getCode(), "更新订单状态失败，存在并发修改");
            }

            log.info("更新支付订单对账状态成功，支付订单号：{}，对账状态：{}",
                    paymentOrder.getPmtDealNo(), pmtCompFlag);
        } catch (BusinessException e) {
            log.error("业务异常：支付订单号：{}，错误：{}", checkResultPO.getPmtDealNo(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("保存支付对账结果和更新订单状态异常，支付订单号：{}，错误：{}",
                    checkResultPO.getPmtDealNo(), e.getMessage(), e);
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "保存对账结果失败");
        }
    }

    /**
     * @description: 分页查询支付对账结果
     * @param query 查询条件
     * @return 支付对账结果列表
     * @author: shaoyang.li
     * @date: 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    public List<PaymentCheckResultDTO> selectPaymentCheckResultPage(PaymentCheckResultQuery query) {
        log.info("分页查询支付对账结果，查询条件：{}", query);
        return customizeHwPaymentCheckResultPOMapper.selectPaymentCheckResultPage(query);
    }
}
