/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.validate.chain.atomic.order;

import com.howbuy.dtms.order.dao.po.HwCounterAuditOrderDtlPO;
import com.howbuy.dtms.order.service.commom.annotaion.TradeValidatorAnnotation;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.enums.TradeValidatorEnum;
import com.howbuy.dtms.order.service.commom.exception.ExceptionUtils;
import com.howbuy.dtms.order.service.repository.CounterAuditOrderRepository;
import com.howbuy.dtms.order.service.validate.chain.TradeValidator;
import com.howbuy.dtms.order.service.validate.context.TradeContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 柜台在途撤单校验器
 * <AUTHOR>
 * @date 2025-07-08 20:01:30
 * @since JDK 1.8
 */
@Service
@Slf4j
@TradeValidatorAnnotation(TradeValidatorEnum.COUNTER_IN_TRANSIT_CANCEL)
public class CounterInTransitCancelValidator implements TradeValidator {

    @Resource
    private CounterAuditOrderRepository counterAuditOrderRepository;

    @Override
    public void validate(TradeContext context) {
        String dealNo = String.valueOf(context.getHwDealOrder().getDealNo());
        
        log.info("开始柜台在途撤单校验，订单号：{}", dealNo);
        
        // 查询柜台审核订单明细表，检查是否存在在途撤单
        List<HwCounterAuditOrderDtlPO> counterOrderDtlList = 
            counterAuditOrderRepository.selectInTransitCancelOrderDtl(dealNo);
        
        if (CollectionUtils.isNotEmpty(counterOrderDtlList)) {
            log.warn("订单存在柜台在途撤单，不允许操作，订单号：{}，在途撤单数量：{}", 
                dealNo, counterOrderDtlList.size());
            
            ExceptionUtils.throwValidateException(ExceptionEnum.ORDER_SUBMIT_STATUS_NOT_SUPPORT_OPERATION,
                    "订单存在柜台在途撤单，不允许操作");
        }
        
        log.info("柜台在途撤单校验通过，订单号：{}", dealNo);
    }
}
