/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.batch.capitalfile;

import com.google.common.collect.Lists;
import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.order.dao.po.HwCapitalRefundPO;
import com.howbuy.dtms.order.dao.po.IoCapitalRefundExportPO;
import com.howbuy.dtms.order.dao.po.IoFileProcessRecPO;
import com.howbuy.dtms.order.service.business.batch.AbstractGenerateFileService;
import com.howbuy.dtms.order.service.business.batch.traderacordfile.domain.GenerateTradeFileParam;
import com.howbuy.dtms.order.service.commom.constant.Constants;
import com.howbuy.dtms.order.service.commom.constant.FileConstants;
import com.howbuy.dtms.order.service.commom.enums.IoFileTypeEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.FileGenerateUtil;
import com.howbuy.dtms.order.service.repository.IoCapitalRefundExportRepository;
import com.howbuy.dtms.order.service.repository.HwCapitalRefundRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 资金退款文件生成服务
 * <AUTHOR>
 * @date 2025-07-10 15:29:03
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CapitalRefundFileGenerateService extends AbstractGenerateFileService<IoCapitalRefundExportPO> {

    /**
     * 文件头
     */
    private static final String FILE_HEAD = "订单号|冻结号|香港客户号|基金交易账号|基金代码|基金简称|基金币种|管理人代码|业务类型|支付方式|回款方向|退款金额|退款日期";

    @Resource
    private HwCapitalRefundRepository hwCapitalRefundRepository;

    @Resource
    private IoCapitalRefundExportRepository ioCapitalRefundExportRepository;

    @Override
    protected IoFileTypeEnum getFileTypeEnum() {
        return IoFileTypeEnum.CAPITAL_REFUND_FILE;
    }

    @Override
    protected String getStoreConfig() {
        return FileConstants.CAPITAL_FILE_STORE_CONFIG;
    }

    @Override
    protected List<IoCapitalRefundExportPO> queryIoRecExportList(GenerateTradeFileParam param) {
        IoFileProcessRecPO ioFileProcessRecPO = param.getIoFileProcessRecPO();
        
        // 查询未处理的退款数据
        List<HwCapitalRefundPO> unprocessedRefunds = hwCapitalRefundRepository.selectUnprocessedRefunds();
        if (CollectionUtils.isEmpty(unprocessedRefunds)) {
            String errorMessage = "没有未处理的退款数据，资金退款文件不生成空文件";
            log.error(errorMessage);
            // 发送异常告警
            AlertLogUtil.alert(this.getClass().getName(), errorMessage);
            // 抛出业务异常
            throw new BusinessException(errorMessage);
        }
        
        log.info("查询到未处理的退款数据：{} 条", unprocessedRefunds.size());
        
        // 转换为导出记录
        List<IoCapitalRefundExportPO> exportList = buildExportList(ioFileProcessRecPO, unprocessedRefunds);
        
        return exportList;
    }

    /**
     * @description: 构建导出记录列表
     * @param ioFileProcessRecPO 文件处理记录
     * @param unprocessedRefunds 未处理的退款数据
     * @return 导出记录列表
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private List<IoCapitalRefundExportPO> buildExportList(IoFileProcessRecPO ioFileProcessRecPO, List<HwCapitalRefundPO> unprocessedRefunds) {
        List<IoCapitalRefundExportPO> exportList = new ArrayList<>();
        
        for (HwCapitalRefundPO refund : unprocessedRefunds) {
            IoCapitalRefundExportPO export = new IoCapitalRefundExportPO();
            
            export.setExportDt(ioFileProcessRecPO.getFileDt());
            export.setFileId(ioFileProcessRecPO.getId());
            export.setDealNo(refund.getDealNo());
            export.setRelationalDealNo(refund.getRelationalDealNo());
            export.setHkCustNo(refund.getHkCustNo());
            export.setFundTxAcctNo(refund.getFundTxAcctNo());
            export.setFundCode(refund.getFundCode());
            export.setFundAbbr(refund.getFundAbbr());
            export.setFundCurrency(refund.getFundCurrency());
            export.setFundManCode(refund.getFundManCode());

            BusinessCodeEnum businessCodeEnum = BusinessCodeEnum.getByMCode(refund.getMiddleBusiCode());
            // 储蓄罐支付失败赎回确认成功退款（确认成功退款）业务码 1124 -> 124
            if(BusinessCodeEnum.REDEEM == businessCodeEnum) {
                export.setBusiCode(businessCodeEnum.getMCode().substring(1));
            } else {
                // 买入付款成功撤单退款
                export.setBusiCode(businessCodeEnum.getCode());
            }
            export.setPaymentType(refund.getPaymentType());
            export.setRefundDirection(refund.getRefundDirection());
            export.setRefundAmt(refund.getRefundAmt());
            export.setRefundDt(refund.getRefundDt());
            
            Date now = new Date();
            export.setCreateTimestamp(now);
            export.setUpdateTimestamp(now);
            
            exportList.add(export);
        }
        
        return exportList;
    }

    @Override
    protected String fileHeaderLine(GenerateTradeFileParam param, List<IoCapitalRefundExportPO> rList) {
        return StringUtils.join(rList.size(), Constants.FILE_RN, FILE_HEAD);
    }

    @Override
    protected List<String> getFileLineData(IoCapitalRefundExportPO item) {
        List<String> fieldList = Lists.newArrayList();
        
        // 订单号|冻结号|香港客户号|基金交易账号|基金代码|基金简称|基金币种|管理人代码|业务类型|支付方式|回款方向|退款金额|退款日期
        fieldList.add(FileGenerateUtil.null2String(item.getDealNo()));
        fieldList.add(FileGenerateUtil.null2String(item.getRelationalDealNo()));
        fieldList.add(FileGenerateUtil.null2String(item.getHkCustNo()));
        fieldList.add(FileGenerateUtil.null2String(item.getFundTxAcctNo()));
        fieldList.add(FileGenerateUtil.null2String(item.getFundCode()));
        fieldList.add(FileGenerateUtil.null2String(item.getFundAbbr()));
        fieldList.add(FileGenerateUtil.null2String(item.getFundCurrency()));
        fieldList.add(FileGenerateUtil.null2String(item.getFundManCode()));
        fieldList.add(FileGenerateUtil.null2String(item.getBusiCode()));
        fieldList.add(FileGenerateUtil.null2String(item.getPaymentType()));
        fieldList.add(FileGenerateUtil.null2String(item.getRefundDirection()));
        fieldList.add(FileGenerateUtil.null2String(item.getRefundAmt()));
        fieldList.add(FileGenerateUtil.null2String(item.getRefundDt()));
        
        return fieldList;
    }

    @Override
    protected void writeFile(IoFileProcessRecPO ioFileProcessRecPO, String fileContent) throws IOException {
        super.write(ioFileProcessRecPO, fileContent);
    }

    @Override
    protected void writeEmptyDateFile(IoFileProcessRecPO ioFileProcessRecPO) throws IOException {
        String emptyContent = StringUtils.join(0, Constants.FILE_RN, FILE_HEAD);
        super.write(ioFileProcessRecPO, emptyContent);
    }

    /**
     * @description: 文件写入成功后的处理
     * @param ioFileProcessRecPO 文件处理记录
     * @param exportList 导出记录列表
     * @return 处理结果
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    @Override
    protected boolean afterSuccessWrite(IoFileProcessRecPO ioFileProcessRecPO, List<IoCapitalRefundExportPO> exportList) {
        if (CollectionUtils.isEmpty(exportList)) {
            log.info("导出记录为空，无需后续处理");
            return true;
        }

        try {
            log.info("文件写入成功后的处理，导出记录数：{}", exportList.size());

            // 获取订单号列表
            List<Long> dealNoList = exportList.stream()
                    .map(IoCapitalRefundExportPO::getDealNo)
                    .collect(Collectors.toList());

            // 调用Repository层的事务方法处理批量插入和更新
            return ioCapitalRefundExportRepository.saveExportListAndUpdateStatus(exportList, dealNoList);

        } catch (Exception e) {
            String errorMessage = String.format("资金退款文件写入成功后处理异常，文件ID：%s，导出记录数：%d，异常信息：%s", 
                ioFileProcessRecPO.getId(), exportList.size(), e.getMessage());
            log.error(errorMessage, e);
            // 发送异常告警
            AlertLogUtil.alert(this.getClass().getName(), errorMessage);
            return false;
        }
    }
}
