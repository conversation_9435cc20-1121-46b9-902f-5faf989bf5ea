package com.howbuy.dtms.order.service.commom.utils.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> jiong.peng
 * @Description:
 * @Date: 2020-11-11 15:16
 */
public class ExcelListener<T> extends AnalysisEventListener<T> {

    private List<T> list = new LinkedList<>();

    @Override
    public void invoke(T data, AnalysisContext context) {
        //数据存储到list，供批量处理，或后续自己业务逻辑处理。
        list.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        //解析结束销毁不用的资源
    }

    public List<T> getList() {
        return Collections.unmodifiableList(list);
    }
}
