/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.repository;

import com.howbuy.dtms.order.dao.mapper.mysql.customize.HwSubPaidDtlInfoCustomizeMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.order.HwSubPaidDtlInfoMapper;
import com.howbuy.dtms.order.dao.po.HwSubPaidDtlInfoPO;
import com.howbuy.dtms.order.dao.po.HwSubPaidSumInfoPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 认缴实缴明细信息Repository
 * @date 2025/4/7 14:25
 * @since JDK 1.8
 */
@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class HwSubPaidDtlInfoRepository {

    @Resource
    private HwSubPaidDtlInfoCustomizeMapper hwSubPaidDtlInfoCustomizeMapper;

    @Resource
    private HwSubPaidDtlInfoMapper hwSubPaidDtlInfoMapper;

    /**
     * @description: 根据订单号、基金代码、基金交易账号和香港客户号查询认缴实缴明细信息
     * @param dealNo 订单号
     * @param fundCode	基金编码
     * @param fundTxAcctNo	基金交易账号
     * @param hkCustNo 香港客户号
     * @return com.howbuy.dtms.order.dao.po.HwSubPaidDtlInfoPO
     * @author: jinqing.rao
     * @date: 2025/6/12 19:50
     * @since JDK 1.8
     */
    public HwSubPaidDtlInfoPO selectByDealNoAndFundCodeAndFundTxAcctNo(Long dealNo, String fundCode, String fundTxAcctNo, String hkCustNo) {
       return hwSubPaidDtlInfoCustomizeMapper.selectByDealNoAndFundCodeAndFundTxAcctNo(dealNo, fundCode, fundTxAcctNo, hkCustNo);
    }

    /**
     * @description: 插入认缴实缴明细信息
     * @param hwSubPaidDtlInfoPO 认缴实缴明细信息
     * @return void
     * @author: jinqing.rao
     * @date: 2025/6/12 20:06
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void insertSelective(HwSubPaidDtlInfoPO hwSubPaidDtlInfoPO) {
        hwSubPaidDtlInfoMapper.insertSelective(hwSubPaidDtlInfoPO);
    }
} 