/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.trade.buy;

import com.howbuy.dtms.order.client.domain.request.buy.CanBuyRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.buy.FundCanBuyResponse;
import com.howbuy.dtms.order.client.facade.trade.buy.CrmFundCanBuyFacade;
import com.howbuy.dtms.order.service.service.trade.buy.CanBuyService;
import com.howbuy.dtms.order.service.service.trade.buy.CrmCanBuyService;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: CRM基金是否可购买接口
 * <AUTHOR>
 * @date 2025/6/9 9:01
 * @since JDK 1.8
 */
@DubboService
public class CrmFundCanBuyFacadeImpl implements CrmFundCanBuyFacade {

    @Resource
    private CrmCanBuyService crmCanBuyService;

    @Override
    public Response<FundCanBuyResponse> execute(CanBuyRequest canBuyRequest) {
        //参数校验
        ParamsValidator.validate(canBuyRequest, "hkCustNo", "fundCode");
        crmCanBuyService.process(canBuyRequest);
        return Response.ok();
    }
}
