package com.howbuy.dtms.order.service.provider.dubbo.query.payment;

import com.howbuy.dtms.order.client.domain.request.payment.QueryPaymentCheckResultRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.QueryPaymentCheckResultResponse;
import com.howbuy.dtms.order.client.facade.query.payment.QueryPaymentCheckResultFacade;
import com.howbuy.dtms.order.service.service.query.pay.QueryPaymentCheckResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * 支付对账结果查询接口实现
 *
 * <AUTHOR>
 * @date 2025-07-07 17:13:51
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class QueryPaymentCheckResultFacadeImpl implements QueryPaymentCheckResultFacade {

    @Resource
    private QueryPaymentCheckResultService queryPaymentCheckResultService;

    /**
     * 支付对账结果查询
     *
     * @param request 查询请求参数
     * @return 查询结果
     * <AUTHOR>
     * @date 2025-07-07 17:13:51
     * @since JDK 1.8
     */
    @Override
    public Response<QueryPaymentCheckResultResponse> execute(QueryPaymentCheckResultRequest request) {
        QueryPaymentCheckResultResponse response = queryPaymentCheckResultService.queryPaymentCheckResult(request);
        return Response.ok(response);
    }
}
