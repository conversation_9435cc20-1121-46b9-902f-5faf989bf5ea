/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job.domain;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/5/23 13:43
 * @since JDK 1.8
 */
@Setter
@Getter
public class SubDealOrderInitDTO implements Serializable {

    private static final long serialVersionUID = 3074018824828103324L;

    @ExcelProperty(value = "业务类型")
    private String businessType;

    @ExcelProperty(value = "中台业务代码")
    private String middleBusiCode;

    @ExcelProperty(value = "订单状态")
    private String orderStatus;

    @ExcelProperty(value = "预约单号")
    private String prebookDealNo;

    @ExcelProperty(value = "香港客户号")
    private String hkCustNo;

    @ExcelProperty(value = "客户名称")
    private String custChineseName;

    @ExcelProperty(value = "产品代码")
    private String productCode;

    @ExcelProperty(value = "产品简称")
    private String productName;

    @ExcelProperty(value = "币种")
    private String currency;


    @ExcelProperty(value = "申请日期")
    private String appDt;

    @ExcelProperty(value = "申请时间")
    private String appTm;


    @ExcelProperty(value = "支付方式")
    private String paymentTypeList;


    @ExcelProperty(value = "付款状态")
    private String payStatus;


    @ExcelProperty(value = "打款凭证状态")
    private String payVoucherStatus;


    @ExcelProperty(value = "投资者资质")
    private String qualificationType;

    @ExcelProperty(value = "客户风险等级")
    private String custRiskLevel;


    @ExcelProperty(value = "首次购买标识")
    private String firstBuyFlag;


    @ExcelProperty(value = "是否同意换汇")
    private String isAgreeCurrencyExchange;


    @ExcelProperty(value = "交易渠道")
    private String tradeChannel;


    @ExcelProperty(value = "成单方式")
    private String orderFormType;


    @ExcelProperty(value = "是否支持预约")
    private String supportPrebookFlag;

    @ExcelProperty(value = "客户类型")
    private String invstType;


    @ExcelProperty(value = "证件号")
    private String idNoMask;

    @ExcelProperty(value = "申请状态")
    private String appStatus;

    @ExcelProperty(value = "上报状态")
    private String submitStatus;

    @ExcelProperty(value = "申请金额")
    private String appAmt;

    @ExcelProperty(value = "净申请金额(认缴金额)")
    private String netAppAmt;

    @ExcelProperty(value = "预估手续费(认缴手续费)")
    private String estimateFee;

    @ExcelProperty(value = "费率")
    private String feeRate;

    @ExcelProperty(value = "折扣率")
    private String discountRate;

    @ExcelProperty(value = "预计上报日期")
    private String preSubmitTaDt;

    @ExcelProperty(value = "预计上报时间")
    private String preSubmitTaTm;

    @ExcelProperty(value = "开放日期")
    private String openDt;


    @ExcelProperty(value = "交易日期")
    private String taTradeDt;


    @ExcelProperty(value = "确认日期")
    private String ackDt;


    @ExcelProperty(value = "确认金额")
    private String ackAmt;


    @ExcelProperty(value = "确认份额")
    private String ackVol;



    @ExcelProperty(value = "确认净值")
    private String ackNav;

    @ExcelProperty(value = "确认手续费")
    private String fee;


    @ExcelProperty(value = "净值日期")
    private String ackNavDt;

}
