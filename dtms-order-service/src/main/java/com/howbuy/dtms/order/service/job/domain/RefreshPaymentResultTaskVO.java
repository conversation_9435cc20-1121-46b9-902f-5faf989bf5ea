/**
 * Copyright (c) 2025, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 支付状态刷新定时任务消息VO
 * <AUTHOR>
 * @date 2025-07-04 15:08:30
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefreshPaymentResultTaskVO extends BaseTaskMessageVO {
    
    // 由于是定时触发任务，消息体不包含业务数据，仅作为批处理任务的启动信号
    // 如果后续需要支持特定参数，可在此添加字段
}
