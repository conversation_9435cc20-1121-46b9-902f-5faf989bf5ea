/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.query.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.piggytradeapp.QueryPiggyTradeAppResultRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.QueryPiggyTradeAppResultResponse;
import com.howbuy.dtms.order.client.facade.query.piggytradeapp.QueryPiggyTradeAppResultFacade;
import com.howbuy.dtms.order.service.service.query.piggytradeapp.QueryPiggyTradeAppResultService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 查询储蓄罐交易申请结果接口实现
 * <AUTHOR>
 * @date 2025-07-15 19:32:38
 * @since JDK 1.8
 */
@Slf4j
@DubboService
public class QueryPiggyTradeAppResultFacadeImpl implements QueryPiggyTradeAppResultFacade {

    @Resource
    private QueryPiggyTradeAppResultService queryPiggyTradeAppResultService;

    /**
     * @description: 查询储蓄罐交易申请结果
     * @param request 查询请求参数
     * @return Response<QueryPiggyTradeAppResultResponse> 查询结果
     * <AUTHOR>
     * @date 2025-07-15 19:32:38
     * @since JDK 1.8
     */
    @Override
    public Response<QueryPiggyTradeAppResultResponse> execute(QueryPiggyTradeAppResultRequest request) {
        log.info("查询储蓄罐交易申请结果开始，请求参数：{}", request);
        
        try {
            QueryPiggyTradeAppResultResponse response = queryPiggyTradeAppResultService.queryListByImportAppId(request);
            log.info("查询储蓄罐交易申请结果成功，返回记录数：{}", response.getList() != null ? response.getList().size() : 0);
            return Response.ok(response);
        } catch (Exception e) {
            log.error("查询储蓄罐交易申请结果失败，请求参数：{}", request, e);
            return Response.fail("查询失败：" + e.getMessage());
        }
    }
}
