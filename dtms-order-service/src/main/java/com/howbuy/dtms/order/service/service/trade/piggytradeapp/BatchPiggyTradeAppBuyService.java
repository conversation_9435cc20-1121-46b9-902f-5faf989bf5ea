/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.service.trade.piggytradeapp;

import com.howbuy.dtms.common.enums.*;
import com.howbuy.dtms.order.client.domain.request.piggytradeapp.BatchPiggyTradeAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.BatchPiggyTradeAppBuyResponse;
import com.howbuy.dtms.order.dao.po.HwPiggyTradeAppImport;
import com.howbuy.dtms.order.service.commom.constant.Constants;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.exception.ValidateException;
import com.howbuy.dtms.order.service.commom.utils.AssertUtils;
import com.howbuy.dtms.order.service.commom.utils.DateUtils;
import com.howbuy.dtms.order.service.commom.utils.TradeUtils;
import com.howbuy.dtms.order.service.outerservice.dtms.settle.DtmsSettleOuterService;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.QueryFundInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.CustFeeDiscountDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.FundBasicInfoDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.FundFeeRateDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.OpenDtDTO;
import com.howbuy.dtms.order.service.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.hkacc.domain.HkCustInfoDTO;
import com.howbuy.dtms.order.service.repository.HwPiggyTradeAppImportRepository;
import com.howbuy.dtms.product.client.enums.CollectRecipientEnum;
import com.howbuy.dtms.product.client.enums.FeeTypeEnum;
import com.howbuy.dtms.settle.client.facade.query.vo.CounterCloseStatusVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 批量储蓄罐交易申请买入业务服务
 * @date 2025-07-16 14:45:30
 * @since JDK 1.8
 */
@Service
@Slf4j
public class BatchPiggyTradeAppBuyService {

    @Resource
    private QueryFundInfoOuterService queryFundInfoOuterService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    private DtmsSettleOuterService dtmsSettleOuterService;

    @Resource
    private HwPiggyTradeAppImportRepository hwPiggyTradeAppImportRepository;

    /**
     * @param request 请求参数
     * @return com.howbuy.dtms.order.client.domain.response.piggytradeapp.BatchPiggyTradeAppBuyResponse
     * @description: 批量储蓄罐交易申请买入处理
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    public BatchPiggyTradeAppBuyResponse process(BatchPiggyTradeAppBuyRequest request) {
        log.info("批量储蓄罐交易申请买入开始处理，请求参数：{}", request);

        // 参数校验
        validateParams(request);

        // 前置查询
        PreQueryResult preQueryResult = preQuery(request);

        // 构建储蓄罐交易申请导入记录
        List<HwPiggyTradeAppImport> importRecords = buildImportRecords(request, preQueryResult);

        // 数据事务处理
        processTransaction(request, importRecords);

        BatchPiggyTradeAppBuyResponse response = new BatchPiggyTradeAppBuyResponse();

        log.info("批量储蓄罐交易申请买入处理完成");
        return response;
    }

    /**
     * @param request 请求参数
     * @description: 参数校验
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private void validateParams(BatchPiggyTradeAppBuyRequest request) {
        AssertUtils.nonNullParam(request.getPiggyTradeAppBuyList(), "储蓄罐申请买入列表不能为空");
        AssertUtils.isTrue(request.getPiggyTradeAppBuyList().size() <= 200, "储蓄罐申请买入列表大小不能超过200");

        // 校验每个PiggyTradeAppBuyItem的字段
        for (BatchPiggyTradeAppBuyRequest.PiggyTradeAppBuyItem item : request.getPiggyTradeAppBuyList()) {
            // 非空校验
            AssertUtils.nonNullParam(item.getImportAppId(), "导入申请id不能为空");
            AssertUtils.nonNullParam(item.getPiggyAppSource(), "储蓄罐申请来源不能为空");
            AssertUtils.nonNullParam(item.getHkCustNo(), "香港客户号不能为空");
            AssertUtils.nonNullParam(item.getFundTxAcctNo(), "基金交易账号不能为空");
            AssertUtils.nonNullParam(item.getAppAmt(), "申请金额不能为空");

            // 校验piggyAppSource枚举值是否正确
            if (!PiggyAppSourceEnum.isValidCode(item.getPiggyAppSource())) {
                throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(),
                        "储蓄罐申请来源值不正确：" + item.getPiggyAppSource());
            }
        }

        // 校验importAppId是否已存在
        List<String> importAppIds = request.getPiggyTradeAppBuyList().stream()
                .map(BatchPiggyTradeAppBuyRequest.PiggyTradeAppBuyItem::getImportAppId)
                .collect(Collectors.toList());

        List<HwPiggyTradeAppImport> existingRecords = hwPiggyTradeAppImportRepository.queryByImportAppIds(importAppIds);
        if (CollectionUtils.isNotEmpty(existingRecords)) {
            List<String> validRecords = new ArrayList<>();
            List<String> failedRecords = new ArrayList<>();

            for (HwPiggyTradeAppImport record : existingRecords) {
                if (PiggyTradeAppGenerateEnum.GENERATE_FAILED.getCode().equals(record.getIsGenerated())) {
                    failedRecords.add(record.getImportAppId());
                } else {
                    validRecords.add(record.getImportAppId());
                }
            }

            if (CollectionUtils.isNotEmpty(validRecords)) {
                throw new ValidateException(ExceptionEnum.SYSTEM_ERROR.getCode(),
                        "申请Id" + String.join(",", validRecords) + "已存在");
            }
        }
    }

    /**
     * @param request 请求参数
     * @return PreQueryResult 前置查询结果
     * @description: 前置查询
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private PreQueryResult preQuery(BatchPiggyTradeAppBuyRequest request) {
        PreQueryResult result = new PreQueryResult();

        // 查询当前可购买的储蓄罐基金信息
        String currentDate = DateUtils.getCurrentDate();
        FundBasicInfoDTO fundInfo = queryFundInfoOuterService.querySupportBuyPiggyBuyFundList(currentDate);
        AssertUtils.nonNullParam(fundInfo, "未查到可购买的储蓄罐基金产品");
        result.setFundInfo(fundInfo);

        // 去重客户号，批量查询客户信息
        List<String> hkCustNos = request.getPiggyTradeAppBuyList().stream()
                .map(BatchPiggyTradeAppBuyRequest.PiggyTradeAppBuyItem::getHkCustNo)
                .distinct()
                .collect(Collectors.toList());

        List<HkCustInfoDTO> custInfoList = hkCustInfoOuterService.getHkCustInfoList(hkCustNos);
        if (CollectionUtils.isEmpty(custInfoList)) {
            throw new ValidateException(ExceptionEnum.ACCOUNT_NOT_EXISTS);
        }

        Map<String, HkCustInfoDTO> custInfoMap = custInfoList.stream()
                .collect(Collectors.toMap(HkCustInfoDTO::getHkCustNo, custInfo -> custInfo));
        result.setCustInfoMap(custInfoMap);

        // 查询开放日信息，获取预计上报日
        String preSubmitTaDt = queryPreSubmitTaDt(fundInfo);
        result.setPreSubmitTaDt(preSubmitTaDt);

        // 查询费率
        List<String> invstTypes = custInfoList.stream()
                .map(HkCustInfoDTO::getInvstType)
                .distinct()
                .collect(Collectors.toList());

        Map<String, List<FundFeeRateDTO>> feeRateMap = queryFeeRateMap(fundInfo.getFundCode(), invstTypes);
        result.setFeeRateMap(feeRateMap);

        // 批量查询客户申请折扣
        Map<String, CustFeeDiscountDTO> custFeeDiscountMap = queryCustFeeDiscountMap(fundInfo.getFundCode(), hkCustNos);
        result.setCustFeeDiscountMap(custFeeDiscountMap);

        return result;
    }

    /**
     * @param fundInfo 基金信息
     * @return String 预计上报日期
     * @description: 查询预计上报日期
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private String queryPreSubmitTaDt(FundBasicInfoDTO fundInfo) {
        // 判断基金代码是否已收市
        CounterCloseStatusVO counterCloseStatus = dtmsSettleOuterService.queryCounterCloseStatus(fundInfo.getFundCode());
        boolean isClosed = YesOrNoEnum.YES.getValue().equals(counterCloseStatus.getStat());

        // 计算申请时间
        String currentDate = DateUtils.getCurrentDate();
        String orderEndTime = fundInfo.getOrderEndTm();
        int adjustMinutes = isClosed ? 1 : -1;
        String appTime = DateUtils.adjustTimeByMinutes(orderEndTime, adjustMinutes);
        AssertUtils.notEmpty(appTime, "申请时间不能为空", ExceptionEnum.PARAMS_ERROR);

        // 查询开放日信息
        OpenDtDTO openDtDTO = queryFundInfoOuterService.QueryOpenDtInfoRequest(
                fundInfo.getFundCode(),
                BusinessCodeEnum.PURCHASE.getCode(),
                currentDate,
                appTime,
                Constants.ZERO_INTERVAL
        );
        if (Objects.isNull(openDtDTO)) {
            throw new BusinessException(ExceptionEnum.PRODUCT_NOT_IN_RESERVATION_PERIOD);
        }

        return openDtDTO.getPreSubmitTaDt();
    }

    /**
     * @param fundCode   基金代码
     * @param invstTypes 投资者类型列表
     * @return Map<String, List < FundFeeRateDTO>> 费率映射
     * @description: 查询费率映射
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private Map<String, List<FundFeeRateDTO>> queryFeeRateMap(String fundCode, List<String> invstTypes) {
        Map<String, List<FundFeeRateDTO>> feeRateMap = new HashMap<>();

        for (String invstType : invstTypes) {
            List<FundFeeRateDTO> feeRateList = queryFundInfoOuterService.queryFundFeeRateList(
                    fundCode,
                    BusinessCodeEnum.PURCHASE.getCode(),
                    invstType,
                    CollectRecipientEnum.HOWBUY.getCode()
            );
            feeRateMap.put(invstType, feeRateList);
        }

        return feeRateMap;
    }

    /**
     * @param fundCode  基金代码
     * @param hkCustNos 客户号列表
     * @return Map<String, CustFeeDiscountDTO> 客户申请折扣映射
     * @description: 查询客户申请折扣映射
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private Map<String, CustFeeDiscountDTO> queryCustFeeDiscountMap(String fundCode, List<String> hkCustNos) {
        String currentDate = DateUtils.getCurrentDate();
        List<CustFeeDiscountDTO> custFeeDiscountList = queryFundInfoOuterService.queryCustFeeDiscount(
                hkCustNos,
                fundCode, FeeTypeEnum.SUB_FEE.getCode(),
                currentDate
        );

        if (CollectionUtils.isEmpty(custFeeDiscountList)) {
            return new HashMap<>();
        }

        return custFeeDiscountList.stream()
                .collect(Collectors.toMap(CustFeeDiscountDTO::getHkCustNo, discount -> discount));
    }

    /**
     * @param request        请求参数
     * @param preQueryResult 前置查询结果
     * @return List<HwPiggyTradeAppImport> 导入记录列表
     * @description: 构建储蓄罐交易申请导入记录
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private List<HwPiggyTradeAppImport> buildImportRecords(BatchPiggyTradeAppBuyRequest request, PreQueryResult preQueryResult) {
        List<HwPiggyTradeAppImport> importRecords = new ArrayList<>();
        String currentDate = DateUtils.getCurrentDate();

        for (BatchPiggyTradeAppBuyRequest.PiggyTradeAppBuyItem item : request.getPiggyTradeAppBuyList()) {
            HwPiggyTradeAppImport record = new HwPiggyTradeAppImport();

            // 基本信息
            record.setImportAppId(item.getImportAppId());
            record.setFundTxAcctNo(item.getFundTxAcctNo());
            record.setImportDt(currentDate);
            record.setCreator(Constants.SYSTEM_OPERATOR);
            record.setRelationalDealNo(StringUtils.isNotBlank(item.getRelationalDealNo()) ? Long.valueOf(item.getRelationalDealNo()) : null);
            record.setPiggyAppSource(item.getPiggyAppSource());

            // 客户信息
            HkCustInfoDTO custInfo = preQueryResult.getCustInfoMap().get(item.getHkCustNo());
            if (custInfo == null) {
                throw new ValidateException(ExceptionEnum.ACCOUNT_NOT_EXISTS.getCode(), "客户号" + item.getHkCustNo() + "不存在");
            }
            record.setHkCustNo(custInfo.getHkCustNo());
            record.setCustName(StringUtils.isNotEmpty(custInfo.getCustChineseName()) ? custInfo.getCustChineseName() : custInfo.getCustEnName());

            // 基金信息
            FundBasicInfoDTO fundInfo = preQueryResult.getFundInfo();
            record.setProductCode(fundInfo.getFundCode());
            record.setProductName(fundInfo.getFundName());
            record.setCurrency(fundInfo.getCurrency());
            record.setMiddleBusiCode(BusinessCodeEnum.PURCHASE.getMCode());
            record.setPreSubmitTaDt(preQueryResult.getPreSubmitTaDt());

            // 申请金额
            record.setAppAmt(item.getAppAmt());

            // 支付方式
            String paymentType = getPaymentType(item.getPiggyAppSource());
            record.setPaymentType(paymentType);

            // 计算手续费、买入金额、折扣率
            calculateFeeAndAmount(record, custInfo, fundInfo, preQueryResult, item.getAppAmt());

            // 校验买入金额
            if (record.getBuyAmt().compareTo(BigDecimal.ZERO) <= 0) {
                throw new ValidateException(ExceptionEnum.SYSTEM_ERROR.getCode(),
                        String.format("申请Id%s的申请金额%s，手续费%s，买入金额小于0",
                                item.getImportAppId(), item.getAppAmt(), record.getFee()));
            }

            importRecords.add(record);
        }

        return importRecords;
    }

    /**
     * @param piggyAppSource 储蓄罐申请来源
     * @return String 支付方式
     * @description: 获取支付方式
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private String getPaymentType(String piggyAppSource) {
        if (PiggyAppSourceEnum.EXCEL.getCode().equals(piggyAppSource) || PiggyAppSourceEnum.AVAILABLE_BALANCE.getCode().equals(piggyAppSource)) {
            return PayMethodEnum.ELECTRIC_REMITTANCE.getCode();
        } else {
            return PayMethodEnum.TRANSFER.getCode();
        }
    }

    /**
     * @param record         导入记录
     * @param custInfo       客户信息
     * @param fundInfo       基金信息
     * @param preQueryResult 前置查询结果
     * @param appAmt         申请金额
     * @description: 计算手续费、买入金额、折扣率
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private void calculateFeeAndAmount(HwPiggyTradeAppImport record, HkCustInfoDTO custInfo,
                                       FundBasicInfoDTO fundInfo, PreQueryResult preQueryResult, BigDecimal appAmt) {
        // 获取客户申请折扣率
        CustFeeDiscountDTO custFeeDiscount = preQueryResult.getCustFeeDiscountMap().get(custInfo.getHkCustNo());
        BigDecimal discountRate = custFeeDiscount != null ? custFeeDiscount.getDiscountRate() : BigDecimal.ONE;
        discountRate = discountRate.min(BigDecimal.ONE);
        record.setDiscountRate(discountRate);

        // 获取交易费率
        BigDecimal feeRate = getFeeRate(custInfo, preQueryResult.getFeeRateMap(), appAmt);
        AssertUtils.nonNullParam(feeRate, "未查询到交易费率");

        // 根据币种确定精度
        int scale = TradeUtils.isJPYCurrency(fundInfo.getCurrency()) ? Constants.JPY_AMOUNT_SCALE : Constants.AMOUNT_SCALE;

        BigDecimal fee;
        BigDecimal buyAmt;

        // 根据费用计算方式计算
        if (Constants.FEE_CAL_MODE_OUT.equals(fundInfo.getFeeCalMode())) { // 外扣法
            // 外扣法
            buyAmt = appAmt.divide(BigDecimal.ONE.add(feeRate.multiply(discountRate)), scale, RoundingMode.DOWN);
            fee = appAmt.subtract(buyAmt);
        } else {
            // 内扣法
            buyAmt = appAmt;
            fee = buyAmt.multiply(feeRate).multiply(discountRate).setScale(scale, RoundingMode.DOWN);
        }

        record.setFee(fee);
        record.setBuyAmt(buyAmt);
    }

    /**
     * @param custInfo   客户信息
     * @param feeRateMap 费率映射
     * @param appAmt     申请金额
     * @return BigDecimal 交易费率
     * @description: 获取交易费率
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private BigDecimal getFeeRate(HkCustInfoDTO custInfo, Map<String, List<FundFeeRateDTO>> feeRateMap, BigDecimal appAmt) {
        List<FundFeeRateDTO> feeRateList = feeRateMap.get(custInfo.getInvstType());
        if (CollectionUtils.isEmpty(feeRateList)) {
            return null;
        }

        // 过滤费率方式为按金额获取的费率
        List<FundFeeRateDTO> amtFeeRateList = feeRateList.stream()
                .filter(rate -> Constants.GET_FEE_RATE_METHOD_AMOUNT.equals(rate.getGetFeeRateMethod()))
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(amtFeeRateList)) {
            return null;
        }

        // 根据申请金额匹配费率
        for (FundFeeRateDTO feeRate : amtFeeRateList) {
            if (appAmt.compareTo(feeRate.getMinFeeAmt()) >= 0 &&
                    appAmt.compareTo(feeRate.getMaxFeeAmt()) <= 0) {
                return feeRate.getFeeRate();
            }
        }

        return null;
    }


    /**
     * @param request       请求参数
     * @param importRecords 导入记录列表
     * @description: 数据事务处理
     * @author: shaoyang.li
     * @date: 2025-07-16 14:45:30
     * @since JDK 1.8
     */
    private void processTransaction(BatchPiggyTradeAppBuyRequest request, List<HwPiggyTradeAppImport> importRecords) {
        // 获取生成失败的记录
        List<String> importAppIds = request.getPiggyTradeAppBuyList().stream()
                .map(BatchPiggyTradeAppBuyRequest.PiggyTradeAppBuyItem::getImportAppId)
                .collect(Collectors.toList());

        List<HwPiggyTradeAppImport> existingRecords = hwPiggyTradeAppImportRepository.queryByImportAppIds(importAppIds);
        List<String> failedImportAppIds = existingRecords.stream()
                .filter(record -> PiggyTradeAppGenerateEnum.GENERATE_FAILED.getCode().equals(record.getIsGenerated()))
                .map(HwPiggyTradeAppImport::getImportAppId)
                .collect(Collectors.toList());

        // 在一个事务中批量处理记录（先删除失败记录，再插入新记录）
        hwPiggyTradeAppImportRepository.batchProcessRecords(failedImportAppIds, importRecords);
    }

    /**
     * 前置查询结果
     */
    private static class PreQueryResult {
        private FundBasicInfoDTO fundInfo;
        private Map<String, HkCustInfoDTO> custInfoMap;
        private String preSubmitTaDt;
        private Map<String, List<FundFeeRateDTO>> feeRateMap;
        private Map<String, CustFeeDiscountDTO> custFeeDiscountMap;

        public FundBasicInfoDTO getFundInfo() {
            return fundInfo;
        }

        public void setFundInfo(FundBasicInfoDTO fundInfo) {
            this.fundInfo = fundInfo;
        }

        public Map<String, HkCustInfoDTO> getCustInfoMap() {
            return custInfoMap;
        }

        public void setCustInfoMap(Map<String, HkCustInfoDTO> custInfoMap) {
            this.custInfoMap = custInfoMap;
        }

        public String getPreSubmitTaDt() {
            return preSubmitTaDt;
        }

        public void setPreSubmitTaDt(String preSubmitTaDt) {
            this.preSubmitTaDt = preSubmitTaDt;
        }

        public Map<String, List<FundFeeRateDTO>> getFeeRateMap() {
            return feeRateMap;
        }

        public void setFeeRateMap(Map<String, List<FundFeeRateDTO>> feeRateMap) {
            this.feeRateMap = feeRateMap;
        }

        public Map<String, CustFeeDiscountDTO> getCustFeeDiscountMap() {
            return custFeeDiscountMap;
        }

        public void setCustFeeDiscountMap(Map<String, CustFeeDiscountDTO> custFeeDiscountMap) {
            this.custFeeDiscountMap = custFeeDiscountMap;
        }
    }
}
