/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.github.pagehelper.PageHelper;
import com.google.common.base.Throwables;
import com.howbuy.dtms.common.enums.NewRecStatEnum;
import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.client.domain.response.PageVo;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.dao.query.PayingOrderQuery;
import com.howbuy.dtms.order.service.business.batch.BatchOperateExecutor;
import com.howbuy.dtms.order.service.business.batch.QueryExecuteService;
import com.howbuy.dtms.order.service.business.payment.ProcessPmtResultBusiness;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.job.domain.RefreshPaymentResultTaskVO;
import com.howbuy.dtms.order.service.outerservice.pay.PayOuterService;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwDealOrderDtlRepository;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description: 支付状态刷新定时任务
 * <AUTHOR>
 * @date 2025-07-04 15:08:30
 * @since JDK 1.8
 */
@Slf4j
@Component
public class RefreshPaymentResultJob extends AbstractBatchMessageJob<RefreshPaymentResultTaskVO> {

    @Value("${TOPIC.DTMS_ORDER_EC_TASK_REFRESH_PAY_RESULT}")
    private String topic;

    @Resource
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Resource
    private HwDealOrderRepository hwDealOrderRepository;

    @Resource
    private HwDealOrderDtlRepository hwDealOrderDtlRepository;

    @Resource
    private PayOuterService payOuterService;

    @Resource
    private ProcessPmtResultBusiness processPmtResultBusiness;

    @Resource
    private BatchOperateExecutor batchOperateExecutor;

    /**
     * 查询时间窗口：10分钟前到180天前
     */
    private static final int QUERY_START_MINUTES = 10;
    private static final int QUERY_END_DAYS = 180;

    @Override
    protected String getTopicName() {
        return topic;
    }

    @Override
    protected void doProcessJob(RefreshPaymentResultTaskVO message) {
        log.info("支付状态刷新定时任务开始执行");

        // 构建查询条件
        PayingOrderQuery query = buildQueryCondition();

        // 使用批处理执行器进行分页处理
        int totalProcessed = batchOperateExecutor.queryBeforeExecute(query,
                new QueryExecuteService<PayingOrderQuery, HwPaymentOrderPO>() {
                    @Override
                    public PageVo<HwPaymentOrderPO> queryPage(PayingOrderQuery queryCondition) {
                        return queryPayingOrdersPage(queryCondition);
                    }

                    @Override
                    public int execute(List<HwPaymentOrderPO> paymentOrders) {
                        return processPaymentOrders(paymentOrders);
                    }
                });

        log.info("支付状态刷新定时任务执行完成，共处理订单数量：{}", totalProcessed);
    }

    /**
     * @description: 构建查询条件
     * @return 查询条件
     * @author: shaoyang.li
     * @date: 2025-07-04 15:08:30
     * @since JDK 1.8
     */
    private PayingOrderQuery buildQueryCondition() {
        PayingOrderQuery query = new PayingOrderQuery();
        
        // 设置支付状态为付款中
        query.setTxPmtFlag(TxPmtFlagEnum.PAYING.getCode());
        
        // 设置记录状态为有效
        query.setRecStat(NewRecStatEnum.NORMAL.getValue());
        
        // 设置时间窗口：当前时间10分钟前到180天前
        Date currentTime = new Date();
        
        // 结束时间：当前时间10分钟前
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(currentTime);
        endCalendar.add(Calendar.MINUTE, -QUERY_START_MINUTES);
        query.setUpdateTimeEnd(endCalendar.getTime());
        
        // 开始时间：180天前
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(currentTime);
        startCalendar.add(Calendar.DAY_OF_YEAR, -QUERY_END_DAYS);
        query.setUpdateTimeStart(startCalendar.getTime());
        
        log.info("构建查询条件完成，支付状态：{}，记录状态：{}，更新时间范围：{} - {}",
                query.getTxPmtFlag(), query.getRecStat(), 
                query.getUpdateTimeStart(), query.getUpdateTimeEnd());
        
        return query;
    }

    /**
     * @description: 分页查询支付中状态的支付订单（使用PageHelper）
     * @param query 查询条件
     * @return 分页结果
     * @author: shaoyang.li
     * @date: 2025-07-04 15:08:30
     * @since JDK 1.8
     */
    private PageVo<HwPaymentOrderPO> queryPayingOrdersPage(PayingOrderQuery query) {
        log.info("分页查询支付中状态的支付订单，页号：{}，每页大小：{}", query.getPage(), query.getSize());

        // 使用PageHelper进行分页
        PageHelper.startPage(query.getPage(), query.getSize());

        List<HwPaymentOrderPO> paymentOrders = hwPaymentOrderRepository.selectPayingOrders(
                query.getTxPmtFlag(),
                query.getRecStat(),
                query.getUpdateTimeStart(),
                query.getUpdateTimeEnd());

        // 构建分页结果
        PageVo<HwPaymentOrderPO> pageVo = new PageVo<>();
        pageVo.setList(paymentOrders);

        log.info("分页查询支付中状态的支付订单完成，当前页数据量：{}",
                CollectionUtils.isEmpty(paymentOrders) ? 0 : paymentOrders.size());

        return pageVo;
    }

    /**
     * @description: 处理支付订单列表
     * @param paymentOrders 支付订单列表
     * @return 处理成功的订单数量
     * @author: shaoyang.li
     * @date: 2025-07-04 15:08:30
     * @since JDK 1.8
     */
    private int processPaymentOrders(List<HwPaymentOrderPO> paymentOrders) {
        if (CollectionUtils.isEmpty(paymentOrders)) {
            log.info("当前批次无支付订单需要处理");
            return 0;
        }

        log.info("开始处理支付订单批次，订单数量：{}", paymentOrders.size());

        int processedCount = 0;
        for (HwPaymentOrderPO paymentOrder : paymentOrders) {
            try {
                // 处理单笔支付订单
                boolean processed = processSinglePaymentOrder(paymentOrder);
                if (processed) {
                    processedCount++;
                }
            } catch (Exception e) {
                // 单笔订单处理失败不影响其他订单，记录日志并继续处理
                log.error("处理支付订单异常，支付订单号：{}，异常信息：{}",
                        paymentOrder.getPmtDealNo(), e.getMessage(), e);
                AlertLogUtil.alert(this.getClass().getName(),
                        String.format("处理支付订单异常，支付订单号：%s，异常：%s",
                                paymentOrder.getPmtDealNo(), e.getMessage()));
            }
        }

        log.info("支付订单批次处理完成，总数量：{}，成功处理：{}", paymentOrders.size(), processedCount);
        return processedCount;
    }

    /**
     * @description: 处理单笔支付订单
     * @param paymentOrder 支付订单
     * @return 是否处理成功
     * @author: shaoyang.li
     * @date: 2025-07-04 15:08:30
     * @since JDK 1.8
     */
    private boolean processSinglePaymentOrder(HwPaymentOrderPO paymentOrder) {
        log.info("开始处理支付订单，支付订单号：{}", paymentOrder.getPmtDealNo());

        // 1. 查询关联的交易订单和明细（仅交易类型需要）
        HwDealOrder dealOrder = null;
        List<HwDealOrderDtl> dealOrderDtlList = null;

        if (PaymentOrderTypeEnum.TRADE.getCode().equals(paymentOrder.getOrderType())) {
            // 查询交易订单
            dealOrder = hwDealOrderRepository.selectByDealNo(paymentOrder.getDealNo());
            if (Objects.isNull(dealOrder)) {
                log.error("交易订单不存在，支付订单号：{}，交易订单号：{}",
                        paymentOrder.getPmtDealNo(), paymentOrder.getDealNo());
                AlertLogUtil.alert(this.getClass().getName(),
                        String.format("交易订单不存在，支付订单号：%s，交易订单号：%s",
                                paymentOrder.getPmtDealNo(), paymentOrder.getDealNo()));
                return false;
            }

            // 查询交易订单明细
            dealOrderDtlList = hwDealOrderDtlRepository.selectListByDealNo(paymentOrder.getDealNo());
            if (CollectionUtils.isEmpty(dealOrderDtlList)) {
                log.error("交易订单明细不存在，支付订单号：{}，交易订单号：{}",
                        paymentOrder.getPmtDealNo(), paymentOrder.getDealNo());
                AlertLogUtil.alert(this.getClass().getName(),
                        String.format("交易订单明细不存在，支付订单号：%s，交易订单号：%s",
                                paymentOrder.getPmtDealNo(), paymentOrder.getDealNo()));
                return false;
            }
        }

        // 2. 调用支付外部服务查询支付结果
        log.info("调用支付外部服务查询支付结果，支付订单号：{}", paymentOrder.getPmtDealNo());
        PaymentResultDTO paymentResult = payOuterService.queryPayResult(paymentOrder.getPmtDealNo().toString());

        // 3. 校验支付外部服务调用结果
        if (!isPaymentServiceCallSuccess(paymentResult, paymentOrder.getPmtDealNo())) {
            return false;
        }

        // 4. 调用支付结果处理服务
        try {
            processPmtResultBusiness.process(paymentOrder, dealOrder, dealOrderDtlList, paymentResult);
            log.info("支付订单处理成功，支付订单号：{}", paymentOrder.getPmtDealNo());
            return true;
        } catch (Exception e) {
            log.error("支付结果处理异常，支付订单号：{}，异常信息：{}",
                    paymentOrder.getPmtDealNo(), e.getMessage(), e);
            throw e;
        }
    }

    /**
     * @description: 校验支付外部服务调用是否成功
     * @param paymentResult 支付结果
     * @param pmtDealNo 支付订单号
     * @return 是否调用成功
     * @author: shaoyang.li
     * @date: 2025-07-04 15:08:30
     * @since JDK 1.8
     */
    private boolean isPaymentServiceCallSuccess(PaymentResultDTO paymentResult, Long pmtDealNo) {
        // 1. 检查返回结果是否为空
        if (Objects.isNull(paymentResult)) {
            log.info("支付外部服务返回结果为空，跳过处理，支付订单号：{}", pmtDealNo);
            return false;
        }

        // 2. 检查服务调用成功标记
        if (!paymentResult.isSuccess()) {
            log.info("支付外部服务调用失败，跳过处理，支付订单号：{}，失败原因：{}",
                    pmtDealNo, paymentResult.getRetDesc());
            return false;
        }

        // 3. 调用成功，记录日志
        log.info("支付外部服务调用成功，支付订单号：{}，支付状态：{}",
                pmtDealNo, paymentResult.getTxPmtFlag());
        return true;
    }
}
