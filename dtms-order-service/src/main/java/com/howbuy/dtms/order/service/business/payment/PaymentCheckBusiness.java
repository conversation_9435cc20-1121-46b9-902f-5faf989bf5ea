/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.payment;

import com.github.pagehelper.PageHelper;
import com.howbuy.dtms.common.enums.NewRecStatEnum;
import com.howbuy.dtms.common.enums.PmtCompFlagEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPaymentOrderPOMapper;
import com.howbuy.dtms.order.dao.po.HwPaymentCheckResultPO;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.dao.query.PaymentCheckQuery;
import com.howbuy.dtms.order.service.cacheservice.CacheKeyPrefix;
import com.howbuy.dtms.order.service.cacheservice.lock.LockService;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.outerservice.pay.PayOuterService;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwPaymentCheckResultRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @description: 支付对账业务处理类
 * <AUTHOR>
 * @date 2025-07-04 16:20:10
 * @since JDK 1.8
 */
@Component
@Slf4j
public class PaymentCheckBusiness {

    /**
     * 分布式锁过期时间（秒）
     */
    private static final int DEFAULT_LOCK_EXPIRE_SECONDS = 3600;

    /**
     * 分页查询每页大小
     */
    private static final int PAGE_SIZE = 500;

    /**
     * 默认查询天数（180天）
     */
    private static final int DEFAULT_QUERY_DAYS = 180;

    /**
     * 对账不平备注模板
     */
    private static final String MISMATCH_MEMO_TEMPLATE = "%s不一致";

    /**
     * 支付侧订单不存在备注
     */
    private static final String NO_ORDER_MEMO = "支付侧订单不存在";

    @Resource
    private LockService lockService;

    @Resource
    private CustomizeHwPaymentOrderPOMapper customizeHwPaymentOrderPOMapper;

    @Resource
    private PayOuterService payOuterService;

    @Resource
    private HwPaymentCheckResultRepository hwPaymentCheckResultRepository;

    /**
     * @description: 支付对账主方法
     * @param pmtCheckDt 对账日期，格式：yyyyMMdd
     * @param pmtCompFlagList 对账状态列表
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    public void paymentCheck(String pmtCheckDt, List<String> pmtCompFlagList) {
        log.info("开始执行支付对账，对账日期：{}，对账状态列表：{}", pmtCheckDt, pmtCompFlagList);

        // 参数校验
        validatePaymentCheckParams(pmtCompFlagList);

        String lockKey = buildLockKey();

        // 获取分布式锁
        if (!lockService.getLock(lockKey, DEFAULT_LOCK_EXPIRE_SECONDS)) {
            log.warn("获取支付对账锁失败，可能存在并发执行");
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        try {
            // 执行对账业务逻辑
            executePaymentCheck(pmtCheckDt, pmtCompFlagList);
            log.info("支付对账执行完成");
        } finally {
            // 释放分布式锁
            lockService.releaseLock(lockKey);
            log.info("释放支付对账锁：{}", lockKey);
        }
    }

    /**
     * @description: 执行支付对账业务逻辑
     * @param pmtCheckDt 对账日期，格式：yyyyMMdd
     * @param pmtCompFlagList 对账状态列表
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private void executePaymentCheck(String pmtCheckDt, List<String> pmtCompFlagList) {
        int pageNum = 1;
        int totalProcessed = 0;
        int successCount = 0;
        int failCount = 0;

        while (true) {
            log.info("开始处理第{}页支付对账数据", pageNum);

            // 分页查询待对账订单
            PageHelper.startPage(pageNum, PAGE_SIZE);
            List<HwPaymentOrderPO> paymentOrders = queryPaymentOrdersForCheck(pmtCheckDt, pmtCompFlagList);

            if (CollectionUtils.isEmpty(paymentOrders)) {
                log.info("第{}页没有查询到待对账订单，对账结束", pageNum);
                break;
            }

            log.info("第{}页查询到{}条待对账订单", pageNum, paymentOrders.size());

            // 处理当前页的订单
            for (HwPaymentOrderPO paymentOrder : paymentOrders) {
                try {
                    processPaymentOrderCheck(paymentOrder);
                    successCount++;
                } catch (Exception e) {
                    log.error("处理支付订单对账失败，支付订单号：{}，错误：{}", 
                            paymentOrder.getPmtDealNo(), e.getMessage(), e);
                    failCount++;
                    // 发送告警但不中断整个批次
                    AlertLogUtil.alert(this.getClass().getSimpleName(),
                            String.format("支付对账处理失败，支付订单号：%s，错误：%s", 
                                    paymentOrder.getPmtDealNo(), e.getMessage()));
                }
                totalProcessed++;
            }

            pageNum++;
        }

        log.info("支付对账处理完成，总处理：{}条，成功：{}条，失败：{}条", 
                totalProcessed, successCount, failCount);
    }

    /**
     * @description: 处理单个支付订单的对账
     * @param paymentOrder 支付订单
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private void processPaymentOrderCheck(HwPaymentOrderPO paymentOrder) {
        log.info("开始处理支付订单对账，支付订单号：{}", paymentOrder.getPmtDealNo());

        // 调用外部支付服务查询支付结果
        PaymentResultDTO paymentResult = payOuterService.queryPayResult(paymentOrder.getPmtDealNo().toString());

        // 处理查询结果
        processPaymentCheckResult(paymentOrder, paymentResult);

        log.info("支付订单对账处理完成，支付订单号：{}", paymentOrder.getPmtDealNo());
    }

    /**
     * @description: 处理支付对账结果
     * @param paymentOrder 支付订单
     * @param paymentResult 外部支付结果
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private void processPaymentCheckResult(HwPaymentOrderPO paymentOrder, PaymentResultDTO paymentResult) {
        // 跳过处理的情况：外部接口返回的pmtDealNo非空，但pmtCompFlag不为2-对账完成
        if (StringUtils.isNotBlank(paymentResult.getPmtDealNo()) && 
            !PmtCompFlagEnum.CHECK_COMPLETE.getCode().equals(paymentResult.getPmtCompFlag())) {
            log.info("跳过处理支付订单，支付订单号：{}，外部对账状态：{}", 
                    paymentOrder.getPmtDealNo(), paymentResult.getPmtCompFlag());
            return;
        }

        // 判断对账结果
        PaymentCheckResult checkResult = comparePaymentData(paymentOrder, paymentResult);

        // 构建对账结果记录
        HwPaymentCheckResultPO checkResultPO = buildPaymentCheckResult(paymentOrder, paymentResult, checkResult);

        // 在事务中保存对账结果并更新订单状态（事务由Repository层控制）
        hwPaymentCheckResultRepository.savePaymentCheckResultAndUpdateOrderStatus(
                checkResultPO, paymentOrder, checkResult.getCompFlag());

        // 如果是对账不平记录，发送告警
        if (PmtCompFlagEnum.CHECK_MISMATCH.getCode().equals(checkResultPO.getPmtCompFlag())) {
            AlertLogUtil.alert(this.getClass().getSimpleName(),
                    String.format("发现对账不平订单，支付订单号：%s，原因：%s",
                            checkResultPO.getPmtDealNo(), checkResultPO.getMemo()));
        }
    }

    /**
     * @description: 比较支付数据
     * @param paymentOrder 内部支付订单
     * @param paymentResult 外部支付结果
     * @return 对账结果
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private PaymentCheckResult comparePaymentData(HwPaymentOrderPO paymentOrder, PaymentResultDTO paymentResult) {
        PaymentCheckResult result = new PaymentCheckResult();

        // 如果外部接口未返回pmtDealNo，判定为对账不平（对手方无此订单）
        if (StringUtils.isBlank(paymentResult.getPmtDealNo())) {
            result.setCompFlag(PmtCompFlagEnum.CHECK_MISMATCH.getCode());
            result.setMemo(NO_ORDER_MEMO);
            return result;
        }

        // 比较关键信息
        StringBuilder mismatchReasons = new StringBuilder();

        // 比较支付金额
        if (!compareAmount(paymentOrder.getPmtAmt(), paymentResult.getPmtAmt())) {
            appendMismatchReason(mismatchReasons, "支付金额");
        }

        // 比较交易支付标记
        if (!StringUtils.equals(paymentOrder.getTxPmtFlag(), paymentResult.getTxPmtFlag())) {
            appendMismatchReason(mismatchReasons, "交易支付标记");
        }

        // 比较币种
        if (!StringUtils.equals(paymentOrder.getCurrency(), paymentResult.getCurrency())) {
            appendMismatchReason(mismatchReasons, "币种");
        }

        // 判断对账结果
        if (mismatchReasons.length() > 0) {
            result.setCompFlag(PmtCompFlagEnum.CHECK_MISMATCH.getCode());
            result.setMemo(String.format(MISMATCH_MEMO_TEMPLATE, mismatchReasons.toString()));
        } else {
            result.setCompFlag(PmtCompFlagEnum.CHECK_COMPLETE.getCode());
            result.setMemo("对账完成");
        }

        return result;
    }

    /**
     * @description: 比较金额
     * @param internalAmount 内部金额
     * @param externalAmount 外部金额
     * @return 是否相等
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private boolean compareAmount(BigDecimal internalAmount, BigDecimal externalAmount) {
        // 任意金额为空都算对账失败
        if (internalAmount == null || externalAmount == null) {
            return false;
        }
        return internalAmount.compareTo(externalAmount) == 0;
    }

    /**
     * @description: 添加不匹配原因
     * @param reasons 原因StringBuilder
     * @param reason 具体原因
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private void appendMismatchReason(StringBuilder reasons, String reason) {
        if (reasons.length() > 0) {
            reasons.append("、");
        }
        reasons.append(reason);
    }

    /**
     * @description: 构建支付对账结果
     * @param paymentOrder 支付订单
     * @param paymentResult 外部支付结果
     * @param checkResult 对账结果
     * @return 对账结果PO
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private HwPaymentCheckResultPO buildPaymentCheckResult(HwPaymentOrderPO paymentOrder,
                                                          PaymentResultDTO paymentResult,
                                                          PaymentCheckResult checkResult) {
        HwPaymentCheckResultPO resultPO = new HwPaymentCheckResultPO();

        // 设置内部订单信息
        resultPO.setPmtDealNo(paymentOrder.getPmtDealNo());
        resultPO.setDealNo(paymentOrder.getDealNo());
        resultPO.setHkCustNo(paymentOrder.getHkCustNo());
        resultPO.setFundCode(paymentOrder.getFundCode());
        resultPO.setCurrency(paymentOrder.getCurrency());
        resultPO.setPmtAmt(paymentOrder.getPmtAmt());
        resultPO.setTxPmtFlag(paymentOrder.getTxPmtFlag());

        // 设置对账日期
        resultPO.setPmtCheckDt(paymentOrder.getPmtCheckDt());

        // 设置外部支付信息
        resultPO.setOutPmtDealNo(paymentResult.getOutPmtDealNo());
        resultPO.setOutPmtAmt(paymentResult.getPmtAmt());
        resultPO.setOutCurrency(paymentResult.getCurrency());
        resultPO.setOutPmtFlag(paymentResult.getTxPmtFlag());

        // 设置对账结果
        resultPO.setPmtCompFlag(checkResult.getCompFlag());
        resultPO.setMemo(checkResult.getMemo());

        // 设置记录状态
        resultPO.setRecStat(NewRecStatEnum.NORMAL.getValue());

        return resultPO;
    }

    /**
     * @description: 查询待对账的支付订单
     * @param pmtCheckDt 对账日期，格式：yyyyMMdd
     * @param pmtCompFlagList 对账状态列表
     * @return 待对账订单列表
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private List<HwPaymentOrderPO> queryPaymentOrdersForCheck(String pmtCheckDt, List<String> pmtCompFlagList) {
        PaymentCheckQuery query = buildPaymentCheckQuery(pmtCheckDt, pmtCompFlagList);
        return customizeHwPaymentOrderPOMapper.selectPaymentOrdersForCheck(query);
    }

    /**
     * @description: 构建支付对账查询条件
     * @param pmtCheckDt 对账日期，格式：yyyyMMdd
     * @param pmtCompFlagList 对账状态列表
     * @return 查询条件
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private PaymentCheckQuery buildPaymentCheckQuery(String pmtCheckDt, List<String> pmtCompFlagList) {
        PaymentCheckQuery query = new PaymentCheckQuery();

        // 设置对账状态列表
        query.setPmtCompFlagList(pmtCompFlagList);

        // 设置交易支付标记（只对账付款成功或付款失败的订单）
        query.setTxPmtFlagList(Arrays.asList(
                TxPmtFlagEnum.PAY_SUCCESS.getCode(),
                TxPmtFlagEnum.PAY_FAIL.getCode()));

        // 设置记录状态为有效
        query.setRecStat(NewRecStatEnum.NORMAL.getValue());

        // 设置时间条件
        if (StringUtils.isNotBlank(pmtCheckDt)) {
            query.setPmtCheckDt(pmtCheckDt);
        } else {
            // 查询180天内的订单
            Calendar calendar = Calendar.getInstance();
            Date endTime = calendar.getTime();
            calendar.add(Calendar.DAY_OF_MONTH, -DEFAULT_QUERY_DAYS);
            Date startTime = calendar.getTime();

            query.setUpdateTimeStart(startTime);
            query.setUpdateTimeEnd(endTime);
        }

        // 设置排序
        query.setOrderBy("update_timestamp");
        query.setOrderDirection("asc");

        return query;
    }

    /**
     * @description: 参数校验
     * @param pmtCompFlagList 对账状态列表
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private void validatePaymentCheckParams(List<String> pmtCompFlagList) {
        if (CollectionUtils.isEmpty(pmtCompFlagList)) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "对账状态列表不能为空");
        }

        // 校验对账状态是否有效
        for (String pmtCompFlag : pmtCompFlagList) {
            PmtCompFlagEnum pmtCompFlagEnum = PmtCompFlagEnum.getEnumByCode(pmtCompFlag);
            if (pmtCompFlagEnum == null) {
                throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(),
                        "无效的对账状态：" + pmtCompFlag);
            }

            // 不允许对账成功状态进行对账
            if (PmtCompFlagEnum.CHECK_COMPLETE.equals(pmtCompFlagEnum)) {
                throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(),
                        "不允许对已对账成功的订单进行对账，状态：" + PmtCompFlagEnum.CHECK_COMPLETE.getDesc());
            }
        }
    }

    /**
     * @description: 构建分布式锁Key
     * @return 锁Key
     * @author: shaoyang.li
     * @date: 2025-07-04 16:20:10
     * @since JDK 1.8
     */
    private String buildLockKey() {
        return CacheKeyPrefix.LOCK_KEY_PREFIX + "payment_check";
    }

    /**
     * 支付对账结果内部类
     */
    private static class PaymentCheckResult {
        /**
         * 对账标记
         */
        private String compFlag;

        /**
         * 备注
         */
        private String memo;

        public String getCompFlag() {
            return compFlag;
        }

        public void setCompFlag(String compFlag) {
            this.compFlag = compFlag;
        }

        public String getMemo() {
            return memo;
        }

        public void setMemo(String memo) {
            this.memo = memo;
        }
    }
}
