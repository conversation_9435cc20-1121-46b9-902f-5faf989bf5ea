package com.howbuy.dtms.order.service.service.trade.buy;

import com.howbuy.dtms.common.enums.*;
import com.howbuy.dtms.order.client.domain.request.buy.PiggyAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.buy.PiggyAppBuyResponse;
import com.howbuy.dtms.order.dao.bo.OrderCreateBO;
import com.howbuy.dtms.order.dao.param.PiggyTradeAppImportUpdateParam;
import com.howbuy.dtms.order.service.business.fundshare.FundShareService;
import com.howbuy.dtms.order.service.business.ordercreate.OrderCreateService;
import com.howbuy.dtms.order.service.business.ordermessage.OrderMessageService;
import com.howbuy.dtms.order.service.commom.enums.TradeValidatorEnum;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.service.AbstractTradeService;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import com.howbuy.dtms.order.service.validate.chain.TradeValidatorHelper;
import com.howbuy.dtms.order.service.validate.context.TradeContext;
import com.howbuy.dtms.order.service.validate.context.TradeContextBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 储蓄罐申请购买服务类
 * @date 2024/8/13 20:05
 * @since JDK 1.8
 */
@Service
public class PiggyAppBuyService extends AbstractTradeService<PiggyAppBuyRequest, TradeContext> {

    @Resource
    private OrderCreateService orderCreateService;
    @Resource
    private HwDealOrderRepository hwDealOrderRepository;
    @Resource
    private OrderMessageService orderMessageService;
    @Resource
    private TradeContextBuilder tradeContextBuilder;
    @Resource
    private TradeValidatorHelper tradeValidatorHelper;
    @Resource
    private FundShareService fundShareService;


    public PiggyAppBuyResponse process(PiggyAppBuyRequest request) {

        // 校验
        TradeContext context = tradeValidate(request);
        //创建订单及订单明细
        OrderCreateBO orderCreateBO = orderCreateService.createOrder(context);

        // 设置打款凭证状态为无需上传
        orderCreateBO.getHwDealOrder().setPayVoucherStatus(PayVoucherStatusEnum.NO_NEED_UPLOAD.getCode());
        // 设置关联订单号(订单表的订单号)
        if(StringUtils.isNotEmpty(request.getExternalDealNo())) {
            orderCreateBO.getHwDealOrder().setRelationalDealNo(Long.valueOf(request.getExternalDealNo()));
        }
        // 储蓄罐申请单更新参数
        orderCreateBO.setPiggyTradeAppImportUpdateParam(buildCounterPiggyImportUpdateParam(request, orderCreateBO));
        //新增订单及订单明细
        hwDealOrderRepository.saveDealOrder(orderCreateBO);
        //发送订单消息
        orderMessageService.buyOrderMessage(context, orderCreateBO);

        PiggyAppBuyResponse response = new PiggyAppBuyResponse();
        response.setDealNo(String.valueOf(orderCreateBO.getHwDealOrder().getDealNo()));
        response.setDealDtlNo(String.valueOf(orderCreateBO.getHwDealOrderDtl().getDealDtlNo()));
        return response;
    }

    private PiggyTradeAppImportUpdateParam buildCounterPiggyImportUpdateParam(PiggyAppBuyRequest request, OrderCreateBO orderCreateBO) {
        PiggyTradeAppImportUpdateParam piggyParam = new PiggyTradeAppImportUpdateParam();
        piggyParam.setPiggyAppId(request.getExternalDealNo());
        piggyParam.setModifier(request.getOperator());
        piggyParam.setIsGenerated(PiggyTradeAppGenerateEnum.GENERATED.getCode());
        piggyParam.setDealNo(String.valueOf(orderCreateBO.getHwDealOrder().getDealNo()));
        piggyParam.setRemark("订单生成成功");
        piggyParam.setModifyTime(new Date());
        piggyParam.setOldIsGenerated(PiggyTradeAppGenerateEnum.NOT_GENERATE.getCode());
        return piggyParam;
    }

    @Override
    protected TradeContext tradeValidate(PiggyAppBuyRequest request) {
        // 基类参数校验
        ParamsValidator.validate(request,
                "appDt",
                "appTm",
                "tradeChannel",
                "outletCode",
                "ipAddress",
                "externalDealNo",
                "hkCustNo",
                "fundCode",
                "payMethod",
                "buyAmt",
                "discountRate",
                "operator"
        );
        // 并发控制校验
        validateConcurrent(request);

        return doValidate(request);
    }

    @Override
    protected TradeContext doValidate(PiggyAppBuyRequest request) {
        // 构建上下文
        TradeContext context = tradeContextBuilder.buildTradeContext(request, request.getHkCustNo(), request.getFundCode(), BusinessTypeEnum.BUY, null);
        context.setExternalDealNo(request.getExternalDealNo());
        // 构建买入参数
        context.setBuyBean(new TradeContext.BuyBean());
        context.getBuyBean().setBuyAmt(request.getBuyAmt());
        context.getBuyBean().setEstimateFee(request.getEstimateFee());
        // 设置折扣类型为折扣率
        context.getBuyBean().setDiscountType(DiscountTypeEnum.DISCOUNT_RATE.getCode());
        // 设置折扣率
        context.getBuyBean().setApplyDiscountRate(request.getDiscountRate());

        // 设置基金交易账号
        context.setFundTxAcctNo(request.getFundTxAcctNo());

        PayMethodEnum payMethodEnum = PayMethodEnum.getEnumByCode(request.getPayMethod());
        context.getBuyBean().setPayMethodEnum(payMethodEnum);
        // 首次交易标识
        context.getBuyBean().setFirstBuyFlagEnum(fundShareService.getFirstTrade(request.getHkCustNo(), request.getFundCode()));

        // 构建校验链
        List<String> validatorList = tradeValidatorHelper.buildChain(
                TradeValidatorEnum.ACCOUNT_STATUS,
                TradeValidatorEnum.FUND_TX_ACCT_STAT,
                TradeValidatorEnum.BUY_PIGGY_FUND,
                TradeValidatorEnum.PREBOOK_DEAL_NO_VALID,
                TradeValidatorEnum.EXTERNAL_ORDER_NO,
                TradeValidatorEnum.RISK_LEVEL,
                TradeValidatorEnum.DERIVATIVE_EXPERIENCE,
                TradeValidatorEnum.INVESTOR_QUALIFICATION,
                TradeValidatorEnum.PRODUCT_AGE,
                TradeValidatorEnum.PRODUCT_BUSINESS,
                TradeValidatorEnum.PAY_METHOD,
                TradeValidatorEnum.OPEN_DT_BY_PAY_METHOD,
                TradeValidatorEnum.PRODUCT_NAV_STATUS,
                TradeValidatorEnum.BUY_TRADE_LIMIT,
                TradeValidatorEnum.COUNTER_BUY_FEE,
                TradeValidatorEnum.TRADE_DIFFERENTIAL

        );
        // 执行校验
        tradeValidatorHelper.doValidate(context, validatorList);
        return context;
    }
}
