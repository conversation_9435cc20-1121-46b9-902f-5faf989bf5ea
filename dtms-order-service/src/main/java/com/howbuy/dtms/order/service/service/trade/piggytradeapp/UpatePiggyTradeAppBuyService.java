/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.service.trade.piggytradeapp;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.common.enums.PiggyAppSourceEnum;
import com.howbuy.dtms.common.enums.PiggyTradeAppGenerateEnum;
import com.howbuy.dtms.order.client.domain.request.piggytradeapp.UpdatePiggyTradeAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.UpdatePiggyTradeAppBuyResponse;
import com.howbuy.dtms.order.dao.po.HwPiggyTradeAppImport;
import com.howbuy.dtms.order.service.commom.constant.Constants;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.ValidateException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.AssertUtils;
import com.howbuy.dtms.order.service.commom.utils.TradeUtils;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.QueryFundInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.FundBasicInfoDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.FundFeeRateDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.QueryFundFeeRateByAppAmtRequestDTO;
import com.howbuy.dtms.order.service.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.hkacc.domain.HkCustInfoDTO;
import com.howbuy.dtms.order.service.repository.HwPiggyTradeAppImportRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 修改储蓄罐交易申请买入业务逻辑
 * @date 2025-07-18 16:09:17
 * @since JDK 1.8
 */
@Service
@Slf4j
public class UpatePiggyTradeAppBuyService {

    @Resource
    private HwPiggyTradeAppImportRepository hwPiggyTradeAppImportRepository;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    private QueryFundInfoOuterService queryFundInfoOuterService;

    /**
     * @param request 请求参数
     * @return UpatePiggyTradeAppBuyResponse 处理结果
     * @description: 修改储蓄罐交易申请买入处理方法
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    public UpdatePiggyTradeAppBuyResponse process(UpdatePiggyTradeAppBuyRequest request) {
        log.info("修改储蓄罐交易申请买入开始，请求参数：{}", request);

        // 参数校验
        validateParams(request);

        // 查询储蓄罐交易申请记录
        HwPiggyTradeAppImport piggyTradeAppImport = queryPiggyTradeAppImport(request.getImportAppId());

        // 业务校验
        validateBusiness(piggyTradeAppImport);

        // 校验客户信息
        HkCustInfoDTO hkCustInfo = validateCustomer(piggyTradeAppImport.getHkCustNo());

        // 前置查询
        FundBasicInfoDTO fundBasicInfo = queryFundInfoOuterService.queryFundBasicInfo(piggyTradeAppImport.getProductCode());
        FundFeeRateDTO fundFeeRate = queryFundFeeRate(hkCustInfo, piggyTradeAppImport.getProductCode(), new BigDecimal(request.getBuyAmt()));

        // 计算手续费、申请金额
        calculateFeeAndAmount(piggyTradeAppImport, fundBasicInfo, fundFeeRate, request);

        // 更新储蓄罐交易申请记录
        updatePiggyTradeAppImport(piggyTradeAppImport, request);

        log.info("修改储蓄罐交易申请买入完成，导入申请id：{}", request.getImportAppId());
        return new UpdatePiggyTradeAppBuyResponse();
    }

    /**
     * @param request 请求参数
     * @description: 参数校验
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    private void validateParams(UpdatePiggyTradeAppBuyRequest request) {
        // 入参非空校验
        AssertUtils.nonNullParam(request.getImportAppId(), "导入申请id不能为空");
        AssertUtils.nonNullParam(request.getBuyAmt(), "买入金额不能为空");
        AssertUtils.nonNullParam(request.getDiscountRate(), "折扣率不能为空");
        AssertUtils.nonNullParam(request.getFee(), "手续费不能为空");
        AssertUtils.nonNullParam(request.getOperator(), "操作人不能为空");
        AssertUtils.nonNullParam(request.getRemark(), "备注不能为空");

        // 校验买入金额的格式以及必须大于0
        try {
            BigDecimal buyAmt = new BigDecimal(request.getBuyAmt());
            AssertUtils.isTrue(buyAmt.compareTo(BigDecimal.ZERO) > 0, "买入金额必须大于0");
        } catch (NumberFormatException e) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "买入金额格式错误");
        }

        // 校验折扣率的格式以及必须大于等于0且小于等于1
        try {
            BigDecimal discountRate = new BigDecimal(request.getDiscountRate());
            AssertUtils.isTrue(discountRate.compareTo(BigDecimal.ZERO) >= 0 && discountRate.compareTo(BigDecimal.ONE) <= 1,
                    "折扣率必须大于等于0且小于等于1");
        } catch (NumberFormatException e) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "折扣率格式错误");
        }

        // 校验手续费的格式以及必须大于等于0
        try {
            BigDecimal fee = new BigDecimal(request.getFee());
            AssertUtils.isTrue(fee.compareTo(BigDecimal.ZERO) >= 0, "手续费必须大于等于0");
        } catch (NumberFormatException e) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "手续费格式错误");
        }
    }

    /**
     * @param importAppId 导入申请id
     * @return HwPiggyTradeAppImport 储蓄罐交易申请记录
     * @description: 查询储蓄罐交易申请记录
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    private HwPiggyTradeAppImport queryPiggyTradeAppImport(String importAppId) {
        HwPiggyTradeAppImport piggyTradeAppImport = hwPiggyTradeAppImportRepository.queryByImportAppId(importAppId);
        AssertUtils.nonNullParam(piggyTradeAppImport, "储蓄罐交易申请记录不存在");
        return piggyTradeAppImport;
    }

    /**
     * @param piggyTradeAppImport 储蓄罐交易申请记录
     * @description: 业务校验
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    private void validateBusiness(HwPiggyTradeAppImport piggyTradeAppImport) {
        // 校验中台业务码=申购
        if (!BusinessCodeEnum.PURCHASE.getMCode().equals(piggyTradeAppImport.getMiddleBusiCode())) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "赎回记录不允许修改");
        }

        // 校验储蓄罐申请来源=可用余额、excel
        String piggyAppSource = piggyTradeAppImport.getPiggyAppSource();
        if (!PiggyAppSourceEnum.EXCEL.getCode().equals(piggyAppSource) &&
                !PiggyAppSourceEnum.AVAILABLE_BALANCE.getCode().equals(piggyAppSource)) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "控管表下发数据不允许修改");
        }

        // 校验生成状态=0-未生成
        if (!PiggyTradeAppGenerateEnum.NOT_GENERATE.getCode().equals(piggyTradeAppImport.getIsGenerated())) {
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "当前记录订单生成状态不是未生成");
        }
    }

    /**
     * @param hkCustNo 香港客户号
     * @return HkCustInfoDTO 客户信息
     * @description: 校验客户信息
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    private HkCustInfoDTO validateCustomer(String hkCustNo) {
        HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(hkCustNo);
        AssertUtils.nonNullParam(hkCustInfo, "客户信息不存在");
        return hkCustInfo;
    }

    /**
     * @param hkCustInfo 客户信息
     * @param fundCode   基金代码
     * @param buyAmt     买入金额
     * @return FundFeeRateDTO 基金费率
     * @description: 查询基金费率
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    private FundFeeRateDTO queryFundFeeRate(HkCustInfoDTO hkCustInfo, String fundCode, BigDecimal buyAmt) {
        QueryFundFeeRateByAppAmtRequestDTO requestDTO = new QueryFundFeeRateByAppAmtRequestDTO();
        requestDTO.setFundCode(fundCode);
        requestDTO.setBusiCode(BusinessCodeEnum.PURCHASE.getCode());
        requestDTO.setInvstType(hkCustInfo.getInvstType());
        requestDTO.setCollectRecipient(Constants.COLLECT_RECIPIENT_HM);
        requestDTO.setAppAmt(buyAmt);

        return queryFundInfoOuterService.queryFundFeeRateByAppAmt(requestDTO);
    }

    /**
     * @param piggyTradeAppImport 储蓄罐交易申请记录
     * @param fundBasicInfo       基金信息
     * @param fundFeeRate         基金费率
     * @param request             请求参数
     * @description: 计算手续费、申请金额
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    private void calculateFeeAndAmount(HwPiggyTradeAppImport piggyTradeAppImport, FundBasicInfoDTO fundBasicInfo,
                                       FundFeeRateDTO fundFeeRate, UpdatePiggyTradeAppBuyRequest request) {
        BigDecimal buyAmt = new BigDecimal(request.getBuyAmt());
        BigDecimal discountRate = new BigDecimal(request.getDiscountRate());
        BigDecimal requestFee = new BigDecimal(request.getFee());

        // 根据基金的币种类型进行截位
        int scale = TradeUtils.isJPYCurrency(fundBasicInfo.getCurrency()) ? Constants.JPY_AMOUNT_SCALE : Constants.AMOUNT_SCALE;

        BigDecimal fee;
        BigDecimal appAmt;

        if (Constants.FEE_CAL_MODE_OUT.equals(fundBasicInfo.getFeeCalMode())) {
            // 外扣法：手续费=净申购金额*交易费率*折扣率，申请金额=净申购金额+手续费
            fee = buyAmt.multiply(fundFeeRate.getFeeRate()).multiply(discountRate).setScale(scale, RoundingMode.DOWN);
            appAmt = buyAmt.add(fee);
        } else {
            // 内扣法：手续费=净申购金额*交易费率*折扣率，净申购金额=申请金额
            fee = buyAmt.multiply(fundFeeRate.getFeeRate()).multiply(discountRate).setScale(scale, RoundingMode.DOWN);
            appAmt = buyAmt;
        }

        if (fee.compareTo(requestFee) != 0) {
            // 校验手续费是否与计算结果一致
            String errorMsg = String.format("计算手续费与请求不一致，导入申请id：%s，计算结果：%s，请求结果：%s",
                    piggyTradeAppImport.getImportAppId(), fee, requestFee);
            log.error(errorMsg);
            AlertLogUtil.alert(UpatePiggyTradeAppBuyService.class.getName(), errorMsg);
            throw new ValidateException(ExceptionEnum.PARAMS_ERROR.getCode(), "计算手续费与请求不一致");
        }

        // 设置计算结果
        piggyTradeAppImport.setBuyAmt(buyAmt);
        piggyTradeAppImport.setFee(fee);
        piggyTradeAppImport.setDiscountRate(discountRate);
        piggyTradeAppImport.setAppAmt(appAmt);
    }

    /**
     * @param piggyTradeAppImport 储蓄罐交易申请记录
     * @param request             请求参数
     * @description: 更新储蓄罐交易申请记录
     * @author: shaoyang.li
     * @date: 2025-07-18 16:09:17
     * @since JDK 1.8
     */
    private void updatePiggyTradeAppImport(HwPiggyTradeAppImport piggyTradeAppImport, UpdatePiggyTradeAppBuyRequest request) {
        // 设置更新字段
        piggyTradeAppImport.setRemark(request.getRemark());
        piggyTradeAppImport.setModifier(request.getOperator());
        piggyTradeAppImport.setUpdateTime(new Date());

        // 执行更新，WHERE条件为是否删除为未删除，是否生成为未生成
        int updateCount = hwPiggyTradeAppImportRepository.updateByImportAppId(piggyTradeAppImport);

        // 校验更新结果
        if (updateCount != 1) {
            String errorMsg = String.format("更新储蓄罐交易申请记录失败，导入申请id：%s，影响行数：%d",
                    request.getImportAppId(), updateCount);
            log.error(errorMsg);
            AlertLogUtil.alert(UpatePiggyTradeAppBuyService.class.getName(), errorMsg);
            throw new ValidateException(ExceptionEnum.DB_ERROR.getCode(), "更新储蓄罐交易申请记录失败");
        }
    }
}
