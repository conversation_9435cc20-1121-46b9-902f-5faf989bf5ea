/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.EqualsAndHashCode;

/**
 * @description: 储蓄罐支付失败赎回撤单任务消息VO
 * @author: shaoyang.li
 * @date: 2025-07-10 19:59:33
 * @since JDK 1.8
 */
@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
public class PiggyPayFailRedeemCancelTaskVO extends BaseTaskMessageVO {
    
    /**
     * 是否工作日校验（1-是，其他-否）
     */
    private String workdayCheck;
}
