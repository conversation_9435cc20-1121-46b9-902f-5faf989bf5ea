package com.howbuy.dtms.order.service.commom.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.ExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.util.List;

/**
 * <AUTHOR>
 * @description 使用EasyExcelAPI辅助工具类
 * @date 2024/4/19 12:00
 * @since JDK 1.8
 */
@Slf4j
public class EasyExcelUtils {

    /**
     * 读取导入的excel
     * @param file 文件对象
     * @param clz 解析类定义
     * @param sheetNo 读取那个sheet
     * @param headRowNumber 表头占几行
     * @return 返回解析后结果
     * @param <T> 解析对象类型
     */
    public static<T> List<T> readExcel(MultipartFile file, Class<T> clz, int sheetNo, int headRowNumber) {
        List<T> list = null;
        try (BufferedInputStream buffer =  new BufferedInputStream(file.getInputStream())) {
            ExcelListener<T> listener = new ExcelListener<>();
            EasyExcel.read(buffer, clz, listener).sheet().sheetNo(sheetNo).headRowNumber(headRowNumber).doRead();
            list = listener.getList();
        } catch (IOException e) {
            log.error("读取上传excel异常",e);
            ExceptionUtils.throwBusinessException(ExceptionEnum.SYSTEM_ERROR);
        }
        return list;
    }
    /**
     * 读取导入的excel
     * @param fileBytes 文件字节数字
     * @param clz 解析类定义
     * @param sheetNo 读取那个sheet
     * @param headRowNumber 表头占几行
     * @return 返回解析后结果
     * @param <T> 解析对象类型
     */
    public static<T> List<T> readExcel(byte[] fileBytes, Class<T> clz, int sheetNo, int headRowNumber) {

        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(fileBytes);
        List<T> list = null;
        try (BufferedInputStream buffer =  new BufferedInputStream(byteArrayInputStream)) {
            ExcelListener<T> listener = new ExcelListener<>();
            EasyExcel.read(buffer, clz, listener).excelType(ExcelTypeEnum.XLSX).sheet().sheetNo(sheetNo).headRowNumber(headRowNumber).doRead();
            list = listener.getList();
        } catch (IOException e) {
            log.error("读取上传excel异常",e);
            ExceptionUtils.throwBusinessException(ExceptionEnum.SYSTEM_ERROR);
        }
        return list;
    }


    /**
     * 下载Excel
     * @param response Http响应对象
     * @param clazz 导出对象类定义
     * @param fileName 导出文件名称
     * @param data 导出数据
     * @param <T> 导出对象定义
     * @throws IOException
     */
    public static<T> void download(HttpServletResponse response, Class<T> clazz, String fileName, List<T> data) throws IOException {
        try (OutputStream outputStream = response.getOutputStream()) {
            response.reset();
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName,"UTF-8") + ".xls");
            EasyExcel.write(outputStream, clazz).sheet().sheetName(fileName).doWrite(data);
        }
    }

    /**
     * 生成Excel文件流字符串
     * @param clazz 导出对象类定义
     * @param fileName 导出文件名称
     * @param data 导出数据
     * @param <T> 导出对象定义
     * @throws IOException
     */
    public static<T> String generateFileByteStr(Class<T> clazz, String fileName, List<T> data) throws IOException {
       String fileByteStr = null;
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            EasyExcel.write(outputStream, clazz).sheet().sheetName(fileName).doWrite(data);
            fileByteStr  = Base64.encodeBase64String(outputStream.toByteArray());
        }
        return fileByteStr;
    }
}
