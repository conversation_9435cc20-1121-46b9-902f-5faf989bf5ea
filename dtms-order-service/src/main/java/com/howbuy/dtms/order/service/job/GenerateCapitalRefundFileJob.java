/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.howbuy.dtms.order.service.business.batch.capitalfile.CapitalRefundFileProcess;
import com.howbuy.dtms.order.service.job.AbstractBatchMessageJob;
import com.howbuy.dtms.order.service.job.domain.CapitalRefundFileTaskVO;
import com.howbuy.dtms.order.service.outerservice.dtms.settle.DtmsSettleOuterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 生成资金退款文件定时任务
 * @date 2025-07-10 15:29:03
 * @since JDK 1.8
 */
@Slf4j
@Component
public class GenerateCapitalRefundFileJob extends AbstractBatchMessageJob<CapitalRefundFileTaskVO> {

    @Value("${TOPIC.DTMS_ORDER_EC_TASK_CAPITAL_REFUND_FILE}")
    private String topic;

    @Resource
    private CapitalRefundFileProcess capitalRefundFileProcess;

    @Resource
    private DtmsSettleOuterService dtmsSettleOuterService;

    /**
     * @return Topic名称
     * @description: 获取Topic名称
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    @Override
    protected String getTopicName() {
        return topic;
    }

    /**
     * @param message 任务消息
     * @description: 执行具体的业务处理逻辑
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    @Override
    protected void doProcessJob(CapitalRefundFileTaskVO message) {
        log.info("生成资金退款文件定时任务开始执行，消息内容：{}", message);

        // 工作日校验
        if (!isHongKongWorkday(message.getCheckWorkday())) {
            log.info("当前日期非香港工作日，跳过任务执行");
            return;
        }

        // 执行资金退款文件生成处理
        long startTime = System.currentTimeMillis();
        capitalRefundFileProcess.process();
        long costTime = System.currentTimeMillis() - startTime;

        // 记录执行结果
        String resultMessage = String.format("生成资金退款文件定时任务执行完成，耗时：%d 毫秒", costTime);
        log.info(resultMessage);
    }
}
