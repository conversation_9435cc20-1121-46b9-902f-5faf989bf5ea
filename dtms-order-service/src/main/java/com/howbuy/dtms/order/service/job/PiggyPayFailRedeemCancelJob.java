/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.howbuy.dtms.order.service.business.piggypayfailredeemcancel.PiggyPayFailRedeemCancelProcessor;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.job.AbstractBatchMessageJob;
import com.howbuy.dtms.order.service.job.domain.PiggyPayFailRedeemCancelTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 储蓄罐支付失败赎回撤单调度任务
 * @author: shaoyang.li
 * @date: 2025-07-10 19:59:33
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PiggyPayFailRedeemCancelJob extends AbstractBatchMessageJob<PiggyPayFailRedeemCancelTaskVO> {

    @Value("${TOPIC.DTMS_ORDER_EC_TASK_PIGGY_PAY_FAIL_REDEEM_CANCEL}")
    private String topic;

    @Resource
    private PiggyPayFailRedeemCancelProcessor piggyPayFailRedeemCancelProcessor;

    @Override
    protected String getTopicName() {
        return topic;
    }

    @Override
    protected void doProcessJob(PiggyPayFailRedeemCancelTaskVO message) {
        log.info("储蓄罐支付失败赎回撤单调度任务开始执行，消息：{}", message);
        // 1. 判断当前是否香港工作日
        if (!isHongKongWorkday(message.getWorkdayCheck())) {
            log.info("非香港工作日，跳过储蓄罐支付失败赎回撤单处理");
            return;
        }

        // 2. 调用业务处理器执行具体逻辑
        piggyPayFailRedeemCancelProcessor.process();

        log.info("储蓄罐支付失败赎回撤单调度任务执行完成");
    }
}
