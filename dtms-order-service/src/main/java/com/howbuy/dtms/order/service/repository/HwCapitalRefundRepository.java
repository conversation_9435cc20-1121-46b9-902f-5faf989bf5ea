/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.repository;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.howbuy.dtms.common.annotation.DataSource;
import com.howbuy.dtms.order.client.domain.response.PageVo;
import com.howbuy.dtms.order.dao.bo.CapitalRefundOrderBO;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwCapitalRefundPOMapper;
import com.howbuy.dtms.order.dao.po.HwCapitalRefundPO;
import com.howbuy.dtms.order.service.commom.utils.ListSplitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 海外资金退款数据仓库
 * @date 2025-07-10 13:47:32
 * @since JDK 1.8
 */
@Slf4j
@Repository
@DataSource(value = "mysql")
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class HwCapitalRefundRepository {

    @Resource
    private CustomizeHwCapitalRefundPOMapper customizeHwCapitalRefundPOMapper;

    /**
     * 分页查询支付成功撤单退款数据
     *
     * @param updateTimeStart 更新时间开始
     * @param page            页码
     * @param size            每页大小
     * @return 分页结果
     */
    public PageVo<CapitalRefundOrderBO> selectPaidCancelRefundOrdersPage(Date updateTimeStart, int page, int size) {
        log.info("分页查询支付成功撤单退款数据，更新时间开始：{}", updateTimeStart);

        PageHelper.startPage(page, size);
        Page<CapitalRefundOrderBO> pageResult = (Page<CapitalRefundOrderBO>) customizeHwCapitalRefundPOMapper.selectPaidCancelRefundOrders(updateTimeStart);

        PageVo<CapitalRefundOrderBO> result = new PageVo<>();
        result.setTotal(pageResult.getTotal());
        result.setPages(pageResult.getPages());
        result.setList(pageResult.getResult());

        log.info("查询支付成功撤单退款数据完成，总记录数：{}", pageResult.getTotal());
        return result;
    }

    /**
     * 分页查询储蓄罐赎回成功退款数据
     *
     * @param updateTimeStart 更新时间开始
     * @param page            页码
     * @param size            每页大小
     * @return 分页结果
     */
    public PageVo<CapitalRefundOrderBO> selectPiggyRedeemRefundOrdersPage(Date updateTimeStart, int page, int size) {
        log.info("分页查询储蓄罐赎回成功退款数据，更新时间开始：{}", updateTimeStart);

        PageHelper.startPage(page, size);
        Page<CapitalRefundOrderBO> pageResult = (Page<CapitalRefundOrderBO>) customizeHwCapitalRefundPOMapper.selectPiggyRedeemRefundOrders(updateTimeStart);

        PageVo<CapitalRefundOrderBO> result = new PageVo<>();
        result.setTotal(pageResult.getTotal());
        result.setPages(pageResult.getPages());
        result.setList(pageResult.getResult());

        log.info("查询储蓄罐赎回成功退款数据完成，总记录数：{}", pageResult.getTotal());
        return result;
    }

    /**
     * 批量插入退款记录
     *
     * @param refundList 退款记录列表
     * @return 插入成功的记录数
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int batchInsert(List<HwCapitalRefundPO> refundList) {
        log.info("批量插入退款记录，记录数：{}", refundList.size());

        int result = customizeHwCapitalRefundPOMapper.batchInsert(refundList);

        log.info("批量插入退款记录完成，成功插入：{} 条", result);
        return result;
    }

    /**
     * @return 未处理的退款数据列表
     * @description: 查询未处理的退款数据
     * @author: shaoyang.li
     * @date: 2025-07-10 16:22:33
     * @since JDK 1.8
     */
    public List<HwCapitalRefundPO> selectUnprocessedRefunds() {
        log.info("查询未处理的退款数据");

        List<HwCapitalRefundPO> result = customizeHwCapitalRefundPOMapper.selectUnprocessedRefunds();

        log.info("查询未处理的退款数据完成，记录数：{}", result.size());
        return result;
    }

    /**
     * @param dealNoList 订单号列表
     * @return 更新成功的记录数
     * @description: 批量更新退款记录处理状态为已处理
     * @author: shaoyang.li
     * @date: 2025-07-10 16:22:33
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int batchUpdateProcessStatusToProcessed(List<Long> dealNoList) {
        if (CollectionUtils.isEmpty(dealNoList)) {
            log.warn("批量更新退款记录处理状态，订单号列表为空");
            return 0;
        }

        log.info("批量更新退款记录处理状态为已处理，订单号数量：{}", dealNoList.size());

        int totalUpdated = 0;
        // 分批处理，每批500条，避免IN语句参数过多
        ListSplitter<Long> listSplitter = new ListSplitter<>(dealNoList, 500);

        while (listSplitter.hasNext()) {
            List<Long> batch = listSplitter.next();
            int result = customizeHwCapitalRefundPOMapper.batchUpdateProcessStatusToProcessed(batch);
            totalUpdated += result;
            log.info("分批更新退款记录状态，当前批次订单数：{}，更新记录数：{}", batch.size(), result);
        }

        log.info("批量更新退款记录处理状态完成，总更新记录数：{}", totalUpdated);
        return totalUpdated;
    }
}
