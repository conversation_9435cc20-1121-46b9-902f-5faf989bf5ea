/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.batch.capitalfile;

import com.google.common.base.Throwables;
import com.howbuy.dtms.order.dao.bo.UpdateIoFileProcessBO;
import com.howbuy.dtms.order.dao.po.HwCapitalRefundPO;
import com.howbuy.dtms.order.dao.po.IoFileProcessRecPO;
import com.howbuy.dtms.order.service.business.batch.traderacordfile.domain.GenerateTradeFileParam;
import com.howbuy.dtms.order.service.business.sendmq.SendMqService;
import com.howbuy.dtms.order.service.commom.enums.FileStatusEnum;
import com.howbuy.dtms.order.service.commom.enums.IoFileTypeEnum;
import com.howbuy.dtms.order.service.commom.enums.ProcessStatusEnum;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.DateUtils;
import com.howbuy.dtms.order.service.mq.domain.batch.capitalfile.CapitalFileMessageDTO;
import com.howbuy.dtms.order.service.repository.IoFileProcessRecRepository;
import com.howbuy.dtms.order.service.repository.HwCapitalRefundRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description: 资金退款文件处理器
 * <AUTHOR>
 * @date 2025-07-10 15:29:03
 * @since JDK 1.8
 */
@Slf4j
@Service
public class CapitalRefundFileProcess {

    @Resource
    private IoFileProcessRecRepository ioFileProcessRecRepository;

    @Resource
    private HwCapitalRefundRepository hwCapitalRefundRepository;

    @Resource
    private CapitalRefundFileGenerateService capitalRefundFileGenerateService;

    @Resource
    private SendMqService sendMqService;

    /**
     * @description: 处理资金退款文件生成
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    public void process() {
        log.info("生成资金退款文件开始");

        // 查询待处理的退款数据
        List<HwCapitalRefundPO> unprocessedRefunds = queryUnprocessedRefunds();
        if (CollectionUtils.isEmpty(unprocessedRefunds)) {
            log.info("没有待处理的资金退款数据，任务结束");
            return;
        }

        log.info("查询到待处理的资金退款数据：{} 条", unprocessedRefunds.size());

        // 创建文件处理记录
        IoFileProcessRecPO ioFileProcessRecPO = createFileProcessRecord();

        try {
            // 使用乐观锁更新文件状态为处理中
            updateFileStatusToProcessing(ioFileProcessRecPO);

            log.info("资金退款文件开始生成，文件名：{}", ioFileProcessRecPO.getFileName());
            
            // 生成文件
            GenerateTradeFileParam param = new GenerateTradeFileParam();
            param.setIoFileProcessRecPO(ioFileProcessRecPO);
            capitalRefundFileGenerateService.generateFile(param);

            // 修改文件状态为生成成功
            updateFileStatusToSuccess(ioFileProcessRecPO);

        } catch (Exception e) {
            log.error("生成资金退款文件异常：{}", Throwables.getStackTraceAsString(e));
            // 修改文件状态为生成失败
            updateFileStatusToFailed(ioFileProcessRecPO);
            AlertLogUtil.alert(this.getClass().getName(), 
                String.format("生成资金退款文件异常！文件名：%s，异常信息：%s", 
                    ioFileProcessRecPO.getFileName(), e.getMessage()));
            throw e;
        }

        // 发送资金退款文件消息
        sendCapitalRefundFileMessage(ioFileProcessRecPO);

        log.info("生成资金退款文件结束！");
    }

    /**
     * @description: 查询未处理的退款数据
     * @return 未处理的退款数据列表
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private List<HwCapitalRefundPO> queryUnprocessedRefunds() {
        // 查询处理状态为0-未处理的数据
        return hwCapitalRefundRepository.selectUnprocessedRefunds();
    }

    /**
     * @description: 创建文件处理记录
     * @return 文件处理记录
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private IoFileProcessRecPO createFileProcessRecord() {
        IoFileProcessRecPO ioFileProcessRecPO = buildIoFileProcessRecPO();
        ioFileProcessRecRepository.insert(ioFileProcessRecPO);
        log.info("创建资金退款文件处理记录，文件名：{}", ioFileProcessRecPO.getFileName());
        return ioFileProcessRecPO;
    }

    /**
     * @description: 构建文件处理记录对象
     * @return 文件处理记录对象
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private IoFileProcessRecPO buildIoFileProcessRecPO() {
        IoFileProcessRecPO ioFileProcessRecPO = new IoFileProcessRecPO();
        String currentDate = DateUtils.getCurrentDate();
        String currentDateTime = DateUtils.dateFormatToString(new Date(), "yyyyMMddHHmmss");
        
        ioFileProcessRecPO.setFileDt(currentDate);
        ioFileProcessRecPO.setFileType(IoFileTypeEnum.CAPITAL_REFUND_FILE.getCode());
        ioFileProcessRecPO.setFileStatus(FileStatusEnum.UNPROCESSED.getCode());
        ioFileProcessRecPO.setProcessStatus(ProcessStatusEnum.UNPROCESSED.getCode());
        
        // 构建文件路径和文件名，包含时分秒
        String fileName = String.format(IoFileTypeEnum.CAPITAL_REFUND_FILE.getFileName(), currentDateTime);
        ioFileProcessRecPO.setFileName(fileName);
        ioFileProcessRecPO.setFilePath(IoFileTypeEnum.CAPITAL_REFUND_FILE.getFilePath());
        
        Date now = new Date();
        ioFileProcessRecPO.setCreateTimestamp(now);
        ioFileProcessRecPO.setUpdateTimestamp(now);
        
        return ioFileProcessRecPO;
    }

    /**
     * @description: 更新文件状态为处理中
     * @param ioFileProcessRecPO 文件处理记录
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private void updateFileStatusToProcessing(IoFileProcessRecPO ioFileProcessRecPO) {
        UpdateIoFileProcessBO updateBO = buildUpdateBO(ioFileProcessRecPO.getId(), 
            FileStatusEnum.PROCESSING.getCode(), 
            Arrays.asList(FileStatusEnum.UNPROCESSED.getCode()));
        ioFileProcessRecRepository.updateFileStatus(updateBO);
    }

    /**
     * @description: 更新文件状态为生成成功
     * @param ioFileProcessRecPO 文件处理记录
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private void updateFileStatusToSuccess(IoFileProcessRecPO ioFileProcessRecPO) {
        UpdateIoFileProcessBO updateBO = buildUpdateBO(ioFileProcessRecPO.getId(), 
            FileStatusEnum.GENERATE_SUCCESS.getCode(), 
            Arrays.asList(FileStatusEnum.PROCESSING.getCode()));
        ioFileProcessRecRepository.updateFileStatus(updateBO);
    }

    /**
     * @description: 更新文件状态为生成失败
     * @param ioFileProcessRecPO 文件处理记录
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private void updateFileStatusToFailed(IoFileProcessRecPO ioFileProcessRecPO) {
        UpdateIoFileProcessBO updateBO = buildUpdateBO(ioFileProcessRecPO.getId(), 
            FileStatusEnum.GENERATE_FAILED.getCode(), 
            Arrays.asList(FileStatusEnum.PROCESSING.getCode()));
        ioFileProcessRecRepository.updateFileStatus(updateBO);
    }

    /**
     * @description: 构建更新BO对象
     * @param id 文件记录ID
     * @param newStatus 新状态
     * @param oldStatusList 原状态列表
     * @return 更新BO对象
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private UpdateIoFileProcessBO buildUpdateBO(Long id, String newStatus, List<String> oldStatusList) {
        UpdateIoFileProcessBO updateBO = new UpdateIoFileProcessBO();
        updateBO.setId(id);
        updateBO.setFileStatus(newStatus);
        updateBO.setOldFileStatusList(oldStatusList);
        updateBO.setUpdateTimestamp(new Date());
        return updateBO;
    }

    /**
     * @description: 发送资金退款文件消息
     * @param ioFileProcessRecPO 文件处理记录
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private void sendCapitalRefundFileMessage(IoFileProcessRecPO ioFileProcessRecPO) {
        log.info("资金退款文件消息开始发送，文件名：{}", ioFileProcessRecPO.getFileName());
        
        // 构建消息DTO
        CapitalFileMessageDTO messageDTO = buildCapitalFileMessageDTO(ioFileProcessRecPO);
        
        // 发送消息
        sendMqService.sendCapitalFileMessage(messageDTO);
        
        // 修改处理状态为成功
        UpdateIoFileProcessBO updateProcessBO = buildUpdateProcessBO(ioFileProcessRecPO.getId(),
            ProcessStatusEnum.SUCCESS.getCode(), 
            Arrays.asList(ProcessStatusEnum.UNPROCESSED.getCode(), ProcessStatusEnum.SUCCESS.getCode()));
        ioFileProcessRecRepository.updateProcessStatus(updateProcessBO);
        
        log.info("资金退款文件消息发送完成");
    }

    /**
     * @description: 构建资金文件消息DTO
     * @param ioFileProcessRecPO 文件处理记录
     * @return 消息DTO
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private CapitalFileMessageDTO buildCapitalFileMessageDTO(IoFileProcessRecPO ioFileProcessRecPO) {
        CapitalFileMessageDTO messageDTO = new CapitalFileMessageDTO();
        messageDTO.setFileType(IoFileTypeEnum.CAPITAL_REFUND_FILE.getCode());
        messageDTO.setFilePath(ioFileProcessRecPO.getFilePath());
        messageDTO.setFileName(ioFileProcessRecPO.getFileName());
        messageDTO.setSubmitDt(ioFileProcessRecPO.getFileDt());
        messageDTO.setAckDt(ioFileProcessRecPO.getFileDt());
        return messageDTO;
    }

    /**
     * @description: 构建处理状态更新BO对象
     * @param id 文件记录ID
     * @param newStatus 新状态
     * @param oldStatusList 原状态列表
     * @return 更新BO对象
     * @author: shaoyang.li
     * @date: 2025-07-10 15:29:03
     * @since JDK 1.8
     */
    private UpdateIoFileProcessBO buildUpdateProcessBO(Long id, String newStatus, List<String> oldStatusList) {
        UpdateIoFileProcessBO updateBO = new UpdateIoFileProcessBO();
        updateBO.setId(id);
        updateBO.setProcessStatus(newStatus);
        updateBO.setOldProcessStatusList(oldStatusList);
        updateBO.setUpdateTimestamp(new Date());
        return updateBO;
    }
}
