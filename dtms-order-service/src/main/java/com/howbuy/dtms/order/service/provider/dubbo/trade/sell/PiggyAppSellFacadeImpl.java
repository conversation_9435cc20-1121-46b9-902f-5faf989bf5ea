package com.howbuy.dtms.order.service.provider.dubbo.trade.sell;

import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.facade.trade.sell.PiggyAppSellFacade;
import com.howbuy.dtms.order.client.domain.request.sell.PiggyAppSellRequest;
import com.howbuy.dtms.order.client.domain.response.sell.PiggyAppSellResponse;
import com.howbuy.dtms.order.service.service.trade.sell.PiggyAppSellService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 储蓄罐申请卖出接口实现
 * @date 2024/8/13 20:03
 * @since JDK 1.8
 */
@DubboService
public class PiggyAppSellFacadeImpl implements PiggyAppSellFacade {
    @Resource
    private PiggyAppSellService piggyAppSellService;

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.sell.PiggyAppSellFacade.execute(PiggyAppSellRequest request)
     * @apiVersion 1.0.0
     * @apiGroup dtms-order-remote
     * @apiName PiggyAppSellFacade
     * @apiDescription 储蓄罐申请卖出
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundTxAcctNo 基金交易账号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} redeemMethod 赎回方式 1-按份额赎回；2-按金额赎回
     * @apiParam (请求体) {String} redeemDirection 赎回方向 1-回银行卡|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投
     * @apiParam (请求体) {Number} appAmt 申请金额
     * @apiParam (请求体) {Number} appVol 申请份额
     * @apiParam (请求体) {Number} relationalDealNo 关联订单号,订单表的订单号
     * @apiParam (请求体) {String} operator 操作人
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"externalDealNo":"wyCdzQzz7","appAmt":7730.567588829184,"hkCustNo":"LsfFL","ipAddress":"khr2V","deviceName":"bsLcfeVuc","systemVersion":"JC","operator":"SZgHRP1","fundTxAcctNo":"AKH5","redeemDirection":"aDOT2","appTm":"pc8zg","macAddress":"tW6V6D","redeemMethod":"x5K1TV8cs","deviceSerialNo":"OLMqlNZ","fundCode":"H","relationalDealNo":8884,"appVol":7730.0231700854165,"appDt":"AG","deviceModel":"8y0zMiXyTT","txCode":"pR","outletCode":"Ict78X3","tradeChannel":"eDMO"}
     * @apiSuccess (响应结果) {String} dealNo 订单号
     * @apiSuccess (响应结果) {String} dealDtlNo 明细订单号
     * @apiSuccessExample 响应结果示例
     * {"dealNo":"k3XKJX","dealDtlNo":"L"}
     */
    @Override
    public Response<PiggyAppSellResponse> execute(PiggyAppSellRequest piggyAppSellRequest) {
        PiggyAppSellResponse response = piggyAppSellService.process(piggyAppSellRequest);
        return Response.ok(response);
    }
}
