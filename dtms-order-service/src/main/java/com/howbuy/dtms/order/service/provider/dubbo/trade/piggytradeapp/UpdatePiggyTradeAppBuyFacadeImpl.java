/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.trade.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.piggytradeapp.UpdatePiggyTradeAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.UpdatePiggyTradeAppBuyResponse;
import com.howbuy.dtms.order.client.facade.trade.piggytradeapp.UpdatePiggyTradeAppBuyFacade;
import com.howbuy.dtms.order.service.service.trade.piggytradeapp.UpatePiggyTradeAppBuyService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 修改储蓄罐交易申请买入接口实现
 * <AUTHOR>
 * @date 2025-07-18 16:09:17
 * @since JDK 1.8
 */
@DubboService
public class UpdatePiggyTradeAppBuyFacadeImpl implements UpdatePiggyTradeAppBuyFacade {

    @Resource
    private UpatePiggyTradeAppBuyService upatePiggyTradeAppBuyService;

    @Override
    public Response<UpdatePiggyTradeAppBuyResponse> execute(UpdatePiggyTradeAppBuyRequest request) {
        UpdatePiggyTradeAppBuyResponse response = upatePiggyTradeAppBuyService.process(request);
        return Response.ok(response);
    }
}
