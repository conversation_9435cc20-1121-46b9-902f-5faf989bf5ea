/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.trade.piggytradeapp;

import com.howbuy.dtms.order.client.domain.request.piggytradeapp.BatchPiggyTradeAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.piggytradeapp.BatchPiggyTradeAppBuyResponse;
import com.howbuy.dtms.order.client.facade.trade.piggytradeapp.BatchPiggyTradeAppBuyFacade;
import com.howbuy.dtms.order.service.service.trade.piggytradeapp.BatchPiggyTradeAppBuyService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: 批量储蓄罐交易申请买入接口实现
 * <AUTHOR>
 * @date 2025-07-16 14:45:30
 * @since JDK 1.8
 */
@DubboService
public class BatchPiggyTradeAppBuyFacadeImpl implements BatchPiggyTradeAppBuyFacade {

    @Resource
    private BatchPiggyTradeAppBuyService batchPiggyTradeAppBuyService;

    @Override
    public Response<BatchPiggyTradeAppBuyResponse> execute(BatchPiggyTradeAppBuyRequest request) {
        BatchPiggyTradeAppBuyResponse response = batchPiggyTradeAppBuyService.process(request);
        return Response.ok(response);
    }
}
