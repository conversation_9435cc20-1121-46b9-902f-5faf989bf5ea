/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.query.sell;

import com.howbuy.dtms.order.client.domain.request.sell.QuerySellInfoRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.sell.SellInfoVO;
import com.howbuy.dtms.order.client.facade.query.sell.QueryFundSellInfoFacade;
import com.howbuy.dtms.order.service.service.query.querysell.QuerySellInfoService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/6/3 9:41
 * @since JDK 1.8
 */
@DubboService
public class QueryFundSellInfoFacadeImpl implements QueryFundSellInfoFacade {

    @Resource
    private QuerySellInfoService querySellInfoService;
    
    /**
     * @description: 基金赎回页面查询接口
     * @author: jinqing.rao
     * @date: 2025/6/3 9:44
     * @since JDK 1.8
     */
    @Override
    public Response<SellInfoVO> execute(QuerySellInfoRequest request) {
        return Response.ok(querySellInfoService.process(request));
    }
}
