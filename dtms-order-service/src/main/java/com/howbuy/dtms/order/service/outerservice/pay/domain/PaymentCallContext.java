/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.pay.domain;

import lombok.Getter;
import lombok.Setter;

/**
 * @description: 支付调用方法入参封装对象
 * <AUTHOR>
 * @date 2025-07-02 15:23:50
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentCallContext {
    
    /**
     * 支付订单号
     */
    private String pmtDealNo;
    
    /**
     * 订单号
     */
    private String dealNo;
    
    /**
     * 订单类型 1-交易 2-edda入金
     */
    private String orderType;
    
    /**
     * 香港客户号
     */
    private String hkCustNo;
    
    /**
     * 基金交易账号
     */
    private String fundTxAcctNo;
    
    /**
     * 资金账号
     */
    private String cpAcctNo;
    
    /**
     * 基金代码
     */
    private String fundCode;
    
    /**
     * 币种
     */
    private String currency;
    
    /**
     * 支付金额
     */
    private String pmtAmt;
    
    /**
     * 支付方式
     */
    private String paymentType;
    
    /**
     * 对账日期
     */
    private String pmtCheckDt;
} 