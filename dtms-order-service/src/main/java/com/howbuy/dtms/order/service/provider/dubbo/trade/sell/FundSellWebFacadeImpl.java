package com.howbuy.dtms.order.service.provider.dubbo.trade.sell;

import com.howbuy.dtms.order.client.domain.request.sell.SellWebRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.sell.SellWebVO;
import com.howbuy.dtms.order.client.facade.trade.sell.FundSellWebFacade;
import com.howbuy.dtms.order.service.service.trade.sell.SellWebService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class FundSellWebFacadeImpl implements FundSellWebFacade {

    @Resource
    private SellWebService sellService;
    
    /**
     * @description: 基金赎回接口
     * @param sellWebRequest
     * @return com.howbuy.dtms.order.client.domain.response.Response<com.howbuy.dtms.order.client.domain.response.sell.SellWebVO>
     * @author: jinqing.rao
     * @date: 2025/6/3 9:50
     * @since JDK 1.8
     */
    @Override
    public Response<SellWebVO> execute(SellWebRequest sellWebRequest) {
        return Response.ok(sellService.process(sellWebRequest));
    }
}
