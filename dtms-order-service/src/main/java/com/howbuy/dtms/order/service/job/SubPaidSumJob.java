/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import com.howbuy.dtms.order.dao.po.HwSubPaidDtlInfoPO;
import com.howbuy.dtms.order.dao.po.HwSubPaidSumInfoPO;
import com.howbuy.dtms.order.service.business.sequence.SequenceService;
import com.howbuy.dtms.order.service.commom.utils.MathUtils;
import com.howbuy.dtms.order.service.job.domain.BaseTaskMessageVO;
import com.howbuy.dtms.order.service.repository.HwDealOrderDtlRepository;
import com.howbuy.dtms.order.service.repository.HwSubPaidDtlInfoRepository;
import com.howbuy.dtms.order.service.repository.HwSubPaidSumInfoRepository;
import crm.howbuy.base.enums.YesOrNoEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2025/6/12 17:47
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SubPaidSumJob extends AbstractBatchMessageJob<BaseTaskMessageVO> {
    @Value("${TOPIC.DTMS_ORDER_SUB_PAID_SUM_INIT}")
    private String topic;

    @Resource
    private HwDealOrderDtlRepository hwDealOrderDtlRepository;

    @Resource
    private HwSubPaidSumInfoRepository hwSubPaidSumInfoRepository;


    @Resource
    private HwSubPaidDtlInfoRepository hwSubPaidDtlInfoRepository;

    @Override
    public void doProcessJob(BaseTaskMessageVO message) {
        // 根据订单号查询 H04289,H04287 基金订单信息
        List<HwDealOrderDtl> hwDealOrderDtlList = hwDealOrderDtlRepository.selectAckSuccessDealByFundCode(Arrays.asList("H00022","H04289", "H04287"));
        if (CollectionUtils.isEmpty(hwDealOrderDtlList)) {
            return;
        }
        for (HwDealOrderDtl hwDealOrderDtl : hwDealOrderDtlList) {
            // 查询订单明细流水是否存在
            HwSubPaidDtlInfoPO hwSubPaidDtlInfoPO = hwSubPaidDtlInfoRepository.selectByDealNoAndFundCodeAndFundTxAcctNo(hwDealOrderDtl.getDealNo(), hwDealOrderDtl.getFundCode(), hwDealOrderDtl.getFundTxAcctNo(), hwDealOrderDtl.getHkCustNo());
            if(null != hwSubPaidDtlInfoPO){
                log.info("订单已存在认缴实缴明细，不处理，dealNo:{}",hwSubPaidDtlInfoPO.getDealNo());
                continue;
            }
            Date date = new Date();
            HwSubPaidSumInfoPO hwSubPaidSumInfoPO = hwSubPaidSumInfoRepository.selectByFundTxAcctNoAndFundCode(hwDealOrderDtl.getHkCustNo(), hwDealOrderDtl.getFundTxAcctNo(), hwDealOrderDtl.getFundCode());
            if(null == hwSubPaidSumInfoPO){
                // 新增
                hwSubPaidSumInfoPO = new HwSubPaidSumInfoPO();
                hwSubPaidSumInfoPO.setSerialNo(SequenceService.getSnowflakeIdDefault());
                hwSubPaidSumInfoPO.setHkCustNo(hwDealOrderDtl.getHkCustNo());
                hwSubPaidSumInfoPO.setFundTxAcctNo(hwDealOrderDtl.getFundTxAcctNo());
                hwSubPaidSumInfoPO.setFundCode(hwDealOrderDtl.getFundCode());
                if(BusinessCodeEnum._112A.getMCode().equals(hwDealOrderDtl.getMiddleBusiCode())){
                    hwSubPaidSumInfoPO.setSubTotalAmt(null == hwDealOrderDtl.getNetAppAmt() ? BigDecimal.ZERO : hwDealOrderDtl.getNetAppAmt());
                    hwSubPaidSumInfoPO.setCumPaidTotalAmt(BigDecimal.ZERO);
                }else{
                    hwSubPaidSumInfoPO.setSubTotalAmt(BigDecimal.ZERO);
                    BigDecimal ackAmt = hwDealOrderDtl.getAckAmt();
                    BigDecimal fee = hwDealOrderDtl.getFee();
                    BigDecimal netAckAmt = MathUtils.subtract(ackAmt, fee);
                    hwSubPaidSumInfoPO.setCumPaidTotalAmt(null == hwDealOrderDtl.getAckAmt() ? BigDecimal.ZERO : netAckAmt);
                }
                hwSubPaidSumInfoPO.setVersion(0);
                hwSubPaidSumInfoPO.setRecStat(YesOrNoEnum.YES.getCode());
                hwSubPaidSumInfoPO.setCreateTimestamp(date);
                hwSubPaidSumInfoPO.setUpdateTimestamp(date);
                hwSubPaidSumInfoRepository.insertSelective(hwSubPaidSumInfoPO);
                //插入明细
                insertHwSubPaidDtlInfoPO(hwDealOrderDtl, hwSubPaidSumInfoPO, date);
            }else{
                // 更新
                BigDecimal cumPaidTotalAmt = hwSubPaidSumInfoPO.getCumPaidTotalAmt();
                BigDecimal subTotalAmt = hwSubPaidSumInfoPO.getSubTotalAmt();
                if(BusinessCodeEnum._112A.getMCode().equals(hwDealOrderDtl.getMiddleBusiCode())){
                    hwSubPaidSumInfoPO.setSubTotalAmt(MathUtils.add(subTotalAmt, hwDealOrderDtl.getNetAppAmt()));
                }else{
                    BigDecimal ackAmt = hwDealOrderDtl.getAckAmt();
                    BigDecimal fee = hwDealOrderDtl.getFee();
                    BigDecimal netAckAmt = MathUtils.subtract(ackAmt, fee);
                    hwSubPaidSumInfoPO.setCumPaidTotalAmt(MathUtils.add(cumPaidTotalAmt, netAckAmt));
                }
                hwSubPaidSumInfoPO.setUpdateTimestamp(date);
                hwSubPaidSumInfoRepository.updateByPrimaryKeySelective(hwSubPaidSumInfoPO);
                //插入明细
                insertHwSubPaidDtlInfoPO(hwDealOrderDtl, hwSubPaidSumInfoPO, date);
            }

        }
    }

    /**
     * @description: 初始化认缴实缴明细
     * @param hwDealOrderDtl	
     * @param hwSubPaidSumInfoPO	
     * @param date
     * @return void
     * @author: jinqing.rao
     * @date: 2025/6/12 19:55
     * @since JDK 1.8
     */
    private void insertHwSubPaidDtlInfoPO(HwDealOrderDtl hwDealOrderDtl, HwSubPaidSumInfoPO hwSubPaidSumInfoPO, Date date) {
        HwSubPaidDtlInfoPO  hwSubPaidDtlInfoPO = new HwSubPaidDtlInfoPO();
        hwSubPaidDtlInfoPO.setSerialNo(hwSubPaidSumInfoPO.getSerialNo());
        hwSubPaidDtlInfoPO.setDtlSerialNo(SequenceService.getSnowflakeIdDefault());
        hwSubPaidDtlInfoPO.setDealNo(hwDealOrderDtl.getDealNo());
        hwSubPaidDtlInfoPO.setFundCode(hwDealOrderDtl.getFundCode());
        hwSubPaidDtlInfoPO.setFundTxAcctNo(hwDealOrderDtl.getFundTxAcctNo());
        hwSubPaidDtlInfoPO.setHkCustNo(hwDealOrderDtl.getHkCustNo());
        hwSubPaidDtlInfoPO.setMiddleBusiCode(hwDealOrderDtl.getMiddleBusiCode());
        hwSubPaidDtlInfoPO.setVersion(0);
        if(BusinessCodeEnum._112A.getMCode().equals(hwDealOrderDtl.getMiddleBusiCode())){
            hwSubPaidDtlInfoPO.setSubAmt(hwDealOrderDtl.getNetAppAmt());
            hwSubPaidDtlInfoPO.setPaidAmt(BigDecimal.ZERO);
        }else{
            hwSubPaidDtlInfoPO.setSubAmt(BigDecimal.ZERO);
            BigDecimal ackAmt = hwDealOrderDtl.getAckAmt();
            BigDecimal fee = hwDealOrderDtl.getFee();
            BigDecimal netAckAmt = MathUtils.subtract(ackAmt, fee);
            hwSubPaidDtlInfoPO.setPaidAmt(netAckAmt);
        }
        hwSubPaidDtlInfoPO.setRecStat(YesOrNoEnum.YES.getCode());
        hwSubPaidDtlInfoPO.setCreateTimestamp(date);
        hwSubPaidDtlInfoPO.setUpdateTimestamp(date);
        hwSubPaidDtlInfoRepository.insertSelective(hwSubPaidDtlInfoPO);
    }

    @Override
    protected String getTopicName() {
        return topic;
    }
}
