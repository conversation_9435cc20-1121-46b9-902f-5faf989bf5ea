/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: (开放日信息)
 * <AUTHOR>
 * @date 2025/7/16 11:17
 * @since JDK 1.8
 */
@Getter
@Setter
public class OpenDtDTO {
    /**
     * 基金代码
     */
    private String fundCode;

    /**
     * 业务类型
     */
    private String busiCode;

    /**
     * 开放日期
     */
    private String openDt;

    /**
     * 预计上报日期
     */
    private String preSubmitTaDt;

    /**
     * 预计上报时间
     */
    private String preSubmitTaTm;

    /**
     * 产品打款截止日期
     */
    private String paymentDeadlineDt;

    /**
     * 产品打款截止时间
     */
    private String paymentDeadlineTime;

    /**
     * 预约开始日期
     */
    private String advanceStartDt;

    /**
     * 预约开始时间
     */
    private String advanceStartTm;

    /**
     * 预约结束日期
     */
    private String advanceEndDt;

    /**
     * 预约结束时间
     */
    private String advanceEndTm;

    /**
     * 开放开始日期
     */
    private String openStartDt;

    /**
     * 开放结束日期
     */
    private String openEndDt;

    /**
     * 本期额度
     */
    private BigDecimal currentLimit;

    /**
     * 本期人数
     */
    private Integer currentNum;

    /**
     * 首次意向实缴比例
     */
    private BigDecimal firstPayInRatio;

    /**
     * 本期实缴比例
     */
    private BigDecimal currentPayInRatio;

    /**
     * 本期每人限制笔数
     */
    private Integer currentPerLimitCount;

    /**
     * 本期每人限制金额
     */
    private BigDecimal currentPerLimitAmt;

    /**
     * 接口请求，申请日期
     */
    private String appDt;
    /**
     * 接口请求，申请时间
     */
    private String appTm;
    /**
     * 接口请求，指定间隔
     */
    private int interval;

    /**
     * 下单结束时间，
     * 每日开放产品，取基金基本信息的下单结束时间返回
     * 预约产品，取预计日历的预约结束时间返回
     */
    private String orderEndTm;
}
