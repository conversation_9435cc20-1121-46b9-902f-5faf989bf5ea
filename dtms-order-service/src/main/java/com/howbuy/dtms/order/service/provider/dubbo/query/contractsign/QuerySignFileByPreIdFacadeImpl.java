/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.query.contractsign;

import com.howbuy.dtms.order.client.domain.request.contractsign.QueryPrebookSignContractRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.contractsign.PrebookSignContractVO;
import com.howbuy.dtms.order.client.facade.query.contractsign.QuerySignFileByPreIdFacade;
import com.howbuy.dtms.order.service.service.trade.contractsign.CustContractSignService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/6/9 15:44
 * @since JDK 1.8
 */
@DubboService
public class QuerySignFileByPreIdFacadeImpl implements QuerySignFileByPreIdFacade {

    @Resource
    private CustContractSignService custContractSignService;
    @Override
    public Response<PrebookSignContractVO> execute(QueryPrebookSignContractRequest request) {
        return Response.ok(custContractSignService.queryByPreid(request.getPrebookId()));
    }
}
