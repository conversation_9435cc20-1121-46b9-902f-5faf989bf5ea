/*
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.payment;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.common.enums.*;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.hkacc.domain.HkCustInfoDTO;
import com.howbuy.dtms.order.service.outerservice.pay.PayOuterService;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentCallContext;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 异步支付业务处理
 * @date 2025-07-02 16:04:22
 * @since JDK 1.8
 */
@Component
@Slf4j
public class AsyncDoPaymentBusiness {

    @Resource
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Resource
    private HwDealOrderRepository hwDealOrderRepository;

    @Resource
    private PayOuterService payOuterService;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    /**
     * @param pmtDealNo 支付订单号
     * @description: 异步支付处理
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    public void process(String pmtDealNo) {
        log.info("异步支付处理开始，支付订单号：{}", pmtDealNo);

        try {
            // 1. 支付订单校验
            HwPaymentOrderPO paymentOrder = validatePaymentOrder(pmtDealNo);

            // 2. 关联业务订单校验
            HwDealOrder dealOrder = null;
            if (PaymentOrderTypeEnum.TRADE.getCode().equals(paymentOrder.getOrderType())) {
                dealOrder = validateDealOrder(paymentOrder.getDealNo().toString());
            }

            // 3. 测试账号过滤
            HkCustInfoDTO hkCustInfoDTO = hkCustInfoOuterService.getHkCustInfo(paymentOrder.getHkCustNo());
            if (YesOrNoEnum.YES.getValue().equals(hkCustInfoDTO.getTest())) {
                log.info("异步支付-测试账号过滤，香港客户号：{}，支付订单号：{}", paymentOrder.getHkCustNo(), pmtDealNo);
                return;
            }

            // 4. 支付前置处理（事务内）
            executePrePaymentProcess(paymentOrder, dealOrder);

            // 5. 调用外部支付
            PaymentResultDTO paymentResult = callExternalPayment(paymentOrder);

            // 6. 支付后置处理
            executePostPaymentProcess(pmtDealNo, paymentResult);

            log.info("异步支付处理完成，支付订单号：{}", pmtDealNo);

        } catch (BusinessException e) {
            String msg = String.format("异步支付处理业务异常，支付订单号：%s，异常信息：%s", pmtDealNo, e.getExceptionDesc());
            log.error(msg, e);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw e;
        } catch (Exception e) {
            String msg = String.format("异步支付处理系统异常，支付订单号：%s，异常信息：%s", pmtDealNo, e.getMessage());
            log.error(msg, e);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR);
        }
    }

    /**
     * @param pmtDealNo 支付订单号
     * @return 支付订单信息
     * @description: 校验支付订单
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    private HwPaymentOrderPO validatePaymentOrder(String pmtDealNo) {
        log.info("校验支付订单，支付订单号：{}", pmtDealNo);

        if (StringUtils.isBlank(pmtDealNo)) {
            log.error("支付订单号为空");
            AlertLogUtil.alert(this.getClass().getSimpleName(), "支付订单号为空");
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付订单号不能为空");
        }

        HwPaymentOrderPO paymentOrder = hwPaymentOrderRepository.selectByPmtDealNo(Long.valueOf(pmtDealNo));
        if (Objects.isNull(paymentOrder)) {
            String msg = String.format("支付订单不存在，支付订单号：%s", pmtDealNo);
            log.error(msg);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付订单不存在");
        }

        if (!TxPmtFlagEnum.UN_PAY.getCode().equals(paymentOrder.getTxPmtFlag())) {
            String msg = String.format("支付订单状态不正确，支付订单号：%s，当前状态：%s", pmtDealNo, TxPmtFlagEnum.getDescByCode(paymentOrder.getTxPmtFlag()));
            log.error(msg);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付订单状态不正确");
        }

        log.info("支付订单校验通过，支付订单号：{}", pmtDealNo);
        return paymentOrder;
    }

    /**
     * @param dealNo 订单号
     * @return 交易订单信息
     * @description: 校验关联的交易订单
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    private HwDealOrder validateDealOrder(String dealNo) {
        log.info("校验关联交易订单，订单号：{}", dealNo);

        HwDealOrder dealOrder = hwDealOrderRepository.selectByDealNo(Long.valueOf(dealNo));
        if (Objects.isNull(dealOrder)) {
            String msg = String.format("关联交易订单不存在，订单号：%s", dealNo);
            log.error(msg);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "关联交易订单不存在");
        }

        if (!PayStatusEnum.UN_PAY.getCode().equals(dealOrder.getPayStatus())) {
            String msg = String.format("交易订单支付状态不正确，订单号：%s，当前支付状态：%s", dealNo, PayStatusEnum.getDescByCode(dealOrder.getPayStatus()));
            log.error(msg);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "交易订单支付状态不正确");
        }

        if (!OrderStatusEnum.APPLY_SUCCESS.getCode().equals(dealOrder.getOrderStatus())) {
            String msg = String.format("交易订单状态不正确，订单号：%s，当前订单状态：%s", dealNo, OrderStatusEnum.getDescByCode(dealOrder.getOrderStatus()));
            log.error(msg);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "交易订单状态不正确");
        }

        log.info("关联交易订单校验通过，订单号：{}", dealNo);
        return dealOrder;
    }

    /**
     * @param paymentOrder 支付订单
     * @param dealOrder    交易订单
     * @description: 执行支付前置处理（事务内）
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    private void executePrePaymentProcess(HwPaymentOrderPO paymentOrder, HwDealOrder dealOrder) {
        log.info("执行支付前置处理，支付订单号：{}", paymentOrder.getPmtDealNo());

        try {
            // 调用Repository层的事务方法来完成状态更新
            hwPaymentOrderRepository.updateToPayingStatusWithTransaction(paymentOrder, dealOrder);

            log.info("支付前置处理完成，支付订单号：{}", paymentOrder.getPmtDealNo());

        } catch (RuntimeException e) {
            String msg = String.format("支付前置处理失败，支付订单号：%s，异常信息：%s", paymentOrder.getPmtDealNo(), e.getMessage());
            log.error(msg, e);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }
    }

    /**
     * @param paymentOrder 支付订单
     * @return 支付结果
     * @description: 调用外部支付服务
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    private PaymentResultDTO callExternalPayment(HwPaymentOrderPO paymentOrder) {
        log.info("调用外部支付服务，支付订单号：{}", paymentOrder.getPmtDealNo());

        try {
            // 构建支付调用上下文
            PaymentCallContext context = buildPaymentCallContext(paymentOrder);

            // 调用外部支付服务
            PaymentResultDTO result = payOuterService.callPayment(context);

            log.info("外部支付服务调用完成，支付订单号：{}，返回结果：{}",
                    paymentOrder.getPmtDealNo(), JSON.toJSONString(result));

            return result;

        } catch (BusinessException e) {
            log.error("调用外部支付服务业务异常，支付订单号：{}，异常信息：{}",
                    paymentOrder.getPmtDealNo(), e.getExceptionDesc(), e);
            throw e;
        } catch (Exception e) {
            log.error("调用外部支付服务系统异常，支付订单号：{}", paymentOrder.getPmtDealNo(), e);
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "调用外部支付服务失败");
        }
    }

    /**
     * @param paymentOrder 支付订单
     * @return 支付调用上下文
     * @description: 构建支付调用上下文
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    private PaymentCallContext buildPaymentCallContext(HwPaymentOrderPO paymentOrder) {
        PaymentCallContext context = new PaymentCallContext();
        context.setPmtDealNo(paymentOrder.getPmtDealNo().toString());
        context.setDealNo(paymentOrder.getDealNo().toString());
        context.setOrderType(paymentOrder.getOrderType());
        context.setHkCustNo(paymentOrder.getHkCustNo());
        context.setFundTxAcctNo(paymentOrder.getFundTxAcctNo());
        context.setCpAcctNo(paymentOrder.getCpAcctNo());
        context.setFundCode(paymentOrder.getFundCode());
        context.setCurrency(paymentOrder.getCurrency());
        context.setPmtAmt(paymentOrder.getPmtAmt().toString());
        context.setPmtCheckDt(paymentOrder.getPmtCheckDt());

        return context;
    }

    /**
     * @param pmtDealNo     支付订单号
     * @param paymentResult 支付结果
     * @description: 执行支付后置处理
     * @author: shaoyang.li
     * @date: 2025-07-02 16:04:22
     * @since JDK 1.8
     */
    private void executePostPaymentProcess(String pmtDealNo, PaymentResultDTO paymentResult) {
        log.info("执行支付后置处理，支付订单号：{}，支付结果：{}", pmtDealNo, JSON.toJSONString(paymentResult));

        try {
            // 更新支付结果
            int updateResult = hwPaymentOrderRepository.updatePaymentResultWithOptimisticLock(
                    Long.valueOf(pmtDealNo),
                    paymentResult.getRetCode(),
                    paymentResult.getRetDesc(),
                    paymentResult.getOutPmtDealNo());

            if (updateResult != 1) {
                String msg = String.format("更新支付结果失败，支付订单号：%s，影响行数：%d", pmtDealNo, updateResult);
                log.error(msg);
                AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
                // 注意：这里不抛出异常，因为支付可能已经成功，只是更新失败，需要人工介入核查
            } else {
                log.info("支付结果更新成功，支付订单号：{}", pmtDealNo);
            }

        } catch (Exception e) {
            String msg = String.format("支付后置处理异常，支付订单号：%s，异常信息：%s", pmtDealNo, e.getMessage());
            log.error(msg, e);
            AlertLogUtil.alert(this.getClass().getSimpleName(), msg);
            // 注意：这里不抛出异常，因为支付可能已经成功，只是后置处理失败，需要人工介入核查
        }

        log.info("支付后置处理完成，支付订单号：{}", pmtDealNo);
    }
}
