package com.howbuy.dtms.order.service.provider.dubbo.trade.payment;

import com.howbuy.dtms.order.client.domain.request.payment.PaymentCheckRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.PaymentCheckResponse;
import com.howbuy.dtms.order.client.facade.trade.payment.PaymentCheckFacade;
import com.howbuy.dtms.order.service.service.trade.payment.PaymentCheckService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 支付对账接口实现
 * <AUTHOR>
 * @date 2025-07-07
 * @since JDK 1.8
 */
@Slf4j
@DubboService
@Component
public class PaymentCheckFacadeImpl implements PaymentCheckFacade {

    @Resource
    private PaymentCheckService paymentCheckService;

    @Override
    public Response<PaymentCheckResponse> execute(PaymentCheckRequest request) {
        return paymentCheckService.execute(request);
    }

}
