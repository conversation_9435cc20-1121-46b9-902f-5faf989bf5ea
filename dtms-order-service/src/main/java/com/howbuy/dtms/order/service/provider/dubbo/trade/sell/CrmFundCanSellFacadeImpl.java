/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.provider.dubbo.trade.sell;

import com.howbuy.dtms.order.client.domain.request.sell.CanSellRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.sell.dubbo.FundCanSellResponse;
import com.howbuy.dtms.order.client.facade.trade.sell.CrmFundCanSellFacade;
import com.howbuy.dtms.order.service.service.trade.sell.CrmCanSellService;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: CRM是否可赎回接口
 * @date 2025/4/25 11:05
 * @since JDK 1.8
 */
@DubboService
public class CrmFundCanSellFacadeImpl implements CrmFundCanSellFacade {

    @Resource
    private CrmCanSellService crmCanSellService;

    @Override
    public Response<FundCanSellResponse> execute(CanSellRequest canSellRequest) {
        //参数校验
        ParamsValidator.validate(canSellRequest, "hkCustNo", "fundCode");
        crmCanSellService.process(canSellRequest);
        return Response.ok();
    }
}
