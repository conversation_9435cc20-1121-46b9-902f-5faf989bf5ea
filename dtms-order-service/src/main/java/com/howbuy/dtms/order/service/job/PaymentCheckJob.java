/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.google.common.base.Throwables;
import com.howbuy.dtms.common.enums.PmtCompFlagEnum;
import com.howbuy.dtms.order.service.business.payment.PaymentCheckBusiness;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.job.domain.PaymentCheckTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @description: 支付对账定时任务处理器
 * <AUTHOR>
 * @date 2025-07-07 15:41:42
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PaymentCheckJob extends AbstractBatchMessageJob<PaymentCheckTaskVO> {

    @Value("${TOPIC.DTMS_ORDER_EC_TASK_PAYMENT_CHECK}")
    private String topic;

    @Resource
    private PaymentCheckBusiness paymentCheckBusiness;

    /**
     * @description: 获取Topic名称
     * @return Topic名称
     * @author: shaoyang.li
     * @date: 2025-07-07 15:41:42
     * @since JDK 1.8
     */
    @Override
    protected String getTopicName() {
        return topic;
    }

    /**
     * @description: 处理支付对账定时任务
     * @param message 任务消息
     * @author: shaoyang.li
     * @date: 2025-07-07 15:41:42
     * @since JDK 1.8
     */
    @Override
    protected void doProcessJob(PaymentCheckTaskVO message) {
        log.info("支付对账定时任务开始执行，消息内容：{}", message);
        // 解析消息参数
        String pmtCheckDt = message.getPmtCheckDt();
        List<String> pmtCompFlagList = parsePmtCompFlagList(message);

        log.info("支付对账参数 - 对账日期：{}，对账状态列表：{}", pmtCheckDt, pmtCompFlagList);

        // 调用支付对账业务处理
        paymentCheckBusiness.paymentCheck(pmtCheckDt, pmtCompFlagList);
        log.info("支付对账定时任务执行完成");
    }

    /**
     * @description: 解析对账状态列表参数
     * @param message 任务消息
     * @return 对账状态列表
     * @author: shaoyang.li
     * @date: 2025-07-07 15:41:42
     * @since JDK 1.8
     */
    private List<String> parsePmtCompFlagList(PaymentCheckTaskVO message) {
        // 如果消息中指定了对账状态列表，则使用指定的
        if (CollectionUtils.isNotEmpty(message.getPmtCompFlagList())) {
            log.info("使用消息中指定的对账状态列表：{}", message.getPmtCompFlagList());
            return message.getPmtCompFlagList();
        }
        
        // 默认使用1-未对账状态
        List<String> defaultPmtCompFlagList = Arrays.asList(PmtCompFlagEnum.UN_CHECK.getCode());
        log.info("使用默认对账状态列表：{}", defaultPmtCompFlagList);
        return defaultPmtCompFlagList;
    }

}
