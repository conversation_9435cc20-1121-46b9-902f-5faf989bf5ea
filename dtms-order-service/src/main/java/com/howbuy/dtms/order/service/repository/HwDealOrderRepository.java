/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.repository;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.page.PageMethod;
import com.howbuy.dtms.common.annotation.DataSource;
import com.howbuy.dtms.common.enums.PmtCompFlagEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.dao.bo.OrderCreateBO;
import com.howbuy.dtms.order.dao.bo.OrderUpdateBO;
import com.howbuy.dtms.order.dao.bo.PiggyPayFailRedeemOrderBO;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwDealOrderDtlMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwDealOrderMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPaymentOrderPOMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.customize.CustomizeHwPiggyTradeAppImportMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.fundtxacct.HwFundTxAcctMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.order.HwDealOrderDtlMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.order.HwDealOrderMapper;
import com.howbuy.dtms.order.dao.mapper.mysql.order.HwPaymentOrderPOMapper;
import com.howbuy.dtms.order.dao.param.CancelOrderBatchUpdateParam;
import com.howbuy.dtms.order.dao.param.CancelOrderDtlUpdateParam;
import com.howbuy.dtms.order.dao.param.CancelOrderUpdateParam;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.query.DealOrderListForAppTradeListQuery;
import com.howbuy.dtms.order.dao.query.HwDealOrderQuery;
import com.howbuy.dtms.order.dao.query.OrderInitQuery;
import com.howbuy.dtms.order.dao.query.QueryHwDealOrderListDTO;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/4/17 19:30
 * @since JDK 1.8
 */
@Slf4j
@Repository
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
@DataSource(value = "mysql")
public class HwDealOrderRepository {

    @Resource
    private HwDealOrderMapper hwDealOrderMapper;

    @Resource
    private CustomizeHwDealOrderMapper customizeHwDealOrderMapper;

    @Resource
    private HwDealOrderDtlMapper hwDealOrderDtlMapper;

    @Resource
    private CustomizeHwDealOrderDtlMapper customizeHwDealOrderDtlMapper;

    @Resource
    private HwFundTxAcctMapper hwFundTxAcctMapper;

    @Resource
    private CustomizeHwPaymentOrderPOMapper customizeHwPaymentOrderPOMapper;

    @Resource
    private HwPaymentOrderPOMapper hwPaymentOrderPOMapper;

    @Resource
    private CustomizeHwPiggyTradeAppImportMapper customizeHwPiggyTradeAppImportMapper;

    /**
     * @param orderCreateBO
     * @return void
     * @description:保存订单
     * <AUTHOR>
     * @date 2024/4/18 17:40
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void saveDealOrder(OrderCreateBO orderCreateBO) {
        hwDealOrderMapper.insertSelective(orderCreateBO.getHwDealOrder());
        hwDealOrderDtlMapper.insertSelective(orderCreateBO.getHwDealOrderDtl());
        if (Objects.nonNull(orderCreateBO.getHwFundTxAcct())) {
            hwFundTxAcctMapper.insertSelective(orderCreateBO.getHwFundTxAcct());
        }
        // 判断PaymentOrderPO非空，则进行PaymentOrderPO新增操作
        if (Objects.nonNull(orderCreateBO.getPaymentOrderPO())) {
            hwPaymentOrderPOMapper.insertSelective(orderCreateBO.getPaymentOrderPO());
        }
        // 储蓄罐申请单乐观锁更新
        if (orderCreateBO.getPiggyTradeAppImportUpdateParam() != null) {
            int updateResult = customizeHwPiggyTradeAppImportMapper.updateIsGeneratedByAppIdAndOldStatus(
                    orderCreateBO.getPiggyTradeAppImportUpdateParam());
            if (updateResult != 1) {
                String errorMsg = String.format("更新储蓄罐申请失败：{}，影响行数：{}",
                        JSON.toJSONString(orderCreateBO.getPiggyTradeAppImportUpdateParam()), updateResult);
                log.error(errorMsg);
                AlertLogUtil.alert(HwDealOrderRepository.class.getName(), errorMsg);
                throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
            }
        }
    }

    /**
     * @param orderUpdateBO
     * @return void
     * @description:更新订单
     * <AUTHOR>
     * @date 2024/5/9 8:59
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void updateDealOrder(OrderUpdateBO orderUpdateBO) {
        if (Objects.nonNull(orderUpdateBO.getUpdateDealOrder())) {
            customizeHwDealOrderMapper.updateByDealNo(orderUpdateBO.getUpdateDealOrder());
        }
        if (Objects.nonNull(orderUpdateBO.getUpdateDealOrderDtl())) {
            customizeHwDealOrderDtlMapper.updateByDealNo(orderUpdateBO.getUpdateDealOrderDtl());
        }
        if (Objects.nonNull(orderUpdateBO.getUpdatePaymentOrder())) {
            customizeHwPaymentOrderPOMapper.updateByDealNo(orderUpdateBO.getUpdatePaymentOrder());
        }
    }

    /**
     * @param orderUpdateBOList
     * @return void
     * @description:(批量更新订单)
     * <AUTHOR>
     * @date 2025/4/28 15:03
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchUpdateDealOrder(List<OrderUpdateBO> orderUpdateBOList) {
        for (OrderUpdateBO orderUpdateBO : orderUpdateBOList) {
            if (Objects.nonNull(orderUpdateBO.getUpdateDealOrder())) {
                customizeHwDealOrderMapper.updateByDealNo(orderUpdateBO.getUpdateDealOrder());
            }
            if (Objects.nonNull(orderUpdateBO.getUpdateDealOrderDtl())) {
                customizeHwDealOrderDtlMapper.updateByDealNo(orderUpdateBO.getUpdateDealOrderDtl());
            }
        }
    }

    /**
     * @param cancelOrderBatchUpdateParamList 撤单批量更新参数列表
     * @return void
     * @description: 批量撤单更新订单（支持乐观锁）
     * @author: shaoyang.li
     * @date: 2025-07-08 20:01:30
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchUpdateCancelOrder(List<CancelOrderBatchUpdateParam> cancelOrderBatchUpdateParamList) {
        for (CancelOrderBatchUpdateParam cancelOrderBatchUpdateParam : cancelOrderBatchUpdateParamList) {
            updateCancelOrder(cancelOrderBatchUpdateParam);
        }
    }

    /**
     * @param cancelOrderBatchUpdateParam 撤单批量更新参数
     * @return void
     * @description: 撤单更新订单（支持乐观锁）
     * @author: shaoyang.li
     * @date: 2025-07-08 20:01:30
     * @since JDK 1.8
     */
    private void updateCancelOrder(CancelOrderBatchUpdateParam cancelOrderBatchUpdateParam) {
        CancelOrderUpdateParam param = cancelOrderBatchUpdateParam.getCancelOrderUpdateParam();
        List<CancelOrderDtlUpdateParam> dtlUpdateParamList = cancelOrderBatchUpdateParam.getCancelOrderDtlUpdateParamList();

        log.info("开始撤单更新订单，订单号：{}", param.getDealNo());

        // 1. 更新交易订单表
        updateDealOrderForCancel(param);

        // 2. 更新交易订单明细表
        for (CancelOrderDtlUpdateParam dtlUpdateParam : dtlUpdateParamList) {
            updateDealOrderDtlForCancel(dtlUpdateParam);
        }

        // 3. 更新支付订单表
        updatePaymentOrderForCancel(param);

        log.info("撤单更新订单完成，订单号：{}", param.getDealNo());
    }

    /**
     * @param param 撤单更新参数
     * @return void
     * @description: 更新交易订单表（撤单）
     * @author: shaoyang.li
     * @date: 2025-07-09 13:20:44
     * @since JDK 1.8
     */
    private void updateDealOrderForCancel(CancelOrderUpdateParam param) {
        // 使用乐观锁更新订单状态和支付状态（撤单场景，不限制旧时间戳）
        int updateResult = customizeHwDealOrderMapper.updatePayStatusAndOrderStatusForCancel(param);

        // 校验更新结果
        if (updateResult != 1) {
            String errorMsg = String.format("撤单更新交易订单失败，可能存在并发冲突，订单号：%s，影响行数：%d",
                    param.getDealNo(), updateResult);
            log.error(errorMsg);
            AlertLogUtil.alert(HwDealOrderRepository.class.getName(), errorMsg);
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        log.info("撤单更新交易订单成功，订单号：{}，订单状态：{}，支付状态：{}",
                param.getDealNo(), param.getOrderStatus(), param.getPayStatus());
    }

    /**
     * @param dtlUpdateParam 订单明细更新参数
     * @return void
     * @description: 更新交易订单明细表（撤单）
     * @author: shaoyang.li
     * @date: 2025-07-08 20:01:30
     * @since JDK 1.8
     */
    private void updateDealOrderDtlForCancel(CancelOrderDtlUpdateParam dtlUpdateParam) {
        // 使用乐观锁更新订单明细申请状态（撤单场景，不限制旧时间戳）
        int updateResult = customizeHwDealOrderDtlMapper.updateAppStatusForCancel(dtlUpdateParam);

        // 校验更新结果
        if (updateResult != 1) {
            String errorMsg = String.format("撤单更新订单明细失败，可能存在并发冲突，明细ID：%s，影响行数：%d",
                    dtlUpdateParam.getId(), updateResult);
            log.error(errorMsg);
            AlertLogUtil.alert(HwDealOrderRepository.class.getName(), errorMsg);
            throw new BusinessException(ExceptionEnum.CONCURRENT_ERROR);
        }

        log.info("撤单更新订单明细成功，明细ID：{}，申请状态：{}",
                dtlUpdateParam.getId(), dtlUpdateParam.getAppStatus());
    }

    /**
     * @param param 撤单更新参数
     * @return void
     * @description: 更新支付订单表（撤单）
     * @author: shaoyang.li
     * @date: 2025-07-09 11:28:18
     * @since JDK 1.8
     */
    private void updatePaymentOrderForCancel(CancelOrderUpdateParam param) {
        log.info("撤单更新支付订单开始，订单号：{}", param.getDealNo());

        // 直接更新支付订单状态为无需付款和无需对账，更新条件为交易支付标识为1-未付款
        Date currentTimestamp = new Date();
        int updateResult = customizeHwPaymentOrderPOMapper.updateForCancel(
                param.getDealNo(),
                TxPmtFlagEnum.NO_NEED_PAY.getCode(), // 无需付款
                PmtCompFlagEnum.NO_NEED_CHECK.getCode(), // 无需对账
                TxPmtFlagEnum.UN_PAY.getCode(), // 更新条件：未付款
                currentTimestamp
        );

        if (updateResult > 0) {
            log.info("撤单更新支付订单成功，订单号：{}，影响行数：{}", param.getDealNo(), updateResult);
        } else {
            log.info("撤单更新支付订单无影响，订单号：{}，可能不存在未付款的支付订单", param.getDealNo());
        }
    }

    /**
     * @param hwDealOrder
     * @return int
     * @description:订单修改
     * <AUTHOR>
     * @date 2024/11/6 17:18
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int updateByDealNo(HwDealOrder hwDealOrder) {
        return customizeHwDealOrderMapper.updateByDealNo(hwDealOrder);
    }

    /**
     * @param externalDealNo
     * @return com.howbuy.dtms.order.dao.po.HwDealOrder
     * @description:根据外部订单号查询订单
     * <AUTHOR>
     * @date 2024/5/14 9:51
     * @since JDK 1.8
     */
    public HwDealOrder selectByExternalDealNo(String externalDealNo) {
        return customizeHwDealOrderMapper.selectByExternalDealNo(externalDealNo);
    }

    /**
     * @param hkCustNo
     * @param dealNo
     * @return java.util.List<com.howbuy.dtms.order.dao.bo.HwDealOrderBO>
     * @description: 根据香港客户号、订单号查询订单
     * <AUTHOR>
     * @date 2024/4/24 14:48
     * @since JDK 1.8
     */
    public HwDealOrder selectByHkCustNoAndDealNo(String hkCustNo, Long dealNo) {
        return customizeHwDealOrderMapper.selectByHkCustNoAndDealNo(hkCustNo, dealNo);
    }

    /**
     * @param dealNo
     * @return java.util.List<com.howbuy.dtms.order.dao.bo.HwDealOrderBO>
     * @description: 根据香港客户号、订单号查询订单
     * <AUTHOR>
     * @date 2024/4/24 14:48
     * @since JDK 1.8
     */
    public HwDealOrder selectByDealNo(Long dealNo) {
        return customizeHwDealOrderMapper.selectByDealNo(dealNo);
    }

    /**
     * @param dealNo
     * @param hkCustNo
     * @return com.howbuy.dtms.order.dao.po.HwDealOrder
     * @description:(根据香港客户号、订单号查询订单)
     * @author: xufanchao
     * @date: 2024/12/24 17:15
     * @since JDK 1.8
     */
    public HwDealOrder selectByDealNoAndHkCustNo(Long dealNo, String hkCustNo) {
        return customizeHwDealOrderMapper.selectByDealNoAndHkCustNo(dealNo, hkCustNo);
    }

    /**
     * @param hkCustNo
     * @param prebookId
     * @return java.util.List<com.howbuy.dtms.order.dao.bo.HwDealOrderBO>
     * @description:根据香港客户号、预约id查询订单
     * <AUTHOR>
     * @date 2024/4/29 18:28
     * @since JDK 1.8
     */
    public HwDealOrder selectByHkCustNoAndPrebookId(String hkCustNo, String prebookId) {
        return customizeHwDealOrderMapper.selectByHkCustNoAndPrebookId(hkCustNo, prebookId);
    }

    /**
     * @param hkCustNo
     * @param fundCode
     * @return int
     * @description: 查询买入在途统计
     * <AUTHOR>
     * @date 2024/4/24 15:06
     * @since JDK 1.8
     */
    public int selectBuyInTransitCount(String hkCustNo, String fundCode) {
        return customizeHwDealOrderMapper.selectBuyInTransitCount(hkCustNo, fundCode);
    }


    /**
     * @param hkCustNo     香港客户号
     * @param fundCodeList 基金编码Code
     * @return int
     * @description: 批量查询购买在途统计
     * @author: jinqing.rao
     * @date: 2024/10/24 19:38
     * @since JDK 1.8
     */
    public int selectBuyInTransitCountByFundCodeList(String hkCustNo, List<String> fundCodeList) {
        return customizeHwDealOrderMapper.selectBuyInTransitCountByFundCodeList(hkCustNo, fundCodeList);
    }

    /**
     * 取申请成功但未付款的购买订单笔数
     *
     * @param hkCustNo
     * @return
     */
    public List<HwDealOrder> selectUnpaid(String hkCustNo) {
        return customizeHwDealOrderMapper.selectUnpaid(hkCustNo);
    }

    /**
     * 查询待确认订单：购买订单-取付款成功但未确认的订单笔数、赎回订单-取申请成功但未确认的赎回订单笔数)
     *
     * @param hkCustNo
     * @return
     */
    public List<HwDealOrder> selectUnconfirmed(String hkCustNo) {
        return customizeHwDealOrderMapper.selectUnconfirmed(hkCustNo);
    }

    /**
     * 查询在途订单数据
     *
     * @param hkCustNo
     * @return
     */
    public int selectDirectOnWayAmtByFundCode(String hkCustNo) {
        return customizeHwDealOrderMapper.selectDirectOnWayAmtByFundCode(hkCustNo);
    }

    /**
     * @param dealNo
     * @param mbusiCodeList
     * @param hkCustNo
     * @param orderStatus
     * @param fundCodes
     * @return java.util.List<com.howbuy.dtms.order.dao.po.HwDealOrder>
     * @description:(分页查询)
     * @author: xufanchao
     * @date: 2024/12/12 14:08
     * @since JDK 1.8
     */
    public List<HwDealOrder> queryHwOrderInfoByPage(Long dealNo, List<String> mbusiCodeList, String hkCustNo, String orderStatus, List<String> fundCodes) {
        return customizeHwDealOrderMapper.queryHwOrderInfoByPage(dealNo, mbusiCodeList, hkCustNo, orderStatus, fundCodes);
    }

    /**
     * @param hkCustNo
     * @param orderStatus
     * @param fundCodes
     * @return java.util.List<com.howbuy.dtms.order.dao.bo.HwDealOrderBO>
     * @description:分页查询订单
     * <AUTHOR>
     * @date 2024/5/11 9:56
     * @since JDK 1.8
     */
    public List<HwDealOrder> queryOrderInfoByPage(String hkCustNo, String orderStatus, List<String> fundCodes) {
        return customizeHwDealOrderMapper.queryOrderInfoByPage(hkCustNo, orderStatus, fundCodes);
    }


    /**
     * @param
     * @return java.util.List<com.howbuy.dtms.order.dao.bo.HwApplyDealOrderBO>
     * @description:()
     * @author: xufanchao
     * @date: 2024/5/10 15:32
     * @since JDK 1.8
     */
    public List<HwDealOrder> listApplyDeal() {
        return customizeHwDealOrderMapper.listApplyDeal();
    }

    /**
     * @param query
     * @return java.util.List<com.howbuy.dtms.order.dao.po.HwDealOrder>
     * @description:(查询订单列表)
     * <AUTHOR>
     * @date 2025/4/24 13:30
     * @since JDK 1.8
     */
    public List<HwDealOrder> selectOrderListByCondition(HwDealOrderQuery query) {
        return customizeHwDealOrderMapper.selectOrderListByCondition(query);
    }

    /**
     * @param orderInitQuery
     * @return com.github.pagehelper.Page<com.howbuy.dtms.order.dao.bo.OrderInitBO>
     * @description:查询订单初始化分页
     * <AUTHOR>
     * @date 2024/5/23 16:37
     * @since JDK 1.8
     */
    public Page<HwDealOrder> selectOrderInitPage(OrderInitQuery orderInitQuery) {
        return customizeHwDealOrderMapper.selectOrderInitPage(orderInitQuery);
    }

    /**
     * @param
     * @return java.util.List<com.howbuy.dtms.order.dao.bo.HwDealOrderBO>
     * @description:查询所有在途订单
     * <AUTHOR>
     * @date 2024/6/4 9:42
     * @since JDK 1.8
     */
    public List<HwDealOrder> selectAllInTransitOrderList() {
        return customizeHwDealOrderMapper.selectAllInTransitOrderList();
    }

    /**
     * @param queryDto
     * @return java.util.List<com.howbuy.dtms.order.dao.po.CmHkProductTradeInfo>
     * @description:(请在此添加描述)
     * @author: xufanchao
     * @date: 2024/11/29 16:10
     * @since JDK 1.8
     */
    public Page<HwDealOrder> selectListByHwOrderRequest(QueryHwDealOrderListDTO queryDto) {
        return customizeHwDealOrderMapper.selectListByHwOrderRequest(queryDto);
    }

    /**
     * 订单号查询订单信息
     *
     * @param dealNoList 订单号列表
     * @return List
     */
    public List<HwDealOrder> selectHwOrderListByDealNo(List<Long> dealNoList) {
        return customizeHwDealOrderMapper.selectByHwOrderNoList(dealNoList);
    }

    /**
     * 订单号查询订单信息 根据申请日期倒序
     *
     * @param dealNoList 订单号列表
     * @return List
     */
    public List<HwDealOrder> selectHwOrderListOrderByAppDtByDealNo(List<Long> dealNoList) {
        return customizeHwDealOrderMapper.selectHwOrderListOrderByAppDtByDealNo(dealNoList);
    }

    /**
     * 根据更新时间 & 业务类型查询所有订单明细号
     *
     * @param updateTime      上次更新时间
     * @param middleBusiCodes 业务类型
     * @return List<Long>
     */
    public List<Long> listHwDealNoListByUpdateTime(String updateTime, List<String> middleBusiCodes) {
        return customizeHwDealOrderMapper.listHwDealNoListByUpdateTime(updateTime, middleBusiCodes);
    }

    /**
     * 根据订单号查询订单列表
     *
     * @param dealNos 订单号
     * @return List<HwDealOrder>
     */
    public List<HwDealOrder> selectListByDealNoList(List<Long> dealNos) {
        return customizeHwDealOrderMapper.selectListByDealNoList(dealNos);
    }

    /**
     * @param hkCustNo
     * @param curCodeSet
     * @return java.util.List<com.howbuy.dtms.order.dao.po.HwDealOrder>
     * @description: 根据订单号和币种查询朱订单信息
     * @author: jinqing.rao
     * @date: 2025/1/9 10:32
     * @since JDK 1.8
     */
    public List<HwDealOrder> selectByHkcustNoAndCurCode(String hkCustNo, Set<String> curCodeSet) {
        return customizeHwDealOrderMapper.selectByHkcustNoAndCurCode(hkCustNo, curCodeSet);
    }

    /**
     * @param orderCreateBOList 订单创建对象列表
     * @return void
     * @description: 批量创建订单、订单明细、基金交易账号
     * <AUTHOR>
     * @date 2025-03-25 15:49:51
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void batchSaveDealOrder(List<OrderCreateBO> orderCreateBOList) {
        if (CollectionUtils.isEmpty(orderCreateBOList)) {
            return;
        }

        for (OrderCreateBO orderCreateBO : orderCreateBOList) {
            try {
                // 创建订单
                hwDealOrderMapper.insertSelective(orderCreateBO.getHwDealOrder());

                // 创建订单明细
                hwDealOrderDtlMapper.insertSelective(orderCreateBO.getHwDealOrderDtl());

                // 创建基金交易账号(如果存在)
                if (Objects.nonNull(orderCreateBO.getHwFundTxAcct())) {
                    hwFundTxAcctMapper.insertSelective(orderCreateBO.getHwFundTxAcct());
                }
            } catch (Exception e) {
                log.error("批量创建订单异常，订单号：{}，客户号：{}",
                        orderCreateBO.getHwDealOrder().getDealNo(),
                        orderCreateBO.getHwDealOrder().getHkCustNo(), e);
                throw new BusinessException(ExceptionEnum.BATCH_ORDER_SAVE_ERROR.getCode(),
                        String.format("客户号[%s]创建订单失败：%s",
                                orderCreateBO.getHwDealOrder().getHkCustNo(), e.getMessage()));
            }
        }
    }

    /**
     * @param appTradeListQuery 查询条件
     * @param page              当前页
     * @param size              每页条数
     * @return java.util.List<com.howbuy.dtms.order.dao.po.HwDealOrder>
     * @description: APP交易记录列表查询
     * @author: jinqing.rao
     * @date: 2025/3/31 17:46
     * @since JDK 1.8
     */
    public Page<HwDealOrder> queryDealOrderListForAppTradeList(DealOrderListForAppTradeListQuery appTradeListQuery, int page, int size) {
        PageMethod.startPage(page, size);
        return customizeHwDealOrderMapper.queryDealOrderListForAppTradeList(appTradeListQuery);
    }

    /**
     * @param dealNo
     * @return java.util.List<com.howbuy.dtms.order.dao.po.HwDealOrder>
     * @description: 通过关联订单号查询订单信息
     * @author: jinqing.rao
     * @date: 2025/4/3 9:23
     * @since JDK 1.8
     */
    public List<HwDealOrder> queryOrderInfoByRelationDealNo(Long dealNo) {
        return customizeHwDealOrderMapper.queryOrderInfoByRelationDealNo(dealNo);
    }

    /**
     * @param hkCustNo     香港客户号
     * @param fundCode     基金编码
     * @param firstBuyFlag 是否首次购买
     * @return com.howbuy.dtms.order.dao.po.HwDealOrder
     * @description: 通过客户号、基金代码、是否首购查询订单信息
     * @author: jinqing.rao
     * @date: 2025/6/10 13:07
     * @since JDK 1.8
     */
    public HwDealOrder selectOrderByHkCustNoAndFundCodeAndFirstBuyFlag(String hkCustNo, String fundCode, String firstBuyFlag, List<String> businessCodeList) {
        return customizeHwDealOrderMapper.selectOrderByHkCustNoAndFundCodeAndFirstBuyFlag(hkCustNo, fundCode, firstBuyFlag, businessCodeList);
    }

    /**
     * @param buyUpdateTimeStart 买入订单更新时间起始
     * @return 赎回订单列表（包含香港客户号和订单号）
     * @description: 查询储蓄罐支付失败赎回订单列表
     * @author: shaoyang.li
     * @date: 2025-07-10 19:59:33
     * @since JDK 1.8
     */
    public List<PiggyPayFailRedeemOrderBO> selectPiggyPayFailRedeemOrders(Date buyUpdateTimeStart) {
        log.info("查询储蓄罐支付失败赎回订单列表，买入订单更新时间起始：{}", buyUpdateTimeStart);

        return customizeHwDealOrderMapper.selectPiggyPayFailRedeemOrders(buyUpdateTimeStart);
    }


}
