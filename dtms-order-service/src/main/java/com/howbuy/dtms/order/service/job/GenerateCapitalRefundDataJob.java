/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.howbuy.dtms.order.service.business.capitalrefund.CapitalRefundDataProcessor;
import com.howbuy.dtms.order.service.job.AbstractBatchMessageJob;
import com.howbuy.dtms.order.service.job.domain.CapitalRefundDataTaskVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 生成资金退款数据定时任务
 * @date 2025-07-10 13:47:32
 * @since JDK 1.8
 */
@Slf4j
@Component
public class GenerateCapitalRefundDataJob extends AbstractBatchMessageJob<CapitalRefundDataTaskVO> {

    @Value("${TOPIC.DTMS_ORDER_EC_TASK_CAPITAL_REFUND_DATA}")
    private String topic;

    @Resource
    private CapitalRefundDataProcessor capitalRefundDataProcessor;

    @Override
    protected String getTopicName() {
        return topic;
    }

    @Override
    protected void doProcessJob(CapitalRefundDataTaskVO message) {
        log.info("生成资金退款数据定时任务开始执行，消息内容：{}", message);

        // 工作日校验
        if (!isHongKongWorkday(message.getWorkdayCheck())) {
            log.info("当前日期非香港工作日，跳过任务执行");
            return;
        }

        // 执行资金退款数据生成
        long startTime = System.currentTimeMillis();
        int totalGenerated = capitalRefundDataProcessor.processCapitalRefundData();
        long costTime = System.currentTimeMillis() - startTime;

        // 记录执行结果
        String resultMessage = String.format("生成资金退款数据定时任务执行完成，共生成退款记录：%d 条，耗时：%d 毫秒",
                totalGenerated, costTime);
        log.info(resultMessage);
    }
}
