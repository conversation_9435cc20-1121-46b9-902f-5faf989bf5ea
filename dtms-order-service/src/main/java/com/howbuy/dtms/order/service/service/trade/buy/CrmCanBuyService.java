/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.service.trade.buy;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.common.enums.BusinessTypeEnum;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import com.howbuy.dtms.common.enums.YesOrNoEnum;
import com.howbuy.dtms.order.client.domain.request.buy.CanBuyRequest;
import com.howbuy.dtms.order.dao.po.HwFundTxAcctPO;
import com.howbuy.dtms.order.service.business.fundtxacct.FundTxAcctService;
import com.howbuy.dtms.order.service.commom.enums.TradeValidatorEnum;
import com.howbuy.dtms.order.service.repository.HwFundTxAcctRepository;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import com.howbuy.dtms.order.service.validate.chain.TradeValidatorHelper;
import com.howbuy.dtms.order.service.validate.context.TradeContext;
import com.howbuy.dtms.order.service.validate.context.TradeContextBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * @description: CRM是否可购买接口
 * @author: jinqing.rao
 * @date: 2025/6/9 10:20
 * @since JDK 1.8
 */
@Service
public class CrmCanBuyService {

    @Resource
    private TradeValidatorHelper tradeValidatorHelper;

    @Resource
    private TradeContextBuilder tradeContextBuilder;

    @Resource
    private HwFundTxAcctRepository hwFundTxAcctRepository;

    @Resource
    private FundTxAcctService fundTxAcctService;

    /**
     * @param request
     * @return void
     * @description:处理
     * <AUTHOR>
     * @date 2024/4/11 10:21
     * @since JDK 1.8
     */
    public void process(CanBuyRequest request) {
        // 基础参数校验 非交易不提供outletCode，不校验
        ParamsValidator.validate(request, "appDt", "appTm", "tradeChannel", "ipAddress");
        // 构建上下文
        TradeContext context = tradeContextBuilder.buildTradeContext(request, request.getHkCustNo(), request.getFundCode(), BusinessTypeEnum.BUY, null);

        // 查询默认非全委基金交易账号
        HwFundTxAcctPO hwFundTxAcctPO = hwFundTxAcctRepository.getNotFullFundTxAcct(request.getHkCustNo());
        if (Objects.isNull(hwFundTxAcctPO)) {
            hwFundTxAcctPO = fundTxAcctService.createFundTxAcct(context.getHkCustNo(), FundTxAcctTypeEnum.NON_FULL.getCode(), null);
        }
        context.setFundTxAcctNo(hwFundTxAcctPO.getFundTxAcctNo());
        context.setHwFundTxAcct(hwFundTxAcctPO);

        // 默认购买 0
        TradeContext.BuyBean buyBean = new TradeContext.BuyBean();
        buyBean.setBuyAmt(BigDecimal.ZERO);
        context.setBuyBean(buyBean);
        // 分次Call产品的校验逻辑
        if (StringUtils.equals(YesOrNoEnum.YES.getValue(), context.getFundBasicInfoDTO().getGradationCall())) {
            gradationCallValidate(context);
        } else {
            // 构建校验链
            List<String> validatorList = tradeValidatorHelper.buildChain(
                    TradeValidatorEnum.PRODUCT_AGE,
                    TradeValidatorEnum.PRODUCT_CHANNEL,
                    TradeValidatorEnum.PRODUCT_BUSINESS,
                    TradeValidatorEnum.OPEN_DT,
                    TradeValidatorEnum.PRODUCT_NAV_STATUS,
                    TradeValidatorEnum.CURRENT_PERIOD_NUMBER_AND_AMOUNT,
                    TradeValidatorEnum.FUND_FEE_RATE_CONFIG
            );
            // 执行校验
            tradeValidatorHelper.doValidate(context, validatorList);
        }
    }

    /**
     * @param context 上下文内容
     * @return void
     * @description: 分次CALL校验
     * @author: jinqing.rao
     * @date: 2025/5/20 16:04
     * @since JDK 1.8
     */
    private void gradationCallValidate(TradeContext context) {

        // 认缴订单校验
        subOrderValidator(context);

        // 实缴订单校验
        paidOrderValidator(context);

    }


    /**
     * @param context
     * @return void
     * @description: 实缴开发日历校验
     * @author: jinqing.rao
     * @date: 2025/5/21 12:19
     * @since JDK 1.8
     */
    private void paidOrderValidator(TradeContext context) {
        TradeContext paidOpenDtTradeContext = new TradeContext();
        paidOpenDtTradeContext.setBaseRequest(context.getBaseRequest());
        paidOpenDtTradeContext.setHkCustNo(context.getHkCustNo());
        paidOpenDtTradeContext.setFundCode(context.getFundCode());
        paidOpenDtTradeContext.setIsSupportPreBook(context.getIsSupportPreBook());
        paidOpenDtTradeContext.setFundBasicInfoDTO(context.getFundBasicInfoDTO());
        paidOpenDtTradeContext.setProductChannelEnum(context.getProductChannelEnum());
        paidOpenDtTradeContext.setBuyBean(context.getBuyBean());
        paidOpenDtTradeContext.setProductBusinessCode(BusinessCodeEnum._112B.getCode());
        paidOpenDtTradeContext.setBusinessCodeEnum(BusinessCodeEnum._112B);

        paidOpenDtTradeContext.setProductChannelEnum(context.getProductChannelEnum());
        paidOpenDtTradeContext.setHkCustInfoDTO(context.getHkCustInfoDTO());
        // 构建校验链
        List<String> paidOpenDtValidatorList = tradeValidatorHelper.buildChain(
                TradeValidatorEnum.PRODUCT_AGE,
                TradeValidatorEnum.PRODUCT_CHANNEL,
                TradeValidatorEnum.PRODUCT_BUSINESS,
                TradeValidatorEnum.FUND_FEE_RATE_CONFIG,
                TradeValidatorEnum.OPEN_DT,
                TradeValidatorEnum.PRODUCT_NAV_STATUS,
                TradeValidatorEnum.CURRENT_PERIOD_NUMBER_AND_AMOUNT,
                TradeValidatorEnum.FIRST_PAID_RATIO_PARAMS_CONFIG,
                TradeValidatorEnum.CURRENT_PERIOD_SINGLE_NUMBER_AND_AMOUNT
        );
        tradeValidatorHelper.doValidate(paidOpenDtTradeContext, paidOpenDtValidatorList);
    }

    /**
     * @param context
     * @return void
     * @description: 认缴开放日历校验
     * @author: jinqing.rao
     * @date: 2025/5/21 12:19
     * @since JDK 1.8
     */
    private void subOrderValidator(TradeContext context) {
        TradeContext subOpenDtTradeContext = new TradeContext();
        subOpenDtTradeContext.setBaseRequest(context.getBaseRequest());
        subOpenDtTradeContext.setHkCustNo(context.getHkCustNo());
        subOpenDtTradeContext.setFundCode(context.getFundCode());
        subOpenDtTradeContext.setIsSupportPreBook(context.getIsSupportPreBook());
        subOpenDtTradeContext.setFundBasicInfoDTO(context.getFundBasicInfoDTO());
        subOpenDtTradeContext.setHkCustInfoDTO(context.getHkCustInfoDTO());

        subOpenDtTradeContext.setBuyBean(context.getBuyBean());
        subOpenDtTradeContext.setProductChannelEnum(context.getProductChannelEnum());

        subOpenDtTradeContext.setProductBusinessCode(BusinessCodeEnum._112A.getCode());
        subOpenDtTradeContext.setBusinessCodeEnum(BusinessCodeEnum._112A);
        // 构建校验链
        List<String> openDtValidatorList = tradeValidatorHelper.buildChain(
                TradeValidatorEnum.PRODUCT_AGE,
                TradeValidatorEnum.OPEN_DT,
                TradeValidatorEnum.PRODUCT_NAV_STATUS,
                TradeValidatorEnum.CURRENT_PERIOD_NUMBER_AND_AMOUNT,
                TradeValidatorEnum.CURRENT_PERIOD_SINGLE_NUMBER_AND_AMOUNT
        );
        tradeValidatorHelper.doValidate(subOpenDtTradeContext, openDtValidatorList);
    }
}
