/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.pay.domain;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * @description: 支付结果数据传输对象
 * <AUTHOR>
 * @date 2025-07-02 15:23:50
 * @since JDK 1.8
 */
@Getter
@Setter
public class PaymentResultDTO {

    /**
     * 调用成功标记（true-调用成功，false-调用失败）
     */
    private boolean success;

    /**
     * 支付返回码
     */
    private String retCode;
    
    /**
     * 支付返回描述
     */
    private String retDesc;
    
    /**
     * 外部支付订单号
     */
    private String outPmtDealNo;
    
    /**
     * 支付订单号
     */
    private String pmtDealNo;
    
    /**
     * 订单类型
     */
    private String orderType;
    
    /**
     * 交易支付标记
     */
    private String txPmtFlag;
    
    /**
     * 支付对账标记
     */
    private String pmtCompFlag;
    
    /**
     * 币种
     */
    private String currency;
    
    /**
     * 支付金额
     */
    private BigDecimal pmtAmt;
    
    /**
     * 对账日期
     */
    private String pmtCheckDt;
}