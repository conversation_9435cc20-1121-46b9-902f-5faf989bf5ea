/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.validate.chain.atomic.buy;

import com.howbuy.dtms.order.service.commom.annotaion.TradeValidatorAnnotation;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.enums.TradeValidatorEnum;
import com.howbuy.dtms.order.service.commom.exception.ValidateException;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.OpenDtDTO;
import com.howbuy.dtms.order.service.validate.chain.TradeValidator;
import com.howbuy.dtms.order.service.validate.context.TradeContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: (首次实缴比例校验)
 * @date 2025/4/7 15:22
 * @since JDK 1.8
 */
@Slf4j
@Service
@TradeValidatorAnnotation(TradeValidatorEnum.FIRST_PAID_RATIO_PARAMS_CONFIG)
public class FirstPaidRatioParamsConfigValidator implements TradeValidator {


    @Override
    public void validate(TradeContext context) {

        OpenDtDTO openDtDTO = context.getOpenDtDTO();
        // 空或者 等于0
        if(null == openDtDTO.getFirstPayInRatio() || openDtDTO.getFirstPayInRatio().compareTo(BigDecimal.ZERO) <= 0){
            throw new ValidateException(ExceptionEnum.FIRST_PAID_RATIO_NOT_CONFIGURED);
        }
    }
}
