package com.howbuy.dtms.order.service.provider.dubbo.trade.buy;

import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.facade.trade.buy.PiggyAppBuyFacade;
import com.howbuy.dtms.order.client.domain.request.buy.PiggyAppBuyRequest;
import com.howbuy.dtms.order.client.domain.response.buy.PiggyAppBuyResponse;
import com.howbuy.dtms.order.service.service.trade.buy.PiggyAppBuyService;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/13 20:03
 * @since JDK 1.8
 */
@DubboService
public class PiggyAppBuyFacadeImpl implements PiggyAppBuyFacade {

    @Resource
    private PiggyAppBuyService piggyAppBuyService;

    /**
     * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.buy.PiggyAppBuyFacade.execute(PiggyAppBuyRequest request)
     * @apiVersion 1.0.0
     * @apiGroup dtms-order-remote
     * @apiName PiggyAppBuyFacade
     * @apiDescription 储蓄罐申请买入
     * @apiParam (请求体) {String} hkCustNo 香港客户号
     * @apiParam (请求体) {String} fundCode 基金代码
     * @apiParam (请求体) {String} payMethod 支付方式  1-电汇、2-支票、3-海外储蓄罐
     * @apiParam (请求体) {Number} buyAmt 买入金额
     * @apiParam (请求体) {Number} estimateFee 预估手续费
     * @apiParam (请求体) {String} fundTxAcctNo 基金交易账号
     * @apiParam (请求体) {Number} discountRate 折扣率
     * @apiParam (请求体) {String} relationalDealNo 关联订单号
     * @apiParam (请求体) {String} txCode 交易码
     * @apiParam (请求体) {String} appDt 申请日期 yyyyMMdd
     * @apiParam (请求体) {String} appTm 申请时间 HHmmss
     * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP；
     * @apiParam (请求体) {String} outletCode 网点号
     * @apiParam (请求体) {String} ipAddress ipAddress
     * @apiParam (请求体) {String} externalDealNo 外部订单号
     * @apiParam (请求体) {String} macAddress MAC地址
     * @apiParam (请求体) {String} deviceSerialNo 设备序列号
     * @apiParam (请求体) {String} deviceModel 设备型号
     * @apiParam (请求体) {String} deviceName 设备名称
     * @apiParam (请求体) {String} systemVersion 系统版本号
     * @apiParamExample 请求体示例
     * {"buyAmt":75.**************,"externalDealNo":"cDLKR","hkCustNo":"Z","preSubmitTaDt":"KUTQXElTls","ipAddress":"vN2","estimateFee":953.3071543759186,"dealNo":2673,"deviceName":"oEt3FguxP","systemVersion":"1","appTm":"RVUC","macAddress":"8Kjg8","deviceSerialNo":"l7xrfvF5cn","fundCode":"DbFE0GTL","payMethod":"SAPoO0ry","payEndDt":"ra89e","appDt":"F0","deviceModel":"K6KjTK541","txCode":"zfNLkZh3r","outletCode":"Z","tradeChannel":"1croVhE8bC"}
     * @apiSuccess (响应结果) {String} code 状态码
     * @apiSuccess (响应结果) {String} description 描述信息
     * @apiSuccess (响应结果) {Object} data 数据封装
     * @apiSuccess (响应结果) {String} data.dealNo 订单号
     * @apiSuccess (响应结果) {String} data.dealDtlNo 明细订单号
     * @apiSuccessExample 响应结果示例
     * {"code":"qyMgR4gA","data":{"dealNo":8659,"dealDtlNo":8693},"description":"9Cfp"}
     */
    @Override
    public Response<PiggyAppBuyResponse> execute(PiggyAppBuyRequest piggyAppBuyRequest) {
        PiggyAppBuyResponse response = piggyAppBuyService.process(piggyAppBuyRequest);
        return Response.ok(response);
    }
}
