/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 生成资金退款数据定时任务消息VO
 * <AUTHOR>
 * @date 2025-07-10 13:47:32
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CapitalRefundDataTaskVO extends BaseTaskMessageVO {
    
    /**
     * 工作日校验开关
     * "1"表示需要校验香港工作日，"0"或不传此字段表示不校验，直接执行
     */
    private String workdayCheck;
}
