/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.repository;

import com.howbuy.dtms.common.annotation.DataSource;
import com.howbuy.dtms.order.dao.mapper.mysql.io.IoCapitalRefundExportPOMapper;
import com.howbuy.dtms.order.dao.po.IoCapitalRefundExportPO;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.ListSplitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 资金退款导出流水表数据仓库
 * <AUTHOR>
 * @date 2025-07-10 15:29:03
 * @since JDK 1.8
 */
@Slf4j
@Repository
@DataSource(value = "mysql")
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class IoCapitalRefundExportRepository {

    @Resource
    private IoCapitalRefundExportPOMapper ioCapitalRefundExportPOMapper;

    @Resource
    private HwCapitalRefundRepository hwCapitalRefundRepository;

    /**
     * 批量插入资金退款导出记录
     * 
     * @param exportList 导出记录列表
     * @return 插入成功的记录数
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int batchInsert(List<IoCapitalRefundExportPO> exportList) {
        if (CollectionUtils.isEmpty(exportList)) {
            log.warn("批量插入资金退款导出记录，记录列表为空");
            return 0;
        }
        
        log.info("批量插入资金退款导出记录，记录数：{}", exportList.size());
        
        int totalInserted = 0;
        // 分批处理，每批1000条
        ListSplitter<IoCapitalRefundExportPO> listSplitter = new ListSplitter<>(exportList, 1000);
        
        while (listSplitter.hasNext()) {
            List<IoCapitalRefundExportPO> batch = listSplitter.next();
            int result = ioCapitalRefundExportPOMapper.batchInsert(batch);
            totalInserted += result;
        }
        
        log.info("批量插入资金退款导出记录完成，成功插入：{} 条", totalInserted);
        return totalInserted;
    }

    /**
     * 插入单条资金退款导出记录
     * 
     * @param exportPO 导出记录
     * @return 插入成功的记录数
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int insert(IoCapitalRefundExportPO exportPO) {
        log.info("插入资金退款导出记录，订单号：{}", exportPO.getDealNo());
        
        int result = ioCapitalRefundExportPOMapper.insertSelective(exportPO);
        
        log.info("插入资金退款导出记录完成，订单号：{}，结果：{}", exportPO.getDealNo(), result);
        return result;
    }

    /**
     * @description: 保存导出记录列表并更新退款状态（事务处理）
     * @param exportList 导出记录列表
     * @param dealNoList 订单号列表
     * @return 处理结果
     * @author: shaoyang.li
     * @date: 2025-07-10 16:22:33
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public boolean saveExportListAndUpdateStatus(List<IoCapitalRefundExportPO> exportList, List<Long> dealNoList) {
        try {
            log.info("开始保存导出记录并更新退款状态，导出记录数：{}，订单号数：{}", exportList.size(), dealNoList.size());

            // 1. 批量插入导出记录
            int insertCount = batchInsert(exportList);
            log.info("批量插入导出记录完成，插入记录数：{}", insertCount);

            // 2. 批量更新退款记录处理状态为已处理
            int updateCount = hwCapitalRefundRepository.batchUpdateProcessStatusToProcessed(dealNoList);
            log.info("批量更新退款记录处理状态完成，更新记录数：{}", updateCount);

            // 3. 校验插入条数与更新条数是否一致
            int expectedCount = exportList.size();
            if (insertCount != expectedCount || updateCount != expectedCount) {
                String errorMessage = String.format("插入条数与修改条数不一致！预期条数：%d，插入条数：%d，更新条数：%d", 
                    expectedCount, insertCount, updateCount);
                log.error(errorMessage);
                // 发送异常告警
                AlertLogUtil.alert(this.getClass().getName(), errorMessage);
                // 抛出业务异常，触发事务回滚
                throw new BusinessException(errorMessage);
            }

            log.info("保存导出记录并更新退款状态成功，处理记录数：{}", expectedCount);
            return true;
        } catch (Exception e) {
            log.error("保存导出记录并更新退款状态异常：{}", e.getMessage(), e);
            throw e; // 重新抛出异常，让事务回滚
        }
    }
}
