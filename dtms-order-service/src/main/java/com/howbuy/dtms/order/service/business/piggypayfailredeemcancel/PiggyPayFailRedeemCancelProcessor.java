/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.business.piggypayfailredeemcancel;

import com.google.common.base.Throwables;
import com.howbuy.dtms.common.enums.CancelTypeEnum;
import com.howbuy.dtms.common.enums.TradeChannelEnum;
import com.howbuy.dtms.order.client.domain.request.cannel.CounterCancelRequest;
import com.howbuy.dtms.order.dao.bo.PiggyPayFailRedeemOrderBO;
import com.howbuy.dtms.order.service.commom.constant.Constants;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.DateUtils;

import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.service.trade.cancelorder.CounterCancelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @description: 储蓄罐支付失败赎回撤单处理器
 * @author: shaoyang.li
 * @date: 2025-07-10 19:59:33
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PiggyPayFailRedeemCancelProcessor {

    @Resource
    private HwDealOrderRepository hwDealOrderRepository;
    
    @Resource
    private CounterCancelService counterCancelService;

    /**
     * @description: 处理储蓄罐支付失败赎回撤单任务
     * @author: shaoyang.li
     * @date: 2025-07-10 19:59:33
     * @since JDK 1.8
     */
    public void process() {
        log.info("储蓄罐支付失败赎回撤单处理开始");
        
        // 1. 查询符合条件的赎回订单列表（香港客户号+订单号）
        List<PiggyPayFailRedeemOrderBO> redeemOrders = queryPiggyPayFailRedeemOrders();
        
        if (redeemOrders.isEmpty()) {
            log.info("未查询到符合条件的储蓄罐支付失败赎回订单");
            return;
        }
        
        log.info("查询到{}个符合条件的储蓄罐支付失败赎回订单", redeemOrders.size());
        
        // 2. 遍历订单列表，调用柜台撤单接口
        int successCount = 0;
        int failCount = 0;
        
        for (PiggyPayFailRedeemOrderBO redeemOrder : redeemOrders) {
            try {
                // 调用柜台撤单接口
                processCounterCancel(redeemOrder);
                successCount++;
                log.info("客户号：{}，订单号：{}，柜台撤单处理成功", redeemOrder.getHkCustNo(), redeemOrder.getDealNo());
            } catch (Exception e) {
                failCount++;
                log.error("客户号：{}，订单号：{}，柜台撤单处理失败，异常信息：{}", 
                    redeemOrder.getHkCustNo(), redeemOrder.getDealNo(), Throwables.getStackTraceAsString(e));
                AlertLogUtil.alert(this.getClass().getName(), 
                    String.format("储蓄罐支付失败赎回撤单处理失败！客户号：%s，订单号：%s，异常信息：%s", 
                        redeemOrder.getHkCustNo(), redeemOrder.getDealNo(), e.getMessage()));
            }
        }
        
        log.info("储蓄罐支付失败赎回撤单处理完成，总数：{}，成功：{}，失败：{}", 
                redeemOrders.size(), successCount, failCount);
    }

    /**
     * @description: 查询符合条件的储蓄罐支付失败赎回订单列表
     * @return 订单列表（包含香港客户号和订单号）
     * @author: shaoyang.li
     * @date: 2025-07-10 19:59:33
     * @since JDK 1.8
     */
    private List<PiggyPayFailRedeemOrderBO> queryPiggyPayFailRedeemOrders() {
        // 计算查询时间范围：当前时间-10天
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -10);
        Date tenDaysAgo = calendar.getTime();
        
        log.info("查询储蓄罐支付失败赎回订单，时间范围：{} 至今", DateUtils.dateFormatToString(tenDaysAgo, DateUtils.YYYY_MM_DD_HH_MM_SS));
        
        return hwDealOrderRepository.selectPiggyPayFailRedeemOrders(tenDaysAgo);
    }

    /**
     * @description: 调用柜台撤单接口处理订单
     * @param redeemOrder 赎回订单信息
     * @author: shaoyang.li
     * @date: 2025-07-10 19:59:33
     * @since JDK 1.8
     */
    private void processCounterCancel(PiggyPayFailRedeemOrderBO redeemOrder) {
        // 构建柜台撤单请求
        CounterCancelRequest request = new CounterCancelRequest();
        request.setHkCustNo(redeemOrder.getHkCustNo());
        request.setDealNo(String.valueOf(redeemOrder.getDealNo()));
        
        // 获取当前服务器时间
        Date currentTime = new Date();
        request.setAppDt(DateUtils.dateFormatToString(currentTime, DateUtils.YYYYMMDD));
        request.setAppTm(DateUtils.dateFormatToString(currentTime, DateUtils.HHMMSS));
        
        // 设置交易渠道为柜台
        request.setTradeChannel(TradeChannelEnum.COUNTER.getCode());
        
        // 设置网点代码为香港网点
        request.setOutletCode(Constants.HK_OUTLET_CODE);
        
        // 设置IP地址为本机地址
        request.setIpAddress(Constants.IP_ADDRESS);
        
        // 设置外部订单号为订单号
        request.setExternalDealNo(String.valueOf(redeemOrder.getDealNo()));
        
        // 设置撤单类型为强制取消
        request.setCancelType(CancelTypeEnum.FORCE_CANCEL.getCode());
        request.setCancelReason("储蓄罐支付失败自动撤单");

        // 调用柜台撤单服务
        counterCancelService.process(request);
    }
}
