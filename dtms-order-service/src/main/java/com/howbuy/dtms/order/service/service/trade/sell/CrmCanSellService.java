/**
 * Copyright (c) 2024, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.service.trade.sell;

import com.howbuy.dtms.common.enums.BusinessCodeEnum;
import com.howbuy.dtms.common.enums.BusinessTypeEnum;
import com.howbuy.dtms.common.enums.FundTxAcctTypeEnum;
import com.howbuy.dtms.order.client.domain.request.sell.CanSellRequest;
import com.howbuy.dtms.order.dao.po.HwFundTxAcctPO;
import com.howbuy.dtms.order.service.business.fundtxacct.FundTxAcctService;
import com.howbuy.dtms.order.service.commom.enums.TradeValidatorEnum;
import com.howbuy.dtms.order.service.repository.HwFundTxAcctRepository;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import com.howbuy.dtms.order.service.validate.chain.TradeValidatorHelper;
import com.howbuy.dtms.order.service.validate.context.TradeContext;
import com.howbuy.dtms.order.service.validate.context.TradeContextBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @description: CRM是否可卖出服务
 * @author: jinqing.rao
 * @date: 2025/6/9 9:55
 * @since JDK 1.8
 */
@Service
public class CrmCanSellService {
    @Resource
    private TradeValidatorHelper tradeValidatorHelper;

    @Resource
    private TradeContextBuilder tradeContextBuilder;

    @Resource
    private HwFundTxAcctRepository hwFundTxAcctRepository;

    @Resource
    private FundTxAcctService fundTxAcctService;

    /**
     * @param request
     * @return void
     * @description:处理
     * <AUTHOR>
     * @date 2024/5/6 15:09
     * @since JDK 1.8
     */
    public void process(CanSellRequest request) {
        // 基础参数校验 非交易不提供outletCode，不校验
        ParamsValidator.validate(request, "appDt", "appTm", "tradeChannel", "ipAddress");
        // 构建上下文
        TradeContext context = tradeContextBuilder.buildTradeContext(request, request.getHkCustNo(), request.getFundCode(), BusinessTypeEnum.SELL, BusinessCodeEnum.REDEEM);

        context.setSellBean(new TradeContext.SellBean());

        // 如果基金校验账号空，默认用非全委的基金交易账号
        if (StringUtils.isBlank(request.getFundTxAcctNo())) {
            HwFundTxAcctPO hwFundTxAcctPO = hwFundTxAcctRepository.getNotFullFundTxAcct(request.getHkCustNo());
            if (Objects.isNull(hwFundTxAcctPO)) {
                hwFundTxAcctPO = fundTxAcctService.createFundTxAcct(context.getHkCustNo(), FundTxAcctTypeEnum.NON_FULL.getCode(), null);
            }
            context.setFundTxAcctNo(hwFundTxAcctPO.getFundTxAcctNo());
            context.setHwFundTxAcct(hwFundTxAcctPO);
        }
        // 构建校验链
        List<String> validatorList = tradeValidatorHelper.buildChain(
                TradeValidatorEnum.PRODUCT_CHANNEL,
                TradeValidatorEnum.PRODUCT_BUSINESS,
                TradeValidatorEnum.OPEN_DT,
                TradeValidatorEnum.PRODUCT_NAV_STATUS,
                TradeValidatorEnum.BALANCE_AVAILABLE_SHARE
        );
        // 执行校验
        tradeValidatorHelper.doValidate(context, validatorList);
    }

}
