package com.howbuy.dtms.order.service.service.trade.payment;

import com.howbuy.dtms.common.enums.OrderStatusEnum;
import com.howbuy.dtms.common.enums.PayStatusEnum;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.client.domain.request.payment.ResetTxPmtFlagRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.ResetTxPmtFlagResponse;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.outerservice.pay.PayOuterService;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 重置交易支付标识业务服务
 * <AUTHOR>
 * @date 2025-07-07 19:02:55
 * @since JDK 1.8
 */
@Service
@Slf4j
public class ResetTxPmtFlagService {

    @Resource
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Resource
    private HwDealOrderRepository hwDealOrderRepository;

    @Resource
    private PayOuterService payOuterService;

    /**
     * @description: 重置交易支付标识
     * @param request 重置请求参数
     * @return 重置结果
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    public Response<ResetTxPmtFlagResponse> execute(ResetTxPmtFlagRequest request) {
        log.info("重置交易支付标识开始，支付订单号：{}", request.getPmtDealNo());

        try {
            // 1. 参数校验并转换
            Long pmtDealNo = validateAndConvertPmtDealNo(request);

            // 2. 查询并校验支付订单
            HwPaymentOrderPO paymentOrder = validatePaymentOrder(pmtDealNo);

            // 3. 查询并校验关联交易订单
            HwDealOrder dealOrder = null;
            if (PaymentOrderTypeEnum.TRADE.getCode().equals(paymentOrder.getOrderType())) {
                dealOrder = validateDealOrder(paymentOrder.getDealNo());
            }

            // 4. 校验外部支付系统状态
            validateExternalPaymentStatus(pmtDealNo);

            // 5. 执行状态重置
            resetPaymentStatus(paymentOrder, dealOrder);

            log.info("重置交易支付标识成功，支付订单号：{}", request.getPmtDealNo());

            ResetTxPmtFlagResponse response = new ResetTxPmtFlagResponse();
            return Response.ok(response);
        } catch (BusinessException e) {
            log.error("重置交易支付标识业务异常，支付订单号：{}，异常码：{}，异常信息：{}", 
                    request.getPmtDealNo(), e.getExceptionCode(), e.getExceptionDesc());
            return Response.fail(e.getExceptionCode(), e.getExceptionDesc());
        } catch (Exception e) {
            log.error("重置交易支付标识系统异常，支付订单号：{}", request.getPmtDealNo(), e);
            AlertLogUtil.alert("重置交易支付标识系统异常",
                    String.format("支付订单号：%s，异常信息：%s", request.getPmtDealNo(), e.getMessage()));
            return Response.fail(ExceptionEnum.SYSTEM_ERROR.getCode(), ExceptionEnum.SYSTEM_ERROR.getDesc());
        }
    }

    /**
     * @description: 参数校验并转换
     * @param request 请求参数
     * @return 转换后的支付订单号
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    private Long validateAndConvertPmtDealNo(ResetTxPmtFlagRequest request) {
        if (StringUtils.isBlank(request.getPmtDealNo())) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR);
        }

        try {
            return Long.valueOf(request.getPmtDealNo());
        } catch (NumberFormatException e) {
            throw new BusinessException(ExceptionEnum.PAYMENT_ORDER_NO_FORMAT_ERROR);
        }
    }

    /**
     * @description: 校验支付订单
     * @param pmtDealNo 支付订单号
     * @return 支付订单信息
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    private HwPaymentOrderPO validatePaymentOrder(Long pmtDealNo) {
        HwPaymentOrderPO paymentOrder = hwPaymentOrderRepository.selectByPmtDealNo(pmtDealNo);
        if (paymentOrder == null) {
            throw new BusinessException(ExceptionEnum.PAYMENT_ORDER_NOT_EXISTS);
        }

        if (!TxPmtFlagEnum.PAYING.getCode().equals(paymentOrder.getTxPmtFlag())) {
            throw new BusinessException(ExceptionEnum.PAYMENT_ORDER_STATUS_ERROR.getCode(),
                    "支付订单状态不正确，当前状态：" + TxPmtFlagEnum.getDescByCode(paymentOrder.getTxPmtFlag()));
        }

        return paymentOrder;
    }

    /**
     * @description: 校验关联交易订单
     * @param dealNo 交易订单号
     * @return 交易订单信息
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    private HwDealOrder validateDealOrder(Long dealNo) {
        HwDealOrder dealOrder = hwDealOrderRepository.selectByDealNo(dealNo);
        if (dealOrder == null) {
            throw new BusinessException(ExceptionEnum.RELATED_DEAL_ORDER_NOT_EXISTS);
        }

        // 根据设计文档，交易订单的支付状态应该是"2"（付款中），订单状态应该是"1"（申请成功）
        if (!PayStatusEnum.PAYING.getCode().equals(dealOrder.getPayStatus()) ||
            !OrderStatusEnum.APPLY_SUCCESS.getCode().equals(dealOrder.getOrderStatus())) {
            throw new BusinessException(ExceptionEnum.RELATED_DEAL_ORDER_STATUS_ERROR.getCode(),
                    String.format("关联交易订单状态不正确，支付状态：%s，订单状态：%s",
                            dealOrder.getPayStatus(), dealOrder.getOrderStatus()));
        }

        return dealOrder;
    }

    /**
     * @description: 校验外部支付系统状态
     * @param pmtDealNo 支付订单号
     * @author: shaoyang.li
     * @date: 2025-07-08 11:20:37
     * @since JDK 1.8
     */
    private void validateExternalPaymentStatus(Long pmtDealNo) {
        PaymentResultDTO paymentResult = payOuterService.queryPayResult(pmtDealNo.toString());

        // 通过success字段判断调用是否成功
        if (!paymentResult.isSuccess()) {
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(),
                    "查询外部支付系统失败，支付订单号：" + pmtDealNo);
        }

        // 如果外部支付订单号不为空，说明外部支付系统中已存在该订单
        if (StringUtils.isNotBlank(paymentResult.getOutPmtDealNo())) {
            throw new BusinessException(ExceptionEnum.EXTERNAL_PAYMENT_ORDER_EXISTS);
        }
    }

    /**
     * @description: 执行状态重置
     * @param paymentOrder 支付订单
     * @param dealOrder 交易订单
     * @author: shaoyang.li
     * @date: 2025-07-07 19:02:55
     * @since JDK 1.8
     */
    private void resetPaymentStatus(HwPaymentOrderPO paymentOrder, HwDealOrder dealOrder) {
        try {
            // 使用Repository层的事务方法进行状态重置
            hwPaymentOrderRepository.resetTxPmtFlagWithTransaction(paymentOrder, dealOrder);
        } catch (Exception e) {
            log.error("重置支付状态失败，支付订单号：{}", paymentOrder.getPmtDealNo(), e);
            AlertLogUtil.alert("重置支付状态失败",
                    String.format("支付订单号：%s，异常信息：%s", paymentOrder.getPmtDealNo(), e.getMessage()));
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR);
        }
    }
}
