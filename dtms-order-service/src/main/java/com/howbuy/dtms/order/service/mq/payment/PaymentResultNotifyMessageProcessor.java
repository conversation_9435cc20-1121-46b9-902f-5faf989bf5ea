/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.mq.payment;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.common.enums.TxPmtFlagEnum;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import com.howbuy.dtms.order.dao.po.HwPaymentOrderPO;
import com.howbuy.dtms.order.service.business.payment.ProcessPmtResultBusiness;
import com.howbuy.dtms.common.enums.PaymentOrderTypeEnum;
import com.howbuy.dtms.order.service.commom.utils.AlertLogUtil;
import com.howbuy.dtms.order.service.commom.utils.AssertUtils;
import com.howbuy.dtms.order.service.mq.AbstractMessageProcessor;
import com.howbuy.dtms.order.service.mq.domain.payment.PaymentResultNotifyMessageDTO;
import com.howbuy.dtms.order.service.outerservice.pay.domain.PaymentResultDTO;
import com.howbuy.dtms.order.service.repository.HwDealOrderDtlRepository;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.repository.HwPaymentOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 支付结果通知消息处理器
 * @author: shaoyang.li
 * @date: 2025-07-04 14:14:52
 * @since JDK 1.8
 */
@Slf4j
@Component
public class PaymentResultNotifyMessageProcessor extends AbstractMessageProcessor<PaymentResultNotifyMessageDTO> {

    @Value("${TOPIC.HK_PAY_PAY_RESULT_NOTIFY}")
    private String topic;

    @Resource
    private HwPaymentOrderRepository hwPaymentOrderRepository;

    @Resource
    private HwDealOrderRepository hwDealOrderRepository;

    @Resource
    private HwDealOrderDtlRepository hwDealOrderDtlRepository;

    @Resource
    private ProcessPmtResultBusiness processPmtResultBusiness;

    @Override
    protected String getTopicName() {
        return topic;
    }

    /**
     * @description: 处理支付结果通知消息
     * @param message 支付结果通知消息
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    @Override
    protected void handleMessage(PaymentResultNotifyMessageDTO message) {
        log.info("支付结果通知消息处理开始，消息内容：{}", JSON.toJSONString(message));
        
        try {
            // 1. 参数校验
            validateMessage(message);
            
            // 2. 查询支付订单
            HwPaymentOrderPO paymentOrder = queryPaymentOrder(message.getPmtDealNo());
            if (paymentOrder == null) {
                log.error("支付订单不存在，支付订单号：{}", message.getPmtDealNo());
                AlertLogUtil.alert(this.getClass().getSimpleName(),
                        String.format("支付订单不存在，支付订单号：%s", message.getPmtDealNo()));
                return;
            }
            
            // 3. 幂等性判断
            if (isPaymentOrderInFinalStatus(paymentOrder)) {
                log.info("支付订单已是最终状态，跳过处理，支付订单号：{}，当前状态：{}",
                        message.getPmtDealNo(), paymentOrder.getTxPmtFlag());
                return;
            }
            
            // 4. 查询关联的交易订单（如果是交易订单类型）
            HwDealOrder dealOrder = null;
            List<HwDealOrderDtl> dealOrderDtlList = null;
            
            if (isTradeOrderType(message.getOrderType())) {
                dealOrder = queryDealOrder(paymentOrder);
                if (dealOrder == null) {
                    log.error("关联的交易订单不存在，支付订单号：{}，交易订单号：{}", 
                            message.getPmtDealNo(), paymentOrder.getDealNo());
                    AlertLogUtil.alert(this.getClass().getSimpleName(),
                            String.format("关联的交易订单不存在，支付订单号：%s，交易订单号：%s", 
                                    message.getPmtDealNo(), paymentOrder.getDealNo()));
                    return;
                }
                
                dealOrderDtlList = queryDealOrderDtlList(dealOrder.getDealNo());
                if (CollectionUtils.isEmpty(dealOrderDtlList)) {
                    log.error("关联的交易订单明细不存在，支付订单号：{}，交易订单号：{}", 
                            message.getPmtDealNo(), dealOrder.getDealNo());
                    AlertLogUtil.alert(this.getClass().getSimpleName(),
                            String.format("关联的交易订单明细不存在，支付订单号：%s，交易订单号：%s", 
                                    message.getPmtDealNo(), dealOrder.getDealNo()));
                    return;
                }
            }
            
            // 5. 转换消息为支付结果对象
            PaymentResultDTO paymentResult = convertToPaymentResult(message);
            
            // 6. 调用支付结果处理服务
            processPmtResultBusiness.process(paymentOrder, dealOrder, dealOrderDtlList, paymentResult);
            
            log.info("支付结果通知消息处理成功，支付订单号：{}", message.getPmtDealNo());
            
        } catch (Exception e) {
            log.error("支付结果通知消息处理失败，支付订单号：{}，异常信息：{}", 
                    message != null ? message.getPmtDealNo() : "null", e.getMessage(), e);
            // 框架会自动处理异常告警，这里重新抛出异常让框架处理
            throw e;
        }
    }

    /**
     * @description: 校验消息参数
     * @param message 支付结果通知消息
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    private void validateMessage(PaymentResultNotifyMessageDTO message) {
        AssertUtils.nonNullParam(message, "支付结果通知消息不能为空");
        
        // 校验必填字段不为空且不为空字符串
        if (StringUtils.isBlank(message.getPmtDealNo())) {
            AssertUtils.throwParamErrorException("支付订单号不能为空");
        }
        
        if (StringUtils.isBlank(message.getTxPmtFlag())) {
            AssertUtils.throwParamErrorException("交易支付状态不能为空");
        }
        
        if (StringUtils.isBlank(message.getPmtCompFlag())) {
            AssertUtils.throwParamErrorException("支付完成状态不能为空");
        }
        
        if (StringUtils.isBlank(message.getRetCode())) {
            AssertUtils.throwParamErrorException("返回码不能为空");
        }
        
        if (StringUtils.isBlank(message.getOrderType())) {
            AssertUtils.throwParamErrorException("订单类型不能为空");
        }
        
        if (StringUtils.isBlank(message.getCurrency())) {
            AssertUtils.throwParamErrorException("币种不能为空");
        }
        
        if (StringUtils.isBlank(message.getPmtAmt())) {
            AssertUtils.throwParamErrorException("支付金额不能为空");
        }
        
        // 校验支付金额格式
        try {
            new BigDecimal(message.getPmtAmt());
        } catch (NumberFormatException e) {
            AssertUtils.throwParamErrorException("支付金额格式不正确");
        }
    }

    /**
     * @description: 查询支付订单
     * @param pmtDealNo 支付订单号
     * @return 支付订单信息
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    private HwPaymentOrderPO queryPaymentOrder(String pmtDealNo) {
        log.info("查询支付订单，支付订单号：{}", pmtDealNo);
        return hwPaymentOrderRepository.selectByPmtDealNo(Long.valueOf(pmtDealNo));
    }

    /**
     * @description: 判断支付订单是否处于最终状态（用于幂等性判断）
     * @param paymentOrder 支付订单
     * @return 是否处于最终状态
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    private boolean isPaymentOrderInFinalStatus(HwPaymentOrderPO paymentOrder) {
        String txPmtFlag = paymentOrder.getTxPmtFlag();
        
        // 支付成功或支付失败都是最终状态
        return TxPmtFlagEnum.PAY_SUCCESS.getCode().equals(txPmtFlag)
                || TxPmtFlagEnum.PAY_FAIL.getCode().equals(txPmtFlag);
    }

    /**
     * @description: 判断是否为交易订单类型
     * @param orderType 订单类型
     * @return 是否为交易订单
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    private boolean isTradeOrderType(String orderType) {
        return PaymentOrderTypeEnum.TRADE.getCode().equals(orderType);
    }

    /**
     * @description: 查询关联的交易订单
     * @param paymentOrder 支付订单
     * @return 交易订单信息
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    private HwDealOrder queryDealOrder(HwPaymentOrderPO paymentOrder) {
        if (paymentOrder.getDealNo() == null) {
            log.warn("支付订单未关联交易订单号，支付订单号：{}", paymentOrder.getPmtDealNo());
            return null;
        }
        
        log.info("查询关联的交易订单，交易订单号：{}", paymentOrder.getDealNo());
        return hwDealOrderRepository.selectByDealNo(paymentOrder.getDealNo());
    }

    /**
     * @description: 查询交易订单明细列表
     * @param dealNo 交易订单号
     * @return 交易订单明细列表
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    private List<HwDealOrderDtl> queryDealOrderDtlList(Long dealNo) {
        log.info("查询交易订单明细列表，交易订单号：{}", dealNo);
        return hwDealOrderDtlRepository.selectListByDealNo(dealNo);
    }

    /**
     * @description: 转换消息为支付结果对象
     * @param message 支付结果通知消息
     * @return 支付结果对象
     * @author: shaoyang.li
     * @date: 2025-07-04 14:14:52
     * @since JDK 1.8
     */
    private PaymentResultDTO convertToPaymentResult(PaymentResultNotifyMessageDTO message) {
        PaymentResultDTO paymentResult = new PaymentResultDTO();
        
        paymentResult.setPmtDealNo(message.getPmtDealNo());
        paymentResult.setTxPmtFlag(message.getTxPmtFlag());
        paymentResult.setPmtCompFlag(message.getPmtCompFlag());
        paymentResult.setRetCode(message.getRetCode());
        paymentResult.setRetDesc(message.getRetDesc());
        paymentResult.setOutPmtDealNo(message.getOutPmtDealNo());
        paymentResult.setPmtCheckDt(message.getPmtCheckDt());
        paymentResult.setOrderType(message.getOrderType());
        paymentResult.setCurrency(message.getCurrency());
        // 设置支付金额，转换为BigDecimal
        if (StringUtils.isNotBlank(message.getPmtAmt())) {
            try {
                paymentResult.setPmtAmt(new BigDecimal(message.getPmtAmt()));
            } catch (NumberFormatException e) {
                log.error("支付金额格式错误，支付订单号：{}，金额：{}", message.getPmtDealNo(), message.getPmtAmt(), e);
                throw new RuntimeException("支付金额格式错误：" + message.getPmtAmt(), e);
            }
        } else {
            paymentResult.setPmtAmt(null);
        }
        
        log.info("转换支付结果对象完成，支付订单号：{}，支付状态：{}", 
                paymentResult.getPmtDealNo(), paymentResult.getTxPmtFlag());
        
        return paymentResult;
    }
} 