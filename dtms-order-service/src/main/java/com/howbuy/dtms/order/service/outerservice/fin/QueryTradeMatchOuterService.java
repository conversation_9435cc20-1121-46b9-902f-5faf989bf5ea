/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.fin;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.outerservice.fin.domain.TradeMatchDTO;
import com.howbuy.hkfin.facade.ResultDto;
import com.howbuy.hkfin.facade.query.tradedetail.QueryTradeDetailMatchStatusFacade;
import com.howbuy.hkfin.facade.query.tradedetail.QueryTradeDetailMatchStatusRequest;
import com.howbuy.hkfin.facade.query.tradedetail.QueryTradeDetailMatchStatusResponse;
import com.howbuy.hkfin.facade.query.tradedetail.TradeMatchDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * @description: (请在此添加描述)
 * <AUTHOR>
 * @date 2025/7/29 9:03
 * @since JDK 1.8
 */
@Slf4j
@Service
public class QueryTradeMatchOuterService {


    @DubboReference(registry = "hk-fin-service", check = false)
    private QueryTradeDetailMatchStatusFacade queryTradeDetailMatchStatusFacade;


    /**
     * @param pmtDealNo
     * @return void
     * @description:(查询资金匹配信息)
     * <AUTHOR>
     * @date 2025/7/28 18:42
     * @since JDK 1.8
     */
    public TradeMatchDTO queryTradeDetailMatchStatus(Long pmtDealNo) {
        QueryTradeDetailMatchStatusRequest request = new QueryTradeDetailMatchStatusRequest();
        request.setMiddlePayNoList(Lists.newArrayList(String.valueOf(pmtDealNo)));
        ResultDto<QueryTradeDetailMatchStatusResponse> response = queryTradeDetailMatchStatusFacade.execute(request);

        if (null == response) {
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "查询资金匹配状态接口异常");
        }
        if (!response.isSuccess()) {
            log.error("queryHkCashBalanceSettleStatus>>>查询资金匹配状态接口错误,错误码:{},错误信息:{}",
                    response.getReturnCode(), response.getDescription());
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "查询资金匹配状态接口错误");
        }
        if (null == response.getData()) {
            log.error("queryHkCashBalanceSettleStatus>>>查询资金匹配状态接口响应为空,错误信息:{}", JSON.toJSONString(response));
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "查询资金匹配状态接口错误");
        }
        // 数据不存在 或者 匹配状态为0-未匹配
        // 匹配状态：0-未匹配、1-匹配成功、2-匹配失败、3-匹配中
        if (CollectionUtils.isEmpty(response.getData().getMatchDtoList())) {
            return null;
        }
        TradeMatchDto tradeMatchDto = response.getData().getMatchDtoList().get(0);

        TradeMatchDTO tradeMatchDTO = new TradeMatchDTO();
        tradeMatchDTO.setPayNo(tradeMatchDto.getPayNo());
        tradeMatchDTO.setMiddlePayNo(tradeMatchDto.getMiddlePayNo());
        tradeMatchDTO.setMatchStatus(tradeMatchDto.getMatchStatus());
        return tradeMatchDTO;
    }

}
