package com.howbuy.dtms.order.service.service.trade.payment;

import com.howbuy.dtms.common.enums.PmtCompFlagEnum;
import com.howbuy.dtms.order.client.domain.request.payment.PaymentCheckRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.PaymentCheckResponse;
import com.howbuy.dtms.order.service.business.payment.PaymentCheckBusiness;
import com.howbuy.dtms.order.service.commom.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.commom.exception.BusinessException;
import com.howbuy.dtms.order.service.commom.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @description: 支付对账业务服务
 * <AUTHOR>
 * @date 2025-07-07
 * @since JDK 1.8
 */
@Service
@Slf4j
public class PaymentCheckService {

    @Resource
    private PaymentCheckBusiness paymentCheckBusiness;

    /**
     * @description: 执行支付对账
     * @param request 支付对账请求
     * @return 支付对账响应
     * @author: shaoyang.li
     * @date: 2025-07-07
     * @since JDK 1.8
     */
    public Response<PaymentCheckResponse> execute(PaymentCheckRequest request) {
        log.info("开始执行支付对账，请求参数：{}", request);

        try {
            // 参数校验
            validateRequest(request);

            // 初始化pmtCompFlagList为1-未对账
            List<String> pmtCompFlagList = Arrays.asList(PmtCompFlagEnum.UN_CHECK.getCode());

            // 调用业务处理类执行对账
            paymentCheckBusiness.paymentCheck(request.getPmtCheckDt(), pmtCompFlagList);

            log.info("支付对账执行成功");
            return Response.ok("支付对账执行成功", new PaymentCheckResponse());

        } catch (BusinessException e) {
            log.error("支付对账业务异常，错误：{}", e.getExceptionDesc(), e);
            return Response.fail(e.getExceptionCode(), e.getExceptionDesc());
        } catch (Exception e) {
            log.error("支付对账执行失败，错误：{}", e.getMessage(), e);
            return Response.fail("支付对账执行失败：" + e.getMessage());
        }
    }

    /**
     * @description: 校验请求参数
     * @param request 支付对账请求
     * @author: shaoyang.li
     * @date: 2025-07-07
     * @since JDK 1.8
     */
    private void validateRequest(PaymentCheckRequest request) {
        if (request == null) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "请求参数不能为空");
        }

        if (StringUtils.isBlank(request.getPmtCheckDt())) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付对账日期不能为空");
        }

        // 校验日期格式
        if (!DateUtils.isValidYmd(request.getPmtCheckDt())) {
            throw new BusinessException(ExceptionEnum.PARAMS_ERROR.getCode(), "支付对账日期格式错误，应为yyyyMMdd格式");
        }
    }

}
