package com.howbuy.dtms.order.service.provider.dubbo.trade.payment;

import com.howbuy.dtms.order.client.domain.request.payment.ResetTxPmtFlagRequest;
import com.howbuy.dtms.order.client.domain.response.Response;
import com.howbuy.dtms.order.client.domain.response.payment.ResetTxPmtFlagResponse;
import com.howbuy.dtms.order.client.facade.trade.payment.ResetTxPmtFlagFacade;
import com.howbuy.dtms.order.service.service.trade.payment.ResetTxPmtFlagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 重置交易支付标识接口实现
 * <AUTHOR>
 * @date 2025-07-07 19:02:55
 * @since JDK 1.8
 */
@Slf4j
@DubboService
@Component
public class ResetTxPmtFlagFacadeImpl implements ResetTxPmtFlagFacade {

    @Resource
    private ResetTxPmtFlagService resetTxPmtFlagService;

    @Override
    public Response<ResetTxPmtFlagResponse> execute(ResetTxPmtFlagRequest request) {
        return resetTxPmtFlagService.execute(request);
    }
}
