/**
 * Copyright (c) 2025, <PERSON>gH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @description: 支付对账定时任务消息VO
 * <AUTHOR>
 * @date 2025-07-07 15:41:42
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PaymentCheckTaskVO extends BaseTaskMessageVO {

    /**
     * 支付对账日期，格式：yyyyMMdd
     * 为空时默认查询180天内的订单
     */
    private String pmtCheckDt;

    /**
     * 对账状态列表
     * 默认为["1"]（1-未对账）
     * 可配置为["1", "3"]（1-未对账、3-对账不平）
     */
    private List<String> pmtCompFlagList;

}
