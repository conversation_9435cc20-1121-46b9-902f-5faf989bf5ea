/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.howbuy.dtms.common.enums.*;
import com.howbuy.dtms.order.dao.bo.OrderCreateBO;
import com.howbuy.dtms.order.dao.bo.OrderUpdateBO;
import com.howbuy.dtms.order.dao.po.HwDealOrder;
import com.howbuy.dtms.order.dao.po.HwDealOrderDtl;
import com.howbuy.dtms.order.dao.query.HwDealOrderQuery;
import com.howbuy.dtms.order.service.business.sequence.SequenceService;
import com.howbuy.dtms.order.service.commom.utils.excel.EasyExcelUtils;
import com.howbuy.dtms.order.service.job.domain.SubAndFirstPaidDealOrderInitTaskVO;
import com.howbuy.dtms.order.service.job.domain.SubDealOrderInitDTO;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.QueryFundInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.domain.FundBasicInfoDTO;
import com.howbuy.dtms.order.service.outerservice.hkacc.HkCustInfoOuterService;
import com.howbuy.dtms.order.service.outerservice.hkacc.domain.HkCustInfoCipherDTO;
import com.howbuy.dtms.order.service.outerservice.hkacc.domain.HkCustInfoDTO;
import com.howbuy.dtms.order.service.repository.HwDealOrderDtlRepository;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * @description: 认缴首次实缴初始化脚本
 * <AUTHOR>
 * @date 2025/5/23 11:25
 * @since JDK 1.8
 */
@Slf4j
@Component
public class SubDealOrderInitJob extends AbstractBatchMessageJob<SubAndFirstPaidDealOrderInitTaskVO> {

    @Value("${TOPIC.DTMS_ORDER_SUB_ORDER_INIT}")
    private String topic;

    @Resource
    private HwDealOrderRepository hwDealOrderRepository;
    @Resource
    private HwDealOrderDtlRepository hwDealOrderDtlRepository;

    @Resource
    private ResourceLoader resourceLoader;

    @Resource
    private HkCustInfoOuterService hkCustInfoOuterService;

    @Resource
    private QueryFundInfoOuterService queryFundInfoOuterService;

    /**
     * 基金信息map
     */
    private final Map<String, FundBasicInfoDTO> fundBasicInfoDTOMap = Maps.newHashMap();


    
    /**
     * @description: 初始化脚本
     * @param message
     * @return void
     * @author: jinqing.rao
     * @date: 2025/5/23 11:29
     * @since JDK 1.8
     */
    //@Override
    public void doProcessJob(SubAndFirstPaidDealOrderInitTaskVO message) {
        log.info("SubDealOrderInitJob>>> 认缴订单初始化任务开始");

        // 解析数据
        List<SubDealOrderInitDTO> subDealOrderInitDTOList = getSubDealOrderInitDTOS();
        // 判空
        if(CollectionUtils.isEmpty(subDealOrderInitDTOList)){
            log.info("空文件不处理");
            return;
        }
        log.info("SubDealOrderInitJob>>> 获取文件数据成功，数据条数：{}", subDealOrderInitDTOList.size());

        Date date = new Date();
        for (SubDealOrderInitDTO m : subDealOrderInitDTOList) {
            if (paramsCheck(message, m)) {
                continue;
            }
            // 判断是否存在,支持脚本重复执行
            HwDealOrderQuery hwDealOrderQuery = new HwDealOrderQuery();
            hwDealOrderQuery.setHkCustNo(m.getHkCustNo());
            hwDealOrderQuery.setFundCode(m.getProductCode());
            hwDealOrderQuery.setMidBusinessCodeList(Collections.singletonList(BusinessCodeEnum._112A.getMCode()));
            List<HwDealOrder> hwDealOrders = hwDealOrderRepository.selectOrderListByCondition(hwDealOrderQuery);
            if(CollectionUtils.isNotEmpty(hwDealOrders)){
                log.info("SubDealOrderInitJob>>> 认缴订单已存在，hkCustNo :{},productCode :{}",m.getHkCustNo(), m.getProductCode());
                continue;
            }
            // 默认获取对应的首次实缴订单明细
            List<String> businessCodeList = getBusinessCodeList(m);

            HwDealOrder firstBuyPaidOrder = hwDealOrderRepository.selectOrderByHkCustNoAndFundCodeAndFirstBuyFlag(m.getHkCustNo(), m.getProductCode(), FirstBuyFlagEnum.FIRST_BUY.getCode(),businessCodeList);
            if (null == firstBuyPaidOrder) {
                log.info("SubDealOrderInitJob>>> 首次购买订单不存在，hkCustNo :{},productCode :{}",m.getHkCustNo(), m.getProductCode());
                continue;
            }
            // 通过订单号查询订单明细号
            List<HwDealOrderDtl> hwDealOrderDtlsList = hwDealOrderDtlRepository.selectHwDtlByDealNo(Collections.singletonList(firstBuyPaidOrder.getDealNo()));
            if (CollectionUtils.isEmpty(hwDealOrderDtlsList)) {
                log.info("SubDealOrderInitJob>>> 首次购买订单明细不存在，hkCustNo :{},productCode :{}",m.getHkCustNo(), m.getProductCode());
                continue;
            }
            HwDealOrderDtl paidOrderDtl = hwDealOrderDtlsList.get(0);
            // 客户信息
            HkCustInfoDTO hkCustInfo = hkCustInfoOuterService.getHkCustInfo(m.getHkCustNo());
            // 基金名称赋值基金简称，对外展示都是用基金简称
            FundBasicInfoDTO fundBasicInfoDTO = fundBasicInfoDTOMap.computeIfAbsent(m.getProductCode(), key ->
                    queryFundInfoOuterService.queryFundBasicInfo(key));
            HwDealOrder hwDealOrder = getHwDealOrder(m,date, hkCustInfo, fundBasicInfoDTO, firstBuyPaidOrder);
            HwDealOrderDtl hwOrderDtl = getHwOrderDtl(m, hwDealOrder, firstBuyPaidOrder, paidOrderDtl, fundBasicInfoDTO);
            // 更新实缴订单明细中间业务码
            // 更新订单
            OrderCreateBO orderCreateBO = new OrderCreateBO();
            orderCreateBO.setHwDealOrder(hwDealOrder);
            orderCreateBO.setHwDealOrderDtl(hwOrderDtl);
            log.info("SubDealOrderInitJob>>> 创建认缴订单信息,orderInfo:{}", JSON.toJSONString(orderCreateBO));
            hwDealOrderRepository.saveDealOrder(orderCreateBO);

            // 更新实缴订单和订单明细
            OrderUpdateBO orderUpdateBO = new OrderUpdateBO();
            if(BusinessCodeEnum.SUBS.getMCode().equals(firstBuyPaidOrder.getMiddleBusiCode())){
                firstBuyPaidOrder.setBusinessType(BusinessTypeEnum.SUB_AND_FIRST_PAID.getCode());
            }

            if(BusinessCodeEnum.PURCHASE.getMCode().equals(firstBuyPaidOrder.getMiddleBusiCode())){
                firstBuyPaidOrder.setBusinessType(BusinessTypeEnum.BUY.getCode());
            }
            firstBuyPaidOrder.setMiddleBusiCode(BusinessCodeEnum._112B.getMCode());
            firstBuyPaidOrder.setBatchNo(hwDealOrder.getDealNo());
            firstBuyPaidOrder.setUpdateTimestamp(date);
            orderUpdateBO.setUpdateDealOrder(firstBuyPaidOrder);


            paidOrderDtl.setMiddleBusiCode(BusinessCodeEnum._112B.getMCode());
            paidOrderDtl.setUpdateTimestamp(date);
            paidOrderDtl.setSubAmt(hwOrderDtl.getNetAppAmt());
            orderUpdateBO.setUpdateDealOrderDtl(paidOrderDtl);
            log.info("SubDealOrderInitJob>>> 更新订单信息,orderInfo:{}", JSON.toJSONString(orderUpdateBO));
            hwDealOrderRepository.updateDealOrder(orderUpdateBO);

            updateOtherBuyOrder(m, orderCreateBO);
        }
        log.info("SubDealOrderInitJob>>> 认缴订单初始化任务结束");
    }

    private static boolean paramsCheck(SubAndFirstPaidDealOrderInitTaskVO message, SubDealOrderInitDTO m) {
        if(!StringUtils.isEmpty(message.getHkCustNo()) && !message.getHkCustNo().equals(m.getHkCustNo())){
            return true;
        }
        if(org.apache.commons.lang3.StringUtils.isAnyBlank(m.getHkCustNo(), m.getProductCode())){
            log.info("SubDealOrderInitJob>>> 缺少必要参数，hkCustNo :{},productCode :{}", m.getHkCustNo(), m.getProductCode());
            return true;
        }
        return false;
    }

    private static List<String> getBusinessCodeList(SubDealOrderInitDTO m) {
        List<String> businessCodeList = null;
        // 临时调整的数据，脚本兼容下
        if("6014986".equals(m.getHkCustNo())){
            businessCodeList = Collections.singletonList(BusinessCodeEnum.PURCHASE.getMCode());
        }else{
            businessCodeList = Collections.singletonList(BusinessCodeEnum.SUBS.getMCode());
        }
        return businessCodeList;
    }

    /**
     * @description:(请在此添加描述)
     * @param m	
     * @param orderCreateBO
     * @return void
     * @author: jinqing.rao
     * @date: 2025/6/13 13:23
     * @since JDK 1.8
     */
    private void updateOtherBuyOrder(SubDealOrderInitDTO m,OrderCreateBO orderCreateBO) {
        Date date = new Date();
        HwDealOrder subHwDealOrder = orderCreateBO.getHwDealOrder();
        HwDealOrderDtl subHwDealOrderDtl = orderCreateBO.getHwDealOrderDtl();
        HwDealOrderQuery hwDealOrderQuery = new HwDealOrderQuery();
        hwDealOrderQuery.setHkCustNo(m.getHkCustNo());
        hwDealOrderQuery.setFundCode(m.getProductCode());
        hwDealOrderQuery.setMidBusinessCodeList(Collections.singletonList(BusinessCodeEnum.PURCHASE.getMCode()));
        List<HwDealOrder> otherHwDealOrders = hwDealOrderRepository.selectOrderListByCondition(hwDealOrderQuery);
        otherHwDealOrders.forEach(it -> {
            List<HwDealOrderDtl> hwDealOrderDtls = hwDealOrderDtlRepository.selectListByDealNo(it.getDealNo());
            if(CollectionUtils.isEmpty(hwDealOrderDtls)){
                log.info("SubDealOrderInitJob>>> 认缴订单明细不存在，hkCustNo :{},productCode :{}",m.getHkCustNo(), m.getProductCode());
                return;
            }
            HwDealOrderDtl paidHwDealOrderDtl = hwDealOrderDtls.get(0);
            // 更新实缴订单和订单明细
            OrderUpdateBO orderUpdateBO = new OrderUpdateBO();
            if(BusinessCodeEnum.SUBS.getMCode().equals(it.getMiddleBusiCode())){
                it.setBusinessType(BusinessTypeEnum.SUB_AND_FIRST_PAID.getCode());
            }

            if(BusinessCodeEnum.PURCHASE.getMCode().equals(it.getMiddleBusiCode())){
                it.setBusinessType(BusinessTypeEnum.BUY.getCode());
            }
            it.setMiddleBusiCode(BusinessCodeEnum._112B.getMCode());
            it.setBatchNo(subHwDealOrder.getDealNo());
            it.setUpdateTimestamp(date);
            orderUpdateBO.setUpdateDealOrder(it);


            paidHwDealOrderDtl.setMiddleBusiCode(BusinessCodeEnum._112B.getMCode());
            paidHwDealOrderDtl.setUpdateTimestamp(date);
            paidHwDealOrderDtl.setSubAmt(subHwDealOrderDtl.getNetAppAmt());
            orderUpdateBO.setUpdateDealOrderDtl(paidHwDealOrderDtl);
            hwDealOrderRepository.updateDealOrder(orderUpdateBO);
        });
    }

    /**
     * @description: 初始化脚本
     * @return void
     * @author: jinqing.rao
     * @date: 2025/5/23 11:29
     * @since JDK 1.8
     */
    private List<SubDealOrderInitDTO> getSubDealOrderInitDTOS() {
        // 解析认缴订单数据 新增:读取classpath资源文件
        byte[] fileBytes = null;
        try {
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:认缴数据.xlsx");
            fileBytes = FileCopyUtils.copyToByteArray(resource.getInputStream());

        } catch (Exception e) {
            log.error("读取认缴订单.xlsx失败", e);
            throw new RuntimeException("读取认缴订单文件失败", e);
        }
        // 这里可以继续使用fileBytes进行后续处理
        return EasyExcelUtils.readExcel(fileBytes, SubDealOrderInitDTO.class, 0, 1);
    }

    /**
     * @description: 创建订单信息
     * @return void
     * @author: jinqing.rao
     * @date: 2025/5/23 11:29
     * @since JDK 1.8
     */
    private static HwDealOrderDtl getHwOrderDtl(SubDealOrderInitDTO m, HwDealOrder hwDealOrder, HwDealOrder firstBuyPaidOrder, HwDealOrderDtl paidOrderDtl, FundBasicInfoDTO fundBasicInfoDTO) {
        HwDealOrderDtl hwDealOrderDtl = new HwDealOrderDtl();

        hwDealOrderDtl.setDealDtlNo(SequenceService.getSnowflakeIdDefault());
        hwDealOrderDtl.setDealNo(hwDealOrder.getDealNo());
        hwDealOrderDtl.setRecStat(RecStatEnum.NORMAL.getValue());

        hwDealOrderDtl.setHkCustNo(m.getHkCustNo());
        hwDealOrderDtl.setCpAcctNo(firstBuyPaidOrder.getCpAcctNo());
        hwDealOrderDtl.setFundTxAcctNo(paidOrderDtl.getFundTxAcctNo());

        hwDealOrderDtl.setFundCode(fundBasicInfoDTO.getFundCode());
        // 基金名称赋值基金简称，对外展示都是用基金简称
        hwDealOrderDtl.setFundName(fundBasicInfoDTO.getFundAbbr());
        hwDealOrderDtl.setFundAbbr(fundBasicInfoDTO.getFundAbbr());
        if (YesOrNoEnum.YES.getValue().equals(fundBasicInfoDTO.getIsMotherChildFund())) {
            hwDealOrderDtl.setMainFundCode(fundBasicInfoDTO.getMainFundCode());
        }
        hwDealOrderDtl.setFundCategory(fundBasicInfoDTO.getFundCategory());
        hwDealOrderDtl.setFundRiskLevel(fundBasicInfoDTO.getFundRiskLevel());
        hwDealOrderDtl.setCurrency(fundBasicInfoDTO.getCurrency());

        hwDealOrderDtl.setFundDivMode(fundBasicInfoDTO.getDfltDivMode());

        hwDealOrderDtl.setMiddleBusiCode(BusinessCodeEnum._112A.getMCode());

        hwDealOrderDtl.setFeeCalMode(fundBasicInfoDTO.getFeeCalMode());

        hwDealOrderDtl.setNetAppAmt(new BigDecimal(m.getNetAppAmt()));
        // 实缴订单记录认缴金额
        hwDealOrderDtl.setPayEndDt(paidOrderDtl.getPayEndDt());
        hwDealOrderDtl.setPayEndTm(paidOrderDtl.getPayEndTm());

        hwDealOrderDtl.setAppAmt(new BigDecimal(m.getAppAmt()));
        hwDealOrderDtl.setEstimateFee(new BigDecimal(m.getEstimateFee()));
        hwDealOrderDtl.setPrebookDiscount(paidOrderDtl.getPrebookDiscount());
        hwDealOrderDtl.setDiscountRate(new BigDecimal(m.getDiscountRate()));
        hwDealOrderDtl.setFeeRate(new BigDecimal(m.getFeeRate()));
        hwDealOrderDtl.setDiscountType(paidOrderDtl.getDiscountType());
        hwDealOrderDtl.setDiscountAmt(paidOrderDtl.getDiscountAmt());


        hwDealOrderDtl.setAppStatus(AppStatusEnum.APPLY_SUCCESS.getCode());
        hwDealOrderDtl.setAckStatus(AckStatusEnum.CONFIRM_SUCCESS.getCode());

        hwDealOrderDtl.setOpenDt(m.getOpenDt());
        hwDealOrderDtl.setCreateTimestamp(new Date());
        hwDealOrderDtl.setUpdateTimestamp(new Date());

        hwDealOrderDtl.setPreSubmitTaDt(m.getPreSubmitTaDt());
        hwDealOrderDtl.setPreSubmitTaTm(m.getPreSubmitTaTm());
        hwDealOrderDtl.setSubmitStatus(SubmitStatusEnum.SUBMIT_SUCCESS.getCode());
        hwDealOrderDtl.setFundManCode(fundBasicInfoDTO.getFundManCode());

        // 确认信息
        hwDealOrderDtl.setAckAmt(new BigDecimal(m.getAckAmt()));
        hwDealOrderDtl.setAckDt(m.getAckDt());
        hwDealOrderDtl.setAckVol(new BigDecimal(m.getAckVol()));
        hwDealOrderDtl.setAckNav(new BigDecimal(m.getAckNav()));
        hwDealOrderDtl.setFee(new BigDecimal(m.getFee()));
        hwDealOrderDtl.setAckNavDt(m.getAckNavDt());
        return hwDealOrderDtl;
    }

    /**
     * @description: 创建认购订单表
     * @param m
     * @param date	
     * @param hkCustInfo	
     * @param fundBasicInfoDTO	
     * @param firstBuyPaidOrder
     * @return com.howbuy.dtms.order.dao.po.HwDealOrder
     * @author: jinqing.rao
     * @date: 2025/6/10 13:16
     * @since JDK 1.8
     */
    private HwDealOrder getHwDealOrder(SubDealOrderInitDTO m,
                                       Date date,HkCustInfoDTO hkCustInfo,FundBasicInfoDTO fundBasicInfoDTO,HwDealOrder firstBuyPaidOrder) {
        HwDealOrder hwDealOrder = new HwDealOrder();
        hwDealOrder.setDealNo(SequenceService.getSnowflakeIdDefault());
        hwDealOrder.setRecStat(RecStatEnum.NORMAL.getValue());
        hwDealOrder.setExternalDealNo(String.valueOf(hwDealOrder.getDealNo()));
        hwDealOrder.setOrderFormType(OrderFormTypeEnum.PAPER_ORDER.getCode());
        hwDealOrder.setTxCode("HW0001");
        hwDealOrder.setBusinessType(BusinessTypeEnum.SUB_AND_FIRST_PAID.getCode());
        hwDealOrder.setMiddleBusiCode(BusinessCodeEnum._112A.getMCode() );


        hwDealOrder.setHkCustNo(m.getHkCustNo());
        hwDealOrder.setCustChineseName(m.getCustChineseName());
        hwDealOrder.setIdType(hkCustInfo.getIdType());
        hwDealOrder.setInvstType(hkCustInfo.getInvstType());
        hwDealOrder.setQualificationType(hkCustInfo.getInvestorQualification());
        hwDealOrder.setCustRiskLevel(hkCustInfo.getRiskToleranceLevel());

        HkCustInfoCipherDTO hkCustInfoCipherDTO = hkCustInfoOuterService.queryHkCustInfoCipherDTO(m.getHkCustNo());
        hwDealOrder.setIdNoCipher(hkCustInfoCipherDTO.getIdNoCipher());
        hwDealOrder.setIdNoDigest(hkCustInfoCipherDTO.getIdNoDigest());
        hwDealOrder.setIdNoMask(hkCustInfoCipherDTO.getIdNoMask());

        hwDealOrder.setCpAcctNo(firstBuyPaidOrder.getCpAcctNo());
        hwDealOrder.setBankCode(firstBuyPaidOrder.getBankCode());
        hwDealOrder.setSwiftCode(firstBuyPaidOrder.getSwiftCode());
        hwDealOrder.setBankName(firstBuyPaidOrder.getBankName());
        hwDealOrder.setBankChineseName(firstBuyPaidOrder.getBankChineseName());
        
        hwDealOrder.setBankAcctCipher(firstBuyPaidOrder.getBankAcctCipher());
        hwDealOrder.setBankAcctDigest(firstBuyPaidOrder.getBankAcctDigest());
        hwDealOrder.setBankAcctMask(firstBuyPaidOrder.getBankAcctMask());

        hwDealOrder.setProductName(fundBasicInfoDTO.getFundAbbr());
        hwDealOrder.setProductCode(fundBasicInfoDTO.getFundCode());
        hwDealOrder.setProductAbbr(fundBasicInfoDTO.getFundAbbr());
        hwDealOrder.setCurrency("840");
        hwDealOrder.setSupportPrebookFlag(YesOrNoEnum.YES.getValue());
        hwDealOrder.setBatchNo(hwDealOrder.getDealNo());


        hwDealOrder.setAppDt(m.getAppDt());
        hwDealOrder.setAppTm(m.getAppTm());
        hwDealOrder.setOrderStatus(OrderStatusEnum.CONFIRM_SUCCESS.getCode());
        hwDealOrder.setOpenDt(m.getOpenDt());

        hwDealOrder.setFirstBuyFlag(firstBuyPaidOrder.getFirstBuyFlag());
        hwDealOrder.setIsAgreeCurrencyExchange(firstBuyPaidOrder.getIsAgreeCurrencyExchange());
        hwDealOrder.setAppAmt(new BigDecimal(m.getAppAmt()));
        String payMethod =   null;
        if(m.getPaymentTypeList().equals("电汇")){
            payMethod = PayMethodEnum.ELECTRIC_REMITTANCE.getCode();
        }
        if(m.getPaymentTypeList().equals("海外储蓄罐")){
            payMethod = PayMethodEnum.OVERSEAS_CXG.getCode();
        }
        hwDealOrder.setPaymentTypeList(PayMethodEnum.getValueByEnumCode(payMethod));
        // 认缴业务 打款凭证上传状态-无需上传
        hwDealOrder.setPayVoucherStatus(PayVoucherStatusEnum.NO_NEED_UPLOAD.getCode());
        hwDealOrder.setPayStatus(PayStatusEnum.PAY_SUCCESS.getCode());

        hwDealOrder.setTradeChannel("9");

        hwDealOrder.setMemo("脚本刷数");

        hwDealOrder.setCreateTimestamp(date);
        hwDealOrder.setUpdateTimestamp(date);
        return hwDealOrder;
    }


    @Override
    protected String getTopicName() {
        return topic;
    }
}
