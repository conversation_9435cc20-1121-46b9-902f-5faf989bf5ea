/**
 * Copyright (c) 2025, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.mq.payment;

import com.alibaba.fastjson.JSON;
import com.howbuy.dtms.order.service.business.payment.AsyncDoPaymentBusiness;
import com.howbuy.dtms.order.service.commom.utils.AssertUtils;
import com.howbuy.dtms.order.service.mq.AbstractMessageProcessor;
import com.howbuy.dtms.order.service.mq.domain.payment.AsyncDoPaymentMessageDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description: 异步支付处理消息处理器
 * @author: shaoyang.li
 * @date: 2025-07-03 11:07:45
 * @since JDK 1.8
 */
@Slf4j
@Component
public class AsyncDoPaymentMessageProcessor extends AbstractMessageProcessor<AsyncDoPaymentMessageDTO> {

    @Value("${TOPIC.DTMS_ORDER_ASYNC_DO_PAYMENT}")
    private String topic;

    @Resource
    private AsyncDoPaymentBusiness asyncDoPaymentBusiness;

    @Override
    protected String getTopicName() {
        return topic;
    }

    /**
     * @description: 处理异步支付消息
     * @param message 支付消息
     * @author: shaoyang.li
     * @date: 2025-07-03 11:07:45
     * @since JDK 1.8
     */
    @Override
    protected void handleMessage(AsyncDoPaymentMessageDTO message) {
        log.info("异步支付消息处理开始，消息内容：{}", JSON.toJSONString(message));
        
        try {
            // 1. 参数校验
            validateMessage(message);
            
            // 2. 调用业务服务处理支付
            asyncDoPaymentBusiness.process(message.getPmtDealNo());
            
            log.info("异步支付消息处理成功，支付交易号：{}", message.getPmtDealNo());
            
        } catch (Exception e) {
            log.error("异步支付消息处理失败，支付交易号：{}，异常信息：{}", 
                    message != null ? message.getPmtDealNo() : "null", e.getMessage(), e);
            // 框架会自动处理异常告警，这里重新抛出异常让框架处理
            throw e;
        }
    }

    /**
     * @description: 校验消息参数
     * @param message 支付消息
     * @author: shaoyang.li
     * @date: 2025-07-03 11:07:45
     * @since JDK 1.8
     */
    private void validateMessage(AsyncDoPaymentMessageDTO message) {
        AssertUtils.nonNullParam(message, "支付消息不能为空");
        AssertUtils.nonNullParam(message.getPmtDealNo(), "支付交易号不能为空");
        
        if (StringUtils.isBlank(message.getPmtDealNo())) {
            AssertUtils.throwParamErrorException("支付交易号不能为空字符串");
        }
    }
}
