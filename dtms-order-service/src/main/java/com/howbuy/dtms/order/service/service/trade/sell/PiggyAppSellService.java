package com.howbuy.dtms.order.service.service.trade.sell;

import com.howbuy.dtms.common.enums.*;
import com.howbuy.dtms.order.client.domain.request.sell.PiggyAppSellRequest;
import com.howbuy.dtms.order.client.domain.response.sell.PiggyAppSellResponse;
import com.howbuy.dtms.order.dao.bo.OrderCreateBO;
import com.howbuy.dtms.order.dao.param.PiggyTradeAppImportUpdateParam;
import com.howbuy.dtms.order.service.business.ordercreate.OrderCreateService;
import com.howbuy.dtms.order.service.business.ordermessage.OrderMessageService;
import com.howbuy.dtms.order.service.commom.enums.TradeValidatorEnum;
import com.howbuy.dtms.order.service.commom.utils.AssertUtils;
import com.howbuy.dtms.order.service.repository.HwDealOrderRepository;
import com.howbuy.dtms.order.service.service.AbstractTradeService;
import com.howbuy.dtms.order.service.validate.ParamsValidator;
import com.howbuy.dtms.order.service.validate.chain.TradeValidatorHelper;
import com.howbuy.dtms.order.service.validate.context.TradeContext;
import com.howbuy.dtms.order.service.validate.context.TradeContextBuilder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 储蓄罐申请卖出服务类
 * @date 2024/8/13 20:06
 * @since JDK 1.8
 */
@Service
public class PiggyAppSellService extends AbstractTradeService<PiggyAppSellRequest, TradeContext> {
    @Resource
    private TradeValidatorHelper tradeValidatorHelper;

    @Resource
    private TradeContextBuilder tradeContextBuilder;

    @Resource
    private OrderCreateService orderCreateService;

    @Resource
    private HwDealOrderRepository hwDealOrderRepository;

    @Resource
    private OrderMessageService orderMessageService;


    public PiggyAppSellResponse process(PiggyAppSellRequest request) {
        TradeContext context = tradeValidate(request);
        //创建订单及订单明细
        OrderCreateBO orderCreateBO = orderCreateService.createOrder(context);
        // 设置关联订单号(订单表的订单号)
        if(StringUtils.isNotEmpty(request.getExternalDealNo())) {
            orderCreateBO.getHwDealOrder().setRelationalDealNo(Long.valueOf(request.getExternalDealNo()));
        }
        // 储蓄罐申请单更新参数
        orderCreateBO.setPiggyTradeAppImportUpdateParam(buildPiggyTradeAppImportUpdateParam(request, orderCreateBO));
        //新增订单及订单明细
        hwDealOrderRepository.saveDealOrder(orderCreateBO);
        //发送订单消息
        orderMessageService.sellOrderMessage(context, orderCreateBO);

        PiggyAppSellResponse response = new PiggyAppSellResponse();
        response.setDealNo(String.valueOf(orderCreateBO.getHwDealOrder().getDealNo()));
        response.setDealDtlNo(String.valueOf(orderCreateBO.getHwDealOrderDtl().getDealDtlNo()));
        return response;
    }

    @Override
    protected TradeContext tradeValidate(PiggyAppSellRequest request) {
        // 基类参数校验
        ParamsValidator.validate(request,
                "appDt",
                "appTm",
                "tradeChannel",
                "outletCode",
                "ipAddress",
                "externalDealNo",
                "redeemMethod",
                "redeemDirection",
                "hkCustNo",
                "fundCode",
                "operator"
        );
        if (Objects.equals(RedeemTypeEnum.BY_SHARE.getCode(), request.getRedeemMethod())) {
            ParamsValidator.validate(request, "appVol");
        }
        if (Objects.equals(RedeemTypeEnum.BY_AMOUNT.getCode(), request.getRedeemMethod())) {
            ParamsValidator.validate(request, "appAmt");
        }
        // 并发控制校验
        validateConcurrent(request);

        return doValidate(request);
    }

    @Override
    protected TradeContext doValidate(PiggyAppSellRequest request) {
        TradeContext context = buildContext(request);
        // 构建校验链
        List<String> validatorList = tradeValidatorHelper.buildChain(
                TradeValidatorEnum.ACCOUNT_STATUS,
                TradeValidatorEnum.FUND_TX_ACCT_STAT,
                TradeValidatorEnum.PREBOOK_DEAL_NO_VALID,
                TradeValidatorEnum.EXTERNAL_ORDER_NO,
                TradeValidatorEnum.PRODUCT_CHANNEL,
                TradeValidatorEnum.PRODUCT_BUSINESS,
                TradeValidatorEnum.OPEN_DT,
                TradeValidatorEnum.PRODUCT_NAV_STATUS,
                TradeValidatorEnum.REDEMPTION_METHOD,
                TradeValidatorEnum.REDEMPTION_DIRECTION,
                TradeValidatorEnum.AVAILABLE_SHARE,
                TradeValidatorEnum.SELL_MIN_HOLD,
                TradeValidatorEnum.SELL_TRADE_LIMIT
        );
        // 执行校验
        tradeValidatorHelper.doValidate(context, validatorList);
        return context;
    }

    /**
     * 构建上下文
     *
     * @param request
     * @return
     */
    private TradeContext buildContext(PiggyAppSellRequest request) {
        // 构建上下文
        TradeContext context = tradeContextBuilder.buildTradeContext(request, request.getHkCustNo(), request.getFundCode(), BusinessTypeEnum.SELL, BusinessCodeEnum.REDEEM);
        context.setExternalDealNo(request.getExternalDealNo());
        // 设置基金交易账号
        context.setFundTxAcctNo(request.getFundTxAcctNo());
        // 构建买入参数
        context.setSellBean(new TradeContext.SellBean());
        context.getSellBean().setSellAmt(request.getAppAmt());
        context.getSellBean().setSellVol(request.getAppVol());

        RedeemTypeEnum redeemTypeEnum = RedeemTypeEnum.getEnumByCode(request.getRedeemMethod());
        AssertUtils.nonNullParam(redeemTypeEnum, "赎回方式不存在");
        context.getSellBean().setRedeemTypeEnum(redeemTypeEnum);

        RedeemDirectionEnum redeemDirectionEnum = RedeemDirectionEnum.getEnum(request.getRedeemDirection());
        AssertUtils.nonNullParam(redeemDirectionEnum, "赎回方向不存在");
        context.getSellBean().setRedeemDirectionEnum(redeemDirectionEnum);

        return context;
    }

    private PiggyTradeAppImportUpdateParam buildPiggyTradeAppImportUpdateParam(PiggyAppSellRequest request, OrderCreateBO orderCreateBO) {
        PiggyTradeAppImportUpdateParam piggyParam = new PiggyTradeAppImportUpdateParam();
        piggyParam.setPiggyAppId(request.getExternalDealNo());
        piggyParam.setModifier(request.getOperator());
        piggyParam.setIsGenerated(PiggyTradeAppGenerateEnum.GENERATED.getCode());
        piggyParam.setDealNo(String.valueOf(orderCreateBO.getHwDealOrder().getDealNo()));
        piggyParam.setRemark("订单生成成功");
        piggyParam.setModifyTime(new Date());
        piggyParam.setOldIsGenerated(PiggyTradeAppGenerateEnum.NOT_GENERATE.getCode());
        return piggyParam;
    }

}
