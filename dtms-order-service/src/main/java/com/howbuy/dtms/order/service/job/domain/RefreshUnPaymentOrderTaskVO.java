/**
 * Copyright (c) 2025, <PERSON>g<PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.job.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @description: 刷新未支付订单定时任务消息VO
 * <AUTHOR>
 * @date 2025-07-03 11:34:43
 * @since JDK 1.8
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RefreshUnPaymentOrderTaskVO extends BaseTaskMessageVO {
    
    // 该任务消息体为空，仅作为触发信号使用
    // 所有业务参数通过数据库查询获取
}
