---
description: 根据设计文档生成数据库DDL脚本
globs: .md
alwaysApply: false
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
# 数据库建表规则
# 作者: hongdong.xie
# 创建日期: 2025-02-28 10:30:00
# 描述: 根据数据库设计文档自动生成SQL建表脚本的规则

# 文件命名规则
1. 按数据库名称创建对应的.sql文件
2. 文件名格式: 数据库名_ddl.sql
3. 文件必须包含头部注释，包括文件名、作者、创建日期、描述等信息

# SQL脚本生成规则
1. 基本原则
   - 保持简单性：只创建必要的结构
   - 遵循最小权限原则：不添加未明确指定的约束
   - 保持一致性：相同的命名规范和代码风格

2. 表结构规则
   - 表名必须使用反引号包裹：`TABLE_NAME`
   - 字段名必须使用反引号包裹：`COLUMN_NAME`
   - 表名和字段名必须符合X_X格式的命名规范
   - 必须为表和字段添加注释
   - 每个表必须有主键
   - 主键列使用 INT AUTO_INCREMENT
   - 不使用外键约束
   - 不指定存储引擎
   - 不指定字符集
   - 表和字段命名不符合规范，生成之前要打印出不符合规范的表和字段

3. 字段定义规则
   - 不添加未明确指定的NOT NULL约束
   - 不添加未明确指定的DEFAULT值
   - 数据类型规范：
     * INT(11) 简化为 INT
     * VARCHAR2 改为 VARCHAR
     * DATETIME 用于时间戳
     * VARCHAR(1) 用于状态标识
   - 保留字段长度限制（如VARCHAR的长度）

4. 注释规则
   - 表注释使用COMMENT属性
   - 字段注释使用COMMENT属性
   - 注释内容要清晰说明字段用途
   - 对于枚举值字段，在注释中说明每个值的含义

5. 索引规则
   - 只创建主键索引
   - 除非明确指定，不创建其他索引
   - 如需创建其他索引，遵循以下命名规范：
     * 唯一索引：UK_字段名
     * 普通索引：IDX_字段名

6. SQL语句格式化规则
   - 使用大写的SQL关键字
   - 每个字段定义独占一行
   - 使用4个空格缩进
   - 字段定义之间使用逗号分隔，最后一个字段定义后不加逗号
   - 主键定义放在所有字段定义之后

# 示例
```sql
/*
 * 文件名: xxx.sql
 * 作者: hongdong.xie
 * 创建日期: yyyy-MM-dd HH:mm:ss
 * 描述: xxx库建表脚本
 */

-- 表名
CREATE TABLE `TABLE_NAME` (
    `ID` INT AUTO_INCREMENT COMMENT '主键ID',
    `COLUMN1` VARCHAR(10) COMMENT '字段1说明',
    `COLUMN2` INT COMMENT '字段2说明',
    `STATUS` VARCHAR(1) COMMENT '状态：0-无效；1-有效',
    `CREATE_TIME` DATETIME COMMENT '创建时间',
    `UPDATE_TIME` DATETIME COMMENT '更新时间',
    PRIMARY KEY (`ID`)
) COMMENT='表说明';
```

# 注意事项
1. 建表脚本应该是幂等的，便于重复执行
2. 修改表结构时使用ALTER TABLE语句
3. 每个SQL语句都要有清晰的注释说明
4. 确保SQL语句的兼容性，不使用特定数据库的专有特性 