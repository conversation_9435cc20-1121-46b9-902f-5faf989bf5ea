---
description: 根据设计文档生成dubbo接口定义
globs: 
alwaysApply: false
---

# Your rule content

- You can @ files here
- You can use markdown but dont have to
# DTMS-Order项目API生成规则
# 作者: hongdong.xie
# 日期: 2025-03-05 10:35:21
# 版本: 1.0.0

#==================== Dubbo接口生成规范 ====================
[codestylerules.mdc](mdc:.cursor/rules/codestylerules.mdc)
# Dubbo接口包名与命名规范
dubbo_interface_rules:
  - 接口主包：com.howbuy.dtms.order.client.facade
  - 交易子包：com.howbuy.dtms.order.client.facade.trade（具体业务再分子包，如fundsreceived）
  - 查询子包：com.howbuy.dtms.order.client.facade.query
  - 接口命名：业务功能+Facade (如ConfirmFundsReceivedFacade)
  - 接口继承：BaseFacade<请求类型, 响应类型>
  - 请求类型：业务功能+Request
  - 响应类型：业务功能+Response
  - Response类型：使用com.howbuy.dtms.order.client.domain.response.Response

# 接口修改/新增规则
interface_modify_rules:
  - 判断依据：
    - 设计文档中接口名后标注"——修改"的，表示在原有接口修改
    - 设计文档中接口名后标注"——新增"的，表示新增接口
    - 设计文档中接口名后无标注的，表示新增接口
  - 修改原则：
    - 修改接口时，保持原有的路径和方法名不变
    - 修改接口时，需要兼容原有的功能
    - 修改接口时，新增字段采用追加方式，不要删除或修改原有字段
    - 修改接口时，保持原有的注释格式，仅更新内容

#==================== Dubbo接口实现规范 ====================

# Dubbo接口实现类规范
dubbo_impl_rules:
  - 实现类包名规则：
    - 主包：com.howbuy.dtms.order.service.provider.dubbo
    - 子包结构：应与接口包结构对应，如trade.fundsreceived
    - 完整示例：com.howbuy.dtms.order.service.provider.dubbo.trade.fundsreceived
  - 实现类命名：接口名+Impl (如ConfirmFundsReceivedFacadeImpl)
  - 必须实现相应接口：implements XXXFacade
  - 注解使用：
    - @Slf4j - 用于日志记录
    - @DubboService - 标识为Dubbo服务
    - @Component - 注册为Spring组件
  - Service层交互：
    - 对应Service命名：接口名Service (如ConfirmFundsReceivedService)
    - Service包路径：com.howbuy.dtms.order.service.service.{业务子包}
    - 注入方式：使用@Resource注解
  - Service类规范：
    - Service类直接实现业务逻辑，不需要创建接口和实现类
    - Service类使用@Service注解标识为Spring组件
    - Service类使用@Slf4j注解用于日志记录
    - Service类方法应包含完整的业务逻辑实现
    - 不要在service.impl包下创建实现类，直接在service包下实现业务逻辑

# Dubbo接口与实现同步生成规则
dubbo_sync_generate_rules:
  - 生成顺序：
    1. 根据接口文档在dtms-order-client中生成接口定义
    2. 在dtms-order-service中生成对应的接口实现类
    3. 在dtms-order-service中生成对应的Service类（直接实现业务逻辑）
    4. Service类中不要使用@Transactional注解标识为事务管理，事务管理在repository层实现
  - 同步原则：
    - 接口名变更时，实现类名也必须同步变更
    - 接口方法变更时，实现类方法也必须同步变更
    - 接口参数变更时，实现类参数也必须同步变更
  - 空实现模板：
    ```java
    @Slf4j
    @DubboService
    @Component
    public class XXXFacadeImpl implements XXXFacade {
    
        @Resource
        private XXXService xxxService;
        
        @Override
        public Response<XXXResponse> execute(XXXRequest request) {
            return xxxService.execute(request);
        }
    }
    ```
  - Service类模板：
    ```java
    @Slf4j
    @Service
    public class XXXService {
    
        /**
         * @description: 执行业务逻辑
         * @param request 请求参数
         * @return 处理结果
         */
        public Response<XXXResponse> execute(XXXRequest request) {
            // 业务逻辑实现
            return Response.ok();
        }
    }
    ```

# 请求对象规范
request_rules:
  - 请求对象包名：com.howbuy.dtms.order.client.domain.request.{业务子包}
  - 类命名：业务功能+Request
  - 必须继承：BaseRequest
  - 注解使用：@Setter/@Getter/@EqualsAndHashCode(callSuper = true)
  - 参数验证：使用@MyValidation(validatorType = ValidatorTypeEnum.类型, fieldName = "中文名", isRequired = true)
  - @MyValidation注解类型：使用com.howbuy.commons.validator.MyValidation
  - List、Map等集合对象类型参数，即使需求文档要求必填，也不要加@MyValidation，必填验证在实现类通过代码实现
  - ValidatorTypeEnum类型：使用com.howbuy.commons.validator.util.ValidatorTypeEnum
  - 数值类型：使用BigDecimal表示金额
  - 日期时间：使用String类型，格式在注释中说明

# 响应对象规范
response_rules:
  - 响应对象包名：com.howbuy.dtms.order.client.domain.response.{业务子包}
  - 类命名：业务功能+Response
  - 注解使用：@Getter/@Setter
  - 序列化：实现Serializable接口，定义serialVersionUID
  - 字段说明：每个字段必须添加中文注释

#==================== APIDOC注释规范 ====================

# Dubbo接口APIDOC规范
dubbo_apidoc_rules:
  - 必须包含以下标签：
    - @api {DUBBO} 接口全类名.方法名()
    - @apiVersion 版本号
    - @apiGroup 实现类名
    - @apiName 方法名()
    - @apiDescription 接口功能描述
    - @apiParam (请求体) {类型} 参数名 参数说明
    - @apiParamExample 请求体示例
    - @apiSuccess (响应结果) {类型} 字段名 字段说明
    - @apiSuccessExample 响应结果示例
  - 特殊要求：
    - 请求和响应示例必须包含真实业务场景数据
    - 字段说明必须包含数据格式要求（如日期格式YYYYMMDD）
    - 枚举值必须说明所有可能的取值（如1-电汇、2-支票）
    - 金额字段必须说明精度
    - 生成的apidoc在Facade接口定义上面一行，不要放到package或import上面

# 实现类注释规范
impl_comment_rules:
  ```
  /**
   * @description: 接口实现功能描述
   * <AUTHOR>
   * @date yyyy/MM/dd HH:mm
   * @since JDK 1.8
   */
  ```

# 类注释规范
class_comment_rules:
  ```
  /**
   * @description: 类的功能描述
   * <AUTHOR>
   * @date yyyy/MM/dd HH:mm
   * @since JDK 1.8
   */
  ```

# 字段注释规范
field_comment_rules:
  ```
  /**
   * 字段中文名称
   * 可选：附加说明（如：格式要求/取值范围）
   */
  ```

#==================== 参数验证规范 ====================

# 参数验证规则
validation_rules:
  - 必填字符串：@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中文名", isRequired = true)
  - 可选字符串：@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "中文名", isRequired = false)
  - 必填金额：@MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "中文名", isRequired = true)
  - 必填日期：@MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "日期", isRequired = true)

#==================== 代码示例 ====================

# Dubbo接口示例
dubbo_interface_example:
  ```java
  /**
   * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.fundsreceived.ConfirmFundsReceivedFacade.confirmFundsReceived()
   * @apiVersion 1.0.0
   * @apiGroup ConfirmFundsReceivedFacadeImpl
   * @apiName confirmFundsReceived()
   * @apiDescription 资金到账匹配确认
   * @apiParam (请求体) {String} dealNo 订单号
   * @apiParam (请求体) {String} hkCustNo 香港客户号
   * @apiParam (请求体) {BigDecimal} actualPayAmt 实际打款金额
   * @apiParam (请求体) {String} actualPayDt 实际打款日期(格式:YYYYMMDD)
   * @apiParam (请求体) {String} actualPayTm 实际打款时间(格式:HHmmss)
   * @apiParam (请求体) {String} operator 操作人
   * @apiParam (请求体) {String} currencyFlag 币种标志
   * @apiParam (请求体) {String} voucherStatusFlag 凭证状态标志
   * @apiParam (请求体) {String} tradeChannel 交易渠道 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP
   * @apiParam (请求体) {String} outletCode 网点号
   * @apiParam (请求体) {String} ipAddress IP地址
   * @apiParam (请求体) {String} externalDealNo 外部订单号
   * @apiParam (请求体) {String} macAddress MAC地址
   * @apiParam (请求体) {String} deviceSerialNo 设备序列号
   * @apiParam (请求体) {String} deviceModel 设备型号
   * @apiParam (请求体) {String} deviceName 设备名称
   * @apiParam (请求体) {String} systemVersion 系统版本号
   * @apiParamExample 请求体示例
   * {"dealNo":"D20240806001","hkCustNo":"HK10086","actualPayAmt":6529.48,"actualPayDt":"20240806","actualPayTm":"153010","operator":"admin","currencyFlag":"CNY","voucherStatusFlag":"1","tradeChannel":"9","outletCode":"001","ipAddress":"***********","externalDealNo":"EXT001","macAddress":"00:11:22:33:44:55","deviceSerialNo":"SN12345","deviceModel":"PC","deviceName":"Windows","systemVersion":"10"}
   * @apiSuccess (响应结果) {String} code 状态码
   * @apiSuccess (响应结果) {String} description 描述信息
   * @apiSuccess (响应结果) {Object} data 数据封装
   * @apiSuccess (响应结果) {String} data.description 描述
   * @apiSuccess (响应结果) {String} data.field 二次确认，需要用到的字段
   * @apiSuccessExample 响应结果示例
   * {"code":"0000","data":{"field":"confirmField","description":"匹配成功"},"description":"成功"}
   */
  public interface ConfirmFundsReceivedFacade extends BaseFacade<ConfirmFundsReceivedRequest, ConfirmFundsReceivedResponse> {
  
  }
  ```

# Dubbo接口实现类示例
dubbo_impl_example:
  ```java
  /**
   * @description: 资金到账匹配
   * <AUTHOR>
   * @date 2024/08/06 17:35:21
   * @since JDK 1.8
   */
  @Slf4j
  @DubboService
  @Component
  public class ConfirmFundsReceivedFacadeImpl implements ConfirmFundsReceivedFacade {
  
      @Resource
      private ConfirmFundsReceivedService confirmFundsReceivedService;
      
      @Override
      public Response<ConfirmFundsReceivedResponse> execute(ConfirmFundsReceivedRequest request) {
          return confirmFundsReceivedService.execute(request);
      }
  }
  ```

# Service类示例
service_class_example:
  ```java
  /**
   * @description: 资金到账匹配服务
   * <AUTHOR>
   * @date 2024/08/06 17:35:21
   * @since JDK 1.8
   */
  @Slf4j
  @Service
  public class ConfirmFundsReceivedService {
  
      /**
       * @description: 执行资金到账匹配
       * @param request 请求参数
       * @return 处理结果
       */
      public Response<ConfirmFundsReceivedResponse> execute(ConfirmFundsReceivedRequest request) {
          lreturn Response.ok();
      }
  }
  ```

# 请求对象示例
request_example:
  ```java
  /**
   * @description: 资金到账匹配请求
   * <AUTHOR>
   * @date 2024/08/06 17:35:21
   * @since JDK 1.8
   */
  @Setter
  @Getter
  @EqualsAndHashCode(callSuper = true)
  public class ConfirmFundsReceivedRequest extends BaseRequest {
  
      private static final long serialVersionUID = 5095131815926801586L;
      
      /**
       * 订单号
       */
      private String dealNo;
  
      /**
       * 香港客户号
       */
      private String hkCustNo;
  
      /**
       * 实际打款金额
       * 精确到小数点后2位
       */
      @MyValidation(validatorType = ValidatorTypeEnum.Money, fieldName = "实际打款金额", isRequired = true)
      private BigDecimal actualPayAmt;
  
      /**
       * 实际打款日期
       * 格式：YYYYMMDD
       */
      @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实际打款日期", isRequired = true)
      private String actualPayDt;
  
      /**
       * 实际打款时间
       * 格式：HHmmss
       */
      @MyValidation(validatorType = ValidatorTypeEnum.String, fieldName = "实际打款时间", isRequired = true)
      private String actualPayTm;
      
      // 其他字段省略...
  }
  ```

# 响应对象示例
response_example:
  ```java
  /**
   * @description: 资金到账匹配响应
   * <AUTHOR>
   * @date 2024/08/06 17:35:21
   * @since JDK 1.8
   */
  @Getter
  @Setter
  public class ConfirmFundsReceivedResponse implements Serializable {
  
      private static final long serialVersionUID = -8428152516149003724L;
      
      /**
       * 描述
       * 接口执行结果描述
       */
      private String description;
  
      /**
       * 二次确认，需要用到的字段
       * 当需要二次确认时，此字段不为空
       */
      private String field;
  }
  ```

# 生成指南
generate_guide:
  1. 明确业务需求，确定接口名称和功能
  2. 在正确的包路径下创建接口和对应的请求/响应类
  3. 同步在service模块中创建对应的实现类
  4. 在service模块中创建对应的Service类（直接实现业务逻辑，不需要接口）
  5. 按规范编写APIDOC注释，确保包含所有必要标签
  6. 为请求对象的字段添加适当的验证注解
  7. 所有字段添加明确的中文注释，说明用途和格式要求
  8. 提供真实的请求和响应示例
  9. 对枚举值和特殊格式字段提供详细说明