---
description: 根据数据库表结构生成符合项目规范的MyBatis Mapper和PO类代码
globs: .sql
alwaysApply: false
---
 ---
description: 根据数据库表结构生成符合项目规范的MyBatis Mapper和PO类代码
globs: .sql
alwaysApply: false
---

# MyBatis代码生成规范

本规范用于指导基于数据库表结构生成符合项目规范的MyBatis Mapper和PO类代码。

## 1. 目录结构规范

### 1.1 PO类目录结构
```
dtms-order-dao/src/main/java/com/howbuy/dtms/order/dao/po/表名PO.java
```

### 1.2 基础Mapper接口目录结构
```
dtms-order-dao/src/main/java/com/howbuy/dtms/order/dao/mapper/mysql/表所属模块/表名Mapper.java
```

### 1.3 基础Mapper XML目录结构
```
dtms-order-dao/src/main/resources/com/howbuy/dtms/order/dao/mapper/mysql/表所属模块/表名Mapper.xml
```

### 1.4 自定义Mapper接口目录结构
```
dtms-order-dao/src/main/java/com/howbuy/dtms/order/dao/mapper/mysql/customize/Customize表名Mapper.java
```

### 1.5 自定义Mapper XML目录结构
```
dtms-order-dao/src/main/resources/com/howbuy/dtms/order/dao/mapper/mysql/customize/Customize表名Mapper.xml
```

## 2. 命名规范

### 2.1 PO类命名规范
- 类名：表名转驼峰命名法 + PO后缀
- 例如：表名为hw_submit_deal_order，对应的PO类名为HwSubmitDealOrderPO
- 注解使用：@Getter/@Setter

### 2.2 Mapper接口命名规范
- 基础Mapper接口：表名转驼峰命名法 + Mapper后缀
- 自定义Mapper接口：Customize + 表名转驼峰命名法 + Mapper后缀
- 例如：表名为hw_submit_deal_order，对应的基础Mapper接口名为HwSubmitDealOrderMapper，自定义Mapper接口名为CustomizeHwSubmitDealOrderMapper

### 2.3 Mapper XML命名规范
- 基础Mapper XML：表名转驼峰命名法 + Mapper.xml
- 自定义Mapper XML：Customize + 表名转驼峰命名法 + Mapper.xml
- 例如：表名为hw_submit_deal_order，对应的基础Mapper XML名为HwSubmitDealOrderMapper.xml，自定义Mapper XML名为CustomizeHwSubmitDealOrderMapper.xml

## 3. PO类代码规范

### 3.1 PO类基本结构
```java
package com.howbuy.dtms.order.dao.po;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description: 表名描述
 * @date 当前日期 时间
 * @since JDK 1.8
 */
/**
 * 表注释
 */
@Getter
@Setter
public class 表名PO {
    /**
     * 字段注释
     */
    private 字段类型 字段名;
    
}
```

### 3.2 PO类字段命名规范
- 字段名：数据库字段名转小驼峰命名法
- 例如：数据库字段名为submit_deal_no，对应的Java字段名为submitDealNo

### 3.3 PO类字段类型映射规范
- BIGINT -> Long
- INT -> Integer
- DECIMAL -> BigDecimal
- VARCHAR -> String
- CHAR -> String
- TIMESTAMP -> Date
- DATE -> Date
- TEXT -> String

### 3.4 PO类注释规范
- 类注释：包含作者、描述、日期和JDK版本
- 字段注释：使用数据库表字段的注释

## 4. 基础Mapper接口代码规范

### 4.1 基础Mapper接口基本结构
```java
package com.howbuy.dtms.order.dao.mapper.mysql.表所属模块;

import com.howbuy.dtms.order.dao.po.表名PO;

/**
 * <AUTHOR>
 * @description: 
 * @date 当前日期 时间
 * @since JDK 1.8
 */
public interface 表名Mapper {
    int deleteByPrimaryKey(主键类型 主键名);

    int insert(表名PO record);

    int insertSelective(表名PO record);

    表名PO selectByPrimaryKey(主键类型 主键名);

    int updateByPrimaryKeySelective(表名PO record);

    int updateByPrimaryKey(表名PO record);
}
```

## 5. 基础Mapper XML代码规范

### 5.1 基础Mapper XML基本结构
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.表所属模块.表名Mapper">
  <resultMap id="BaseResultMap" type="com.howbuy.dtms.order.dao.po.表名PO">
    <!--@mbg.generated-->
    <!--@Table 表名-->
    <id column="主键列名" jdbcType="主键列类型" property="主键属性名" />
    <result column="列名1" jdbcType="列类型1" property="属性名1" />
    <result column="列名2" jdbcType="列类型2" property="属性名2" />
    <!-- 其他列... -->
  </resultMap>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    列名1, 列名2, ...
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="主键Java类型" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from 表名
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="主键Java类型">
    <!--@mbg.generated-->
    delete from 表名
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </delete>
  
  <insert id="insert" parameterType="com.howbuy.dtms.order.dao.po.表名PO">
    <!--@mbg.generated-->
    insert into 表名 (列名1, 列名2, ...)
    values (#{属性名1,jdbcType=列类型1}, #{属性名2,jdbcType=列类型2}, ...)
  </insert>
  
  <insert id="insertSelective" parameterType="com.howbuy.dtms.order.dao.po.表名PO">
    <!--@mbg.generated-->
    insert into 表名
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="属性名1 != null">
        列名1,
      </if>
      <if test="属性名2 != null">
        列名2,
      </if>
      <!-- 其他列... -->
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="属性名1 != null">
        #{属性名1,jdbcType=列类型1},
      </if>
      <if test="属性名2 != null">
        #{属性名2,jdbcType=列类型2},
      </if>
      <!-- 其他列... -->
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.howbuy.dtms.order.dao.po.表名PO">
    <!--@mbg.generated-->
    update 表名
    <set>
      <if test="属性名1 != null">
        列名1 = #{属性名1,jdbcType=列类型1},
      </if>
      <if test="属性名2 != null">
        列名2 = #{属性名2,jdbcType=列类型2},
      </if>
      <!-- 其他列... -->
    </set>
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.howbuy.dtms.order.dao.po.表名PO">
    <!--@mbg.generated-->
    update 表名
    set 列名1 = #{属性名1,jdbcType=列类型1},
      列名2 = #{属性名2,jdbcType=列类型2},
      <!-- 其他列... -->
    where 主键列名 = #{主键属性名,jdbcType=主键列类型}
  </update>
</mapper>
```

### 5.2 Mapper XML JDBC类型映射规范
- BIGINT -> BIGINT
- INT -> INTEGER
- DECIMAL -> DECIMAL
- VARCHAR -> VARCHAR
- CHAR -> CHAR
- TIMESTAMP -> TIMESTAMP
- DATE -> DATE
- TEXT -> LONGVARCHAR

## 6. 自定义Mapper接口代码规范

### 6.1 自定义Mapper接口基本结构
```java
package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.po.表名PO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 自定义查询说明
 * @date 当前日期 时间
 * @since JDK 1.8
 */
public interface Customize表名Mapper {
    // 自定义查询方法
    List<表名PO> selectListByXXX(@Param("参数名1") 参数类型1 参数名1, @Param("参数名2") 参数类型2 参数名2);
    
}
```

## 7. 自定义Mapper XML代码规范

### 7.1 自定义Mapper XML基本结构
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.dtms.order.dao.mapper.mysql.customize.Customize表名Mapper">
    
    <!-- 自定义查询 -->
    <select id="selectListByXXX" parameterType="map" resultMap="com.howbuy.dtms.order.dao.mapper.mysql.表所属模块.表名Mapper.BaseResultMap">
        select
        <include refid="com.howbuy.dtms.order.dao.mapper.mysql.表所属模块.表名Mapper.Base_Column_List"/>
        from 表名
        <where>
            <if test="参数名1 != null">
                and 列名1 = #{参数名1,jdbcType=列类型1}
            </if>
            <if test="参数名2 != null">
                and 列名2 = #{参数名2,jdbcType=列类型2}
            </if>
            <!-- 其他条件... -->
        </where>
    </select>
    
</mapper>
```

## 8. 代码生成步骤

### 8.1 生成PO类
1. 根据数据库表结构，生成对应的PO类
2. 确保类名符合命名规范：表名转驼峰命名法 + PO后缀
3. 确保字段名符合命名规范：数据库字段名转小驼峰命名法
4. 确保字段类型符合类型映射规范
5. 添加适当的类注释和字段注释

### 8.2 生成基础Mapper接口
1. 根据PO类，生成对应的基础Mapper接口
2. 确保接口名符合命名规范：表名转驼峰命名法 + Mapper后缀
3. 实现基本的CRUD方法
4. 添加适当的类注释

### 8.3 生成基础Mapper XML
1. 根据PO类和基础Mapper接口，生成对应的基础Mapper XML
2. 确保XML文件名符合命名规范：表名转驼峰命名法 + Mapper.xml
3. 实现基本的CRUD SQL语句
4. 确保resultMap和SQL语句符合规范

### 8.4 生成自定义Mapper接口（可选）
1. 根据业务需求，生成对应的自定义Mapper接口
2. 确保接口名符合命名规范：Customize + 表名转驼峰命名法 + Mapper后缀
3. 实现自定义的查询、更新、删除方法
4. 添加适当的类注释和方法注释

### 8.5 生成自定义Mapper XML（可选）
1. 根据自定义Mapper接口，生成对应的自定义Mapper XML
2. 确保XML文件名符合命名规范：Customize + 表名转驼峰命名法 + Mapper.xml
3. 实现自定义的SQL语句
4. 确保SQL语句符合规范

## 9. 示例

以表hw_submit_deal_order为例，生成的代码如下：

### 9.1 PO类示例
```java
package com.howbuy.dtms.order.dao.po;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 上报交易订单
 * @date 2025-03-10 08:40:54
 * @since JDK 1.8
 */
/**
 * 上报交易订单
 */
public class HwSubmitDealOrderPO {
    /**
     * ID
     */
    private Long id;

    /**
     * 上报订单号
     */
    private Long submitDealNo;
    
    // 其他字段...
    
    // getter和setter方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSubmitDealNo() {
        return submitDealNo;
    }

    public void setSubmitDealNo(Long submitDealNo) {
        this.submitDealNo = submitDealNo;
    }
    
    // 其他getter和setter方法...
}
```

### 9.2 基础Mapper接口示例
```java
package com.howbuy.dtms.order.dao.mapper.mysql.order;

import com.howbuy.dtms.order.dao.po.HwSubmitDealOrderPO;

/**
 * <AUTHOR>
 * @description: 
 * @date 2025-03-10 08:40:54
 * @since JDK 1.8
 */
public interface HwSubmitDealOrderMapper {
    int deleteByPrimaryKey(Long id);

    int insert(HwSubmitDealOrderPO record);

    int insertSelective(HwSubmitDealOrderPO record);

    HwSubmitDealOrderPO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(HwSubmitDealOrderPO record);

    int updateByPrimaryKey(HwSubmitDealOrderPO record);
}
```

### 9.3 自定义Mapper接口示例
```java
package com.howbuy.dtms.order.dao.mapper.mysql.customize;

import com.howbuy.dtms.order.dao.po.HwSubmitDealOrderPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 上报交易订单自定义查询
 * @date 2025-03-10 08:40:54
 * @since JDK 1.8
 */
public interface CustomizeHwSubmitDealOrderMapper {
    
    /**
     * @description: 根据订单明细号查询上报订单列表
     * @param dealDtlNo 订单明细号
     * @return 上报订单列表
     * @author: hongdong.xie
     * @date: 2025-03-10 08:40:54
     * @since JDK 1.8
     */
    List<HwSubmitDealOrderPO> selectListByDealDtlNo(Long dealDtlNo);
}
```

## 10. 注意事项
1. 确保生成的代码符合项目规范
2. 确保字段名和类型映射正确
3. 确保注释完整、准确
4. 确保SQL语句正确、高效
5. 自定义Mapper接口和XML文件应根据实际业务需求进行定制
6. 避免生成冗余或重复的代码
7. 确保生成的代码可以正常编译和运行