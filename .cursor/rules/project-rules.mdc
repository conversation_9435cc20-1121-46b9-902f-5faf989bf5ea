---
description: 海外订单项目级别规范指南code review
globs: 
alwaysApply: true
---
 # 海外订单项目级别规范指南

## 项目概述
海外订单系统(dtms-order)是负责处理好买海外基金交易订单的核心系统，包含订单处理、余额管理、基金交易等功能。项目采用微服务架构，基于Spring Boot + Dubbo实现。

## 项目结构规范

### 模块划分
- **dtms-order-client**: 包含Dubbo接口定义和出入参定义
  - 基础包名: `com.howbuy.dtms.order.client`
  - 接口定义: `com.howbuy.dtms.order.client.facade`
  - 交易类接口: `com.howbuy.dtms.order.client.facade.trade`
  - 查询类接口: `com.howbuy.dtms.order.client.facade.query`
  - 枚举类: `com.howbuy.dtms.order.client.enums`
  - 接口入参: `com.howbuy.dtms.order.client.domain.request`
  - 接口出参: `com.howbuy.dtms.order.client.domain.response`

- **dtms-order-remote**: 包含公共内容，如启动类、切面、异常处理等
  - 基础包名: `com.howbuy.dtms.order.remote`
  - 启动类: `com.howbuy.dtms.order.remote.DtmsOrderApplication`
  - 配置目录: `src/main/resources/`

- **dtms-order-dao**: 包含数据库操作和ORM相关配置
  - 基础包名: `com.howbuy.dtms.order.dao`
  - 数据库操作: `com.howbuy.dtms.order.dao.mapper`
    - **自定义SQL操作**: 对于自定义的SQL操作，应在 `com.howbuy.dtms.order.dao.mapper.mysql.customize` 包中创建名为 `CustomizeXXXXMapper` 的接口。例如，`HwPaymentOrderPOMapper` 对应的自定义Mapper为 `CustomizeHwPaymentOrderPOMapper`。
  - 数据库实体: `com.howbuy.dtms.order.dao.po`
  - 关联查询对象: `com.howbuy.dtms.order.dao.bo`

- **dtms-order-service**: 包含业务逻辑实现
  - 基础包名: `com.howbuy.dtms.order.service`
  - Dubbo接口实现: `com.howbuy.dtms.order.service.provider.dubbo`
  - 业务实现: `com.howbuy.dtms.order.service.service`
  - 事务管理: `com.howbuy.dtms.order.service.repository`

## 命名规范

### 通用命名规则
- **类名**: 使用PascalCase（首字母大写的驼峰命名法），如`UserController`、`OrderService`
- **方法名和变量名**: 使用camelCase（首字母小写的驼峰命名法），如`findUserById`、`orderStatus`
- **常量**: 使用大写字母和下划线分隔，如`MAX_RETRY_COUNT`、`DEFAULT_TIMEOUT`
- **包名**: 全小写，使用点分隔，如`com.howbuy.dtms.order.service`

### 特定组件命名规则

#### 接口和实体类
- **Dubbo接口**: 以`Facade`结尾，如`QueryOrderFacade`、`CreateOrderFacade`
- **接口入参**: 接口名+`Request`，如`QueryOrderRequest`、`CreateOrderRequest`
- **接口出参**: 接口名+`Response`，如`QueryOrderResponse`、`CreateOrderResponse`
- **数据库实体**: 表名+`PO`，如`HwDealOrderPO`、`HwCustFundBalPO`
- **业务对象**: 业务功能+`BO`，如`OrderCreateBO`、`TradeRecordReportedVolSumBO`
- **视图对象**: 业务功能+`VO`，如`CounterBuyVO`、`CounterSellVO`

#### 服务层组件
- **Dubbo实现类**: 接口名+`Impl`，如`QueryOrderFacadeImpl`、`CreateOrderFacadeImpl`
- **Service类**: 业务功能+`Service`，如`CounterBuyService`、`QueryOrderService`
- **Repository类**: 表名+`Repository`，如`HwDealOrderRepository`、`HwCustFundBalRepository`
- **Mapper接口**: 表名+`Mapper`，如`HwDealOrderMapper`、`HwCustFundBalMapper`

## 接口定义规范

### Dubbo接口定义
1. 接口必须继承`BaseFacade<Request, Response>`
2. 接口必须使用APIDOC风格注释，包含以下内容：
   - API路径(`@api`)
   - API版本(`@apiVersion`)
   - API组(`@apiGroup`)
   - API名称(`@apiName`)
   - API描述(`@apiDescription`)
   - 请求参数(`@apiParam`)
   - 请求示例(`@apiParamExample`)
   - 响应结果(`@apiSuccess`)
   - 响应示例(`@apiSuccessExample`)

```java
/**
 * @api {DUBBO} com.howbuy.dtms.order.client.facade.trade.example.ExampleFacade.execute()
 * @apiVersion 1.0.0
 * @apiGroup ExampleFacadeImpl
 * @apiName execute()
 * @apiDescription 接口功能描述
 * @apiParam (请求体) {String} paramName 参数描述
 * @apiParamExample 请求体示例
 * {"paramName":"paramValue"}
 * @apiSuccess (响应结果) {String} code 状态码
 * @apiSuccess (响应结果) {String} description 描述信息
 * @apiSuccess (响应结果) {Object} data 数据封装
 * @apiSuccessExample 响应结果示例
 * {"code":"0000","data":{},"description":"成功"}
 */
public interface ExampleFacade extends BaseFacade<ExampleRequest, ExampleResponse> {
}
```

### 入参/出参定义规范
1. 请求类必须继承`BaseRequest`
2. 响应类必须定义合理的返回结构
3. 所有字段必须有注释说明用途
4. 使用`@Getter`和`@Setter`注解（不使用`@Data`）
5. 字段命名遵循Java驼峰命名法
6. 敏感字段应使用合适的序列化/反序列化策略

```java
@Getter
@Setter
public class ExampleRequest extends BaseRequest {
    /**
     * 客户香港编号
     */
    private String hkCustNo;
    
    /**
     * 基金代码
     */
    private String fundCode;
}
```

## 接口实现规范

### Dubbo接口实现类
1. 实现类必须位于`com.howbuy.dtms.order.service.provider.dubbo`包下，子包结构应与接口包保持一致
2. 实现类命名为接口名+`Impl`
3. 使用`@DubboService`注解暴露服务
4. 通过`@Resource`注入所需的Service类
5. 方法实现应简洁，主要负责参数校验和Service层的调用，不包含具体业务逻辑

```java
@DubboService
public class ExampleFacadeImpl implements ExampleFacade {
    @Resource
    private ExampleService exampleService;
    
    @Override
    public Response<ExampleResponse> execute(ExampleRequest request) {
        return Response.ok(exampleService.process(request));
    }
}
```

## 服务层调用规范

### Service层
1. Service类应位于`com.howbuy.dtms.order.service.service`包下，根据业务功能划分子包
2. 使用`@Service`注解将服务注册到Spring容器
3. 使用`@Slf4j`注解添加日志支持
4. 通过`@Resource`注入Repository类和其他Service类
5. 实现具体业务逻辑，不直接操作数据库
6. 方法应有完整的Javadoc注释

```java
@Service
@Slf4j
public class ExampleService {
    @Resource
    private ExampleRepository exampleRepository;
    
    /**
     * @description: 业务处理方法
     * @param request 请求参数
     * @return 处理结果
     * @author: developer.name
     * @date: 2025-03-06 13:20:55
     * @since JDK 1.8
     */
    public ExampleResponse process(ExampleRequest request) {
        // 业务逻辑实现
        return new ExampleResponse();
    }
}
```

### Repository层
1. Repository类应位于`com.howbuy.dtms.order.service.repository`包下
2. 使用`@Repository`注解将仓库注册到Spring容器
3. 使用`@Transactional`注解控制事务
4. 通过`@Resource`注入Mapper接口
5. 实现数据库操作逻辑，不包含业务逻辑
6. 方法应有完整的Javadoc注释

```java
@Repository
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class ExampleRepository {
    @Resource
    private ExampleMapper exampleMapper;
    
    /**
     * @description: 查询数据
     * @param id 主键ID
     * @return 数据实体
     * @author: developer.name
     * @date: 2025-03-06 13:20:55
     * @since JDK 1.8
     */
    public ExamplePO queryById(Long id) {
        return exampleMapper.selectByPrimaryKey(id);
    }
    
    /**
     * @description: 保存数据
     * @param entity 数据实体
     * @return 影响行数
     * @author: developer.name
     * @date: 2025-03-06 13:20:55
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int save(ExamplePO entity) {
        return exampleMapper.insert(entity);
    }
}
```

## 事务管理规范

### 事务注解使用原则
1. Repository层方法默认使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
2. 数据修改操作使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
3. 查询操作不需要修改默认的事务传播行为
4. 复杂业务场景可在Service层添加事务控制

### 事务传播行为说明
- `REQUIRED`: 如果当前存在事务，则加入该事务；如果当前没有事务，则创建一个新的事务
- `SUPPORTS`: 如果当前存在事务，则加入该事务；如果当前没有事务，则以非事务方式执行
- `MANDATORY`: 如果当前存在事务，则加入该事务；如果当前没有事务，则抛出异常
- `REQUIRES_NEW`: 创建一个新的事务，如果当前存在事务，则挂起当前事务
- `NOT_SUPPORTED`: 以非事务方式执行，如果当前存在事务，则挂起当前事务
- `NEVER`: 以非事务方式执行，如果当前存在事务，则抛出异常
- `NESTED`: 如果当前存在事务，则创建一个事务作为当前事务的嵌套事务来执行；如果当前没有事务，则等价于`REQUIRED`

## 注释规范

### 类注释
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: 类功能描述
 * <AUTHOR>
 * @date 2025-03-06 13:20:55
 * @since JDK 1.8
 */
```

### 方法注释
```java
/**
 * @description: 方法功能描述
 * @param paramName 参数说明
 * @return 返回值说明
 * @author: developer.name
 * @date: 2025-03-06 13:20:55
 * @since JDK 1.8
 */
```

## 异常处理规范

1. 使用统一的异常处理机制
2. 业务异常应继承自应用的基础异常类
3. 合理使用自定义异常和错误码
4. 异常信息应包含足够的上下文信息，便于问题定位
5. 不要捕获异常后不处理或仅打印日志
6. 业务异常优先在 com.howbuy.dtms.order.service.commom.enums.ExceptionEnum查找，如不存在则在ExceptionEnum中新增

```java
try {
    // 业务逻辑
} catch (BusinessException e) {
    log.error("业务处理异常: {}", e.getMessage(), e);
    throw e;
} catch (Exception e) {
    log.error("系统异常: {}", e.getMessage(), e);
    throw new SystemException("系统异常", e);
}
```

## 枚举使用规范
在 `com.howbuy.dtms.order.service.commom.enums` 包下找不到对应的枚举时，可以尝试使用 `com.howbuy.dtms.common.enums` 中定义的公共枚举。常用的公共枚举包括：
- `com.howbuy.dtms.common.enums.AckStatusEnum`
- `com.howbuy.dtms.common.enums.AppStatusEnum`
- `com.howbuy.dtms.common.enums.BusinessCodeEnum`
- `com.howbuy.dtms.common.enums.BusinessTypeEnum`
- `com.howbuy.dtms.common.enums.CancelTypeEnum`
- `com.howbuy.dtms.common.enums.PayStatusEnum`
- `com.howbuy.dtms.common.enums.OrderStatusEnum`
- `com.howbuy.dtms.common.enums.RedeemDirectionEnum`
- `com.howbuy.dtms.common.enums.RedeemTypeEnum`
- `com.howbuy.dtms.common.enums.SubmitStatusEnum`
- `com.howbuy.dtms.common.enums.FundTxAcctTypeEnum`
- `com.howbuy.dtms.common.enums.CounterOrderAuditStatusEnum`

## 日志规范

1. 使用SLF4J + Log4j2进行日志记录
2. 日志级别合理使用:
   - ERROR: 系统错误，需要立即关注的问题
   - WARN: 潜在的问题，可能需要关注
   - INFO: 重要业务操作，可用于生产环境问题跟踪
   - DEBUG: 调试信息，仅在开发和测试环境使用

3. 日志内容应包含足够的上下文信息
4. 敏感信息不应记录到日志中
5. 使用占位符而非字符串拼接

```java
// 正确的做法
log.info("处理订单, 订单号: {}, 客户号: {}", orderNo, custNo);

// 错误的做法
log.info("处理订单, 订单号: " + orderNo + ", 客户号: " + custNo);
```

## 代码审查重点

在进行代码审查时，应重点关注以下方面：

1. **命名规范**: 类名、方法名、变量名是否符合规范
2. **接口定义**: Dubbo接口定义是否符合规范，包括注释、入参出参等
3. **接口实现**: 实现类是否位于正确的包下，是否使用了正确的注解
4. **服务层调用**: Service与Repository的职责是否分明，方法是否有必要的注释
5. **事务管理**: 是否正确使用了事务注解，事务传播行为是否合适
6. **异常处理**: 是否有统一的异常处理机制，是否合理使用了自定义异常
7. **日志规范**: 是否使用了正确的日志级别，日志内容是否合适

每个错误码都应有详细的说明文档，便于开发和运维人员查阅。

## 安全规范

1. 敏感数据（如密码、证件号）需要加密存储
2. API调用需要进行身份验证和授权
3. 防止SQL注入、XSS等常见安全问题
4. 日志中不应包含敏感信息
5. 错误响应不应暴露系统内部信息

## 性能优化指南

1. 合理使用索引提高查询性能
2. 避免N+1查询问题
3. 使用批量操作替代循环单条操作
4. 使用缓存减少数据库访问
5. 大数据量处理时使用分页查询
6. 合理设置连接池参数
7. 使用异步处理提高并发能力

## 附录
- 业务码对照表
- 常用状态码说明
- 数据库表关系图