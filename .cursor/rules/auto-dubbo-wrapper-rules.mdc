---
description: 对外部dubbo接口进行封装
globs: 
alwaysApply: false
---
# Your rule content

- You can @ files here
- You can use markdown but dont have to
# 自动Dubbo接口封装生成规则

## 规则名称
AutoDubboWrapperGenerator

## 规则描述
根据提供的Dubbo接口全路径名，自动识别接口的请求参数和响应参数，生成标准的外部服务封装类。

## 输入参数
- dubboFacadeFullPath: Dubbo接口全限定名，例如：com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneFacade
- registryName: 注册中心名称，例如：acc-center-server

## 自动推断参数
- servicePackage: 从接口路径推断服务包名，例如 acccenter
- serviceName: 从接口名称推断服务名称，例如 AccCommon
- requestClassName: 自动推断请求类名，例如 QueryTxAcctByHboneRequest
- responseClassName: 自动推断响应类名，例如 QueryTxAcctByHboneResponse
- methodName: 从接口名称推断方法名，例如 queryTxAcctNo
- paramType: 分析请求对象自动推断参数类型
- paramName: 分析请求对象自动推断参数名称
- returnType: 分析响应对象自动推断返回类型
- requestSetterMethod: 根据参数名自动推断设置方法
- responseGetterMethod: 根据返回字段自动推断获取方法

## 输出文件
- 文件路径：dtms-order-service/src/main/java/com/howbuy/dtms/order/service/outerservice/{servicePackage}/{serviceName}OuterService.java

## 实现逻辑

```javascript
// 解析接口全路径
function parseDubboFacadePath(facadePath) {
  // 提取包名与类名
  const parts = facadePath.split('.');
  const facadeClassName = parts[parts.length - 1];
  
  // 找到acccenter/productcenter等系统名称
  let servicePackage = '';
  for (let i = 0; i < parts.length; i++) {
    if (parts[i] === 'howbuy' && i+1 < parts.length) {
      servicePackage = parts[i+1];
      break;
    }
  }
  
  // 推断请求和响应类名
  const baseName = facadeClassName.replace('Facade', '');
  const requestClassName = baseName + 'Request';
  const responseClassName = baseName + 'Response';
  
  // 推断请求和响应全路径
  const basePathWithoutClass = parts.slice(0, parts.length - 1).join('.');
  const requestFullPath = basePathWithoutClass + '.' + requestClassName;
  const responseFullPath = basePathWithoutClass + '.' + responseClassName;
  
  // 推导服务名称
  // 从Query/Create/Update + 业务实体 + ByXxx + Facade格式转换为更简洁的服务名
  let serviceName = '';
  // 提取业务实体部分
  if (baseName.startsWith('Query')) {
    const entityPart = baseName.substring(5); // 去掉Query前缀
    // 如果有By，截取By之前的部分
    const byIndex = entityPart.indexOf('By');
    const entity = byIndex > 0 ? entityPart.substring(0, byIndex) : entityPart;
    
    // 如果实体名包含多个单词，提取首字母并组合
    const words = entity.match(/[A-Z][a-z]+/g) || [];
    if (words.length > 1) {
      serviceName = words.join('') + 'Common';
    } else {
      serviceName = entity + 'Common';
    }
  } else if (baseName.startsWith('Create') || baseName.startsWith('Update')) {
    // 类似处理Create/Update接口
    const prefix = baseName.startsWith('Create') ? 'Create' : 'Update';
    const entityPart = baseName.substring(prefix.length);
    const byIndex = entityPart.indexOf('By');
    const entity = byIndex > 0 ? entityPart.substring(0, byIndex) : entityPart;
    
    const words = entity.match(/[A-Z][a-z]+/g) || [];
    if (words.length > 1) {
      serviceName = words.join('') + (baseName.startsWith('Create') ? 'Creator' : 'Updater');
    } else {
      serviceName = entity + (baseName.startsWith('Create') ? 'Creator' : 'Updater');
    }
  } else {
    // 默认处理
    serviceName = baseName;
  }
  
  // 推断方法名
  let methodName = baseName.charAt(0).toLowerCase() + baseName.substring(1);
  // 从服务名提取更简洁的方法名
  if (methodName.indexOf('By') > 0) {
    const mainAction = methodName.startsWith('query') ? 'query' : 
                       methodName.startsWith('create') ? 'create' : 
                       methodName.startsWith('update') ? 'update' : '';
    
    if (mainAction) {
      const entityPart = methodName.substring(mainAction.length);
      const byPart = methodName.substring(methodName.indexOf('By'));
      // 将TxAcct形式转换为txAcct
      const entity = entityPart.charAt(0).toLowerCase() + entityPart.substring(1);
      methodName = mainAction + entity;
    }
  }
  
  // 分析主要参数和返回值 (这部分需要静态分析或约定)
  // 根据接口命名推测
  let paramType = 'String';  // 默认参数类型
  let paramName = '';
  let returnType = 'String';  // 默认返回类型
  let isComplexType = false;
  
  // 从By后面推断参数名
  if (baseName.indexOf('By') > 0) {
    paramName = baseName.substring(baseName.indexOf('By') + 2).replace('Facade', '');
    // 首字母小写
    paramName = paramName.charAt(0).toLowerCase() + paramName.substring(1);
  } else {
    // 默认参数名
    paramName = servicePackage + 'Id';
  }
  
  // 推断请求方法
  const requestSetterMethod = 'set' + paramName.charAt(0).toUpperCase() + paramName.substring(1);
  
  // 推断响应方法和返回类型
  let responseGetterMethod = '';
  let responseDataType = '';
  
  // 根据接口类型推测返回类型
  if (baseName.startsWith('Query')) {
    // 查询接口，推测返回业务实体
    const entityPart = baseName.substring(5);
    const byIndex = entityPart.indexOf('By');
    const entity = byIndex > 0 ? entityPart.substring(0, byIndex) : entityPart;
    
    // 检查是否可能返回复杂对象
    if (entity.length > 0) {
      // 假设复杂对象返回
      isComplexType = true;
      returnType = entity + 'DTO';
      responseDataType = entity + 'VO';
      responseGetterMethod = 'get' + entity;
    } else {
      // 简单类型返回
      responseGetterMethod = 'get' + paramName.charAt(0).toUpperCase() + paramName.substring(1);
    }
  } else {
    // 其他接口类型默认处理
    responseGetterMethod = 'getResult';
  }
  
  return {
    servicePackage,
    serviceName,
    requestClassName,
    responseClassName,
    requestFullPath,
    responseFullPath,
    methodName,
    paramType,
    paramName,
    returnType,
    requestSetterMethod,
    responseGetterMethod,
    isComplexType,
    responseDataType,
    facadeSimpleName: facadeClassName,
    facadeFieldName: facadeClassName.charAt(0).toLowerCase() + facadeClassName.substring(1)
  };
}

// 生成当前日期时间，格式：yyyy-MM-dd HH:mm:ss
function getCurrentDateTime() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

// 主函数
function generateOuterService(dubboFacadeFullPath, registryName, authorName) {
  // 解析接口信息
  const info = parseDubboFacadePath(dubboFacadeFullPath);
  
  // 添加其他必要信息
  info.registryName = registryName;
  info.authorName = authorName;
  info.currentDate = getCurrentDateTime();
  info.methodDescription = `${info.isComplexType ? '查询' : '获取'}${info.paramName}对应的${info.returnType}`;
  info.paramDescription = info.paramName;
  info.returnDescription = info.isComplexType ? `${info.responseDataType}转换后的对象` : '返回值';
  
  // 使用模板生成代码
  return applyTemplate(info);
}

// 应用模板
function applyTemplate(info) {
  // 这里使用模板引擎生成最终代码
  // ...
}
```

## 模板
```java
/**
 * Copyright (c) 2024, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.{{servicePackage}};

import {{dubboFacadeFullPath}};
import {{requestFullPath}};
import {{responseFullPath}};
import com.howbuy.dtms.order.service.commom.constant.OutReturnCodes;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import java.util.Objects;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import com.howbuy.dtms.order.service.common.enums.ExceptionEnum;
import com.howbuy.dtms.order.service.common.exception.BusinessException;

/**
 * <AUTHOR>
 * @description: {{servicePackage}}系统外部服务封装
 * @date {{currentDate}}
 * @since JDK 1.8
 */
@Slf4j
@Service
public class {{serviceName}}OuterService {

    @DubboReference(registry = "{{registryName}}", check = false)
    private {{facadeSimpleName}} {{facadeFieldName}};

    /**
     * @description:({{methodDescription}})
     * @param {{paramName}} {{paramDescription}}
     * @return {{returnType}} {{returnDescription}}
     * <AUTHOR>
     * @date {{currentDate}}
     * @since JDK 1.8
     */
    public {{returnType}} {{methodName}}({{paramType}} {{paramName}}) {
        // 构建请求对象
        {{requestSimpleName}} request = new {{requestSimpleName}}();
        request.{{requestSetterMethod}}({{paramName}});
        
        log.info("{{serviceName}}OuterService>>>{{methodName}}请求参数：{}", JSON.toJSONString(request));
        
        try {
            // 调用外部接口
            {{responseSimpleName}} response = {{facadeFieldName}}.execute(request);
            log.info("{{serviceName}}OuterService>>>{{methodName}}响应结果：{}", JSON.toJSONString(response));
            
            // 校验响应结果
            if (response == null) {
                log.error("{{serviceName}}OuterService>>>{{methodName}}响应为空");
                return null;
            }
            
            // 成功返回
            if (Objects.equals(response.getReturnCode(), OutReturnCodes.TP_DUBBO_SUCCESS)) {
                {{#isSimpleType}}
                return response.{{responseGetterMethod}}();
                {{/isSimpleType}}
                {{#isComplexType}}
                {{responseDataType}} sourceData = response.{{responseGetterMethod}}();
                return convertTo{{returnType}}(sourceData);
                {{/isComplexType}}
            }
            
            // 失败记录日志
            log.error("{{serviceName}}OuterService>>>{{methodName}}失败，错误码：{}，错误信息：{}", 
                response.getReturnCode(), response.getReturnMsg());
            return null;
        } catch (Exception e) {
            log.error("{{serviceName}}OuterService>>>{{methodName}}异常", e);
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), 
                "{{methodName}}异常：" + e.getMessage());
        }
    }
    
    {{#isComplexType}}
    /**
     * 将外部系统响应数据转换为内部业务模型
     * @param sourceData 外部系统数据
     * @return 内部业务模型
     */
    private {{returnType}} convertTo{{returnType}}({{responseDataType}} sourceData) {
        if (sourceData == null) {
            return null;
        }
        
        {{returnType}} targetData = new {{returnType}}();
        // TODO: 填充属性映射，这里需要根据实际情况进行开发
        
        return targetData;
    }
    {{/isComplexType}}
}
```

## 自动推断算法

系统会根据提供的Dubbo接口全路径名执行以下推断：

1. **服务包名(servicePackage)**：
   - 从接口全路径提取公司名后的第一级包名
   - 例如：com.howbuy.acccenter.facade.xx -> acccenter

2. **服务名称(serviceName)**：
   - 从Facade接口名称提取业务实体名
   - 例如：QueryTxAcctByHboneFacade -> TxAcct -> AccTx -> AccCommon
   - 根据业务实体和操作类型自动生成有意义的服务名称

3. **请求和响应类型**：
   - 自动扫描同包下的Request和Response类
   - 通过命名规则匹配：将接口名称中的"Facade"替换为"Request"/"Response"
   - 例如：QueryTxAcctByHboneFacade -> QueryTxAcctByHboneRequest/QueryTxAcctByHboneResponse

4. **方法名(methodName)**：
   - 分析接口名称（去掉Facade后缀，首字母小写）
   - 例如：QueryTxAcctByHboneFacade -> queryTxAcctByHbone
   - 进一步优化为更简洁的形式: queryTxAcct

5. **参数和返回值**：
   - 分析"By"后面的部分推断主要参数名：ByHbone -> hbone
   - 分析接口前缀和实体名推断返回类型：Query + TxAcct -> TxAcctDTO
   - 对于简单类型和复杂对象类型自动区分处理方式

6. **请求/响应方法**：
   - 根据参数名/返回字段名自动推断相应的setter/getter方法名
   - 例如：hboneNo -> setHboneNo/getHboneNo

## 使用说明

此规则能够仅通过Dubbo接口的全路径名自动生成封装服务代码，无需手动输入大量参数。

### 使用步骤

1. 确定外部Dubbo接口的全路径名（类路径）
2. 确定注册中心名称
3. 指定作者姓名
4. 系统将自动识别接口出入参并生成代码

### 使用示例

```
输入参数:
- dubboFacadeFullPath: com.howbuy.acccenter.facade.query.querytxacctbyhbonefacade.QueryTxAcctByHboneFacade
- registryName: acc-center-server

系统自动识别:
- 请求参数类型及参数名：String hboneNo
- 响应返回类型：String (基本类型)
- 请求参数设置方法：setHboneNo
- 响应结果获取方法：getTxAcctNo

生成文件:
dtms-order-service/src/main/java/com/howbuy/dtms/order/service/outerservice/acccenter/AccCommonOuterService.java
```

### 复杂对象示例

对于返回复杂对象的接口，系统会自动生成对象转换方法：

```
输入参数:
- dubboFacadeFullPath: com.howbuy.productcenter.facade.query.queryproductinfofacade.QueryProductInfoFacade
- registryName: dtms-product-remote
- authorName: hongdong.xie

系统自动识别:
- 请求参数：String productCode
- 响应返回类型：ProductInfoVO (复杂对象)
- 需转换为内部DTO：ProductInfoDTO

生成的方法将包含将ProductInfoVO转换为ProductInfoDTO的逻辑框架。
```

### 命名约定和最佳实践

为了使自动推断更加准确，建议遵循以下命名约定：

- **接口命名**: Query/Create/Update + 业务实体 + ByXxx + Facade
- **请求类命名**: Query/Create/Update + 业务实体 + ByXxx + Request
- **响应类命名**: Query/Create/Update + 业务实体 + ByXxx + Response
- **领域对象**：业务实体 + VO/DTO
- **方法命名**：动词 + 名词，如queryXxx, updateXxx, createXxx

遵循这些命名约定将显著提高自动推断的准确性。 