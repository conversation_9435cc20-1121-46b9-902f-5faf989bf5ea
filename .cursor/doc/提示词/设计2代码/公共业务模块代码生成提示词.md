# ✅ AI公共业务模块代码生成提示词（Business层专用）
你是一名资深后端开发工程师，请根据我提供的**业务需求文档**，生成符合当前项目标准的**公共业务模块代码实现**，输出要求如下：
## 📌 Business层定位与职责：
- **包路径**：`com.howbuy.dtms.order.service.business`
- **核心职责**：封装可复用的公共业务逻辑，为上层服务提供原子化业务能力
- **调用关系**：
  - 👆 **被调用层**：Service层、Job层、MQ层
  - 👇 **可调用层**：Repository层、Outerservice层
- **设计原则**：高内聚、低耦合、可复用、单一职责
## 📋 输出要求：
### 🎯 代码结构规范
```
com.howbuy.dtms.order.service.business
├── [业务域]/                    # 按业务域划分目录
│   ├── [具体业务]Business.java   # 公共业务实现类
│   └── package-info.java        # 包说明文档
```
### 🔧 技术实现要求
- **框架注解**：使用`@Component`注册到Spring容器
- **日志支持**：使用`@Slf4j`注解添加日志支持
- **依赖注入**：通过`@Resource`注入Repository和Outerservice
- **事务控制**：Business层不直接控制事务，事务管理由Repository层负责
- **异常处理**：统一异常处理，合理使用自定义异常
### 📝 注释规范
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

/**
 * @description: [业务功能描述]
 * <AUTHOR>
 * @date 2025-06-30 16:04:22
 * @since JDK 1.8
 */
@Component
@Slf4j
public class [业务名]Business {
    
    /**
     * @description: [方法功能描述]
     * @param [参数名] [参数说明]
     * @return [返回值说明]
     * @author: hongdong.xie
     * @date: 2025-06-30 16:04:22
     * @since JDK 1.8
     */
    public [返回类型] [方法名]([参数列表]) {
        // 业务逻辑实现
    }
}
```
## 🏗️ 代码生成模板：
### 1. 基础Business类结构
```java
@Component
@Slf4j
public class [业务名]Business {
    
    @Resource
    private [相关]Repository [变量名]Repository;
    
    @Resource  
    private [外部服务]OuterService [变量名]OuterService;
    
    // 公共业务方法实现
}
```
### 2. 常见Business方法类型
#### 🔹 数据查询类业务
```java
/**
 * @description: 查询[业务对象]信息
 * @param [查询条件] 查询参数
 * @return [业务对象]信息
 */
public [ResultType] query[BusinessObject](QueryParam param) {
    log.info("开始查询[业务对象]，参数：{}", param);
    // 参数校验
    validateQueryParam(param);
    // 调用Repository查询数据
    [DataType] result = [repository].query[Method](param);
    // 业务逻辑处理
    [ResultType] businessResult = process[BusinessLogic](result);
    log.info("查询[业务对象]完成，结果数量：{}", businessResult.size());
    return businessResult;
}
```
#### 🔹 数据处理类业务
```java
/**
 * @description: 处理[业务逻辑]
 * @param [处理对象] 处理参数
 * @return 处理结果
 */
public [ResultType] process[BusinessLogic]([ParamType] param) {
    log.info("开始处理[业务逻辑]，参数：{}", param);
    try {
        // 1. 参数校验
        validate[ProcessParam](param);   
        // 2. 查询基础数据
        [BaseDataType] baseData = query[BaseData](param);
        // 3. 业务规则校验
        validate[BusinessRule](baseData, param);
        // 4. 执行业务逻辑（事务由Repository层控制）
        [ResultType] result = execute[BusinessLogic](baseData, param);
        // 5. 更新相关数据（事务由Repository层控制）
        update[RelatedData](result);
        log.info("处理[业务逻辑]成功，结果：{}", result);
        return result;
    } catch (BusinessException e) {
        log.error("业务处理失败：{}", e.getMessage(), e);
        throw e;
    } catch (Exception e) {
        log.error("系统异常：{}", e.getMessage(), e);
        throw new SystemException("处理[业务逻辑]失败", e);
    }
}
```
#### 🔹 外部调用类业务
```java
/**
 * @description: 调用外部系统[接口名称]
 * @param [调用参数] 调用参数
 * @return 调用结果
 */
public [ResultType] call[ExternalSystem]([ParamType] param) {
    log.info("开始调用外部系统[接口名称]，参数：{}", param);
    try {
        // 1. 参数转换
        [ExternalParamType] externalParam = convert[ToExternalParam](param);
        // 2. 调用外部服务
        [ExternalResultType] externalResult = [outerService].call[Method](externalParam);
        // 3. 结果校验
        validate[ExternalResult](externalResult);
        // 4. 结果转换
        [ResultType] result = convert[FromExternalResult](externalResult);
        log.info("调用外部系统[接口名称]成功");
        return result;
    } catch (ExternalServiceException e) {
        log.error("外部服务调用失败：{}", e.getMessage(), e);
        throw new BusinessException("调用外部系统失败", e);
    } catch (Exception e) {
        log.error("调用外部系统异常：{}", e.getMessage(), e);
        throw new SystemException("调用外部系统异常", e);
    }
}
```
## 🎯 业务设计原则：
### 📐 单一职责原则
- 每个Business类专注于一个业务域
- 每个方法专注于一个业务能力
- 避免大而全的Business类
### 🔄 可复用原则
- 提取共同的业务逻辑
- 避免重复的业务代码
- 支持不同场景的业务组合
### 🛡️ 异常处理原则
- 业务异常：明确的业务错误，抛出BusinessException
- 系统异常：技术性错误，抛出SystemException
- 外部异常：外部调用失败，包装后抛出
### 📊 事务控制原则
- **Business层职责**：不直接控制事务，专注于业务逻辑编排
- **Repository层职责**：负责事务管理和数据库操作
  - 查询操作：使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
  - 修改操作：使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
- **事务边界**：以Repository方法为事务边界，Business层通过调用Repository来间接控制事务
## 🧠 你的实现任务：
根据我提供的业务需求，生成包括以下内容的Business代码：
1. **Business主类**
   - 完整的类结构和注解
   - 必要的依赖注入
   - 核心业务方法实现
2. **辅助方法**
   - 参数校验方法
   - 数据转换方法
   - 业务规则校验方法
3. **异常处理**
   - 合理的try-catch结构
   - 统一的异常包装和抛出
   - 完善的日志记录
4. **注释文档**
   - 完整的类注释和方法注释
   - 关键业务逻辑说明
   - TODO标记未实现部分
## 🚫 禁止事项：
| 类型 | 说明 |
|------|------|
| ❌ 直接操作数据库 | 必须通过Repository层操作数据库 |
| ❌ 直接调用外部接口 | 必须通过Outerservice层调用外部接口 |
| ❌ 包含HTTP接口逻辑 | Business层不处理HTTP请求响应 |
| ❌ 包含MQ消息逻辑 | Business层不处理消息队列相关逻辑 |
| ❌ 直接控制事务 | Business层禁止使用@Transactional注解，事务由Repository层控制 |
| ❌ 硬编码业务规则 | 业务规则应该可配置或可扩展 |
| ❌ 禁止使用魔法值 | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举 |
| ❌ 缺少枚举或常量 | 对于数据库字段或接口参数中的状态、类型等字段，必须创建对应的枚举类或常量 |
## 📎 使用方式：
在我提供业务需求描述后，立即生成符合上述规范的完整Business层代码实现，按照业务逻辑流程结构清晰输出，确保代码可以直接在项目中使用。
**注意**：生成的代码必须严格遵循海外订单项目规范，确保与现有架构完美融合！ 