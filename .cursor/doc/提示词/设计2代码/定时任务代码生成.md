# ✅ AI定时任务代码生成提示词（基于详细设计文档）
你是一名资深后端开发工程师，请根据我提供的**定时任务详细设计文档**内容，生成符合当前项目标准的**定时任务代码实现**，输出要求如下：
## 📌 输出要求：
- **输出格式**：Java 源代码，分模块输出，可直接粘贴到项目中使用  
- **遵循规范**：必须严格遵循项目规范，规则文件路径为 `.cursor/rules/`
- **避免重复造轮子**：请优先使用项目中已有的工具类、封装模块、基础框架，不得重复实现已有功能
- **定时任务架构**：基于消息队列触发的定时任务框架，支持分布式锁和任务状态反馈
- **清晰注释**：对关键逻辑、判断分支、批处理逻辑、异常处理进行清晰注释
- **可维护性**：结构清晰、职责单一、易测试易扩展，体现生产级代码风格
## 📘 输入内容包含：
由我提供的**定时任务详细设计文档**，结构如下：
- **任务名称**：定时任务的功能名称
- **执行频率**：任务执行的时间规则（如每日、每小时等）
- **触发方式**：通过消息队列触发的Topic名称
- **任务参数**：任务执行所需的输入参数
- **业务处理流程**：包括数据查询、业务逻辑、批处理策略、异常策略
- **调用依赖模块**：例如Repository、Service、外部接口等
- **异常处理策略**：包括告警机制、日志记录、失败处理等
- **批处理策略**：是否需要分页处理大数据量
## 🧠 你的实现任务：
### 1. **定时任务主类**
生成继承自 `AbstractBatchMessageJob<T>` 的定时任务类，包含：
- 标准版权头注释（Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.）
- 使用 `@Component` 注解注册到Spring容器
- 通过 `@Value("${TOPIC.XXX}")` 获取Topic配置
- 实现 `getTopicName()` 方法返回Topic名称
- 实现 `doProcessJob(T message)` 方法处理具体业务逻辑
- 合理的异常处理和告警机制
### 2. **任务消息VO类**
生成继承自 `BaseTaskMessageVO` 的消息对象类，包含：
- 标准版权头注释
- 使用 `@Data` 和 `@EqualsAndHashCode(callSuper = true)` 注解
- 定义任务执行所需的参数字段
- 合理的字段注释说明
### 3. **业务处理逻辑**
根据设计文档生成具体的业务处理代码，包含：
- 注入所需的Repository、Service等依赖
- 实现具体的业务逻辑处理
- 如需批处理，使用 `BatchOperateExecutor` 进行分页处理
- 合理的日志记录和性能监控
### 4. **配置参数**
如需要，生成相关的配置参数定义：
- Topic名称配置
- 批处理大小配置
- 其他业务相关配置
## 📋 项目定时任务架构规范：
### 基础架构特点
```java
// 1. 定时任务类继承AbstractBatchMessageJob
public class ExampleJob extends AbstractBatchMessageJob<ExampleTaskVO> {  
    // 2. 通过@Value获取Topic配置
    @Value("${TOPIC.EXAMPLE_TASK}")
    private String topic;
    // 3. 重写getTopicName方法
    @Override
    protected String getTopicName() {
        return topic;
    }
    // 4. 实现doProcessJob方法
    @Override
    protected void doProcessJob(ExampleTaskVO message) {
        // 具体业务逻辑
    }
}
```
### 消息对象规范
```java
// 消息VO继承BaseTaskMessageVO
@Data
@EqualsAndHashCode(callSuper = true)
public class ExampleTaskVO extends BaseTaskMessageVO {
    /**
     * 业务参数字段
     */
    private String paramField;
}
```
### 标准版权头
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
```
### 方法注释规范
```java
/**
 * @description: 方法功能描述
 * @param paramName 参数说明
 * @return 返回值说明
 * @author: hongdong.xie
 * @date: 2025-06-30 11:30:02
 * @since JDK 1.8
 */
```
### 异常处理规范
```java
try {
    // 业务处理逻辑
    log.info("任务处理开始");
    // 具体逻辑...
    log.info("任务处理结束");
} catch (Exception e) {
    // 异常告警
    AlertLogUtil.alert(this.getClass().getName(), 
        String.format("任务执行异常！异常信息：%s", Throwables.getStackTraceAsString(e)));
    log.error("任务执行异常！异常信息：{}", Throwables.getStackTraceAsString(e));
    throw e; // 重新抛出异常，让框架处理
}
```
### 批处理处理规范
```java
// 使用BatchOperateExecutor进行批处理
int count = this.batchOperateExecutor.queryBeforeExecute(query, 
    new QueryExecuteService<QueryType, DataType>() {
        @Override
        public PageVo<DataType> queryPage(QueryType query) {
            // 分页查询逻辑
        }
        @Override
        public int execute(List<DataType> dataList) {
            // 批量处理逻辑
        }
    });
```
## 📂 文件存放位置规范：
### 定时任务类存放位置
- **普通定时任务**：`dtms-order-service/src/main/java/com/howbuy/dtms/order/service/job/`
- **批处理任务**：`dtms-order-service/src/main/java/com/howbuy/dtms/order/service/job/batch/`
### 消息VO存放位置
- **任务消息VO**：`dtms-order-service/src/main/java/com/howbuy/dtms/order/service/job/domain/`
### 命名规范
- **定时任务类**：以 `Job` 结尾，如 `DataSyncJob`、`OrderProcessJob`
- **消息VO类**：以 `TaskVO` 结尾，如 `DataSyncTaskVO`、`OrderProcessTaskVO`
## 🚫 禁止事项：
| 类型 | 说明 |
|------|------|
| ❌ 重复造轮子 | 必须使用项目现有的 `AbstractBatchMessageJob` 框架 |
| ❌ 硬编码配置 | Topic名称必须通过 `@Value` 从配置文件获取 |
| ❌ 违反目录结构 | 所有类必须放在指定的包路径下 |
| ❌ 缺少异常处理 | 必须使用 `AlertLogUtil` 进行异常告警 |
| ❌ 不规范注释 | 必须使用标准的版权头和方法注释 |
| ❌ 不使用批处理 | 大数据量处理必须使用 `BatchOperateExecutor` |
| ❌ 禁止使用魔法值 | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举 |
| ❌ 缺少枚举或常量 | 对于状态、类型等字段，必须创建对应的枚举类或常量 |
## 🎯 自动支持的功能：
通过继承 `AbstractBatchMessageJob`，你的定时任务将自动获得：
- ✅ **分布式锁控制**：防止任务并发执行
- ✅ **任务状态反馈**：自动向调度中心反馈执行结果
- ✅ **统一异常处理**：框架级别的异常捕获和处理
- ✅ **执行时间统计**：自动记录任务执行耗时
- ✅ **消息解析**：自动将消息解析为指定的VO对象
## 📎 提示词使用方式：
在我提供详细的定时任务设计文档后，请立即生成符合上述规范的完整Java实现代码，包括：
1. 定时任务主类（继承AbstractBatchMessageJob）
2. 任务消息VO类（继承BaseTaskMessageVO）
3. 相关的业务处理逻辑
4. 必要的配置参数定义
确保代码结构清晰、注释完整、符合项目规范，可以直接在项目中使用。

## 🎯 自动支持的功能： 