# ✅ AI-Dubbo接口代码生成提示词（基于详细设计）
你是一名资深Java开发和架构师,请根据我提供的**Dubbo接口详细设计文档**内容，严格遵循当前项目的代码规范和结构，生成完整的**后端代码实现**。
## 📌 核心输出要求
- **输出格式**：Java 源代码，按项目模块（`client`, `service`, `dao`）和文件类型（`Facade`, `Request`, `Response`, `Impl`, `Service`, `Repository`, `PO`, `Mapper`）分块输出，确保代码可直接应用到项目中。
- **遵循项目规范**：严格遵守 `.cursor/rules/` 下定义的项目结构、命名约定、注释标准和编码风格。
- **复用优先**：优先使用项目中已有的公共组件、工具类（如日期、字符串、集合处理）、和基础框架，禁止重复造轮子。
- **幂等性与健壮性**：关键的写操作接口（创建、更新）需要考虑幂等性设计，并对外部调用、数据库操作等进行必要的异常处理。
## 📘 你的任务：基于设计文档生成代码
请根据我提供的**详细设计文档**，一次性生成所有相关的代码。
### 设计文档结构（输入）
- **功能名称**: 接口的核心业务功能，例如 "创建海外基金订单"。
- **接口定义**:
    - **接口名**: `CreateFundOrderFacade`
    - **方法名**: `execute`
- **请求参数（Request）**: 字段名、Java类型、中文描述、是否必填、校验规则（如长度、格式、取值范围）。
- **响应参数（Response）**: 字段名、Java类型、中文描述。
- **关键业务处理流程**:
    1.  **参数校验**: 检查请求参数的完整性和有效性。
    2.  **业务校验**: 检查业务规则是否满足，如用户状态、账户余额等。
    3.  **核心逻辑**: 描述数据处理、状态流转、计算等核心步骤。
    4.  **数据持久化**: 说明需要操作的数据库表及字段。
    5.  **外部调用**: 列出需要调用的其他Dubbo服务或HTTP接口。
- **异常处理策略**: 定义不同场景下的错误码和异常信息。
- **事务管理**: 明确事务的边界和传播行为。
### 代码生成清单（输出）
请按以下结构生成代码，确保所有文件都符合项目规范。
#### 1. `dtms-order-client` 模块
- **接口定义 (`Facade`)**
    - **位置**: `dtms-order-client/src/main/java/com/howbuy/dtms/order/client/facade/{trade|query}/...`
    - **命名**: `功能名Facade.java`
    - **规范**:
        - 继承 `BaseFacade<Request, Response>`。
        - 必须包含完整的APIDOC风格注释 (`@api`, `@apiName`, `@apiGroup` 等)。
- **请求对象 (`Request`)**
    - **位置**: `dtms-order-client/src/main/java/com/howbuy/dtms/order/client/domain/request/...`
    - **命名**: `接口名Request.java`
    - **规范**:
        - 继承 `BaseRequest`。
        - 使用 `@Getter` / `@Setter` 注解。
        - 所有字段必须有清晰的中文Javadoc注释。
- **响应对象 (`Response`)**
    - **位置**: `dtms-order-client/src/main/java/com/howbuy/dtms/order/client/domain/response/...`
    - **命名**: `接口名Response.java`
    - **规范**:
        - 使用 `@Getter` / `@Setter` 注解。
        - 所有字段必须有清晰的中文Javadoc注释。
#### 2. `dtms-order-service` 模块
- **接口实现 (`FacadeImpl`)**
    - **位置**: `dtms-order-service/src/main/java/com/howbuy/dtms/order/service/provider/dubbo/{trade|query}/...`
    - **命名**: `功能名FacadeImpl.java`
    - **规范**:
        - 实现对应的 `Facade` 接口。
        - 使用 `@DubboService` 注解暴露服务。
        - 注入 `Service` 层并调用其方法，只做参数转换和基本校验，不实现核心业务逻辑。
        - 添加标准的类和方法注释。
- **业务逻辑 (`Service`)**
    - **位置**: `dtms-order-service/src/main/java/com/howbuy/dtms/order/service/service/{trade|query}/...`
    - **命名**: `功能名Service.java`
    - **规范**:
        - 使用 `@Service` 和 `@Slf4j` 注解。
        - 实现所有核心业务逻辑、业务校验和流程编排。
        - 调用 `Repository` 层进行数据持久化。
        - 方法需有完整的Javadoc注释（`@description`, `@param`, `@return`, `@author`, `@date`）。
- **数据仓库 (`Repository`)**
    - **位置**: `dtms-order-service/src/main/java/com/howbuy/dtms/order/service/repository/...`
    - **命名**: `表名Repository.java`
    - **规范**:
        - 使用 `@Repository` 注解。
        - 注入 `Mapper` 接口，封装数据库操作。
        - 写操作方法使用 `@Transactional(propagation = Propagation.REQUIRED)`。
        - 类级别使用 `@Transactional(propagation = Propagation.SUPPORTS)`。
        - 方法需有完整的Javadoc注释。

#### 3. `dtms-order-dao` 模块
- **数据实体 (`PO`)**
    - **位置**: `dtms-order-dao/src/main/java/com/howbuy/dtms/order/dao/po/...`
    - **命名**: `表名PO.java`
    - **规范**:
        - 与数据库表字段一一对应。
        - 使用 `@Getter` / `@Setter` 注解。
        - 字段有中文注释。
- **Mapper 接口**
    - **位置**: `dtms-order-dao/src/main/java/com/howbuy/dtms/order/dao/mapper/mysql/...`
    - **命名**: `表名Mapper.java`
- **Mapper XML**
    - **位置**: `dtms-order-dao/src/main/resources/com/howbuy/dtms/order/dao/mapper/mysql/...`
    - **命名**: `表名Mapper.xml`
## 🚫 禁止事项
| 类型                 | 说明                                                         |
| -------------------- | ------------------------------------------------------------ |
| ❌ **违反项目规范**  | 所有代码的包结构、命名、注释必须符合 `.cursor/rules/` 中的定义。 |
| ❌ **硬编码**        | 禁止在代码中硬编码任何常量、URL、配置项，应使用常量类或配置中心。 |
| ❌ **逻辑泄露**      | 业务逻辑必须封装在 `Service` 层，`FacadeImpl` 只做调度和校验。 |
| ❌ **忽略异常处理**   | 必须对外部调用和数据库操作进行 `try-catch`，并进行合理的异常转换和日志记录。 |
| ❌ **绕过Repository** | `Service` 层禁止直接注入和调用 `Mapper`，必须通过 `Repository` 层访问数据库。 |
| ❌ **禁止使用魔法值** | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举。 |
| ❌ **缺少枚举或常量** | 对于数据库字段或接口参数中的状态、类型等字段，必须创建对应的枚举类或常量。 |
**请在我提供详细设计文档后，立即按上述要求生成完整、高质量的代码。** 