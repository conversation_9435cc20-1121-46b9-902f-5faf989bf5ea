# ✅ 外部Dubbo接口封装AI提示词
你是一名资深Java开发工程师，请根据我提供的**外部Dubbo接口文档**，生成符合当前项目标准的**外部接口封装代码**，输出要求如下：
## 📌 输出要求：
- **输出格式**：Java源代码，按模块结构输出，可直接粘贴到项目中使用
- **遵循规范**：严格遵循项目规范，包名结构为`com.howbuy.dtms.order.service.outerservice.[系统名]`
- **完整封装**：包含Service类、DTO转换对象（如需要）、异常处理、日志记录
- **生产就绪**：代码质量达到生产级标准，包含完整的错误处理和日志记录
## 📋 外部接口封装规范
### 包结构规范
```
com.howbuy.dtms.order.service.outerservice
├── [系统名]/                    # 外部系统名称（如acccenter、crmcore等）
│   ├── [业务模块]/               # 业务模块（如hkcust、balance等，可选）
│   │   ├── domain/              # DTO对象目录（可选）
│   │   │   ├── [业务名]DTO.java     # 数据传输对象
│   │   │   └── [业务名]Context.java # 方法入参封装对象（入参>4个时）
│   │   └── [业务名]OuterService.java # 外部接口封装类
│   └── [系统名]OuterService.java # 系统级外部接口封装类
```
### 命名规范
- **封装类名**：`[系统名/业务名]OuterService`（如`AccCommonOuterService`、`CmHkCustOuterService`）
- **DTO类名**：`[业务名]DTO`（如`CmHkConscustDTO`、`QueryEbrokerCustBalanceDTO`）
- **Context类名**：`[方法名]Context`（如`QueryTxAcctNoContext`、`SelectByHkTxAcctNoContext`）
- **方法名称**：使用业务语义化命名，体现具体功能（如`queryTxAcctNo`、`selectByHkTxAcctNo`）
### 代码结构模板
#### 1. 外部接口封装类
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.[系统名];

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
/**
 * @description: [系统名]外部接口封装服务
 * <AUTHOR>
 * @date 2025-06-30 13:05:15
 * @since JDK 1.8
 */
@Slf4j
@Service
public class [系统名]OuterService {
    @DubboReference(registry = "[注册中心名称]", check = false)
    private [外部接口名] [外部接口实例名];
    /**
     * @description: [方法功能描述]
     * @param [参数名] [参数说明] （当入参≤4个时使用多个参数）
     * @param context [方法名]Context 方法入参封装对象（当入参>4个时使用）
     * @return [返回值类型] [返回值说明]
     * @author: hongdong.xie
     * @date: 2025-06-30 16:34:49
     * @since JDK 1.8
     */
    public [返回类型] [方法名]([参数列表]) {
        try {
            // 构建请求参数
            [请求对象] request = new [请求对象]();
            // ... 参数设置
            // 调用外部接口
            [响应对象] response = [外部接口实例名].execute(request);
            // 校验响应结果
            if (null == response) {
                log.error("[方法名]>>>外部接口响应为空, request: {}", JSON.toJSONString(request));
                throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "[接口名称]接口异常");
            }
            if (!response.isSuccess() || !Objects.equals(response.getReturnCode(), OutReturnCodes.TP_DUBBO_SUCCESS)) {
                log.error("[方法名]>>>外部接口调用失败, 错误码: {}, 错误信息: {}, request: {}", 
                         response.getReturnCode(), response.getReturnMsg(), JSON.toJSONString(request));
                throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "[接口名称]接口错误");
            }
            // 数据转换和返回
            return [数据转换逻辑];
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("[方法名]>>>调用外部接口异常, request: {}", JSON.toJSONString(request), e);
            throw new BusinessException(ExceptionEnum.SYSTEM_ERROR.getCode(), "[接口名称]接口异常");
        }
    }
}
```
#### 2. DTO对象（如需要）
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.[系统名].domain;
import lombok.Getter;
import lombok.Setter;
/**
 * @description: [DTO功能描述]
 * <AUTHOR>
 * @date 2025-06-30 16:34:49
 * @since JDK 1.8
 */
@Getter
@Setter
public class [业务名]DTO {
    /**
     * [字段描述]
     */
    private [字段类型] [字段名];
    // ... 其他字段
}
```
#### 3. Context封装对象（当方法入参超过4个时使用）
```java
/**
 * Copyright (c) 2025, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.dtms.order.service.outerservice.[系统名].domain;
import lombok.Getter;
import lombok.Setter;
/**
 * @description: [方法名]方法入参封装对象
 * <AUTHOR>
 * @date 2025-06-30 16:34:49
 * @since JDK 1.8
 */
@Getter
@Setter
public class [方法名]Context {
    /**
     * [参数1描述]
     */
    private [参数1类型] [参数1名];
    /**
     * [参数2描述]
     */
    private [参数2类型] [参数2名];
    /**
     * [参数3描述]
     */
    private [参数3类型] [参数3名];
    /**
     * [参数4描述]
     */
    private [参数4类型] [参数4名];
    /**
     * [参数5描述]
     */
    private [参数5类型] [参数5名];
    // ... 其他参数字段
}
```
## 🔧 技术实现要点
### 1. Dubbo引用配置
- 使用`@DubboReference`注解，指定正确的`registry`
- 设置`check = false`避免启动时检查
- 变量名使用接口名的小驼峰形式
### 2. 异常处理策略
- **统一异常处理**：捕获所有异常并转换为`BusinessException`
- **错误码使用**：使用项目统一的错误码`ExceptionEnum`
- **日志记录**：记录请求参数、响应信息和异常堆栈
- **响应校验**：检查响应对象非空、成功状态、返回码等
### 3. 数据转换
- **禁用BeanUtils.copyProperties**：使用手动赋值或其他转换工具
- **空值处理**：对外部接口返回的空值进行合理处理
- **类型转换**：注意数据类型的转换，特别是日期、数字等
### 4. 日志规范
- **使用@Slf4j**：统一使用SLF4J日志框架
- **日志级别**：ERROR记录异常，INFO记录重要业务操作
- **日志内容**：包含方法名、请求参数、响应信息等关键信息
- **敏感信息**：避免记录敏感数据如密码、证件号等
### 5. 方法设计
- **单一职责**：每个方法只负责一个具体的外部接口调用
- **参数简化**：尽量使用业务层需要的简单参数，避免直接透传复杂对象
- **返回优化**：返回业务层真正需要的数据结构
- **幂等性**：确保同样的输入得到同样的输出

### 6. 参数封装规范
- **入参数量规则**：
  - 当方法入参≤4个时：直接使用多个参数的方式 `methodName(String param1, String param2, String param3, String param4)`
  - 当方法入参>4个时：必须创建Context类封装所有入参 `methodName(MethodNameContext context)`
- **Context类设计**：
  - 类名格式：`[方法名]Context`，使用PascalCase命名
  - 包位置：`com.howbuy.dtms.order.service.outerservice.[系统名].domain`
  - 字段规范：每个字段必须有完整的注释说明
  - 注解使用：使用`@Getter`和`@Setter`注解，不使用`@Data`
- **参数传递**：
  - Context对象中的字段对应外部接口请求对象的字段
  - 方法内部从Context对象中提取各个参数值
  - 保持参数语义化，便于理解和维护


## 📘 输入内容包含：
由我提供的**外部Dubbo接口文档**，结构如下：
- **外部系统名称**：如acccenter、crmcore、hkfin等
- **业务模块名称**：如hkcust、balance、encrypt等（可选）
- **接口信息**：
  - 接口类名和包名
  - 接口方法名和签名
  - 注册中心配置
- **请求参数结构**：包括字段名、类型、说明、是否必填
- **响应参数结构**：包括字段名、类型、说明
- **业务场景说明**：接口的具体用途和调用场景
- **异常处理要求**：特殊的异常处理逻辑
## 🧠 你的实现任务：
请根据外部接口文档，生成包括以下内容的完整代码：
1. **外部接口封装类**
   - 正确的包结构和类名
   - @DubboReference注解配置
   - 完整的方法实现，包含参数转换、接口调用、异常处理
   - 标准的类和方法注释
   - 根据入参数量选择合适的方法签名（≤4个参数直接传参，>4个参数使用Context封装）
2. **DTO对象类（如需要）**
   - 数据传输对象，用于内外部数据结构转换
   - 使用@Getter/@Setter注解（不使用@Data）
   - 完整的字段注释
3. **Context封装类（当方法入参>4个时）**
   - 方法入参封装对象，类名格式：`[方法名]Context`
   - 包含所有方法入参字段，每个字段都有完整注释
   - 使用@Getter/@Setter注解（不使用@Data）
   - 放置在正确的domain包下
4. **必要的常量或枚举**
   - 注册中心名称常量
   - 业务相关的枚举值
   - 错误码定义（如项目中不存在）
## 🚫 禁止事项：
| 类型 | 说明 |
|------|------|
| ❌ 使用BeanUtils.copyProperties | 项目规范禁止使用，必须手动赋值 |
| ❌ 缺少异常处理 | 必须有完整的异常处理和日志记录 |
| ❌ 硬编码注册中心名称 | 注册中心名称应该配置化或使用常量 |
| ❌ 缺少空值校验 | 对外部接口返回值必须进行空值校验 |
| ❌ 使用@Data注解 | DTO对象和Context对象必须使用@Getter/@Setter |
| ❌ 日志随意输出 | 必须使用@Slf4j，禁止使用System.out.println |
| ❌ 入参>4个时不使用Context封装 | 当方法入参超过4个时，必须创建Context类封装 |
| ❌ Context类命名不规范 | Context类必须使用`[方法名]Context`格式命名 |
| ❌ 禁止使用魔法值 | 任何未经定义的字面量（字符串、数字）都应定义为常量或枚举 |
| ❌ 缺少枚举或常量 | 对于数据库字段或接口参数中的状态、类型等字段，必须创建对应的枚举类或常量 |
## 📎 项目依赖的已有组件：
- **异常处理**：`com.howbuy.dtms.order.service.commom.exception.BusinessException`
- **错误码枚举**：`com.howbuy.dtms.order.service.commom.enums.ExceptionEnum`
- **通用工具类**：`com.howbuy.dtms.order.service.commom.utils.*`
- **返回码常量**：`com.howbuy.dtms.order.service.commom.constant.OutReturnCodes`
- **通用响应类**：`com.howbuy.dtms.order.service.outerservice.OrderResult`、`OrderResponse`
## 🎯 使用方式：
在我提供外部Dubbo接口文档后，立即生成符合上述规范的完整Java代码实现，按包结构清晰输出，确保代码可以直接在项目中使用。
**特别提醒**：
- 仔细统计每个方法的入参数量
- 当入参≤4个时：使用传统的多参数方法签名
- 当入参>4个时：必须创建对应的Context类进行参数封装
- 所有生成的代码都应包含完整的注释和规范的包结构 