# ✅ AI乐观锁更新代码生成专用提示词
你是一名资深后端开发工程师，当详细设计文档中要求使用**乐观锁更新**时，请根据以下规范生成完整的乐观锁更新实现代码。

## 📌 乐观锁更新使用场景
当出现以下情况时，必须使用乐观锁更新：
- 并发场景下的状态更新
- 定时任务中的批量数据处理
- 分布式环境下的数据一致性保障
- 防止数据被意外覆盖的场景

## 🔧 乐观锁更新完整实现规范

### 1. Service层乐观锁更新逻辑
```java
/**
 * @description: 处理需要乐观锁更新的业务
 * @param businessKey 业务主键
 * @param newStatus 新状态
 * @return 处理结果
 * @author: hongdong.xie
 * @date: 2025-07-03 08:33:56
 * @since JDK 1.8
 */
public void updateWithOptimisticLock(Long businessKey, String newStatus) {
    // 1. 查询当前数据，获取乐观锁字段（update_timestamp）
    [BusinessObjectPO] currentData = [repository].selectByBusinessKey(businessKey);
    if (currentData == null) {
        throw new BusinessException("数据不存在");
    }
    
    // 2. 业务逻辑校验
    validate[BusinessRule](currentData, newStatus);
    
    // 3. 使用乐观锁更新
    Date oldUpdateTimestamp = currentData.getUpdateTimestamp();
    Date newUpdateTimestamp = new Date();
    
    int updateCount = [repository].update[BusinessObject]StatusWithOptimisticLock(
        businessKey, newStatus, oldUpdateTimestamp, newUpdateTimestamp);
    
    // 4. 检查乐观锁更新结果
    if (updateCount == 0) {
        log.warn("乐观锁更新失败，数据可能已被其他进程修改: {}", businessKey);
        throw new BusinessException("数据并发修改冲突，请重试");
    }
    
    log.info("乐观锁更新成功: {}", businessKey);
}

/**
 * @description: 带重试机制的乐观锁更新
 * @param businessKey 业务主键  
 * @param newStatus 新状态
 * @param maxRetries 最大重试次数
 * @return 是否更新成功
 * @author: hongdong.xie
 * @date: 2025-07-03 08:33:56
 * @since JDK 1.8
 */
public boolean updateWithOptimisticLockAndRetry(Long businessKey, String newStatus, int maxRetries) {
    for (int i = 0; i <= maxRetries; i++) {
        try {
            updateWithOptimisticLock(businessKey, newStatus);
            return true;
        } catch (BusinessException e) {
            if (e.getMessage().contains("并发修改冲突") && i < maxRetries) {
                log.info("乐观锁冲突，准备重试: {}, 重试次数: {}", businessKey, i + 1);
                try {
                    Thread.sleep(100 + (i * 50)); // 递增延迟
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new BusinessException("重试过程被中断");
                }
            } else {
                throw e;
            }
        }
    }
    return false;
}

/**
 * @description: 批量乐观锁更新处理
 * @param updateList 批量更新列表
 * @return 批量更新结果
 * @author: hongdong.xie  
 * @date: 2025-07-03 08:33:56
 * @since JDK 1.8
 */
public [BatchUpdateResult] batchUpdate[BusinessObject]WithOptimisticLock(List<[UpdateParam]> updateList) {
    log.info("开始批量乐观锁更新，数量: {}", updateList.size());
    
    [BatchUpdateResult] result = new [BatchUpdateResult]();
    List<[UpdateParam]> failedList = new ArrayList<>();
    
    for ([UpdateParam] updateParam : updateList) {
        try {
            boolean success = update[BusinessObject]StatusWithOptimisticLock(
                    updateParam.getBusinessKey(), updateParam.getNewStatus());
            if (success) {
                result.addSuccess(updateParam);
            } else {
                result.addFailed(updateParam, "乐观锁更新冲突");
                failedList.add(updateParam);
            }
        } catch (Exception e) {
            log.error("批量更新异常，业务主键: {}", updateParam.getBusinessKey(), e);
            result.addFailed(updateParam, e.getMessage());
            failedList.add(updateParam);
        }
    }
    
    log.info("批量乐观锁更新完成，成功: {}, 失败: {}", result.getSuccessCount(), result.getFailedCount());
    return result;
}
```

### 2. Repository层乐观锁更新实现
```java
@Repository
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class [BusinessObject]Repository {
    
    @Resource
    private [BusinessObject]Mapper [businessObject]Mapper;
    
    /**
     * @description: 使用乐观锁更新状态
     * @param businessKey 业务主键
     * @param newStatus 新状态
     * @param oldUpdateTimestamp 原更新时间戳
     * @param newUpdateTimestamp 新更新时间戳
     * @return 更新行数
     * @author: hongdong.xie
     * @date: 2025-07-03 08:33:56
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int update[BusinessObject]StatusWithOptimisticLock(Long businessKey, String newStatus, 
                                                             Date oldUpdateTimestamp, Date newUpdateTimestamp) {
        log.debug("使用乐观锁更新状态, businessKey: {}, newStatus: {}", businessKey, newStatus);
        
        int updateCount = [businessObject]Mapper.update[BusinessObject]StatusWithOptimisticLock(
                businessKey, newStatus, oldUpdateTimestamp, newUpdateTimestamp);
        
        if (updateCount == 0) {
            log.warn("乐观锁更新失败，可能数据已被其他进程修改, businessKey: {}", businessKey);
        } else {
            log.debug("乐观锁更新成功, businessKey: {}, updateCount: {}", businessKey, updateCount);
        }
        
        return updateCount;
    }
}
```

### 3. Mapper接口乐观锁更新方法定义
```java
public interface [BusinessObject]Mapper {
    
    /**
     * @description: 使用乐观锁更新状态
     * @param businessKey 业务主键
     * @param newStatus 新状态
     * @param oldUpdateTimestamp 原更新时间戳
     * @param newUpdateTimestamp 新更新时间戳
     * @return 更新行数
     * @author: hongdong.xie
     * @date: 2025-07-03 08:33:56
     * @since JDK 1.8
     */
    int update[BusinessObject]StatusWithOptimisticLock(@Param("businessKey") Long businessKey,
                                                      @Param("newStatus") String newStatus,
                                                      @Param("oldUpdateTimestamp") Date oldUpdateTimestamp,
                                                      @Param("newUpdateTimestamp") Date newUpdateTimestamp);
}
```

### 4. Mapper XML乐观锁更新SQL实现
```xml
<!-- 使用乐观锁更新状态 -->
<update id="update[BusinessObject]StatusWithOptimisticLock">
    update [table_name]
    set [status_field] = #{newStatus,jdbcType=VARCHAR},
        update_timestamp = #{newUpdateTimestamp,jdbcType=TIMESTAMP}
    where [business_key_field] = #{businessKey,jdbcType=BIGINT}
    and update_timestamp = #{oldUpdateTimestamp,jdbcType=TIMESTAMP}
    and rec_stat = '1'
</update>
```

## 📋 乐观锁更新关键要点

### 🔧 技术实现要点
1. **WHERE条件必须包含**：`update_timestamp = #{oldUpdateTimestamp}` 作为乐观锁条件
2. **SET语句必须包含**：`update_timestamp = #{newUpdateTimestamp}` 更新时间戳
3. **返回值检查**：检查 `updateCount` 是否为0来判断更新成功性
4. **事务控制**：Repository层方法使用 `@Transactional(propagation = Propagation.REQUIRED)`
5. **方法命名**：乐观锁更新方法必须包含 `WithOptimisticLock` 后缀
6. **参数规范**：必须包含 `oldUpdateTimestamp` 和 `newUpdateTimestamp` 参数

### 🛡️ 设计原则
- **时间戳比较**：使用 `update_timestamp` 字段作为乐观锁版本控制
- **原子性更新**：WHERE条件包含时间戳比较，SET语句更新时间戳
- **冲突检测**：通过 `updateCount` 检查是否发生并发修改冲突
- **失败处理**：根据业务需求选择重试、异常抛出或返回失败结果

### 🔄 重试机制原则  
- **智能重试**：区分业务异常（不重试）和乐观锁冲突（可重试）
- **递增延迟**：使用递增延迟避免频繁冲突，提高重试成功率
- **最大次数**：设置合理的最大重试次数，避免无限重试
- **中断处理**：正确处理线程中断异常

### 🔄 批量处理原则
- **独立处理**：每个更新操作独立处理，单个失败不影响其他
- **结果收集**：收集成功和失败的结果，便于后续处理
- **异常隔离**：单个更新异常不影响批量处理的整体流程

## 🚫 乐观锁更新禁止事项
| 类型 | 说明 |
|------|------|
| ❌ 缺少时间戳检查 | WHERE条件必须包含update_timestamp的比较 |
| ❌ 不更新时间戳 | SET语句必须更新update_timestamp为新值 |
| ❌ 不检查返回值 | 必须检查updateCount判断更新是否成功 |
| ❌ 缺少事务注解 | Repository乐观锁更新方法必须使用REQUIRED事务 |
| ❌ 不处理冲突 | 必须处理updateCount为0的情况 |
| ❌ 方法命名不规范 | 乐观锁更新方法必须包含WithOptimisticLock后缀 |
| ❌ 事务配置错误 | Repository层乐观锁更新方法必须使用REQUIRED事务传播 |

## 📊 实际项目案例参考
基于项目中 `HwPaymentOrderRepository.updatePaymentStatusToPayingWithOptimisticLock` 方法的实现模式：
- SQL层面的乐观锁控制
- 完整的日志记录
- 标准的返回值检查
- 正确的事务管理

## 📎 使用方式
当详细设计文档中明确要求使用乐观锁更新时，请结合此提示词生成完整的乐观锁更新实现代码，确保：
1. **完整的实现链条**：Service → Repository → Mapper → XML
2. **标准化的方法命名**：包含 `WithOptimisticLock` 后缀
3. **规范的参数设计**：包含时间戳参数
4. **完整的异常处理**：检查返回值并记录日志
5. **正确的事务控制**：使用正确的事务传播级别
6. **可选的重试机制**：根据业务需求实现重试逻辑 