你是一名资深测试开发/架构师，现在需要你根据以下接口或功能模块的详细设计内容，生成一个接口/功能模块的逻辑校验项的结构化脑图。

要求：
1. 每一个字段或功能点作为一级校验点；
2. 每个校验点应包括：
   - 正常通过场景（✅ 正例）
   - 不通过的异常场景（❌ 反例）
   - 报错信息（如有报错码/异常描述请列出）
3. 输出结构以“树状结构”形式给出，支持用 Mermaid 层级列表表示，如下示例
```mermaid
graph LR
    A[异步支付服务 AsyncDoPaymentService] --> B[支付订单存在性校验]
    A --> C[交易支付标记校验]
    A --> D[交易订单校验]
    A --> E[支付发起前状态更新]
    A --> F[支付发起操作]
    A --> G[支付发起后状态更新]

    B --> B1[✅ 正例-通过: 支付订单存在]
    B --> B2[❌ 反例-不通过: 支付订单不存在<br/>异常日志+监控预警]

    C --> C1[✅ 正例-通过: txPmtFlag=1未付款]
    C --> C2[❌ 反例-不通过: txPmtFlag≠1<br/>异常日志+监控预警]

    D --> D1[交易订单存在性校验]
    D --> D2[交易订单状态校验]

    D1 --> D11[✅ 正例-通过: 交易订单存在]
    D1 --> D12[❌ 反例-不通过: 交易订单不存在<br/>异常日志+监控预警]

    D2 --> D21[✅ 正例-通过: payStatus=1未付款 且 orderStatus=1申请成功]
    D2 --> D22[❌ 反例-不通过: 状态不符合要求<br/>异常日志+监控预警]

    E --> E1[支付订单状态更新]
    E --> E2[交易订单状态更新]

    E1 --> E11[✅ 正例-通过: 乐观锁更新成功<br/>txPmtFlag=4付款中, pmtCompFlag=1未对账]
    E1 --> E12[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1, 异常日志+监控预警+抛异常]

    E2 --> E21[✅ 正例-通过: 乐观锁更新成功<br/>payStatus=2付款中]
    E2 --> E22[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1, 异常日志+监控预警+抛异常]

    F --> F1[✅ 正例-通过: 支付外部服务调用成功]
    F --> F2[❌ 反例-不通过: 支付外部服务调用失败<br/>返回错误码和错误描述]

    G --> G1[✅ 正例-通过: 乐观锁更新成功<br/>更新retCode, retDesc, outPmtDealNo]
    G --> G2[❌ 反例-不通过: 乐观锁冲突<br/>UPDATE影响行数≠1, 异常日志+监控预警]
```
4. 样式参考如下案例结构：

接口名称：认申购提交接口  
- 账户状态
  - 正例-通过：客户状态等于0-正常
  - 反例-不通过：
    - 客户状态不等于0-正常，报错：C021002-客户状态异常
    - 客户信息不存在，报错：C021001-客户不存在
- 绑卡
  - 正例-通过：客户银行卡信息正常
  - 反例-不通过：
    - 客户银行卡信息为空，报错：C021003-未绑定银行卡
- 风险等级匹配
  - 正例-通过：客户风险等级>=产品风险等级
  - 反例-不通过：客户风险等级不满足要求，报错：C021006-风险等级不匹配

请根据以下接口/功能定义内容，生成上述结构的校验项脑图：
【接口定义/字段说明/功能逻辑/详细设计文档】

5. 输出要求：
	- 输出格式为 Markdown（.md）
	- 文件名为当前接口类名，例如 UserRegisterController+脑图.md
  - 输出内容严格按照上面的要求，只需要包含：树状结构、详细校验项说明部分
	- 保存路径为项目根目录下的 .知识库/测试文档/脑图/