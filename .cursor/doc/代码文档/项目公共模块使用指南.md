# DTMS-ORDER 项目公共模块使用指南

> **作者**: hongdong.xie  
> **创建时间**: 2025-06-28 21:52:35  
> **项目**: 海外订单系统(dtms-order)  
> **目的**: 避免重复造轮子，提高开发效率

## 📋 目录

- [1. 常量类](#1-常量类)
- [2. 异常处理](#2-异常处理)
- [3. 工具类](#3-工具类)
- [4. 枚举类](#4-枚举类)
- [5. 外部系统接口封装](#5-外部系统接口封装)
- [6. 统一校验框架](#6-统一校验框架)
- [7. MQ消息处理](#7-mq消息处理)
- [8. 缓存服务](#8-缓存服务)
- [9. 注解类](#9-注解类)

---

## 1. 常量类

### 1.1 Constants - 系统常量类
**包路径**: `com.howbuy.dtms.order.service.commom.constant.Constants`

**主要常量**:
```java
// 日志相关
public static final String TRACE_ID = "traceId";
public static final String XTRACES = "xtraces";

// 预约类型
public static final String NOT_PREBOOK = "0";      // 非预约
public static final String BUY_PREBOOK = "1";      // 仅购买预约
public static final String REDEEM_PREBOOK = "2";   // 仅赎回预约
public static final String ALL_PREBOOK = "3";      // 购买赎回预约

// 账户状态
public static final String ACCOUNT_STATUS_NORMAL = "0";        // 正常
public static final String ACCOUNT_STATUS_OPEN_SUCCESS = "4";  // 开户申请成功

// 基金分类
public static final String FUND_CATEGORY_PUBLIC = "1";   // 公募
public static final String FUND_CATEGORY_PRIVATE = "2";  // 私募
public static final String FUND_CATEGORY_OTHER = "9";    // 其他

// 金额相关
public static final int AMOUNT_SCALE = 2;  // 金额小数位
public static final BigDecimal AMOUNT_TEN_THOUSAND_UNITS = new BigDecimal("10000");

// 系统操作人
public static final String DEFAULT_OPERATOR = "hwztsys";

// 文件相关
public static final String FILE_FILED_SEPARATOR = "|";  // 字段分隔符
public static final String FILE_RN = "\r\n";            // 换行符
public static final String FILE_CHARSET_UTF8 = "UTF-8"; // 编码

// CRM接口状态码
public static final String CRM_SUCCESS_CODE = "0000";
public static final String MESSAGE_SUCCESS_CODE = "0";
```

### 1.2 FileConstants - 文件存储常量
**包路径**: `com.howbuy.dtms.order.service.commom.constant.FileConstants`

```java
// DFile存储配置
public static final String CONTRACT_FILE_STORE_CONFIG = "contract_file_storeConfig";  // 合同文件
public static final String ACK_FILE_STORE_CONFIG = "ack_file_storeConfig";            // 确认文件
public static final String TRADE_FILE_STORE_CONFIG = "trade_file_storeConfig";        // 交易记录文件
public static final String CAPITAL_FILE_STORE_CONFIG = "capital_file_storeConfig";    // 资金文件
```

### 1.3 其他常量类
- `CrmTradeServerPathConstant` - CRM交易服务路径常量
- `FeginServerKeyConstant` - Feign服务Key常量
- `MsgBusinessIdConstants` - 消息业务ID常量
- `OutReturnCodes` - 外部系统返回码常量

---

## 2. 异常处理

### 2.1 BusinessException - 业务异常类
**包路径**: `com.howbuy.dtms.order.service.commom.exception.BusinessException`

**构造方法**:
```java
// 基础构造
public BusinessException(String message)
public BusinessException(String exceptionCode, String exceptionDesc)

// 枚举构造（推荐）
public BusinessException(ExceptionEnum exceptionEnum)
public BusinessException(ExceptionCodeEnum exceptionCodeEnum)
```

**使用示例**:
```java
// 使用枚举抛出异常（推荐）
throw new BusinessException(ExceptionEnum.ACCOUNT_NOT_EXISTS);

// 自定义异常信息
throw new BusinessException("C020001", "系统异常");
```

### 2.2 ExceptionEnum - 异常枚举
**包路径**: `com.howbuy.dtms.order.service.commom.enums.ExceptionEnum`

**常用异常码**:
```java
ORDER_SUCCESS("0000", "成功"),
SYSTEM_ERROR("C020001", "系统异常"),
PARAMS_ERROR("C020002", "参数错误"),
CONCURRENT_ERROR("C020003", "并发异常"),
DB_ERROR("C020996", "数据库异常"),

// 业务异常
ACCOUNT_NOT_EXISTS("C021001", "客户不存在"),
ACCOUNT_STATUS_EXCEPTION("C021002", "客户状态异常"),
RISK_LEVEL_NOT_MATCH("C021004", "客户风测等级与产品风险等级不匹配"),
PRODUCT_NOT_OPEN_BUSINESS("C021012", "产品未开通该业务"),
FUND_NAV_IS_NULL("C023074", "基金净值为空")
```

### 2.3 ExceptionUtils - 异常工具类
**包路径**: `com.howbuy.dtms.order.service.commom.exception.ExceptionUtils`

```java
// 抛出业务异常
public static void throwBusinessException(ExceptionEnum exceptionEnum)

// 使用示例
ExceptionUtils.throwBusinessException(ExceptionEnum.PARAMS_ERROR);
```

---

## 3. 工具类

### 3.1 DateUtils - 日期工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.DateUtils`

**常用方法**:
```java
// 日期格式化
public static String formatToString(LocalDateTime date, String pattern)

// 字符串转日期
public static Date strToDate(String strDate)  // YYYYMMDD格式

// 获取当前时间
public static String getCurrentDate()         // YYYYMMDD格式
public static String getCurrentDate(String format)
public static String getCurrentDateTime()     // HHMMSS格式

// 日期计算
public static String getStrNextMonthByDate(Date d, int month)  // 增加月份
public static String standardDeviation(String startDate, String endDate)  // 计算天数差

// 年龄计算
public static int getAgeByBirth(String birthDay)
```

### 3.2 MathUtils - 数学计算工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.MathUtils`

**主要方法**:
```java
// 安全获取BigDecimal
public static BigDecimal getBigDecimal(BigDecimal n)    // null转ZERO
public static BigDecimal getBigDecimal(String s)       // 空字符串转ZERO

// 基础运算
public static BigDecimal add(BigDecimal b1, BigDecimal b2)
public static BigDecimal subtract(BigDecimal b1, BigDecimal b2)
public static BigDecimal multiply(BigDecimal b1, BigDecimal b2)

// 除法运算（不同舍入模式）
public static BigDecimal divide(BigDecimal b1, BigDecimal b2, int roundDownNum)        // 向下舍入
public static BigDecimal divideRoundUp(BigDecimal b1, BigDecimal b2, int roundDownNum) // 向上舍入
public static BigDecimal divideRoundHalfUp(BigDecimal b1, BigDecimal b2, int roundDownNum) // 四舍五入

// 精度处理
public static BigDecimal scaleWithRoundDown(BigDecimal n)  // 默认2位小数
public static BigDecimal scaleWithRoundDown(BigDecimal n, int scale)
```

### 3.3 AssertUtils - 断言工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.AssertUtils`

**主要方法**:
```java
// 基金配置校验
public static void notEmptyFundConfig(String value, String fundCode, String message)
public static void nonNullFundConfig(Object object, String fundCode, String message)

// 通用校验
public static void nonNull(Object object, String message, ExceptionEnum exceptionEnum)
public static void notEmpty(String value, String message, ExceptionEnum exceptionEnum)
public static void listNonNullParam(List list, String message)

// 快速抛出异常
public static void throwParamErrorException(String desc)
public static void throwFundConfigErrorException(String desc)
```

### 3.4 DFileUtils - 文件操作工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.dfile.DFileUtils`

**主要方法**:
```java
// 路径拼接
public static String joinPath(String... paths)

// 配置获取
public static String getWebdavPath(String storeConfigKey)
public static Path getLocalPath(String storeConfigKey)

// 文件操作
public static void mkDir(DFileInfoDTO fileInfoDTO)
public static List<DFileInfoDTO> list(DFileInfoDTO dir)
public static void batchDelete(DFileInfoDTO... dtoList)
```

### 3.5 MoneyUtils - 金额处理工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.MoneyUtils`

**主要方法**:
```java
// 金额格式化
public static String formatMoney(BigDecimal amount)
public static String formatMoney(BigDecimal amount, String currency)

// 金额计算
public static BigDecimal calculateFee(BigDecimal amount, BigDecimal rate)
public static BigDecimal convertCurrency(BigDecimal amount, String fromCurrency, String toCurrency)
```

### 3.6 BeanUtil - Bean操作工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.BeanUtil`

**主要方法**:
```java
// Bean属性复制（禁止使用BeanUtils.copyProperties）
public static void copyProperties(Object source, Object target)
public static <T> T copyProperties(Object source, Class<T> targetClass)

// Bean转换
public static <T> List<T> convertList(List<?> sourceList, Class<T> targetClass)
```

### 3.7 TradeUtils - 交易工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.TradeUtils`

**主要方法**:
```java
// 交易编号生成
public static String generateTradeNo()
public static String generateOrderNo(String prefix)

// 交易状态判断
public static boolean isSuccessStatus(String status)
public static boolean isFailureStatus(String status)
```

### 3.8 FileGenerateUtil - 文件生成工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.FileGenerateUtil`

**主要方法**:
```java
// Object转String处理
public static String null2String(Object obj)  // null转空字符串，BigDecimal格式化

// 文件内容生成
public static String generateFileContent(List<Object> dataList)
```

### 3.9 SpringContextUtil - Spring上下文工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.SpringContextUtil`

**主要方法**:
```java
// 获取Bean
public static <T> T getBean(Class<T> clazz)
public static Object getBean(String beanName)

// 获取配置
public static String getProperty(String key)
public static String getProperty(String key, String defaultValue)
```

### 3.10 LoggerUtils - 日志工具类
**包路径**: `com.howbuy.dtms.order.service.commom.utils.LoggerUtils`

**主要方法**:
```java
// 结构化日志输出
public static void logInfo(String message, Object... params)
public static void logError(String message, Throwable throwable, Object... params)

// 业务日志记录
public static void logBusiness(String businessType, String businessId, String message)
```

---

## 4. 枚举类

### 4.1 业务枚举
**包路径**: `com.howbuy.dtms.order.service.commom.enums`

**主要枚举**:
```java
// 货币枚举
CurrencyEnum: RMB("156", "人民币"), USD("840", "美元"), HKD("344", "港元")

// 订单状态枚举
OrderStatusEnum: 订单各种状态定义

// 交易状态枚举  
TradeStatusEnum: 交易各种状态定义

// 支付状态枚举
PayStatusEnum: 支付状态定义

// 文件状态枚举
FileStatusEnum: 文件处理状态

// 线上线下枚举
OnlineOfflineEnum: ONLINE("0", "线上补签"), OFFLINE("1", "线下补签")

// 有效性枚举
ValidEnum: VALID_ENUM("1", "合格"), NO_VALIE_ENUM("0", "不合格")
```

### 4.2 交易相关枚举

**TradeTypeEnum** - 交易类型枚举
```java
BUY("01", "申购"),
SELL("02", "赎回"),
DIVIDEND("03", "分红"),
TRANSFER_IN("04", "转入"),
TRANSFER_OUT("05", "转出")
```

**TradeStatusEnum** - 交易状态枚举
```java
INIT("00", "初始"),
PROCESSING("01", "处理中"),
SUCCESS("02", "成功"),
FAILED("03", "失败"),
CANCELLED("04", "已撤销")
```

**PayStatusEnum** - 支付状态枚举
```java
UNPAID("0", "未支付"),
PAYING("1", "支付中"),
PAID("2", "已支付"),
PAY_FAILED("3", "支付失败")
```

### 4.3 文件处理枚举

**FileStatusEnum** - 文件状态枚举
```java
INIT("0", "初始"),
PROCESSING("1", "处理中"),
SUCCESS("2", "成功"),
FAILED("3", "失败")
```

**IoFileTypeEnum** - IO文件类型枚举
```java
INCREMENT_TRADE_RECORD("05", "增量交易记录文件"),
INCREMENT_CUSTOMER_SHARE("06", "增量客户份额文件"),
TODAY_NA_FEE("07", "当日NA费用文件"),
ALL_CUSTOMER_SHARE("08", "全量客户份额文件")
```

### 4.4 消息相关枚举

**SendTopicEnum** - 发送主题枚举
```java
SMS("sms", "短信"),
EMAIL("email", "邮件"),
PUSH("push", "推送")
```

**MessageSendStatusEnum** - 消息发送状态枚举
```java
INIT("0", "初始"),
SENDING("1", "发送中"),
SUCCESS("2", "发送成功"),
FAILED("3", "发送失败")
```

### 4.5 枚举使用规范
```java
// 获取描述
String desc = CurrencyEnum.RMB.getDesc();

// 根据code获取枚举
CurrencyEnum currency = CurrencyEnum.getByCode("156");

// 获取code
String code = CurrencyEnum.RMB.getCode();

// 枚举比较（推荐使用equals）
if (TradeStatusEnum.SUCCESS.getCode().equals(status)) {
    // 处理成功状态
}

// 枚举遍历
for (CurrencyEnum currency : CurrencyEnum.values()) {
    System.out.println(currency.getCode() + ":" + currency.getDesc());
}
```

---

## 5. 外部系统接口封装

### 5.1 产品中心接口封装
**QueryFundInfoOuterService** - 基金信息查询服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.QueryFundInfoOuterService`

**主要方法**:
```java
// 查询单个基金基础信息
public FundBasicInfoDTO queryFundBasicInfo(String fundCode)

// 批量查询基金基础信息
public List<FundBasicInfoDTO> queryFundBasicInfoList(List<String> fundCodeList)

// 查询储蓄罐基金信息
public CxgFundInfoDTO queryCxgFundInfo()
public List<FundBasicInfoDTO> queryAllCxgFundInfo()

// 查询全委产品数据
public List<FundBasicInfoDTO> queryFullyCommissionedProductList()
```

### 5.2 客户信息接口封装
**HkCustInfoOuterService** - 香港客户信息服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.hkacc.HkCustInfoOuterService`

**主要方法**:
```java
// 查询客户基础信息
public HkCustInfoDTO getHkCustInfo(String hkCustNo)

// 查询客户储蓄罐协议信息
public HkCustPiggyAgreementDTO getHkCustPiggyAgreement(String hkCustNo)

// 查询客户明文信息
public HkCustInfoPlaintextDTO getCustInfoPlaintext(String hkCustNo)

// 获取测试客户列表
public List<String> getTestHkCustInfoList()
```

### 5.3 消息发送接口封装
**SendMessageOuterService** - 消息发送服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.cc.SendMessageOuterService`

**主要方法**:
```java
// 发送短信密文
public void sendMobileCipher(String mobileCipher, String templateId, JSONObject templateParams)

// 给投顾发消息
public void sendMessageToCust(String content, String conscode, String conscustNo)

// 根据一账通号发送消息
public Integer sendMsgByHboneNo(String businessId, String hboneNo, String templateVar)
```

### 5.4 加密服务接口封装
**EncryptOuterService** - 加密服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.auth.EncryptOuterService`

```java
// 字符串加密
public String encrypt(String oriStr)
```

### 5.5 交易日服务接口封装
**TradeDayOuterService** - 交易日服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.productcenter.TradeDayOuterService`

```java
// 判断是否交易日
public boolean isTradeDay(Date date)

// 查询海外交易日信息
public TradeDayInfoDTO queryHwTradeDayInfo(String date)
```

### 5.6 余额查询接口封装
**HkQueryBalanceOuterService** - 香港余额查询服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.hkbalance.HkQueryBalanceOuterService`

```java
// 查询客户余额
public HkBalanceDTO queryBalance(String hkCustNo, String currency)

// 查询客户资产
public HkAssetDTO queryAsset(String hkCustNo)
```

### 5.7 银行卡信息接口封装
**HkBankCardInfoOuterService** - 香港银行卡信息服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.hkacc.HkBankCardInfoOuterService`

```java
// 查询银行卡信息
public HkBankCardDTO getBankCardInfo(String hkCustNo)

// 查询默认银行卡
public HkBankCardDTO getDefaultBankCard(String hkCustNo)
```

### 5.8 CRM交易接口封装
**CounterOrderQueryOuterService** - 柜台订单查询服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.crmtrade.counterorderquery.CounterOrderQueryOuterService`

```java
// 查询柜台订单
public CounterOrderDTO queryCounterOrder(String orderNo)

// 查询订单列表
public List<CounterOrderDTO> queryOrderList(String hkCustNo, String startDate, String endDate)
```

### 5.9 预约服务接口封装
**HkPrebookOuterService** - 香港预约服务
**包路径**: `com.howbuy.dtms.order.service.outerservice.crmdt.prebook.HkPrebookOuterService`

```java
// 查询预约详情
public HkPreBookDetailDTO getPreBookDetail(String prebookId)

// 创建预约
public String createPreBook(HkPreBookCreateDTO createDTO)
```

### 5.10 外部接口使用规范

**统一响应处理**:
```java
// 所有外部接口都应该进行响应校验
private void checkResponse(Response<?> response) {
    if (response == null) {
        throw new BusinessException(ExceptionEnum.SYSTEM_ERROR);
    }
    if (!response.isSuccess()) {
        throw new BusinessException(response.getCode(), response.getMessage());
    }
}
```

**异常处理模式**:
```java
// 1. 空值检查
if (CollectionUtils.isEmpty(response.getData())) {
    return new ArrayList<>();  // 或返回null，根据业务需要
}

// 2. 特定错误码处理
if (OutReturnCodes.HK_ACC_ONLINE_CUST_NOT_EXISTS.equals(response.getReturnCode())) {
    throw new BusinessException(ExceptionEnum.ACCOUNT_NOT_EXISTS);
}

// 3. 日志记录
log.info("调用外部接口，请求参数: {}", JSON.toJSONString(request));
log.info("调用外部接口，返回结果: {}", JSON.toJSONString(response));
```

**Dubbo配置规范**:
```java
@DubboReference(registry = "dtms-product-remote", check = false)
private QueryFundBasicInfoFacade queryFundBasicInfoFacade;
```

---

## 6. 统一校验框架

### 6.1 框架概述
**包路径**: `com.howbuy.dtms.order.service.validator`

统一校验框架基于责任链模式，提供可插拔的校验能力，支持动态组合校验器。

### 6.2 核心组件
```java
// 校验器接口
TradeValidator - 所有校验器需实现此接口

// 校验器注解
@TradeValidatorAnnotation - 标识校验器类型

// 校验器注册中心
TradeValidatorMap - 自动注册和管理校验器

// 校验助手
TradeValidatorHelper - 构建和执行校验链

// 校验上下文
TradeContext - 校验过程中的数据传递载体
```

### 6.3 使用示例
```java
// 1. 构建校验链
List<String> validatorChain = TradeValidatorHelper.buildChain(
    TradeValidatorEnum.PARAMS_VALIDATOR,
    TradeValidatorEnum.ACCOUNT_STATUS_VALIDATOR,
    TradeValidatorEnum.RISK_LEVEL_VALIDATOR
);

// 2. 创建校验上下文
TradeContext context = new TradeContext();
context.setHkCustNo("HK123456");
context.setFundCode("F001");

// 3. 执行校验
TradeValidatorHelper.doValidate(context, validatorChain);
```

### 6.4 常用校验器
- `AccountStatusValidator` - 账户状态校验
- `RiskLevelValidator` - 风险等级匹配校验
- `ProductBusinessValidator` - 产品业务支持校验
- `BuyTradeLimitValidator` - 申购限额校验
- `SellTradeLimitValidator` - 赎回限额校验
- `OpenDtValidator` - 交易开放日校验
- `ConcurrentValidator` - 并发控制校验
- `ParamsValidator` - 参数非空校验

---

## 7. MQ消息处理

### 7.1 SendMqService - 消息发送服务
**包路径**: `com.howbuy.dtms.order.service.business.sendmq.SendMqService`

**主要方法**:
```java
// 发送交易消息
public void sendTradeMessage(String topic, String tag, Object messageBody)

// 发送订单消息
public void sendOrderMessage(String businessCode, String orderNo, Object messageData)

// 发送延时消息
public void sendDelayMessage(String topic, String tag, Object messageBody, long delayTime)
```

**使用示例**:
```java
@Resource
private SendMqService sendMqService;

// 发送订单创建消息
OrderCreateMessage message = new OrderCreateMessage();
message.setOrderNo("ORDER123456");
message.setHkCustNo("HK123456");
sendMqService.sendOrderMessage("ORDER_CREATE", "ORDER123456", message);
```

### 7.2 OrderCreateMessageProcessor - 订单消息处理器
**包路径**: `com.howbuy.dtms.order.service.mq.order.OrderCreateMessageProcessor`

**消息处理模式**:
```java
@Component
@RocketMQMessageListener(
    topic = "ORDER_TOPIC",
    consumerGroup = "order-consumer-group"
)
public class OrderCreateMessageProcessor implements RocketMQListener<String> {

    @Override
    public void onMessage(String message) {
        try {
            // 消息处理逻辑
            processOrderMessage(message);
        } catch (Exception e) {
            log.error("处理订单消息失败", e);
            // 异常处理
        }
    }
}
```

### 7.3 MQ消息使用规范

**消息体结构**:
```java
// 标准消息格式
{
    "head": {
        "businessId": "业务ID",
        "messageId": "消息ID",
        "timestamp": "时间戳"
    },
    "body": {
        // 具体业务数据
    }
}
```

**消息发送最佳实践**:
```java
// 1. 消息幂等性处理
public void sendMessageWithIdempotent(String messageId, Object messageBody) {
    if (isMessageSent(messageId)) {
        log.info("消息已发送，跳过: {}", messageId);
        return;
    }
    sendMqService.sendMessage(messageBody);
    markMessageSent(messageId);
}

// 2. 消息重试机制
@Retryable(value = Exception.class, maxAttempts = 3)
public void sendMessageWithRetry(Object messageBody) {
    sendMqService.sendMessage(messageBody);
}

// 3. 消息失败处理
@Recover
public void handleSendFailure(Exception ex, Object messageBody) {
    log.error("消息发送失败，进入失败处理", ex);
    // 记录失败消息，后续补偿处理
}
```

---

## 8. 缓存服务

### 8.1 缓存服务概述
**包路径**: `com.howbuy.dtms.order.service.cacheservice`

项目使用Redis作为缓存中间件，提供统一的缓存操作接口。

### 8.2 缓存操作模式
```java
@Service
public class FundInfoCacheService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    private static final String FUND_INFO_KEY_PREFIX = "fund:info:";
    private static final long CACHE_EXPIRE_TIME = 3600; // 1小时

    // 缓存查询
    public FundBasicInfoDTO getFundInfo(String fundCode) {
        String key = FUND_INFO_KEY_PREFIX + fundCode;
        return (FundBasicInfoDTO) redisTemplate.opsForValue().get(key);
    }

    // 缓存设置
    public void setFundInfo(String fundCode, FundBasicInfoDTO fundInfo) {
        String key = FUND_INFO_KEY_PREFIX + fundCode;
        redisTemplate.opsForValue().set(key, fundInfo, CACHE_EXPIRE_TIME, TimeUnit.SECONDS);
    }

    // 缓存删除
    public void deleteFundInfo(String fundCode) {
        String key = FUND_INFO_KEY_PREFIX + fundCode;
        redisTemplate.delete(key);
    }
}
```

### 8.3 缓存使用规范

**缓存Key命名规范**:
```java
// 格式：业务模块:数据类型:具体标识
public static final String FUND_INFO_KEY = "fund:info:%s";           // 基金信息
public static final String CUST_INFO_KEY = "cust:info:%s";           // 客户信息
public static final String ORDER_LOCK_KEY = "order:lock:%s";         // 订单锁
public static final String TRADE_LIMIT_KEY = "trade:limit:%s:%s";    // 交易限额
```

**缓存过期时间设置**:
```java
// 基础数据缓存（变化较少）
public static final long FUND_INFO_EXPIRE = 3600;      // 1小时
public static final long CUST_INFO_EXPIRE = 1800;      // 30分钟

// 业务数据缓存（变化频繁）
public static final long ORDER_STATUS_EXPIRE = 300;    // 5分钟
public static final long BALANCE_INFO_EXPIRE = 180;    // 3分钟

// 临时数据缓存
public static final long LOCK_EXPIRE = 60;             // 1分钟
public static final long VERIFY_CODE_EXPIRE = 300;     // 5分钟
```

**缓存穿透防护**:
```java
public FundBasicInfoDTO getFundInfoWithProtection(String fundCode) {
    // 1. 先查缓存
    FundBasicInfoDTO cached = getFundInfoFromCache(fundCode);
    if (cached != null) {
        return cached;
    }

    // 2. 加分布式锁防止缓存击穿
    String lockKey = "lock:fund:info:" + fundCode;
    if (redisTemplate.opsForValue().setIfAbsent(lockKey, "1", 30, TimeUnit.SECONDS)) {
        try {
            // 3. 双重检查
            cached = getFundInfoFromCache(fundCode);
            if (cached != null) {
                return cached;
            }

            // 4. 查询数据库
            FundBasicInfoDTO fundInfo = queryFundInfoFromDB(fundCode);

            // 5. 设置缓存（包括空值缓存）
            if (fundInfo != null) {
                setFundInfoToCache(fundCode, fundInfo);
            } else {
                // 空值缓存，防止缓存穿透
                setEmptyFundInfoToCache(fundCode);
            }

            return fundInfo;
        } finally {
            redisTemplate.delete(lockKey);
        }
    } else {
        // 获取锁失败，等待后重试
        try {
            Thread.sleep(100);
            return getFundInfoFromCache(fundCode);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return null;
        }
    }
}
```

---

## 9. 注解类

### 9.1 TradeValidatorAnnotation - 校验器注解
**包路径**: `com.howbuy.dtms.order.service.commom.annotaion.TradeValidatorAnnotation`

用于标识校验器类型，配合统一校验框架使用。

```java
@TradeValidatorAnnotation(TradeValidatorEnum.ACCOUNT_STATUS_VALIDATOR)
@Component
public class AccountStatusValidator implements TradeValidator {
    // 校验器实现
}
```

### 9.2 自定义注解使用规范

**注解定义模式**:
```java
@Target({ElementType.TYPE, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BusinessLog {
    String value() default "";
    String businessType() default "";
    boolean async() default false;
}
```

**注解处理器模式**:
```java
@Aspect
@Component
public class BusinessLogAspect {

    @Around("@annotation(businessLog)")
    public Object around(ProceedingJoinPoint point, BusinessLog businessLog) throws Throwable {
        // 前置处理
        long startTime = System.currentTimeMillis();

        try {
            Object result = point.proceed();
            // 成功后处理
            logSuccess(businessLog, startTime, result);
            return result;
        } catch (Exception e) {
            // 异常处理
            logError(businessLog, startTime, e);
            throw e;
        }
    }
}
```

---

## 📝 使用建议

### 开发规范建议

1. **优先使用现有工具类**：开发前先查看本文档，避免重复实现
2. **统一异常处理**：使用ExceptionEnum定义的异常码，保持系统一致性
3. **合理使用校验框架**：复杂业务逻辑建议使用统一校验框架
4. **遵循命名规范**：新增公共模块时遵循项目命名规范
5. **及时更新文档**：新增公共模块后及时更新本文档

### 性能优化建议

1. **缓存使用**：
   - 基础数据（基金信息、客户信息）优先使用缓存
   - 合理设置缓存过期时间
   - 注意缓存穿透和击穿问题

2. **数据库操作**：
   - 批量操作优于单条操作
   - 合理使用分页查询
   - 避免N+1查询问题

3. **外部接口调用**：
   - 设置合理的超时时间
   - 实现熔断和降级机制
   - 记录详细的调用日志

### 安全规范建议

1. **数据脱敏**：
   - 敏感信息（手机号、身份证）必须加密存储
   - 日志输出时进行脱敏处理
   - 使用EncryptOuterService进行加密

2. **参数校验**：
   - 所有外部输入必须进行校验
   - 使用AssertUtils进行断言校验
   - 统一使用校验框架进行业务校验

3. **权限控制**：
   - 接口调用需要进行权限验证
   - 敏感操作需要记录操作日志
   - 实现接口访问频率限制

### 监控告警建议

1. **业务监控**：
   - 关键业务指标监控（交易成功率、响应时间）
   - 异常情况告警（系统异常、业务异常）
   - 使用AlertLogUtil记录告警日志

2. **系统监控**：
   - 应用性能监控（CPU、内存、GC）
   - 数据库连接池监控
   - 缓存命中率监控

3. **链路追踪**：
   - 使用traceId进行链路追踪
   - 记录关键节点的执行时间
   - 异常时记录完整的调用链路

---

## 🔄 更新记录

| 版本 | 日期 | 更新内容 | 更新人 |
|------|------|----------|--------|
| 1.0 | 2025-06-28 | 初始版本，包含常量类、异常处理、工具类、枚举类、外部接口封装、校验框架、MQ消息处理、缓存服务等完整公共模块 | hongdong.xie |

---

## 📚 相关文档

- [统一校验框架设计文档](.知识库/统一校验框架/设计文档.md)
- [代码Review提示词](代码review提示词.md)
- [外部系统接口设计规范](.cursor/rules/外部系统接口.md)
- [项目架构规范](.cursor/rules/project-rules.mdc)

---

> **注意**: 本文档基于当前项目代码结构生成，如有新增或修改，请及时更新此文档。建议定期review和更新，确保文档与代码保持同步。
