# 1. 接口名称
确认文件消息处理器

# 2. 接口说明
该接口是一个消息消费者，负责处理上游系统（如清算中心）生成的确认文件（Ack File）的通知消息。当收到消息后，它会触发相应文件的导入和处理流程，是自动化批处理任务中的关键一环。

# 3. 接口类型
RocketMQ 消费者

# 4. 接口地址或方法签名
- **Topic**: `${TOPIC.dtms_file_generate_notify_to_order}`
- **消费类**: `com.howbuy.dtms.order.service.mq.batch.ackfile.AckFileMessageProcessor`
- **处理方法**: `protected void handleMessage(AckFileMessageDTO message)`

# 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 传输日期 | transportDt | String | 是 | "20241126" | 文件的业务日期 |
| 文件类型 | fileType | String | 是 | "1" | 1-中台确认明细文件 2-中台份额文件 3-中台份额明细文件 4-NA费用文件 |
| 文件路径 | filePath | String | 是 | "/path/to/your/file.txt" | 文件在服务器上的存储路径 |
| 文件名称 | fileName | String | 是 | "ACK_20241126.txt" | 文件的名称 |
| 基金代码 | fundCode | String | 是 | "000001" | 文件所属的基金代码 |

# 6. 响应参数表
该接口为消息处理器，无直接响应参数。

# 7. 返回码说明
不适用。

# 8. 关键业务逻辑说明
1.  **参数校验**: 检查消息中的 `fileType` 是否为已知的、合法的类型。如果无法识别，则断言失败，流程终止。
2.  **获取分布式锁**: 为了防止同一文件被并发处理，处理器首先会尝试获取一个基于 `transportDt`, `fileType`, 和 `fundCode` 组合的分布式锁。如果获取锁失败，记录日志并发送告警，然后直接返回，等待下次重试或人工干预。
3.  **检查重复处理**: 查询 `io_file_process_rec` 表，检查该文件是否已经被处理过。
    - 如果记录存在且状态为“导入成功” (`IMPORT_SUCCESS`)，说明是重复消息。此时会记录日志、发送告警，然后终止处理，避免重复执行。
    - 如果记录不存在，则创建一个新的文件处理记录，并将其插入数据库，初始状态为“未处理”。
    - 如果记录存在但状态不是“导入成功”，则继续后续处理流程。
4.  **调用导入流程**: 调用 `ackFileImportProcess.execImportProcess()` 方法，将文件处理记录对象 `ioFileProcessRecPO` 传递给下游服务，由其负责具体的
    文件解析、数据导入和业务处理。
5.  **释放锁**: 在 `finally` 块中确保分布式锁被释放，无论处理成功还是失败，以便其他任务可以继续执行。

# 9. 流程图
```plantuml
@startuml
title 确认文件消息处理流程

start
:接收 AckFileMessageDTO 消息;
:根据 message.fileType 获取 IoFileTypeEnum;
if (文件类型是否存在?) then (是)
    :构造分布式锁 uniqKey;
    if (获取锁 lockService.getLock(uniqKey) 成功?) then (是)
        :查询 IoFileProcessRecPO 记录;
        if (记录存在?) then (是)
            if (文件状态是否为 IMPORT_SUCCESS?) then (是)
                :发送告警: "文件已导入成功";
                :释放锁;
                stop
            else (否)
                :继续执行;
            endif
        else (否)
            :构建新的 IoFileProcessRecPO 对象;
            :插入新的文件处理记录到数据库;
        endif
        :调用 ackFileImportProcess.execImportProcess() 处理文件;
        :释放锁;
    else (否)
        :记录日志: "获取锁失败";
        :发送告警;
    endif
else (否)
    :断言失败: "文件类型不存在";
    stop
endif
stop

@enduml
```

# 10. 时序图
```plantuml
@startuml
title 确认文件消息处理时序图

participant "MQ Broker" as MQ
participant "AckFileMessageProcessor" as Processor
participant "LockService" as Lock
participant "IoFileProcessRecRepository" as Repo
participant "AckFileImportProcess" as ImportProcess
participant "AlertLogUtil" as Alert

MQ -> Processor: 发送 AckFileMessageDTO
activate Processor

Processor -> Processor: 校验文件类型
Processor -> Lock: getLock(uniqKey)
activate Lock
Lock --> Processor: return true/false
deactivate Lock

alt 获取锁成功
    Processor -> Repo: selectByFileTypeAndFundCode(...)
    activate Repo
    Repo --> Processor: return IoFileProcessRecPO or null
    deactivate Repo

    alt 记录不存在或处理未成功
        alt 记录不存在
            Processor -> Processor: buildIoFileProcessRecPO(...)
            Processor -> Repo: insert(po)
            activate Repo
            Repo --> Processor: void
            deactivate Repo
        end
        Processor -> ImportProcess: execImportProcess(po)
        activate ImportProcess
        ImportProcess --> Processor: void
        deactivate ImportProcess
    else 记录已存在且成功
        Processor -> Alert: alert("文件已导入成功")
    end

    Processor -> Lock: releaseLock(uniqKey)
    activate Lock
    Lock --> Processor: void
    deactivate Lock
else 获取锁失败
    Processor -> Alert: alert("获取锁失败")
end

deactivate Processor

@enduml
```

# 11. 异常处理机制
- **参数校验失败**: 如果 `fileType` 无效，`AssertUtils.nonNullParam` 会抛出异常，导致消息处理失败，根据MQ策略可能会进行重试。
- **获取锁失败**: 如果无法获取分布式锁，会调用 `AlertLogUtil.alert` 发送告警通知，并终止当前处理。这可能需要人工介入检查锁为何长时间未释放。
- **重复处理**: 如果消息对应的文件记录已经存在且状态为“导入成功”，会发送告警并终止处理，防止业务数据重复导入。
- **其他运行时异常**: `execImportProcess` 中发生的任何未捕获异常，会向上冒泡。由于 `lockService.releaseLock(uniqKey)` 在 `finally` 块中，可以保证锁最终被释放，避免死锁。

# 12. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
| :--- | :--- |
| `IoFileProcessRecRepository` | 数据库访问模块，用于操作 `io_file_process_rec` 表，查询和新增文件处理记录。 |
| `AckFileImportProcess` | 核心业务处理模块，负责执行具体的文件导入、解析和后续业务逻辑。 |
| `LockService` | 分布式锁服务，用于保证并发场景下任务执行的唯一性。 |
| `AlertLogUtil` | 统一告警工具类，用于在关键异常点发送通知。 |
| `AssertUtils` | 断言工具类，用于进行前置参数校验。 |

# 13. 幂等性与安全性说明
- **幂等性**: 通过分布式锁和数据库记录状态检查，实现了接口的幂等性。
  - **分布式锁**: `transportDt` + `fileType` + `fundCode` 构成的唯一键确保了同一文件在同一时间只能有一个实例在处理。
  - **状态检查**: 即使在锁失效的极端情况下，通过查询 `io_file_process_rec` 表中记录的状态，也能防止已成功导入的文件被重复处理。
- **安全性**: 该接口为内部 MQ 消费，不直接对外暴露，安全性由内部网络和 MQ 的访问控制保证。

# 14. 业务处理涉及到的表名
| 表名 | 表注释 |
| :--- | :--- |
| `io_file_process_rec` | IO文件处理记录表，用于跟踪每个文件的处理状态和进度。 |

# 15. 备注与风险点
- **锁过期风险**: 分布式锁设置了 600 秒的过期时间。如果 `ackFileImportProcess.execImportProcess` 的执行时间超过此阈值，锁可能会自动释放，导致其他实例获取到锁并开始处理，存在并发风险。需要确保下游处理逻辑的耗时远小于锁的过期时间。
- **依赖服务可用性**: 依赖 `Redis` (用于分布式锁) 和数据库的稳定性。如果这些外部服务不可用，处理器将无法正常工作。