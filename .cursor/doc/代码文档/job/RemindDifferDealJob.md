# 接口名称
订单开放日差异提醒与更新任务

# 接口说明
这是一个由消息队列（MQ）触发的后台任务（Job）。它旨在解决因基金交易日历变更，导致在途订单的开放日（Open Date）信息与实际不符的问题。

**业务背景**：客户提交购买或赎回订单后，订单状态为"申请成功"，此时记录了一个预计的交易开放日。但有时基金公司会调整交易日历，导致原有的开放日失效。
**核心功能**：此任务会定期扫描所有"申请成功"的订单，通过调用外部服务获取最新的基金交易日历，与订单中记录的开放日进行比对。若发现不一致，任务会自动更新订单的开放日、打款截止时间等关键信息，以确保后续交易流程的准确性。同时，它会将所有变更和处理异常的订单信息汇总，通过邮件通知相关业务或运维人员。

# 接口类型
后台任务 (Job)

# 接口地址或方法签名
- **类**: `com.howbuy.dtms.order.service.job.RemindDifferDealJob`
- **核心方法**: `protected void doProcessJob(CommonTaskVO message)`
- **触发机制**: 监听由EC（任务中心）发送的MQ消息，主题为 `${TOPIC.DTMS_ORDER_EC_TASK_REMIND_DIFFER}`。

# 请求参数表
此任务由MQ消息触发，但消息体 `CommonTaskVO` 的内容在业务逻辑中并未使用。该消息仅作为启动任务的信号。

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- |:--- |
| 任务消息体 | message | `CommonTaskVO` | 是 | `{}` | 仅用于触发任务，其内部属性不被消费。 |

# 响应参数表
此任务没有直接的HTTP/RPC响应。其执行结果通过以下几种方式体现：
1.  **数据库更新**：直接更新`hw_deal_order`（订单主表）和`hw_deal_order_dtl`（订单明细表）中的`open_dt`, `pay_end_dt`, `pay_end_tm`等字段。
2.  **消息队列**：对于每一个被成功更新的订单，会向MQ发送一条订单更新消息，以通知下游系统。
3.  **邮件通知**：任务执行完毕后，会发送一封汇总邮件给指定接收人（`smop.email.address`），内容包括所有被成功更新的订单列表，以及在处理过程中发生异常的订单列表。

# 返回码说明
不适用。任务的执行状态通过日志记录。成功或失败的细节（如更新了哪些订单，哪些订单处理失败）会通过邮件通知。

# 关键业务逻辑说明
1.  **任务触发**：任务消费指定Topic的MQ消息后开始执行。
2.  **数据拉取**：从数据库（`hw_deal_order`表）查询所有`deal_stat`为"申请成功"的在途订单。
3.  **循环处理**：遍历查询到的每一个订单，执行以下核心逻辑：
    a. **前置校验**：检查订单关联的产品是否支持预约购买/赎回（`support_prebook_flag`），如果不支持，则跳过该订单。
    b. **获取最新日历**：调用产品中心的`queryFundTradeCalendarInfo`接口，传入基金代码、业务类型、申请时间等参数，获取该产品最新的交易日历信息（包含最新的开放日`openEndDt`）。
    c. **分类处理**：
        - **对于购买/申购订单** (`1122`, `1123`)：
            i.   调用`openDateComputeService`，结合最新的日历信息和订单的支付方式（电汇/存管），重新计算出准确的开放日`openDt`、打款截止日`payEndDt`和打款截止时间`payEndTm`。
            ii.  检查当前系统时间是否已经晚于新算出的打款截止时间。如果是，则将该订单视为异常，记录并跳过。
            iii. 比较新计算出的`openDt`与订单中原有的`openDt`。若不一致，则认为需要更新。
        - **对于赎回订单** (`1124`)：
            i.   直接使用从交易日历接口获取的`openEndDt`作为新的开放日。
            ii.  比较该`openEndDt`与订单中原有的`openDt`。若不一致，则认为需要更新。
    d. **执行更新**：如果需要更新：
        i.   构造`OrderUpdateBO`对象，包含更新后的`HwDealOrder`和`HwDealOrderDtl`实体。
        ii.  调用`hwDealOrderRepository.updateDealOrder`方法，将变更持久化到数据库。
        iii. 调用`sendMqService.sendOrderUpdateMessage`发送订单变更的MQ消息。
        iv.  将新旧开放日等变更信息封装为`RemindOpenDtDifferDTO`，存入待发送邮件列表。
4.  **异常捕获**：在处理单个订单的整个过程中，使用`try-catch`块包裹。任何步骤（如调用外部接口、数据库更新）发生异常，都会被捕获。异常信息会被记录到日志，并将该订单号加入到`errorList`中，确保单个订单的失败不影响整个任务的执行。
5.  **发送通知**：所有订单遍历完毕后，将成功更新的订单列表和处理异常的`errorList`合并，调用`SendMessageOuterService`发送汇总邮件。

# 流程图
```plantuml
@startuml RemindDifferDealJob_Flow
title 订单开放日差异提醒与更新任务处理流程

start
:消费MQ消息，触发Job;

:查询所有"申请成功"状态的订单;
if (订单列表为空?) then (是)
  :结束;
  stop
endif

:初始化"变更列表"和"异常列表";

while (遍历每个订单)
  note right
    单个订单处理发生任何异常，
    都会被捕获并记录到"异常列表"，
    然后继续处理下一个订单。
  end note

  if (产品是否支持预约?) then (是)
    :调用产品中心接口获取最新交易日历;
    
    if (购买/申购类订单?) then (是)
      :重新计算开放日和打款截止时间;
      if (当前时间 > 打款截止时间?) then (是)
        :将订单号加入"异常列表";
      else (否)
        if (新旧开放日不一致?) then (是)
          :更新订单(DB);
          :发送订单变更MQ消息;
          :将变更信息加入"变更列表";
        endif
      endif
    else (赎回类订单)
      :获取接口返回的开放日作为新开放日;
       if (新旧开放日不一致?) then (是)
        :更新订单(DB);
        :发送订单变更MQ消息;
        :将变更信息加入"变更列表";
      endif
    endif
  else (否)
    :跳过当前订单;
  endif
endwhile

if ("变更列表"或"异常列表"不为空?) then (是)
  :调用消息中心接口发送汇总邮件;
endif

:结束;
stop
@enduml
```

# 时序图
```plantuml
@startuml RemindDifferDealJob_Sequence
title 订单开放日更新时序图

actor "EC调度中心" as Scheduler
participant "MQ" as Mq
participant "RemindDifferDealJob" as Job
participant "HwDealOrderRepository" as Repo
participant "QueryFundInfoOuterService" as FundService
participant "OpenDateComputeService" as ComputeService
participant "SendMqService" as MqService
participant "SendMessageOuterService" as EmailService

Scheduler -> Mq : 发送任务触发消息
Mq -> Job : 消费消息
activate Job

Job -> Repo : listApplyDeal()
activate Repo
Repo --> Job : 返回"申请成功"订单列表
deactivate Repo

loop 对每个订单
    Job -> FundService : queryFundTradeCalendarInfo(req)
    activate FundService
    FundService --> Job : 返回最新交易日历
    deactivate FundService

    alt 购买/申购
        Job -> ComputeService : computeByPayMethod(context)
        activate ComputeService
        ComputeService --> Job : 返回计算后的开放日和截止时间
        deactivate ComputeService
    end

    alt 新旧开放日不一致
        Job -> Repo : updateDealOrder(bo)
        activate Repo
        Repo --> Job : 更新成功
        deactivate Repo

        Job -> MqService : sendOrderUpdateMessage(order)
        activate MqService
        MqService -> Mq : 发送订单变更消息
        deactivate MqService
    end
end

Job -> EmailService : sendEmail(list)
activate EmailService
EmailService --> Job : 发送成功
deactivate EmailService

deactivate Job

@enduml
```

# 异常处理机制
- **容错性**：任务的核心逻辑位于一个遍历订单的循环中，并且对每个订单的处理都包裹在独立的`try-catch(Exception e)`块内。这确保了单个订单的处理失败（如外部接口超时、数据格式错误等）不会中断整个任务的执行，任务会继续处理下一个订单。
- **日志记录**：当捕获到异常时，会使用`log.error`记录详细的错误信息，包括出错的订单号和完整的异常堆栈（`Throwables.getStackTraceAsString(e)`），便于问题排查。
- **失败通知**：处理失败的订单号会被添加到一个`errorList`中。在任务执行的最后阶段，这个列表会作为邮件内容的一部分发送给相关人员，以便进行人工干预。

# 调用的公共模块或外部依赖
| 模块/服务名称 | 功能简述 |
| :--- | :--- |
| `HwDealOrderRepository` | 数据库仓储服务，负责订单数据的查询（`listApplyDeal`）和更新（`updateDealOrder`）。 |
| `QueryFundInfoOuterService` | **外部服务**。调用产品中心（dtms-product），获取基金的交易日历等信息。 |
| `OpenDateComputeService` | 核心业务服务，封装了开放日和相关截止日期的计算逻辑。 |
| `SendMessageOuterService` | **外部服务**。调用消息中心（CC），发送最终的汇总邮件。 |
| `SendMqService` | MQ发送服务，用于向消息队列发送订单数据变更的通知。 |
| `TradeContextBuilder` | 工具类，用于构建和初始化业务处理所需的上下文对象`TradeContext`。 |

# 幂等性与安全性说明
- **幂等性**：该任务不具备严格的幂等性。如果短时间内重复执行：
    - 对于已经更新过的订单，由于新旧开放日已经一致，不会触发二次更新，这部分操作是幂等的。
    - 但是，邮件通知会被重复发送。
    - **建议**：应由上游（如EC调度中心）保证在单个任务周期内，不会重复发送触发消息。
- **安全性**：
    - 这是一个内部后台任务，不直接对外暴露服务，因此没有常规的API认证、授权等安全风险。
    - 依赖的外部服务调用（如产品中心、消息中心）应遵循内部服务间的安全认证机制。

# 备注与风险点
- **性能风险**：任务的执行效率与`listApplyDeal()`查询出的在途订单数量成正比。如果该状态下的订单积压过多，可能导致单次任务执行时间过长，需要关注其性能表现。
- **外部依赖风险**：任务强依赖`QueryFundInfoOuterService`的稳定性和数据准确性。若该服务不可用或返回错误数据，将直接导致本任务逻辑错乱或失败。
- **事务说明**：代码中对`updateDealOrder`的调用没有显式的事务注解。其事务行为依赖于`HwDealOrderRepository`中方法的事务传播设置。从代码看，更新主表和明细表应在一个事务中完成，以保证数据一致性。
- **配置管理**：邮件接收人地址（`smop.email.address`）和MQ主题（`TOPIC.DTMS_ORDER_EC_TASK_REMIND_DIFFER`）通过配置注入，便于在不同环境中部署和调整。 