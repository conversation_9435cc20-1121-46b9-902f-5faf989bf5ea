# 储蓄罐自动赎回任务 - 接口详细设计文档

## 1. 接口名称
储蓄罐自动赎回任务。

## 2. 接口说明
这是一个由MQ消息触发的后台定时任务。当用户使用"储蓄罐"为新的基金认购/申购订单支付时，系统会触发此任务。任务的目的是自动赎回用户持有的"储蓄罐"货币基金，以补足新订单所需的支付金额。

该任务会查询当日所有待处理的、由储蓄罐支付的订单，计算需要赎回的份额或金额，并在中间表（`hw_piggy_trade_app_import`）中创建一条赎回申请记录。后续流程将基于此记录生成最终的赎回交易订单。

## 3. 接口类型
MQ 消费者 (Background Job)。

## 4. 接口地址或方法签名
- **Topic**: `DTMS_ORDER_PIGGY_AUTO_REDEEM_FOR_PIGGY_PAY`
- **Class**: `com.howbuy.dtms.order.service.job.PiggyAutoRedeemForPiggyPayJob`
- **Method**: `doProcessJob(CounterPiggyAutoRedeemVO message)`

## 5. 请求参数表
此任务由消息触发，但消息体本身不包含具体的业务数据，仅作为启动信号。

| 中文名           | 英文名      | 类型   | 是否必填 | 示例值 | 字段说明                           |
|------------------|-------------|--------|----------|--------|------------------------------------|
| EC调度平台任务ID | `qrtzLogId` | String | 否       | "..."  | 由EC调度平台自动赋值，用于日志追踪。 |

## 6. 响应参数表
该任务的执行方法 `doProcessJob` 返回类型为 `void`，没有直接的响应体。任务执行结果通过日志记录、数据库状态变更和系统警报来体现。

## 7. 返回码说明
不适用。作为后台任务，它不直接向调用方返回状态码。

## 8. 关键业务逻辑说明
核心逻辑由 `CounterPiggyAutoRedeemProcessor` 类实现，主要步骤如下：
1.  **前置检查**：
    -   调用 `TradeDayOuterService` 检查当天是否为海外交易日。
    -   调用 `DtmsSettleOuterService` 获取当前工作日，并校验系统是否已完成日终初始化。
2.  **分布式锁**：调用 `LockService` 获取一个基于当前工作日的分布式锁，确保同一天内任务不会并发执行，防止重复处理。
3.  **分页批量处理**：
    -   使用 `BatchOperateExecutor` 框架，分批次（每批200条）从数据库中查询需要处理的订单。
    -   通过 `hwDealOrderDtlRepository.selectAutoRedeemOrderForCounter` 方法，查询出所有符合条件的、由储蓄罐支付的待处理订单。
4.  **订单逐条处理** (`getHwPiggyTradeAppImport` 方法):
    -   **幂等性检查**: 查询 `hw_piggy_trade_app_import` 表，确认该订单是否已生成过赎回申请记录，如已存在则跳过。
    -   **获取协议与产品信息**:
        -   调用 `counterOrderQueryOuterService.getHkCustPiggyAgreement` 获取客户的储蓄罐签约协议，从而得到用于赎回的货币基金代码。
        -   调用 `queryFundInfoOuterService.queryFundBasicInfo` 获取该货币基金的详细信息（如币种、赎回方式等）。
    -   **核心校验**:
        -   校验原订单币种与储蓄罐基金币种是否一致。
        -   校验储蓄罐基金是否支持"全部"赎回方式。
        -   构建交易上下文 `TradeContext`，并依次执行一系列原子校验器：
            -   `openDateComputeService`：计算交易开放日。
            -   `productNavStatusValidator`：校验产品净值状态。
            -   `fundTxAcctStatValidator`：校验基金交易账户状态。
            -   `balanceAvailableShareValidator`：校验客户持有的该货币基金可用份额是否充足。
    -   **构建赎回申请记录**: 如果所有校验通过，则构建一条 `HwPiggyTradeAppImport` 实体，其中包含客户号、基金代码、赎回金额/份额、关联的原始订单号等信息。
5.  **批量入库**：将内存中生成的 `HwPiggyTradeAppImport` 记录列表，通过 `hwPiggyTradeAppImportRepository.insertBatch` 方法批量插入数据库。
6.  **异常处理与告警**:
    -   在单条订单处理过程中，若发生异常，则将该订单号记录到 `autoRedeemErrorList` 列表中。
    -   在任务所有批次处理完成后，检查 `autoRedeemErrorList` 是否为空，若不为空，则通过 `AlertLogUtil` 发送系统警报。
7.  **释放锁**：在 `finally` 代码块中，调用 `lockService.releaseLock` 释放分布式锁。

## 9. 流程图
```plantuml
@startuml
title 储蓄罐自动赎回任务流程图

start
:接收MQ消息，触发任务;

if (非海外交易日?) then (是)
  :记录日志并退出;
  stop
else (否)
endif

if (系统未日初?) then (是)
  :发送警报并退出;
  stop
else (否)
endif

:获取当日分布式锁;
if (获取锁失败?) then (是)
  :发送警报并退出;
  stop
else (否)
  :分页查询待处理的储蓄罐支付订单;
  while (还有未处理的订单?)
    :【循环处理每条订单】;
    if (已生成过赎回IO记录?) then (是)
      :跳过当前订单;
    else (否)
      :获取储蓄罐签约协议;
      :获取储蓄罐基金产品信息;
      :执行一系列交易校验 (账户/产品状态/可用份额等);
      if (校验通过?) then (是)
        :构建赎回IO记录 (HwPiggyTradeAppImport);
        :添加到待入库列表;
      else (否)
        :记录异常订单号;
      endif
    endif
  endwhile
  
  if (待入库列表不为空?) then (是)
    :批量插入赎回IO记录到数据库;
  endif

  if (存在异常订单?) then (是)
    :发送统一警报;
  endif

  :释放分布式锁;
endif

stop
@enduml
```

## 10. 时序图
```plantuml
@startuml
title 储蓄罐自动赎回任务时序图

participant Scheduler as "MQ/调度中心"
participant Job as "PiggyAutoRedeemForPayJob"
participant Processor as "CounterPiggyAutoRedeemProcessor"
participant LockService as "分布式锁服务"
participant DB as "数据库"
participant OuterServices as "外部服务"

Scheduler -> Job : 触发执行 doProcessJob()
Job -> Processor : processJob()

Processor -> OuterServices : queryHwTradeDt() / queryWorkday()
Processor -> LockService : getLock()
activate LockService
LockService --> Processor : success
deactivate LockService

loop 分页查询
  Processor -> DB : selectAutoRedeemOrderForCounter()
  DB --> Processor : HwDealOrderdtlBO 列表
end

loop 订单处理
  Processor -> DB : queryTradeAppImportByRelationalDealNo()
  Processor -> OuterServices : getHkCustPiggyAgreement()
  Processor -> OuterServices : queryFundBasicInfo()
  Processor -> Processor : 执行各项内部校验...
  Processor -> DB : (Validators may query DB)
end

Processor -> DB : insertBatch(HwPiggyTradeAppImport 列表)
activate DB
DB --> Processor: 插入成功
deactivate DB

alt 存在处理失败的订单
  Processor -> OuterServices : AlertLogUtil.alert()
end

Processor -> LockService : releaseLock()
activate LockService
LockService --> Processor : success
deactivate LockService

@enduml
```

## 11. 异常处理机制
- **全局异常**: 整个 `processJob` 方法由 `try-finally` 包裹，确保无论任务是否成功，最终都会释放分布式锁和发送汇总的失败警报。
- **单笔订单异常**: 在 `getHwPiggyTradeAppImport` 方法中，使用 `try-catch` 块捕获处理单笔订单时的所有异常。捕获到异常后，会记录详细的错误日志，并将失败的订单号添加到一个线程安全的 `CopyOnWriteArrayList` 列表中，不会中断整个批处理任务。
- **统一告警**: 任务结束时，如果记录异常订单号的列表不为空，会通过 `AlertLogUtil` 发送一条汇总的系统警报，通知运维人员本次任务中有处理失败的订单。

## 12. 调用的公共模块或外部依赖
| 模块/服务                                 | 功能简述                                                                 |
|-------------------------------------------|--------------------------------------------------------------------------|
| `LockService`                             | 提供分布式锁服务，用于保证任务的单实例执行。                               |
| `BatchOperateExecutor`                    | 批量操作执行器，用于分页查询和处理大量数据。                               |
| `HwDealOrderDtlRepository`                | DAO层，负责查询待处理的、用储蓄罐支付的订单。                              |
| `HwPiggyTradeAppImportRepository`         | DAO层，负责读写储蓄罐赎回导入表，用于幂等性检查和保存赎回申请记录。        |
| `TradeDayOuterService`                    | 外部服务，查询指定日期是否为海外交易日。                                   |
| `DtmsSettleOuterService`                  | 外部服务，查询当前系统的结算工作日，判断是否完成日初。                     |
| `CounterOrderQueryOuterService`           | 外部服务，查询客户的储蓄罐签约详情。                                       |
| `QueryFundInfoOuterService`               | 外部服务，查询基金产品的基本信息。                                         |
| `OpenDateComputeService`                  | 业务服务，根据产品信息和日历计算交易的开放日。                             |
| `ProductNavStatusValidator`               | 校验器，校验产品净值状态是否允许交易。                                     |
| `FundTxAcctStatValidator`                 | 校验器，校验客户的基金交易账户状态是否正常。                               |
| `BalanceAvailableShareValidator`          | 校验器，校验客户持有的基金可用份额。                                       |

## 13. 幂等性与安全性说明
- **幂等性**: 通过两种机制保障：
    1.  **任务级幂等性**: 使用基于"工作日"的分布式锁，防止同一天内多个任务实例并发执行，从根本上避免了重复调度执行的问题。
    2.  **订单级幂等性**: 在处理每一笔订单前，都会先查询 `hw_piggy_trade_app_import` 表，检查是否已存在关联该订单的赎回申请记录。如果存在，则直接跳过，避免了对同一订单的重复处理。
- **安全性**:
    -   该任务为内部后台作业，不暴露任何外部HTTP或Dubbo接口，降低了外部攻击风险。
    -   所有对外部服务的调用均在内部服务网络中进行。

## 14. 备注与风险点
- **处理链路**: 此任务仅负责生成赎回的"申请记录"，并不直接创建最终的赎回订单。依赖后续独立的流程来消费 `hw_piggy_trade_app_import` 表中的数据。
- **深分页风险**: 代码注释中已指出，当待处理订单量巨大时，分页查询可能存在性能问题（深分页）。
- **外部依赖风险**: 任务强依赖多个外部服务，如产品中心、结算中心等。任何一个外部服务的不可用或响应异常，都可能导致订单处理失败或整个任务中断。
- **配置风险**: 储蓄罐基金的赎回方式等配置错误（如未配置为"全部赎回"），会导致校验失败，无法为客户自动赎回。 