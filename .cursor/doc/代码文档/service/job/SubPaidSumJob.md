# SubPaidSumJob 接口设计文档

## 1. 接口名称
认缴实缴汇总数据初始化定时任务

## 2. 接口说明
该定时任务用于初始化认缴实缴汇总数据，主要处理特定基金代码（H00022、H04289、H04287）的基金订单信息，根据业务代码区分认缴和实缴业务，生成认缴实缴汇总信息和明细信息。该任务通过MQ消息触发执行，确保数据的一致性和完整性。

**业务背景：** 在基金业务中，需要对客户的认缴和实缴金额进行统计汇总，以便后续的资金管理和风险控制。

**使用场景：** 
- 定时批量处理基金订单的认缴实缴数据
- 初始化历史订单的认缴实缴汇总信息
- 数据修复和补偿场景

## 3. 接口类型
定时任务 (Scheduled Job)

## 4. 接口地址或方法签名
```java
public class SubPaidSumJob extends AbstractBatchMessageJob<BaseTaskMessageVO>
public void doProcessJob(BaseTaskMessageVO message)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 调度任务ID | qrtzLogId | String | 是 | "12345" | EC调度平台任务ID，由调度平台自动赋值 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 无 | 无 | void | - | - | 该方法无返回值，通过日志记录处理结果 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| SUCCESS | 执行成功 | 任务正常完成 |
| FAIL | 执行失败 | 任务执行过程中发生异常 |
| TIMEOUT | 执行超时 | 任务执行超时 |

## 8. 关键业务逻辑说明

1. **基金订单查询：** 查询指定基金代码（H00022、H04289、H04287）的已确认成功的基金订单明细
2. **重复性检查：** 检查订单明细是否已存在认缴实缴明细记录，避免重复处理
3. **汇总信息处理：** 
   - 如果汇总记录不存在，则新增汇总记录
   - 如果汇总记录已存在，则更新汇总金额
4. **业务代码区分：**
   - 112A（认缴）：更新认缴总金额（subTotalAmt），实缴金额设为0
   - 其他业务代码（实缴）：更新累计实缴总金额（cumPaidTotalAmt），认缴金额设为0
5. **明细记录插入：** 为每个处理的订单插入对应的认缴实缴明细记录
6. **金额计算：** 实缴金额 = 确认金额 - 手续费

## 9. 流程图

```plantuml
@startuml
start
:接收MQ消息;
:获取分布式锁;
if (获取锁成功?) then (是)
  :查询指定基金代码的已确认订单;
  if (订单列表为空?) then (是)
    :结束处理;
    stop
  else (否)
    :遍历订单列表;
    :检查明细是否已存在;
    if (明细已存在?) then (是)
      :跳过当前订单;
    else (否)
      :查询汇总信息;
      if (汇总信息存在?) then (是)
        if (业务代码=112A?) then (是)
          :更新认缴总金额;
        else (否)
          :更新实缴总金额;
        endif
        :更新汇总记录;
      else (否)
        :创建新汇总记录;
        if (业务代码=112A?) then (是)
          :设置认缴金额;
        else (否)
          :设置实缴金额;
        endif
        :插入汇总记录;
      endif
      :插入明细记录;
    endif
  endif
  :释放分布式锁;
  :返回执行结果;
else (否)
  :记录获取锁失败日志;
  stop
endif
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "调度中心" as Scheduler
participant "SubPaidSumJob" as Job
participant "LockService" as Lock
participant "HwDealOrderDtlRepository" as OrderRepo
participant "HwSubPaidSumInfoRepository" as SumRepo
participant "HwSubPaidDtlInfoRepository" as DtlRepo
participant "数据库" as DB

Scheduler -> Job: 发送MQ消息
Job -> Lock: 获取分布式锁
Lock -> Job: 返回锁获取结果

alt 获取锁成功
  Job -> OrderRepo: 查询指定基金订单
  OrderRepo -> DB: 执行查询SQL
  DB -> OrderRepo: 返回订单列表
  OrderRepo -> Job: 返回订单数据
  
  loop 遍历每个订单
    Job -> DtlRepo: 检查明细是否存在
    DtlRepo -> DB: 查询明细记录
    DB -> DtlRepo: 返回查询结果
    DtlRepo -> Job: 返回检查结果
    
    alt 明细不存在
      Job -> SumRepo: 查询汇总信息
      SumRepo -> DB: 查询汇总记录
      DB -> SumRepo: 返回汇总数据
      SumRepo -> Job: 返回汇总信息
      
      alt 汇总信息存在
        Job -> SumRepo: 更新汇总记录
        SumRepo -> DB: 执行更新SQL
      else 汇总信息不存在
        Job -> SumRepo: 插入汇总记录
        SumRepo -> DB: 执行插入SQL
      end
      
      Job -> DtlRepo: 插入明细记录
      DtlRepo -> DB: 执行插入SQL
    end
  end
  
  Job -> Lock: 释放分布式锁
  Job -> Scheduler: 返回执行结果
else 获取锁失败
  Job -> Scheduler: 记录失败日志
end
@enduml
```

## 11. 异常处理机制

| 异常场景 | 处理方式 | 说明 |
|----------|----------|------|
| 获取分布式锁失败 | 记录日志并退出 | 防止并发执行，确保数据一致性 |
| 数据库连接异常 | 抛出异常，任务失败 | 由框架统一处理数据库异常 |
| 数据插入/更新异常 | 事务回滚 | 保证数据的原子性 |
| 业务逻辑异常 | 记录错误日志，继续处理下一条 | 单条记录异常不影响整体处理 |
| MQ消息格式异常 | 记录异常日志，任务失败 | 确保消息格式的正确性 |

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| AbstractBatchMessageJob | 批量消息处理基类，提供分布式锁、消息处理框架 |
| SequenceService | 序列号生成服务，生成雪花ID |
| MathUtils | 数学计算工具类，提供BigDecimal安全计算 |
| HwDealOrderDtlRepository | 订单明细数据访问层 |
| HwSubPaidSumInfoRepository | 认缴实缴汇总信息数据访问层 |
| HwSubPaidDtlInfoRepository | 认缴实缴明细信息数据访问层 |
| BusinessCodeEnum | 业务代码枚举类 |
| YesOrNoEnum | 是否枚举类 |
| LockService | 分布式锁服务 |

## 13. 幂等性与安全性说明

**幂等性：**
- ✅ 支持幂等：通过检查明细记录是否已存在来避免重复处理
- ✅ 分布式锁：确保同一时间只有一个实例执行任务
- ✅ 唯一性约束：通过订单号、基金代码、基金交易账号、客户号组合确保唯一性

**安全性：**
- ✅ 事务控制：数据库操作使用事务确保数据一致性
- ✅ 异常处理：完善的异常捕获和处理机制
- ✅ 日志记录：详细的操作日志便于问题排查
- ✅ 参数校验：对关键业务参数进行校验

**限流：** 无特殊限流要求，通过分布式锁控制并发

**验签：** 无需验签，内部系统调用

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_deal_order_dtl | 基金订单明细表 |
| hw_sub_paid_sum_info | 认缴实缴汇总表 |
| hw_sub_paid_dtl_info | 认缴实缴明细表 |

## 15. 备注与风险点

**注意事项：**
1. 该任务处理的基金代码是硬编码的（H00022、H04289、H04287），如需扩展需要修改代码
2. 业务代码112A表示认缴业务，其他代码表示实缴业务
3. 实缴金额计算公式：实缴金额 = 确认金额 - 手续费

**边界处理：**
1. 空订单列表：直接返回，不进行后续处理
2. 重复订单：通过明细记录检查避免重复处理
3. 金额为null：使用BigDecimal.ZERO作为默认值

**特殊逻辑说明：**
1. 认缴业务（112A）：只更新认缴总金额，实缴金额设为0
2. 实缴业务（非112A）：只更新累计实缴总金额，认缴金额设为0
3. 汇总记录的版本号用于乐观锁控制

**风险点：**
1. 大数据量处理可能导致内存溢出，建议增加分页处理
2. 长时间运行可能导致锁超时，需要合理设置锁过期时间
3. 数据库连接池耗尽风险，需要监控数据库连接使用情况

---

**文档生成时间：** 2025-07-29 13:04:29
**文档版本：** v1.0
**作者：** hongdong.xie
