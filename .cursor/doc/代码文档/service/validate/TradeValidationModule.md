# 1. 模块名称
交易校验模块 (Trade Validation Module)

# 2. 模块作用简述
本模块为海外订单系统提供了一套统一、可扩展的交易前置校验框架。它基于责任链模式，将复杂的校验逻辑拆分为一系列独立的、可复用的原子校验器。通过动态组合这些校验器，可以灵活地构建出适应不同交易场景（如申购、赎回、撤单等）的校验流程，从而保证进入核心业务逻辑的数据的合法性和一致性，提升系统的稳定性和可维护性。

# 3. 功能清单
本模块主要由以下几部分构成：

- **校验器核心组件**:
    - `TradeValidator`: 校验器接口，所有原子校验器均需实现此接口。
    - `TradeValidatorMap`: 校验器注册中心，在应用启动时自动扫描并注册所有 `TradeValidator` 实现。
    - `TradeValidatorHelper`: 校验链执行器，是外部调用校验功能的统一入口。
    - `TradeContext`: 交易校验上下文，用于在不同校验器之间传递数据和状态。

- **通用校验器**:
    - `ConcurrentValidator`: 基于Redis的分布式锁，用于防止并发重复请求。
    - `ParamsValidator`: 提供通用的请求参数非空校验。
    - `PayVoucherValidator`: 专用于打款凭证相关业务的校验逻辑。

- **原子校验器 (`chain/atomic`)**:
    模块提供了丰富的原子校验器，覆盖了账户、产品、交易、风控等多个维度。业务方可以根据需要自由组合。部分校验器列举如下：
    - **账户与客户**:
        - `AccountStatusValidator`: 账户状态校验。
        - `InvestorQualificationValidator`: 投资者资质校验。
        - `RiskLevelValidator`: 客户风险等级与产品风险等级匹配校验。
        - `TradePasswordValidator`: 交易密码正确性校验。
        - `TestAccountValidator`: 测试账号限制校验。
    - **产品与额度**:
        - `ProductChannelValidator`: 产品销售渠道校验。
        - `ProductBusinessValidator`: 产品支持的业务类型校验。
        - `ProductNavStatusValidator`: 产品净值状态与可交易状态校验。
        - `BuyTradeLimitValidator`: 申购/认购交易限额（最大/最小金额）校验。
        - `SellTradeLimitValidator`: 赎回交易限额校验。
        - `CurrentPeriodNumberAndAmountValidator`: 预约产品本期总参与人数和总金额校验。
        - `CurrentPeriodSingleNumberAndAmountValidator`: 预约产品本期单人参与笔数和金额校验。
    - **交易规则**:
        - `OpenDtValidator`: 交易开放日计算与校验。
        - `CounterClosedValidator`: 柜台是否收市校验。
        - `PrebookDealNoValidator`: 预约单号有效性与是否被使用校验。
        - `ExternalDealNoValidator`: 外部订单号唯一性校验。
    - **申购/认购业务 (`buy`)**:
        - `PayMethodValidator`: 支付方式校验。
        - `BuyFeeValidator`: 申购费用计算与校验。
        - `FirstPaidRatioValidator`: 分次CALL产品首次实缴比例校验。
        - `NotFirstPaidValidator`: 分次CALL产品后续实缴资格校验。
    - **赎回业务 (`sell`)**:
        - `AvailableShareValidator`: 可用份额校验。
        - `SellMinHoldValidator`: 赎回后最低持有份额校验。
        - `RedeemDirectionValidator`: 赎回方向（电子/银行卡）校验。
    - **撤单业务 (`cancelorder`)**:
        - `CancelOrderTimeValidator`: 撤单时间窗口校验。
        - `CancelOrderPayMethodValidator`: 原订单支付方式相关校验（如储蓄罐支付）。
    - **修改订单 (`changeorder`)**:
        - `ChangePayMethodValidator`: 修改支付方式的规则校验。

# 4. 关键方法说明

### 4.1 `TradeValidatorHelper.doValidate(TradeContext context, List<String> chain)`
- **方法签名**: `public void doValidate(TradeContext context, List<String> chain)`
- **入参**:
    - `context` (`TradeContext`): 交易上下文对象，包含了本次校验所需的所有数据，如请求参数、客户信息、产品信息等。校验器在执行过程中可以读取和修改上下文。
    - `chain` (`List<String>`): 一个字符串列表，定义了本次需要执行的校验链。列表中的每个字符串对应一个原子校验器的唯一标识（定义于 `TradeValidatorEnum`）。
- **出参**: `void`
- **功能**: 该方法是校验框架的入口。它会严格按照 `chain` 列表中定义的顺序，依次从 `TradeValidatorMap` 中获取对应的校验器实例，并执行其 `validate` 方法。如果任何一个校验器执行失败并抛出 `ValidateException`，整个校验流程将立即中断并向外层抛出异常。

### 4.2 `TradeValidator.validate(TradeContext context)`
- **方法签名**: `void validate(TradeContext context)`
- **入参**:
    - `context` (`TradeContext`): 交易上下文对象。
- **出参**: `void`
- **功能**: 这是所有原子校验器必须实现的接口。每个实现类负责一项具体的校验任务。实现逻辑中，校验器从 `context` 中获取所需数据进行判断，如果校验不通过，应抛出 `ValidateException` 异常。

### 4.3 `ConcurrentValidator.lock(String lockKey)`
- **方法签名**: `public void lock(String lockKey)`
- **入参**:
    - `lockKey` (`String`): 用于加锁的唯一键。
- **出参**: `void`
- **功能**: 尝试获取一个分布式锁。如果获取失败（意味着有并发请求），则会抛出 `ValidateException`，错误码为 `CONCURRENT_ERROR`。常用于防止表单重复提交。

# 5. 关键业务逻辑说明

本校验模块的核心设计是基于**责任链模式**和**策略模式**的结合，并通过Spring的依赖注入和事件监听机制实现自动化注册，达到高内聚、低耦合的目标。

1.  **自动化注册**:
    - `TradeValidatorMap` 类实现了 Spring 的 `ApplicationListener<ContextRefreshedEvent>` 接口。
    - 当Spring容器初始化或刷新完成后，`onApplicationEvent` 方法会被触发。
    - 该方法会扫描容器中所有实现了 `TradeValidator` 接口的Bean。
    - 对于每个找到的校验器Bean，它会读取其类上的 `@TradeValidatorAnnotation` 注解，获取其中定义的 `TradeValidatorEnum` 值作为Key。
    - 最终，将校验器的唯一标识（Key）和校验器实例（Value）存入一个全局的 `ConcurrentHashMap` 中，完成校验器的自动注册。

2.  **校验链的构建与执行**:
    - 业务逻辑层（如某个 `Service`）在处理一笔交易时，首先需要明确该交易场景需要执行哪些校验。
    - 通过调用 `TradeValidatorHelper.buildChain(...)` 方法，将所需的 `TradeValidatorEnum` 枚举组合成一个 `List<String>`，这就是校验链。
    - 随后，业务逻辑层创建一个 `TradeContext` 实例，并填充必要的初始数据（如用户ID、产品代码、交易金额等）。
    - 最后，调用 `TradeValidatorHelper.doValidate(context, chain)` 启动校验流程。`TradeValidatorHelper` 会根据链中的顺序，逐一从 `TradeValidatorMap` 中取出对应的校验器并执行。

3.  **上下文数据传递**:
    - `TradeContext` 对象扮演着数据总线的角色。它在整个校验链中传递。
    - 前续的校验器可能会查询信息（如客户信息、产品详情）并将其放入 `TradeContext`。
    - 后续的校验器可以直接从 `TradeContext` 中获取这些信息使用，避免了重复查询，提高了效率。

# 6. 流程图 (PlantUML)

### 流程图 (Flowchart)
```plantuml
@startuml
title 交易校验模块处理流程 (Flowchart)

start
:Client/Service
发起交易请求;

:Service
1. 创建`TradeContext`并填充初始数据;
2. 根据业务场景，调用`TradeValidatorHelper.buildChain()`构建校验器列表;

:TradeValidatorHelper
3. 调用`doValidate(context, chain)`;

repeat
  :从校验链中获取下一个校验器标识(key);
  :从`TradeValidatorMap`中根据key获取校验器实例;
  :执行`validator.validate(context)`;
  if (校验不通过?) then (yes)
    :抛出`ValidateException`;
    end
  endif
repeat while (校验链中还有未执行的校验器?) is (no)

:Service
所有校验通过，继续执行后续核心业务逻辑;

stop
@enduml
```

### 时序图 (Sequence Diagram)
```plantuml
@startuml
title 交易校验模块时序图

skinparam sequence {
    ArrowColor #333
    ActorBorderColor #333
    LifeLineBorderColor #333
    ParticipantBorderColor #333
    BoxBorderColor #333
    BoxBackgroundColor #F8F8F8
}

actor "业务Service" as Service
participant "TradeValidatorHelper" as Helper
participant "TradeValidatorMap" as ValidatorMap
participant "TradeValidator\n(具体实现)" as Validator

Service -> Service: 1. 构建TradeContext和校验链
Service -> Helper: 2. doValidate(context, chain)
activate Helper

Helper -> Helper: 3. 遍历校验链
loop 每个校验器标识
    Helper -> ValidatorMap: 4. getValidator(validatorKey)
    activate ValidatorMap
    ValidatorMap --> Helper: 5. 返回校验器实例
    deactivate ValidatorMap

    Helper -> Validator: 6. validate(context)
    activate Validator
    Validator -> Validator: 7. 执行具体校验逻辑
    note right
        从 context 获取数据
        若校验失败则抛出 ValidateException
    end note
    Validator --> Helper: (校验通过)
    deactivate Validator
end

Helper --> Service: (所有校验通过)
deactivate Helper

Service -> Service: 8. 继续执行核心业务逻辑

@enduml
```

# 7. 被哪些其他模块调用
本校验模块主要被 `dtms-order-service` 模块中的各类业务 `Service`（例如 `CounterBuyService`, `CounterSellService`, `CancelOrderService` 等）调用，作为其执行核心业务逻辑前的第一道防线。 