# 本期人数与金额校验器 (`CurrentPeriodNumberAndAmountValidator`) 设计文档

## 1. 模块名称
CurrentPeriodNumberAndAmountValidator (本期人数、金额校验器)

## 2. 模块作用简述
本模块专门用于处理有募集期限制的基金申购场景。在客户提交申购请求时，它会校验本次申购是否会使得该募集期的总参与人数或总申购金额突破预设的上限。

## 3. 功能清单
- `validate(TradeContext context)`: 对支持预约的基金，执行本期参与人数和总金额的上限校验。
- `validateNumber(...)`: (私有) 负责具体的人数校验逻辑。
- `validateAmount(...)`: (私有) 负责具体的金额校验逻辑。

## 4. 关键方法说明
- **方法签名**
  ```java
  public void validate(TradeContext context)
  ```
- **入参**
  | 字段名   | 类型           | 是否必填 | 说明                                           |
  |----------|----------------|----------|------------------------------------------------|
  | `context`  | `TradeContext` | 是       | 交易上下文，包含基金代码、开放日、申购金额、客户号等信息。 |

- **出参**
  | 类型   | 含义             |
  |--------|------------------|
  | `void` | 无直接返回值，校验失败时抛出异常。 |

- **异常处理说明**
  - `ValidateException`: 当校验不通过时抛出。
    - `CURRENT_PURCHASE_NUMBER_EXCEEDED`: 本期购买的总人数已超过上限。
    - `CURRENT_PURCHASE_AMOUNT_EXCEEDED`: 本期购买的总金额已超过上限。
  - `AssertException`: 当基金配置（如本期人数、本期额度）缺失时，由 `AssertUtils.nonNullFundConfig` 抛出。

## 5. 关键业务逻辑说明
1.  **前置判断**：首先检查 `TradeContext` 中的 `isSupportPreBook` 标志，判断当前基金是否支持预约购买。如果不支持，则此项校验无意义，方法直接返回，不执行后续逻辑。
2.  **查询当期数据**：调用 `HwDealOrderDtlRepository.selectBuyInTransitGroupHkCustNo` 方法，查询指定基金在指定开放日内所有状态为"申请成功"和"付款成功"的订单，获取这些订单的客户号和金额列表。
3.  **人数上限校验 (`validateNumber`)**:
    a. 从 `TradeContext` 获取产品中心配置的**当期人数上限** (`currentNum`)。
    b. 从第2步的查询结果中提取所有不重复的客户号，形成**当期已有订单客户列表**。
    c. 调用 `HwCustFundBalRepository.queryHoldHkCustNoByFundCode` 查询该基金的所有**存量持仓客户列表**。
    d. 计算**当期新增投资人数**：逻辑为"当期已有订单客户"中，不属于"存量持仓客户"的人数。
    e. 计算**预估总人数**：`当期新增投资人数 + 1` (这里的 `+1` 代表当前这笔新的交易)。*注：代码中对当前客户是否已在当期订单中做了判断，避免重复计算*。
    f. 如果**预估总人数**大于**当期人数上限**，则校验失败，抛出 `ValidateException (CURRENT_PURCHASE_NUMBER_EXCEEDED)`。
4.  **金额上限校验 (`validateAmount`)**:
    a. 从 `TradeContext` 获取产品中心配置的**当期金额上限** (`currentLimit`)。
    b. 累加第2步查询到的所有订单的申购金额，得到**当期已购总金额**。
    c. 计算**预估总金额**：`当期已购总金额 + 本次申购金额`。
    d. 如果**预估总金额**大于**当期金额上限**，则校验失败，抛出 `ValidateException (CURRENT_PURCHASE_AMOUNT_EXCEEDED)`。

## 6. 流程图
```plantuml
@startuml
title CurrentPeriodNumberAndAmountValidator 校验流程
start
:获取交易上下文;
if (基金是否支持预约?) then (是)
    :查询当期已成功的订单列表;
    
    partition 人数校验 {
        :获取当期人数上限配置;
        :获取当期订单客户列表;
        :查询基金的存量持仓客户列表;
        :计算当期的新增投资人数;
        :计算加入当前客户后的预估总人数;
        if (预估总人数 > 人数上限?) then (是)
          :抛出"人数超限"异常;
          stop
        endif
    }

    partition 金额校验 {
        :获取当期金额上限配置;
        :累加当期所有订单总金额;
        :计算加入当前申购额后的预估总金额;
        if (预估总金额 > 金额上限?) then (是)
          :抛出"金额超限"异常;
          stop
        endif
    }
    
    :校验通过;
else (否)
    :跳过校验;
endif
end
@enduml
```

## 7. 时序图
```plantuml
@startuml
title CurrentPeriodNumberAndAmountValidator 时序图
actor "调用方" as caller
participant "CurrentPeriodNumberAndAmountValidator" as validator
participant "HwDealOrderDtlRepository" as orderRepo
participant "HwCustFundBalRepository" as balRepo

caller -> validator: validate(context)
activate validator

alt fund does not support pre-booking
    validator -->> caller: validation is skipped
    deactivate validator
    return
end

validator -> orderRepo: selectBuyInTransitGroupHkCustNo(...)
activate orderRepo
orderRepo --> validator: (payAndAppSuccessCountList)
deactivate orderRepo

' Number Validation
validator -> validator: validateNumber(...)
activate validator
validator -> balRepo: queryHoldHkCustNoByFundCode(...)
activate balRepo
balRepo --> validator: (holdHkCustNoList)
deactivate balRepo
alt projected number of investors > limit
    validator ->> caller: throws ValidateException (NUMBER_EXCEEDED)
    deactivate validator
    return
end
deactivate validator

' Amount Validation
validator -> validator: validateAmount(...)
activate validator
alt projected total amount > limit
    validator ->> caller: throws ValidateException (AMOUNT_EXCEEDED)
    deactivate validator
    return
end
deactivate validator

validator -->> caller: 校验通过
deactivate validator
@enduml
```

## 8. 异常处理机制
| 异常场景                           | 触发条件                                         | 处理方式                                      |
|------------------------------------|--------------------------------------------------|-----------------------------------------------|
| `CURRENT_PURCHASE_NUMBER_EXCEEDED`   | 加上当前申购后，总参与人数超过本期限制。         | 通过 `ExceptionUtils.throwValidateException` 抛出 `ValidateException`。 |
| `CURRENT_PURCHASE_AMOUNT_EXCEEDED`   | 加上当前申购后，总申购金额超过本期限制。         | 通过 `ExceptionUtils.throwValidateException` 抛出 `ValidateException`。 |
| `AssertException`                  | 基金未配置本期人数上限或金额上限。               | 通过 `AssertUtils.nonNullFundConfig` 抛出 `AssertException`。   |

## 9. 调用的公共模块或外部依赖
- **模块名称**: `HwDealOrderDtlRepository`
- **功能简述**: 订单明细表的数据访问仓库，用于查询特定条件下的订单信息。
- **模块名称**: `HwCustFundBalRepository`
- **功能简述**: 客户基金余额表的数据访问仓库，用于查询客户的持仓信息。

## 10. 被哪些其他模块调用
- 该校验器作为交易校验链的一部分，被 `TradeValidateManager` 在执行申购类交易时根据规则动态调用。 