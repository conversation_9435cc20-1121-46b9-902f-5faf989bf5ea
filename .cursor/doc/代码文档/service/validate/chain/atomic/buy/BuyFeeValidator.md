# 通用购买手续费校验器（BuyFeeValidator）

## 1. 模块名称
`BuyFeeValidator`

## 2. 模块作用简述
该模块是一个通用的购买交易手续费校验器。其核心职责是确保前端（或上游服务）传入的预估手续费与后端根据产品费率规则计算出的手续费完全一致。它适用于标准的购买流程。

## 3. 功能清单
- `validate(TradeContext context)`: 执行标准购买场景下的手续费校验。

## 4. 关键方法说明

### 4.1. `validate(TradeContext context)`
该方法是校验器的入口，实现了全部校验逻辑。

- **方法签名**: `public void validate(TradeContext context)`
- **入参**:
  | 字段名   | 类型         | 是否必填 | 说明                               |
  |----------|--------------|----------|------------------------------------|
  | `context`| `TradeContext` | 是       | 包含整个交易所需信息的上下文对象。 |
- **出参**: `void`
- **异常处理**:
  - `ValidateException`:
    - 当交易币种为日元但金额或手续费带有小数时抛出。
    - 当后端计算的手续费与前端传入的不一致时抛出。

## 5. 关键业务逻辑说明
1.  **构建上下文**: 首先，根据 `TradeContext` 中的信息（如基金代码、购买金额、客户信息等）构建一个手续费计算专用的上下文对象 `BuyFeeComputeContext`。
2.  **币种精度校验**: 检查交易币种是否为日元（JPY）。如果是，则严格校验购买金额和预估手续费的小数位数，必须为整数（小数位为0），否则抛出 `JPY_AMT_SCALE_ERROR` 异常。
3.  **计算手续费**: 调用核心的 `FeeComputeService.buyFeeCompute` 方法。此方法会根据基金的产品配置、客户类型、交易渠道、预约信息等多种因素，精确计算出当前交易应收取的手续费。
4.  **手续费一致性校验**:
    - 检查前端请求中是否传入了预估手续费（`estimateFee`）。
    - 如果传入了，则将其与后端第3步计算出的手续费进行 `compareTo` 比较。
    - 如果两者不相等，说明前端的计算逻辑或使用的费率与后端不一致。此时会记录一条详细的错误日志（包含入参手续费和计算手续费），并抛出 `FEE_ERROR` 异常，中断交易流程。
5.  **设置上下文**: 如果手续费校验通过（或前端未传入手续费），将后端计算出的包含详细信息（如原始费用、折扣、实收费用等）的 `BuyFeeComputeInfoVO` 对象设置回 `TradeContext` 中，供后续的订单创建和账务处理等步骤使用。

## 6. 流程图

```plantuml
@startuml
title BuyFeeValidator 校验流程
start
:构建手续费计算上下文;
if (是日元交易?) then (yes)
  :校验购买金额和手续费的小数位;
  if (小数位 > 0?) then (yes)
    :抛出"日元金额小数位错误"异常;
    stop
  endif
endif
:调用 FeeComputeService.buyFeeCompute 计算手续费;
if (前端是否传入了手续费?) then (yes)
  if (计算结果与入参不一致?) then (yes)
    :记录错误日志;
    :抛出"手续费错误"异常;
    stop
  endif
endif
:将计算出的手续费详情(VO)设置到上下文;
:结束;
stop
@enduml
```

## 7. 时序图

```plantuml
@startuml
title BuyFeeValidator 时序图
actor Client
participant TradeService as "交易主服务"
participant Validator as "BuyFeeValidator"
participant Context as "TradeContext"
participant FeeService as "FeeComputeService"

Client -> TradeService: 发起购买交易请求
TradeService -> Validator: validate(context)
Validator -> Context: 获取构建`BuyFeeComputeContext`所需信息
Context --> Validator: 返回信息

Validator -> FeeService: buyFeeCompute(buyFeeComputeContext, prebookDetail)
FeeService --> Validator: BuyFeeComputeInfoVO (计算出的手续费)

Validator -> Context: 获取前端传入的手续费
Context --> Validator: 返回前端手续费
Validator -> Validator: 比较手续费

alt "手续费不一致"
    Validator -> TradeService: throw ValidateException(FEE_ERROR)
end

Validator -> Context: setFeeComputeInfoVO(计算结果)
Validator --> TradeService: 校验通过
TradeService -> Client: 返回交易受理结果
@enduml
```

## 8. 异常处理机制
| 异常场景                           | 异常枚举/代码         | 处理方式                                    |
|------------------------------------|-----------------------|---------------------------------------------|
| 交易币种为日元，但金额有小数       | `JPY_AMT_SCALE_ERROR` | 抛出 `ValidateException`，中断交易流程。    |
| 后端计算的手续费与前端传入的不一致 | `FEE_ERROR`           | 抛出 `ValidateException`，中断交易流程。    |

## 9. 调用的公共模块或外部依赖
- **`FeeComputeService`**:
  - **功能简述**: 核心的手续费计算服务。`BuyFeeValidator` 强依赖此服务来获取权威的手续费计算结果，作为校验的基准。

## 10. 被哪些其他模块调用
- 作为交易校验责任链的一环，被校验链的发起者（如 `TradeService`）调用。它通常在一系列基础参数校验之后、核心业务逻辑执行之前被触发。 