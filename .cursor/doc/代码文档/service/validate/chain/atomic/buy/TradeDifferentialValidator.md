# TradeDifferentialValidator 设计文档

## 模块名称
`TradeDifferentialValidator`

## 模块作用简述
交易级差校验器，用于校验认/申购金额是否满足基金产品的首次购买或追加购买的金额级差（即步进金额）要求。

## 功能清单
- `validate(TradeContext context)`: 执行交易级差的核心校验逻辑。

## 关键方法说明
### `validate(TradeContext context)`
- **方法签名**: `public void validate(TradeContext context)`
- **入参**:
    | 字段名 | 类型 | 是否必填 | 说明 |
    |---|---|---|---|
    | context | `TradeContext` | 是 | 交易上下文，包含本次交易的所有信息。 |
- **`TradeContext` 中使用的关键信息**:
    - `context.getFundBasicInfoDTO()`: 基金基本信息，用于获取首次和追加购买级差。
    - `context.getFundLimitDTO()`: 基金限制信息，用于获取最低认/申购金额。
    - `context.getBuyBean().getBuyAmt()`: 本次购买的金额。
    - `context.getBuyBean().getFirstBuyFlagEnum()`: 首次购买标志。
- **出参**: `void`。如果校验不通过，则直接抛出 `ValidateException` 异常。
- **异常处理说明**:
    - `ExceptionEnum.NOT_SATISFY_FIRST_PURCHASE_DIFFERENTIAL`: 首次购买金额不满足级差要求时抛出。
    - `ExceptionEnum.NOT_SATISFY_ADD_PURCHASE_DIFFERENTIAL`: 追加购买金额不满足级差要求时抛出。

## 关键业务逻辑说明
1. 从交易上下文 `TradeContext` 中获取基金基本信息、基金限制信息以及本次的购买详情（金额、是否首次购买等）。
2. 判断本次购买是否为"首次购买"。
3. **如果是首次购买**:
   - 计算购买金额与"最低认/申购金额"的差额。
   - 获取"首次购买级差" (`firstPurchaseDiffer`)。
4. **如果是追加购买**:
   - 计算购买金额与"最低追加金额"的差额。
   - 获取"追加购买级差" (`appendPurchaseDiffer`)。
5. 检查获取到的级差值是否有效（非空且不为0）。
6. 如果级差值有效，则校验第3步或第4步计算出的差额是否能被级差整除。
7. 如果不能整除，则说明不满足级差要求，根据是首次购买还是追加购买，抛出相应的 `ValidateException` 异常，并中断流程。
8. 如果校验通过，则方法正常结束。

## 流程图 (PlantUML)
```plantuml
@startuml
title TradeDifferentialValidator 校验流程
start
:获取交易上下文信息;
if (是否首次购买?) then (是)
    :result = 购买金额 - 最小申购金额;
    :differential = 首次购买级差;
else (否)
    :result = 购买金额 - 最小追加金额;
    :differential = 追加购买级差;
endif

if (级差值有效 (非空且非0)?) then (是)
  if (result % differential != 0?) then (是)
    if (是否首次购买?) then (是)
        :抛出"不满足首次购买级差"异常;
    else (否)
        :抛出"不满足追加购买级差"异常;
    endif
    stop
  else (校验通过)
  endif
else (无需校验)
endif
:校验通过;
end
@enduml
```

## 时序图 (PlantUML)
```plantuml
@startuml
title TradeDifferentialValidator 时序图
participant "ValidatorChain" as Chain
participant "TradeDifferentialValidator" as Validator
participant "TradeContext" as Context
participant "ExceptionUtils" as ExUtils

Chain ->> Validator: validate(context)
activate Validator

Validator ->> Context: getFundBasicInfoDTO()
activate Context
Context -->> Validator: fundBasicInfoDTO
deactivate Context

Validator ->> Context: getFundLimitDTO()
activate Context
Context -->> Validator: fundLimitDTO
deactivate Context

Validator ->> Context: getBuyBean()
activate Context
Context -->> Validator: buyBean
deactivate Context

alt 首次购买且不满足级差
    Validator ->> ExUtils: throwValidateException(...)
    activate ExUtils
    ExUtils -->> Chain: ValidateException
    deactivate ExUtils
end

alt 追加购买且不满足级差
    Validator ->> ExUtils: throwValidateException(...)
    activate ExUtils
    ExUtils -->> Chain: ValidateException
    deactivate ExUtils
end

deactivate Validator
@enduml
```

## 异常处理机制
校验器通过 `ExceptionUtils.throwValidateException` 方法抛出 `ValidateException`。该工具方法会封装统一的异常信息，包括错误码 (`ExceptionEnum`) 和错误描述 (`MessageTemplates`)，中断当前校验流程。

## 调用的公共模块或外部依赖
- **`TradeContext`**: 交易上下文，用于在校验链中传递数据。
- **`MathUtils`**: `BigDecimal` 数学计算工具类，用于金额减法。
- **`ExceptionUtils`**: 统一异常抛出工具类。
- **`FundBasicInfoDTO`, `FundLimitDTO`**: 数据传输对象，由外部服务（如产品中心）提供，并通过 `TradeContext` 传递。

## 被哪些其他模块调用
- 该校验器作为原子校验规则，被 `TradeValidatorAnnotation` 注解标记，由交易校验链 (`TradeValidateManager` 或类似服务) 根据配置动态调用。 