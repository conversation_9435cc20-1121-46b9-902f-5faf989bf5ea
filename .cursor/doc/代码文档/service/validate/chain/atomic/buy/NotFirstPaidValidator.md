## 1. 模块名称
NotFirstPaidValidator

## 2. 模块作用简述
本校验器专门用于处理**非首次实缴**（包括后续实缴和批量实缴）场景，其核心职责是：
1.  校验客户是否已有认购记录。
2.  为后续流程准备并设置正确的认购总额。
3.  校验本次实缴的比例是否已在产品日历中配置。

## 3. 功能清单
- **validate(TradeContext context)**: 针对实缴业务（业务码 `112B`），查询客户认购汇总信息，校验其存在性，并将汇总金额设置到交易上下文中，同时检查当期实缴比例是否配置。

## 4. 关键方法说明
- ### `validate(TradeContext context)`
  - **方法签名**: `public void validate(TradeContext context)`
  - **入参**:
    | 字段名   | 类型           | 是否必填 | 说明                                                               |
    |----------|----------------|----------|--------------------------------------------------------------------|
    | `context`  | `TradeContext` | 是       | 交易校验上下文，包含了此次交易的所有信息。 |
  - **出参**: `void` - 无返回值。
  - **异常处理说明**: 如果是实缴业务但找不到认购汇总记录，或未配置当期实缴比例，将抛出 `ValidateException`。

## 5. 关键业务逻辑说明
1.  **业务场景过滤**: 首先检查 `TradeContext` 中的业务码（`businessCodeEnum`），如果不是实缴业务（`BusinessCodeEnum._112B`），则直接返回，不执行任何校验逻辑。
2.  **查询认购汇总**: 如果是实缴业务，则调用 `HwSubPaidSumInfoRepository`，根据客户号（`hkCustNo`）、基金交易账号（`fundTxAcctNo`）和基金代码（`fundCode`）查询 `hw_sub_paid_sum_info` 表，获取客户对该基金的认购汇总记录 `HwSubPaidSumInfoPO`。
3.  **校验认购记录**:
    -   检查查询结果 `hwSubPaidSumInfoPO` 是否为 `null`。
    -   检查汇总记录中的累计认购金额 `subTotalAmt` 是否为 `null`。
    -   如果任一条件为真，说明客户没有在途的认购记录，无法进行实缴，抛出 `ValidateException(ExceptionEnum.SUB_SUM_INFO_NOT_EXISTS)`。
4.  **设置认购总额**: 校验通过后，将查询到的累计认购金额 `hwSubPaidSumInfoPO.getSubTotalAmt()` 设置到当前交易的购买信息 `context.getBuyBean().setSubAmt(...)` 中。这一步至关重要，它确保了后续所有基于认购金额的计算（如费用计算）都使用正确的基数。
5.  **校验当期实缴比例**:
    -   检查 `TradeContext` 中的基金交易日历信息 `FundTradeCalendarInfoDTO` 是否为 `null`。
    -   检查日历信息中的当期实缴比例 `currentPayInRatio` 是否为 `null`。
    -   如果任一条件为真，说明产品未配置本次实缴的比例，抛出 `ValidateException(ExceptionEnum.NOT_SET_THIS_PERIOD_PAID_RATE)`。
6.  所有检查通过后，方法正常返回。

## 6. 流程图
```plantuml
@startuml
title NotFirstPaidValidator 校验流程
start
:获取当前交易业务码;
if (是实缴业务(112B)?) then (yes)
  :根据客户号、交易账号、基金代码查询认购汇总信息;
  if (认购汇总信息不存在 或 汇总金额为空?) then (yes)
    :抛出 ValidateException(SUB_SUM_INFO_NOT_EXISTS);
    stop
  else (no)
    :将查询到的认购汇总金额设置到上下文的BuyBean中;
    :获取基金交易日历信息;
    if (日历信息为空 或 当期实缴比例未配置?) then (yes)
      :抛出 ValidateException(NOT_SET_THIS_PERIOD_PAID_RATE);
      stop
    else (no)
      :校验通过;
    endif
  endif
else (no)
  :非实缴业务，跳过校验;
endif
stop
@enduml
```

## 7. 时序图
```plantuml
@startuml
title 非首次实缴校验时序
participant ValidatorChain as "校验链"
participant NotFirstPaidValidator as "非首次实缴校验器"
participant HwSubPaidSumInfoRepository as "认购汇总信息Repository"
participant TradeContext as "交易上下文"

ValidatorChain -> NotFirstPaidValidator: validate(context)
activate NotFirstPaidValidator

alt 是实缴业务(112B)
    NotFirstPaidValidator -> HwSubPaidSumInfoRepository : selectByFundTxAcctNoAndFundCode(...)
    activate HwSubPaidSumInfoRepository
    HwSubPaidSumInfoRepository -->> NotFirstPaidValidator : hwSubPaidSumInfoPO
    deactivate HwSubPaidSumInfoRepository
    
    alt 认购汇总信息不存在
        NotFirstPaidValidator -->> ValidatorChain : throw ValidateException
    else 认购信息存在
        NotFirstPaidValidator -> TradeContext : getBuyBean().setSubAmt(...)
        alt 当期实缴比例未配置
            NotFirstPaidValidator -->> ValidatorChain : throw ValidateException
        else 校验通过
             NotFirstPaidValidator -->> ValidatorChain: void
        end
    end
else 非实缴业务
     NotFirstPaidValidator -->> ValidatorChain: void
end

deactivate NotFirstPaidValidator
@enduml
```

## 8. 异常处理机制
- **主要异常**: `com.howbuy.dtms.order.service.commom.exception.ValidateException`
- **触发场景**:
    1.  当前是实缴业务，但在数据库中找不到对应的客户认购汇总信息。
    2.  当前是实缴业务，但在产品日历中找不到为本次操作配置的实缴比例（`currentPayInRatio`）。
- **处理方式**: 抛出 `ValidateException`，并附带相应的错误枚举（`SUB_SUM_INFO_NOT_EXISTS` 或 `NOT_SET_THIS_PERIOD_PAID_RATE`）。

## 9. 调用的公共模块或外部依赖
- **`com.howbuy.dtms.order.service.repository.HwSubPaidSumInfoRepository`**: 仓储层依赖，用于查询 `hw_sub_paid_sum_info` 数据库表，获取客户的认购汇总数据。

## 10. 被哪些其他模块调用
- 该校验器作为原子校验规则，被注入到Spring容器中，并由交易校验链管理器根据业务场景（特别是实缴业务）动态调用。 