# 认缴手续费校验器（AppSubBuyFeeValidator）

## 1. 模块名称
`AppSubBuyFeeValidator`

## 2. 模块作用简述
该模块是交易验证链中的一个原子校验器，主要负责 **认缴** 场景下的手续费校验。它确保当手续费按"认缴"方式计算时，前端传入的手续费与后端计算的结果一致。同时，它也处理了非认缴计算方式下的逻辑。

## 3. 功能清单
- `validate(TradeContext context)`: 执行认缴手续费相关的核心校验逻辑。

## 4. 关键方法说明

### 4.1. `validate(TradeContext context)`
该方法是校验器的唯一入口，实现了所有的校验逻辑。

- **方法签名**: `public void validate(TradeContext context)`
- **入参**:
  | 字段名   | 类型         | 是否必填 | 说明                               |
  |----------|--------------|----------|------------------------------------|
  | `context`| `TradeContext` | 是       | 包含整个交易所需信息的上下文对象。 |
- **出参**: `void`
- **异常处理**:
  - `ValidateException`: 当校验不通过时抛出。
    - 日元金额小数位错误。
    - 中台业务码不适用（非认缴/实缴业务）。
    - 认缴/实缴手续费类型不正确。
    - 认缴手续费类型配置错误（例如，非认缴类型却计算出了费率）。
    - 前端传入的手续费与后端计算的不一致。

## 5. 关键业务逻辑说明
1.  **构建上下文**: 首先，根据 `TradeContext` 构建一个 `BuyFeeComputeContext`，用于后续的手续费计算。
2.  **币种精度校验**: 检查交易币种是否为日元（JPY）。如果是，则校验交易金额和手续费的小数位数，确保为整数。
3.  **业务码校验**: 校验当前交易的中台业务码是否为认缴（`112A`）或实缴（`112B`）。
4.  **手续费类型校验**: 校验前端入参中的 `subAndFirstPaidFeeRateType` 是否为有效的枚举值。
5.  **查询预约信息**: 调用 `HkPrebookOuterService` 获取客户的基金预约信息 `HkPreBookDetailDTO`。
6.  **计算手续费**: 调用 `FeeComputeService` 的 `buyFeeCompute` 方法，结合交易上下文和预约信息，计算出手续费 `BuyFeeComputeInfoVO`。
7.  **费率配置校验**:
    - 检查前端传入的手续费计算类型。如果类型 **不是** "按认缴计算"（`SUBSCRIBE_PAY_TYPE`），但后端计算出的费率 `feeRate` 却大于0，这被视为一种配置错误（即，非认缴模式下不应该收取认缴费），因此抛出异常。
8.  **手续费一致性校验**:
    - 如果前端传入的类型 **是** "按认缴计算"，则将后端计算出的手续费 `estimateFee` 与前端传入的手续费进行比较。
    - 如果两者不相等，记录错误日志并抛出 `FEE_ERROR` 异常。
    - 如果相等，则将后端计算出的完整手续费信息 `feeComputeInfoVO` 设置到交易上下文中。
9.  **处理非认缴计算场景**: 如果前端传入的类型 **不是** "按认缴计算"，则说明当前交易不在此校验器的主逻辑范围内。此时，会创建一个手续费为0的 `BuyFeeComputeInfoVO` 对象并设置到上下文中，以确保后续流程的统一性。

## 6. 流程图

```plantuml
@startuml
title AppSubBuyFeeValidator 校验流程
start
:构建手续费计算上下文;
if (是日元交易?) then (yes)
  :校验金额和手续费小数位;
  if (小数位 > 0?) then (yes)
    :抛出"日元金额小数位错误"异常;
    stop
  endif
endif
:校验中台业务码 (112A或112B);
:校验入参的"认缴/实缴手续费类型";
:调用HkPrebookOuterService查询预约信息;
:调用FeeComputeService计算手续费;

if (入参费率类型 != "按认缴计算" AND 计算出的费率 > 0?) then (yes)
  :抛出"认缴手续费类型配置不正确"异常;
  stop
endif

if (入参费率类型 == "按认缴计算"?) then (yes)
  if (计算出的手续费与入参不一致?) then (yes)
    :记录日志;
    :抛出"手续费错误"异常;
    stop
  else (no)
    :将计算结果VO设置到上下文;
    :结束;
    stop
  endif
else (no)
  :构造手续费为0的VO;
  :设置到上下文;
  :结束;
  stop
endif
@enduml
```

## 7. 时序图

```plantuml
@startuml
title AppSubBuyFeeValidator 时序图
actor Client
participant TradeService as "交易主服务"
participant Validator as "AppSubBuyFeeValidator"
participant Context as "TradeContext"
participant PrebookService as "HkPrebookOuterService"
participant FeeService as "FeeComputeService"

Client -> TradeService: 发起认缴交易请求
TradeService -> Validator: validate(context)
Validator -> Context: 获取交易信息
Context --> Validator: 返回交易信息

Validator -> PrebookService: getHkPreBookDetail(...)
PrebookService --> Validator: HkPreBookDetailDTO (预约信息)

Validator -> FeeService: buyFeeCompute(context, prebookDetail)
FeeService --> Validator: BuyFeeComputeInfoVO (计算出的手续费)

alt "费率类型为按认缴计算"
    Validator -> Validator: 比较计算手续费与入参手续费
    alt "手续费不一致"
        Validator -> TradeService: throw ValidateException
    end
    Validator -> Context: setFeeComputeInfoVO(计算结果)
else "其他费率类型"
    Validator -> Context: setFeeComputeInfoVO(手续费为0)
end

Validator --> TradeService: 校验通过
TradeService -> Client: 返回交易受理结果
@enduml
```

## 8. 异常处理机制
| 异常场景                           | 异常枚举/代码     | 处理方式                                 |
|------------------------------------|-------------------|------------------------------------------|
| 日元交易金额有小数                 | `JPY_AMT_SCALE_ERROR` | 抛出 `ValidateException`，中断交易。     |
| 中台业务码不适用                   | `PARAMS_ERROR`    | 抛出 `ValidateException`，提示业务码错误。 |
| 认缴/实缴手续费类型无效            | `Assert.notNull`  | 抛出 `IllegalArgumentException`。        |
| 非认缴类型却计算出了认缴费率       | `FEE_ERROR`       | 抛出 `ValidateException`，提示配置错误。   |
| 后端计算的手续费与前端传入的不一致 | `FEE_ERROR`       | 抛出 `ValidateException`，提示手续费错误。   |

## 9. 调用的公共模块或外部依赖
- **`FeeComputeService`**:
  - **功能简述**: 核心的手续费计算服务。`AppSubBuyFeeValidator` 依赖它来根据复杂的业务规则计算手续费。
- **`HkPrebookOuterService`**:
  - **功能简述**: 外部服务接口，用于查询客户的预约信息。手续费的计算可能依赖于预约时的某些条款，因此需要调用此服务。

## 10. 被哪些其他模块调用
- 作为交易校验责任链的一环，被校验链的发起者调用，用于在执行认缴业务前进行手续费合法性验证。 