# PayMethodValidator 设计文档

## 模块名称
`PayMethodValidator`

## 模块作用简述
支付方式校验器。该模块负责校验交易中使用的支付方式是否有效。核心功能是当支付方式为"储蓄罐支付"时，检查客户是否已签署相应的服务协议。

## 功能清单
- `validate(TradeContext context)`: 执行支付方式的核心校验逻辑。

## 关键方法说明
### `validate(TradeContext context)`
- **方法签名**: `public void validate(TradeContext context)`
- **入参**:
    | 字段名 | 类型 | 是否必填 | 说明 |
    |---|---|---|---|
    | context | `TradeContext` | 是 | 交易上下文，包含本次交易的所有信息。 |
- **`TradeContext` 中使用的关键信息**:
    - `context.getBuyBean().getPayMethodEnum()`: 客户选择的支付方式枚举。
    - `context.getHkCustNo()`: 客户的香港客户号，用于查询协议。
- **出参**: `void`. 如果校验不通过，则直接抛出 `ValidateException` 异常。
- **异常处理说明**:
    - `AssertUtils` 会在 `payMethodEnum` 为 `null` 时抛出 `ValidateException`。
    - `ExceptionEnum.NOT_SIGN_CXG_AGREEMENT_ERROR`: 当支付方式为储蓄罐支付，但客户未签约时抛出。

## 关键业务逻辑说明
1. 从交易上下文 `TradeContext` 中获取支付方式 `payMethodEnum`.
2. 使用 `AssertUtils.nonNullParam` 确保支付方式不为空，否则抛出异常。
3. 判断支付方式是否为"储蓄罐支付" (`PayMethodEnum.OVERSEAS_CXG`).
4. **如果是储蓄罐支付**:
   - 从上下文中获取客户号 `hkCustNo`.
   - 调用 `CounterOrderQueryOuterService` 外部服务，传入客户号，查询客户的储蓄罐协议签约状态。
   - 如果外部服务返回未签约 (`hasSignCxg` 为 `false`)，则构造一个 `ValidateException` 并抛出，中断交易流程。
5. 如果支付方式不是储蓄罐支付，或者客户已签约，则校验通过，方法正常结束。

## 流程图 (PlantUML)
```plantuml
@startuml
title PayMethodValidator 校验流程
start
:获取交易上下文信息;
:获取支付方式 (payMethodEnum);
if (支付方式是否为空?) then (是)
    :抛出"支付方式不存在"异常;
    stop
else (否)
endif

if (支付方式 == '储蓄罐支付'?) then (是)
    :获取客户号 (hkCustNo);
    :调用外部服务查询储蓄罐签约状态;
    if (是否已签约?) then (是)
        :校验通过;
    else (否)
        :抛出"未签署储蓄罐协议"异常;
        stop
    endif
else (否)
    :校验通过;
endif
end
@enduml
```

## 时序图 (PlantUML)
```plantuml
@startuml
title PayMethodValidator 时序图
participant "ValidatorChain" as Chain
participant "PayMethodValidator" as Validator
participant "TradeContext" as Context
participant "CounterOrderQueryOuterService" as OuterService

Chain ->> Validator: validate(context)
activate Validator

Validator ->> Context: getBuyBean().getPayMethodEnum()
activate Context
Context -->> Validator: payMethodEnum
deactivate Context

alt 储蓄罐支付
    Validator ->> Context: getHkCustNo()
    activate Context
    Context -->> Validator: hkCustNo
    deactivate Context

    Validator ->> OuterService: hasSignCxg(hkCustNo)
    activate OuterService
    OuterService -->> Validator: false (未签约)
    deactivate OuterService

    Validator -->> Chain: ValidateException
end

deactivate Validator
@enduml
```

## 异常处理机制
- **参数校验**: 使用 `AssertUtils.nonNullParam` 在方法入口处对关键参数进行非空检查，如果为 `null` 则直接抛出 `ValidateException`。
- **业务校验**: 在业务逻辑判断失败后（如未签约），显式地 `throw new ValidateException(ExceptionEnum)` 来中断流程并返回具体的业务错误信息。

## 调用的公共模块或外部依赖
- **`TradeContext`**: 交易上下文，用于在校验链中传递数据。
- **`AssertUtils`**: 断言工具类，用于进行快速的参数非空校验。
- **`CounterOrderQueryOuterService`**: 外部服务接口，用于查询中台系统的订单相关信息，此处用于查询储蓄罐签约状态。
- **`PayMethodEnum`**: 支付方式枚举类，定义了系统支持的支付方式。

## 被哪些其他模块调用
- 该校验器作为原子校验规则，被 `TradeValidatorAnnotation` 注解标记，由交易校验链 (`TradeValidateManager` 或类似服务) 根据配置动态调用。 