## 1. 模块名称
FundFeeRateConfigValidator

## 2. 模块作用简述
本校验器负责检查基金的交易费率是否已在产品系统中正确配置，支持"普通产品"和"分次Call产品"两种模式。

## 3. 功能清单
- **validate(TradeContext context)**: 根据产品类型（普通/分次Call），调用外部产品服务查询对应的费率配置，并确保至少存在一个有效的费率（费率值大于等于0）。

## 4. 关键方法说明
- ### `validate(TradeContext context)`
  - **方法签名**: `public void validate(TradeContext context)`
  - **入参**:
    | 字段名   | 类型           | 是否必填 | 说明                               |
    |----------|----------------|----------|------------------------------------|
    | `context`  | `TradeContext` | 是       | 交易校验上下文，包含所有交易信息。 |
  - **出参**: `void` - 无返回值。
  - **异常处理说明**: 如果费率未配置或所有配置的费率均无效（小于0），将抛出 `ValidateException`。

## 5. 关键业务逻辑说明
1.  整个校验逻辑包裹在 `try-catch` 块中，任何内部发生的异常（如外部服务调用失败）最终都会被捕获并统一抛出 `ValidateException(ExceptionEnum.TRADE_RATE_NOT_CONFIGURED)`。
2.  从 `TradeContext` 中获取基金基本信息 `FundBasicInfoDTO`，判断其 `gradationCall` 标志。
3.  **分次Call产品逻辑** (`gradationCall` 为 "1"):
    a. 调用 `getFundFeeRateDTO` 方法，分别查询**认缴费率**（业务码 `112A`）和**实缴费率**（业务码 `112B`）。
    b. **检查配置存在性**：如果认缴费率列表和实缴费率列表均为空，则认为费率未配置，抛出异常。
    c. **检查费率有效性**：
        - 分别检查认缴费率列表和实缴费率列表中，是否存在至少一个 `feeRate >= 0` 的有效费率。
        - 如果认缴和实缴两种费率配置均无效（即不存在 `feeRate >= 0` 的项），则抛出异常。
4.  **普通产品逻辑**:
    a. 调用 `getFundFeeRateDTO` 方法，根据 `TradeContext` 中当前交易的 `productBusinessCode` 查询交易费率。
    b. **检查配置存在性**：如果返回的费率列表为空，抛出异常。
    c. **检查费率有效性**：检查返回的费率列表中，是否存在至少一个 `feeRate >= 0` 的有效费率。如果不存在，抛出异常。
5.  如果所有检查均通过，则方法正常返回。

- ### `getFundFeeRateDTO(TradeContext context, String busiCode)`
  - 这是一个私有辅助方法，用于封装对外部服务 `QueryFundInfoOuterService` 的调用。
  - 它构建 `QueryFundFeeRateRequestDTO` 请求对象，设置基金代码、业务码、投资者类型等参数，然后调用 `queryFundInfoOuterService.queryFundFeeRate` 获取费率列表。

## 6. 流程图
```plantuml
@startuml
title FundFeeRateConfigValidator 校验流程
start
:从上下文获取基金基本信息;
:判断是否为分次Call产品;
if (是) then (true)
    :查询认缴费率(112A);
    :查询实缴费率(112B);
    if (认缴和实缴费率均未配置?) then (yes)
        :抛出异常(TRADE_RATE_NOT_CONFIGURED);
        stop
    endif
    :检查认缴费率列表是否存在有效费率(>=0);
    :检查实缴费率列表是否存在有效费率(>=0);
    if (认缴和实缴费率均无效?) then (yes)
        :抛出异常(TRADE_RATE_NOT_CONFIGURED);
        stop
    endif
else (否)
    :根据交易业务码查询费率;
    if (费率未配置?) then (yes)
        :抛出异常(TRADE_RATE_NOT_CONFIGURED);
        stop
    endif
    :检查费率列表是否存在有效费率(>=0);
    if (不存在有效费率?) then (yes)
        :抛出异常(TRADE_RATE_NOT_CONFIGURED);
        stop
    endif
endif
:校验通过;
stop
@enduml
```

## 7. 时序图
```plantuml
@startuml
title 基金费率配置校验时序
participant ValidatorChain as "校验链"
participant FundFeeRateConfigValidator as "费率配置校验器"
participant QueryFundInfoOuterService as "产品中心查询服务"

ValidatorChain -> FundFeeRateConfigValidator : validate(context)
activate FundFeeRateConfigValidator

alt 分次Call产品
    FundFeeRateConfigValidator -> QueryFundInfoOuterService : queryFundFeeRate(req_112A)
    activate QueryFundInfoOuterService
    QueryFundInfoOuterService -->> FundFeeRateConfigValidator : subFundFeeRateDTOS
    deactivate QueryFundInfoOuterService
    
    FundFeeRateConfigValidator -> QueryFundInfoOuterService : queryFundFeeRate(req_112B)
    activate QueryFundInfoOuterService
    QueryFundInfoOuterService -->> FundFeeRateConfigValidator : paidFundFeeRateDTOS
    deactivate QueryFundInfoOuterService
else 普通产品
    FundFeeRateConfigValidator -> QueryFundInfoOuterService : queryFundFeeRate(req_busiCode)
    activate QueryFundInfoOuterService
    QueryFundInfoOuterService -->> FundFeeRateConfigValidator : fundFeeRateDTOS
    deactivate QueryFundInfoOuterService
end

alt 费率未配置或无效
    FundFeeRateConfigValidator -->> ValidatorChain : throw ValidateException
    deactivate FundFeeRateConfigValidator
else 校验通过
    FundFeeRateConfigValidator -->> ValidatorChain : void
    deactivate FundFeeRateConfigValidator
end
@enduml
```

## 8. 异常处理机制
- **主要异常**: `com.howbuy.dtms.order.service.commom.exception.ValidateException`
- **触发场景**:
    1.  调用外部费率查询服务时发生任何异常。
    2.  对于普通产品，没有查询到任何费率配置，或者所有配置的费率都小于0。
    3.  对于分次Call产品，认缴费率和实缴费率两种配置同时缺失。
    4.  对于分次Call产品，虽然有配置，但所有配置项（包括认缴和实缴）的费率都小于0。
- **处理方式**: 抛出 `ValidateException`，并附带错误枚举 `ExceptionEnum.TRADE_RATE_NOT_CONFIGURED`。

## 9. 调用的公共模块或外部依赖
- **`com.howbuy.dtms.order.service.outerservice.dtmsproduct.fund.QueryFundInfoOuterService`**: 核心依赖，用于调用产品中心微服务，查询基金的费率配置信息。

## 10. 被哪些其他模块调用
- 该校验器作为原子校验规则，被注入到Spring容器中，并由交易校验链管理器根据业务场景动态调用。 