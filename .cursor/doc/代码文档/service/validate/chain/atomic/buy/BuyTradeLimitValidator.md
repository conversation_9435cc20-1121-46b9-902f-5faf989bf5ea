# 交易限额校验器 (`BuyTradeLimitValidator`) 设计文档

## 1. 模块名称
BuyTradeLimitValidator (交易限额校验器)

## 2. 模块作用简述
本模块负责在客户申购基金时，校验其申购金额是否符合该基金产品的交易限额规定，包括单笔最高申购金额、首次最低申购金额以及追加申购的最低金额。

## 3. 功能清单
- `validate(TradeContext context)`: 执行基金交易限额相关的业务校验。

## 4. 关键方法说明
- **方法签名**
  ```java
  public void validate(TradeContext context)
  ```
- **入参**
  | 字段名   | 类型           | 是否必填 | 说明                                           |
  |----------|----------------|----------|------------------------------------------------|
  | `context`  | `TradeContext` | 是       | 交易上下文，包含申购金额、首次购买标志、基金代码等信息。 |

- **出参**
  | 类型   | 含义             |
  |--------|------------------|
  | `void` | 无直接返回值，校验失败时抛出异常。 |

- **异常处理说明**
  - `ValidateException`: 当校验不通过时抛出。
    - `NET_APP_AMT_MORE_THAN_MAX`: 申购金额超过单笔最高申购金额。
    - `NET_APP_AMT_LESS_THAN_MIN`: (首次购买)申购金额低于单笔最低申购金额。
    - `NET_APP_AMT_LESS_THAN_ADD_MIN`: (追加购买)申购金额低于单笔最低追加申购金额。

## 5. 关键业务逻辑说明
1.  首先调用 `TradeContextBuilder.fillFundLimit` 方法，根据上下文中的基金代码，查询并填充基金的交易限额信息 (`FundLimitDTO`) 到 `TradeContext` 中。
2.  从 `TradeContext` 中获取基金限额对象 (`FundLimitDTO`) 和申购业务详情 (`BuyBean`)。
3.  **最高金额校验**：将当前申购金额 `buyAmt` 与基金的最高申购金额 `maxAppAmt` 进行比较。如果申购金额大于最高限额，则校验失败，抛出 `ValidateException`。
4.  **最低金额校验**：
    a. 根据 `firstBuyFlag` 判断本次购买是否为客户的首次购买。
    b. **首次购买**：如果为是，则将申购金额与基金的`首次`最低申购金额 `minAppAmt` 进行比较。如果申购金额低于该限额，则校验失败，抛出 `ValidateException`。
    c. **追加购买**：如果为否，则将申购金额与基金的`追加`最低申购金额 `minSuppleAmt` 进行比较。如果申购金额低于该限额，则校验失败，抛出 `ValidateException`。
5.  如果所有限额校验均通过，方法正常结束。
6.  *注：在比较前，会处理限额字段为`null`的情况，将其默认为 `0` 以避免空指针异常。*

## 6. 流程图
```plantuml
@startuml
title BuyTradeLimitValidator 校验流程
start
:获取交易上下文 (TradeContext);
:调用 builder 填充基金限额信息;
:获取申购金额 (buyAmt) 及是否首次购买标志;
:获取最高(maxAppAmt), 最低(minAppAmt), 追加(minSuppleAmt)限额;

if (申购金额 > 最高申购金额?) then (是)
  :抛出"超过最高申购金额"异常;
  stop
endif

if (是否首次购买?) then (是)
  if (申购金额 < 首次最低金额?) then (是)
    :抛出"低于首次最低申购金额"异常;
    stop
  endif
else (否)
  if (申购金额 < 追加最低金额?) then (是)
    :抛出"低于追加最低申购金额"异常;
    stop
  endif
endif

:校验通过;
end
@enduml
```

## 7. 时序图
```plantuml
@startuml
title BuyTradeLimitValidator 时序图
actor "调用方" as caller
participant "BuyTradeLimitValidator" as validator
participant "TradeContextBuilder" as builder

caller -> validator: validate(context)
activate validator

validator -> builder: fillFundLimit(context)
activate builder
builder --> validator: (context is updated with FundLimitDTO)
deactivate builder

alt buyAmt > maxAppAmt
    validator ->> caller: throws ValidateException (NET_APP_AMT_MORE_THAN_MAX)
end

if isFirstBuy
    alt buyAmt < minAppAmt
        validator ->> caller: throws ValidateException (NET_APP_AMT_LESS_THAN_MIN)
    end
else
    alt buyAmt < minSuppleAmt
        validator ->> caller: throws ValidateException (NET_APP_AMT_LESS_THAN_ADD_MIN)
    end
end

validator -->> caller: 校验通过
deactivate validator
@enduml
```

## 8. 异常处理机制
| 异常场景                      | 触发条件                               | 处理方式                                      |
|-------------------------------|----------------------------------------|-----------------------------------------------|
| `NET_APP_AMT_MORE_THAN_MAX`     | 申购金额 > 单笔最高申购金额。          | 通过 `ExceptionUtils.throwValidateException` 抛出 `ValidateException`。 |
| `NET_APP_AMT_LESS_THAN_MIN`     | 首次购买时，申购金额 < 单笔最低申购金额。 | 通过 `ExceptionUtils.throwValidateException` 抛出 `ValidateException`。 |
| `NET_APP_AMT_LESS_THAN_ADD_MIN` | 追加购买时，申购金额 < 单笔最低追加金额。 | 通过 `ExceptionUtils.throwValidateException` 抛出 `ValidateException`。 |

## 9. 调用的公共模块或外部依赖
- **模块名称**: `TradeContextBuilder`
- **功能简述**: 交易上下文构建器，负责在校验流程中丰富上下文信息，如此处的填充基金限额信息。

## 10. 被哪些其他模块调用
- 该校验器作为交易校验链的一部分，被 `TradeValidateManager` 在执行申购类交易时根据规则动态调用。 