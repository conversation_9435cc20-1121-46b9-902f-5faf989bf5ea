## 1. 模块名称
FirstPaidRatioValidator

## 2. 模块作用简述
本校验器负责在交易流程中，验证用户实际支付的购买金额（实缴金额）是否严格等于根据"认购总额"和"首次实缴比例"计算出的期望金额。

## 3. 功能清单
- **validate(TradeContext context)**: 根据认购金额、首次实缴比例以及币种精度要求，计算出精确的应付金额，并与用户实际支付金额进行比对。

## 4. 关键方法说明
- ### `validate(TradeContext context)`
  - **方法签名**: `public void validate(TradeContext context)`
  - **入参**:
    | 字段名   | 类型           | 是否必填 | 说明                                                               |
    |----------|----------------|----------|--------------------------------------------------------------------|
    | `context`  | `TradeContext` | 是       | 交易校验上下文，包含了交易的所有信息，如购买信息、基金信息和日历信息。 |
  - **出参**: `void` - 无返回值。
  - **异常处理说明**: 如果计算出的期望实缴金额与用户传入的实际金额不一致，将抛出 `ValidateException` 异常。

## 5. 关键业务逻辑说明
1.  从 `TradeContext` 上下文中获取基金交易日历信息 `FundTradeCalendarInfoDTO`（含首次实缴比例）、基金基本信息 `FundBasicInfoDTO`（含币种）和用户购买信息 `BuyBean`（含认购金额和实缴金额）。
2.  调用 `TradeUtils.isJPYCurrency()` 方法判断基金的币种是否为日元（JPY）。
3.  根据币种决定计算精度：
    - **日元（JPY）**: 期望实缴金额 = 认购金额 (`subAmt`) * 首次实缴比例 (`firstPayInRatio`)。结果使用 `RoundingMode.DOWN`（向下取整）模式，保留 **0** 位小数。
    - **其他币种**: 期望实缴金额 = 认购金额 (`subAmt`) * 首次实缴比例 (`firstPayInRatio`)。结果使用 `RoundingMode.DOWN`（向下取整）模式，保留 **2** 位小数。
4.  将第3步计算出的期望实缴金额与用户购买信息 `BuyBean` 中的实际实缴金额 (`buyAmt`) 进行比较。
5.  如果两个金额不相等（`compareTo` 结果不为 `0`），则认为金额校验失败。此时会记录一条错误日志，包含实缴金额、实缴比例和认购金额，然后抛出 `ValidateException`，错误枚举为 `ExceptionEnum.FIRST_PAID_RATIO_VALIDATE_ERROR`。
6.  如果金额相等，校验通过。

## 6. 流程图
```plantuml
@startuml
title FirstPaidRatioValidator 校验流程
start
:从交易上下文获取日历信息、基金基本信息和购买信息;
:根据币种判断是否为日元(JPY);
if (是日元?) then (true)
  :期望实缴金额 = 认购金额 * 首次实缴比例 (0位小数, 向下取整);
else (非日元)
  :期望实缴金额 = 认购金额 * 首次实缴比例 (2位小数, 向下取整);
endif
:比较期望实缴金额与传入的实缴金额;
if (不相等?) then (true)
  :记录错误日志;
  :抛出 ValidateException (FIRST_PAID_RATIO_VALIDATE_ERROR);
  stop
else (相等)
  :校验通过;
endif
stop
@enduml
```

## 7. 时序图
```plantuml
@startuml
title 首次实缴比例金额校验时序
participant TradeService as "交易服务"
participant ValidatorChain as "校验链"
participant FirstPaidRatioValidator as "首次实缴比例校验器"
participant TradeContext as "交易上下文"
participant TradeUtils as "交易工具类"
participant MathUtils as "数学工具类"

TradeService -> ValidatorChain: execute(context)
activate ValidatorChain

ValidatorChain -> FirstPaidRatioValidator : validate(context)
activate FirstPaidRatioValidator

FirstPaidRatioValidator -> TradeContext : getXXX()
FirstPaidRatioValidator -> TradeUtils : isJPYCurrency(currency)
activate TradeUtils
TradeUtils -->> FirstPaidRatioValidator : boolean
deactivate TradeUtils

FirstPaidRatioValidator -> MathUtils : multiply(...)
activate MathUtils
MathUtils -->> FirstPaidRatioValidator : paidAmt (期望实缴金额)
deactivate MathUtils

alt 金额不匹配
    FirstPaidRatioValidator ->> FirstPaidRatioValidator: log.error(...)
    FirstPaidRatioValidator -->> ValidatorChain : throw ValidateException
    deactivate FirstPaidRatioValidator
    ValidatorChain -->> TradeService: throw ValidateException
    deactivate ValidatorChain
else 校验通过
    FirstPaidRatioValidator -->> ValidatorChain : void
    deactivate FirstPaidRatioValidator
end
@enduml
```

## 8. 异常处理机制
- **主要异常**: `com.howbuy.dtms.order.service.commom.exception.ValidateException`
- **触发场景**: 当根据认购金额、首次实缴比例和币种精度计算出的期望实缴金额，与用户传入的实际实缴金额不一致时。
- **处理方式**: 抛出 `ValidateException`，并附带错误枚举 `ExceptionEnum.FIRST_PAID_RATIO_VALIDATE_ERROR`，由上层统一处理。

## 9. 调用的公共模块或外部依赖
- **`com.howbuy.dtms.order.service.commom.utils.TradeUtils`**: 用于判断基金币种是否为日元。
- **`com.howbuy.dtms.order.service.commom.utils.MathUtils`**: 用于进行高精度的乘法和精度控制运算。

## 10. 被哪些其他模块调用
- 该校验器作为原子校验规则，被注入到Spring容器中，并由交易校验链管理器根据业务场景动态调用。 