# 认缴/实缴手续费校验器（AppPaidBuyFeeValidator）

## 1. 模块名称
`AppPaidBuyFeeValidator`

## 2. 模块作用简述
该模块是交易验证链中的一个原子校验器，专门用于校验 **分次CALL** 产品的 **实缴** 场景下的手续费。它确保了前端传入的手续费与后端根据产品配置计算出的手续费一致，并处理了日元等特殊币种的精度问题。

## 3. 功能清单
- `validate(TradeContext context)`: 执行手续费相关的核心校验逻辑。
- `checkJPYCurrencyAmtScale(TradeContext context)`: 校验日元币种的金额小数位数是否合法（必须为整数）。
- `checkEstimateFee(...)`: 校验前端传入的预估手续费与后端计算的是否一致。
- `buildBuyFeeComputeContext(TradeContext context)`: 构建调用手续费计算服务所需的上下文对象。

## 4. 关键方法说明

### 4.1. `validate(TradeContext context)`
该方法是校验器的入口，负责编排整个校验流程。

- **方法签名**: `public void validate(TradeContext context)`
- **入参**:
  | 字段名   | 类型         | 是否必填 | 说明                               |
  |----------|--------------|----------|------------------------------------|
  | `context`| `TradeContext` | 是       | 包含整个交易所需信息的上下文对象。 |
- **出参**: `void` (校验结果通过抛出异常或在 context 中设置属性来体现)
- **异常处理**:
  - `ValidateException`: 当任何校验规则不通过时抛出，例如：
    - 中台业务码不匹配（非实缴业务）。
    - 认缴/实缴手续费类型不正确或未配置。
    - 费率类型配置错误。
    - 前端传入的手续费与后端计算的不一致。
    - 日元币种金额小数位不为0。

## 5. 关键业务逻辑说明
1.  **币种精度校验**: 首先检查交易币种是否为日元（JPY）。如果是，则校验交易金额和手续费的小数位数，日元交易金额必须为整数，不允许有小数。
2.  **业务码校验**: 校验当前交易的中台业务码是否为实缴业务码（`112B`）。如果不是，说明该校验器不适用于当前交易，直接抛出异常。
3.  **手续费类型校验**: 从交易上下文中获取前端传入的"认缴/实缴手续费类型"（`subAndFirstPaidFeeRateType`），并校验其合法性。
4.  **获取产品费率**: 调用 `FeeComputeService`，根据认缴金额和实缴金额，获取产品配置的"认缴首次实缴费率"。
5.  **费率类型匹配校验**:
    - 将产品返回的费率业务码转换为 `SubAndFirstPaidFeeRateTypeEnum` 枚举。
    - 校验该费率类型是否与前端请求传入的费率类型一致。如果不一致，说明产品配置可能与前端预期不符，抛出异常。
6.  **手续费计算与校验**:
    - **按认缴计算场景**: 如果匹配到的费率类型是"按认缴金额计算"（`SUBSCRIBE_PAY_TYPE`），则不进行后续的手续费校验。此时，系统认为手续费已在认缴阶段计算或前端无需此阶段的计算结果。直接构建一个手续费为0的`BuyFeeComputeInfoVO`对象并设置到上下文中，然后返回。
    - **按实缴计算场景**: 如果费率类型是"按实缴金额计算"，则进入核心的手续费校验逻辑：
        a. 调用 `FeeComputeService` 的 `subAndFirstPaidBuyFeeCompute` 方法，根据实缴金额、费率配置等信息，重新计算一次预估手续费。
        b. 将计算出的手续费与前端传入的预估手续费（`estimateFee`）进行比较。
        c. 如果两者不相等，记录日志并抛出 `FEE_ERROR` 异常。
        d. 如果相等，将包含详细计算结果（如折后手续费、原始手续费等）的 `BuyFeeComputeInfoVO` 对象设置到交易上下文中，供后续业务使用。

## 6. 流程图

```plantuml
@startuml
title AppPaidBuyFeeValidator 校验流程
start
:获取交易上下文;
if (是日元交易?) then (yes)
  :校验金额和手续费小数位;
  if (小数位 > 0?) then (yes)
    :抛出"日元金额小数位错误"异常;
    stop
  endif
endif
if (中台业务码是112B?) then (no)
  :抛出"中台业务码错误"异常;
  stop
endif
:校验"认缴/实缴手续费类型";
if (类型不合法?) then (yes)
  :抛出"手续费类型不正确"异常;
  stop
endif
:调用FeeComputeService获取产品费率;
:校验返回的费率类型;
if (与入参费率类型不匹配?) then (yes)
  :抛出"手续费类型配置不正确"异常;
  stop
endif
if (费率类型为"按认缴计算"?) then (yes)
  :构造手续费为0的VO;
  :设置到上下文;
  :结束;
  stop
else (no)
  :调用FeeComputeService计算手续费;
  if (计算结果与入参不一致?) then (yes)
    :记录日志;
    :抛出"手续费错误"异常;
    stop
  else (no)
    :将计算结果VO设置到上下文;
    :结束;
    stop
  endif
endif
@enduml
```

## 7. 时序图

```plantuml
@startuml
title AppPaidBuyFeeValidator 时序图
actor Client
participant TradeService as "交易主服务"
participant Validator as "AppPaidBuyFeeValidator"
participant Context as "TradeContext"
participant FeeService as "FeeComputeService"

Client -> TradeService: 发起实缴交易请求
TradeService -> Validator: validate(context)
Validator -> Context: 获取交易信息\n(业务码, 金额, 费率类型等)
Context --> Validator: 返回交易信息

Validator -> FeeService: getSubAndFirstPaidFundFeeRateByAppAmt(...)
FeeService --> Validator: FundFeeRateDTO (产品费率配置)

alt "按实缴计算"
  Validator -> FeeService: subAndFirstPaidBuyFeeCompute(...)
  FeeService --> Validator: BuyFeeComputeInfoVO (计算出的手续费)
  Validator -> Context: 获取前端传入的手续费
  Context --> Validator: 返回前端手续费
  Validator -> Validator: 比较手续费
  alt "手续费不一致"
      Validator -> TradeService: throw ValidateException
  end
  Validator -> Context: setFeeComputeInfoVO(计算结果)
end

alt "按认缴计算"
  Validator -> Context: setFeeComputeInfoVO(手续费为0)
end

Validator --> TradeService: 校验通过
TradeService -> Client: 返回交易受理结果
@enduml
```

## 8. 异常处理机制
| 异常场景                       | 异常枚举/代码                | 处理方式                                    |
|--------------------------------|------------------------------|---------------------------------------------|
| 交易币种为日元，但金额有小数   | `JPY_AMT_SCALE_ERROR`        | 抛出 `ValidateException`，中断交易流程。    |
| 中台业务码不是实缴（112B）     | `PARAMS_ERROR`               | 抛出 `ValidateException`，提示业务码错误。    |
| 认缴/实缴手续费类型为空或无效    | `Assert.notNull`             | 抛出 `IllegalArgumentException`，提示类型不正确。 |
| 返回的费率配置业务码无法识别   | `Assert.notNull`             | 抛出 `IllegalArgumentException`，提示费率错误。 |
| 前端入参与产品配置的费率类型不符 | `FEE_ERROR`                  | 抛出 `ValidateException`，提示类型配置不正确。|
| 后端计算的手续费与前端传入的不一致| `FEE_ERROR`                  | 抛出 `ValidateException`，提示手续费错误。    |

## 9. 调用的公共模块或外部依赖
- **`FeeComputeService`**:
  - **功能简述**: 核心的手续费计算服务。负责根据产品代码、交易金额、客户信息等获取费率配置，并执行具体的手续费计算逻辑。`AppPaidBuyFeeValidator` 依赖它来获取费率和计算手续费，以完成校验。

## 10. 被哪些其他模块调用
- 该模块作为交易校验责任链的一环，被校验链的发起者（通常是某个 `TradeService` 或 `ValidatorChainManager`）调用。它通常在执行核心业务逻辑之前被触发，以确保交易参数的合法性。 