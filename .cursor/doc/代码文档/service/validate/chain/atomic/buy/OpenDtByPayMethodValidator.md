# OpenDtByPayMethodValidator 设计文档

## 模块名称
`OpenDtByPayMethodValidator`

## 模块作用简述
根据支付方式计算开放日和打款截止时间，并校验下单时间是否在打款截止时间之前。

## 功能清单
- `validate(TradeContext context)`: 执行开放日及打款截止时间的计算与校验。

## 关键方法说明
### `validate(TradeContext context)`
- **方法签名**: `public void validate(TradeContext context)`
- **入参**:
    | 字段名 | 类型 | 是否必填 | 说明 |
    |---|---|---|---|
    | context | `TradeContext` | 是 | 交易上下文，包含本次交易的所有信息。 |
- **`TradeContext` 中使用的关键信息**:
    - `context.getBaseRequest()`: 从中获取 `appDt` (下单日期) 和 `appTm` (下单时间)。
    - `context` 本身会被传递给 `OpenDateComputeService` 以计算截止时间。
    - `context.getBuyBean()`: 从中获取由 `OpenDateComputeService` 计算并设置的 `payEndDt` 和 `payEndTm`。
- **出参**: `void`。如果校验不通过，则直接抛出 `ValidateException` 异常。该方法会通过其依赖的 `openDateComputeService` 间接修改 `TradeContext`，填入计算出的 `payEndDt` 和 `payEndTm`。
- **异常处理说明**:
    - `ExceptionEnum.PAYMENT_DEADLINE_NOT_SATISFY`: 当下单时间晚于或等于计算出的打款截止时间时抛出。

## 关键业务逻辑说明
1. 调用 `OpenDateComputeService` 的 `computeByPayMethod` 方法，并传入当前的交易上下文 `context`。
2. `OpenDateComputeService` 负责复杂的计算逻辑，根据支付方式、节假日等因素，计算出本次交易对应的开放日、打款截止日期 (`payEndDt`) 和打款截止时间 (`payEndTm`)，并将结果写回 `context` 中的 `buyBean`。
3. 从 `context` 中分别获取客户端的下单日期时间 (`appDt` + `appTm`) 和上一步计算出的打款截止日期时间 (`payEndDt` + `payEndTm`)。
4. 比较两个日期时间字符串。如果下单时间大于或等于打款截止时间，说明客户已错过打款窗口。
5. 在错过打款窗口的情况下，记录一条错误日志，然后通过 `ExceptionUtils` 抛出一个 `ValidateException`，错误码为 `PAYMENT_DEADLINE_NOT_SATISFY`，并附带截止日期和时间信息，中断交易流程。
6. 如果下单时间在截止时间之前，则校验通过，方法正常结束。

## 流程图 (PlantUML)
```plantuml
@startuml
title OpenDtByPayMethodValidator 校验流程
start
:获取交易上下文;
:调用开放日计算服务 (computeByPayMethod)
并更新上下文中的打款截止时间;
:获取下单日期时间 (appDt + appTm);
:获取计算出的打款截止日期时间 (payEndDt + payEndTm);
if (下单日期时间 >= 打款截止日期时间?) then (是)
    :记录错误日志;
    :抛出"不满足打款截止时间"异常;
    stop
else (否)
    :校验通过;
endif
end
@enduml
```

## 时序图 (PlantUML)
```plantuml
@startuml
title OpenDtByPayMethodValidator 时序图
participant "ValidatorChain" as Chain
participant "OpenDtByPayMethodValidator" as Validator
participant "OpenDateComputeService" as ComputeService
participant "TradeContext" as Context
participant "ExceptionUtils" as ExUtils

Chain ->> Validator: validate(context)
activate Validator

Validator ->> ComputeService: computeByPayMethod(context)
activate ComputeService
ComputeService ->> Context: 更新 BuyBean (payEndDt, payEndTm)
ComputeService -->> Validator
deactivate ComputeService

Validator ->> Context: getBaseRequest()
activate Context
Context -->> Validator: baseRequest
deactivate Context

Validator ->> Context: getBuyBean()
activate Context
Context -->> Validator: buyBean
deactivate Context

alt 下单时间 >= 打款截止时间
    Validator ->> ExUtils: throwValidateException(...)
    activate ExUtils
    ExUtils -->> Chain: ValidateException
    deactivate ExUtils
end

deactivate Validator
@enduml
```

## 异常处理机制
校验器通过 `ExceptionUtils.throwValidateException` 方法抛出 `ValidateException`。当校验失败时，该方法会封装统一的异常信息，包括错误码 (`ExceptionEnum.PAYMENT_DEADLINE_NOT_SATISFY`) 和格式化的错误描述 (`MessageTemplates`)，中断当前校验流程。

## 调用的公共模块或外部依赖
- **`TradeContext`**: 交易上下文，用于在校验链中传递数据。此模块既消费上下文信息，也通过依赖服务来丰富上下文内容。
- **`OpenDateComputeService`**: 开放日计算服务。这是一个核心的业务逻辑服务，封装了复杂的开放日和截止时间计算规则。
- **`ExceptionUtils`**: 统一异常抛出工具类。
- **`BaseRequest`**: 基础请求对象，包含了通用的请求参数如 `appDt`, `appTm`。

## 被哪些其他模块调用
- 该校验器作为原子校验规则，被 `TradeValidatorAnnotation` 注解标记，由交易校验链 (`TradeValidateManager` 或类似服务) 根据配置动态调用。 