## 1. 模块名称
FirstPaidRatioParamsConfigValidator

## 2. 模块作用简述
本校验器负责在交易流程中，检查产品的"首次实缴比例"是否已在产品系统中正确配置，确保后续计算有据可依。

## 3. 功能清单
- **validate(TradeContext context)**: 从交易上下文中获取基金的交易日历信息，校验其中的首次实缴比例是否已配置（非空且大于0）。

## 4. 关键方法说明
- ### `validate(TradeContext context)`
  - **方法签名**: `public void validate(TradeContext context)`
  - **入参**:
    | 字段名   | 类型           | 是否必填 | 说明                                                               |
    |----------|----------------|----------|--------------------------------------------------------------------|
    | `context`  | `TradeContext` | 是       | 交易校验上下文，包含了此次交易的所有信息，如此校验器中用到的基金交易日历信息。 |
  - **出参**: `void` - 无返回值。
  - **异常处理说明**: 如果首次实缴比例未配置或配置为0及以下，将抛出 `ValidateException` 异常。

## 5. 关键业务逻辑说明
1.  从传入的交易上下文 `TradeContext` 对象中，获取基金交易日历信息 `FundTradeCalendarInfoDTO`。
2.  判断 `FundTradeCalendarInfoDTO` 对象中的 `firstPayInRatio`（首次实缴比例）字段：
    -   检查是否为 `null`。
    -   如果非 `null`，检查其值是否小于或等于 `0`。
3.  如果上述任一条件为真，则认为首次实缴比例未正确配置，立即中断当前流程，并向上层调用者抛出 `ValidateException` 异常，内置错误枚举为 `ExceptionEnum.FIRST_PAID_RATIO_NOT_CONFIGURED`。
4.  如果比例值有效（非 `null` 且大于 `0`），则校验通过，方法正常返回。

## 6. 流程图
```plantuml
@startuml
title FirstPaidRatioParamsConfigValidator 校验流程
start
:从交易上下文(TradeContext)获取基金交易日历信息(FundTradeCalendarInfoDTO);
:判断首次实缴比例(firstPayInRatio)是否为空或小于等于0;
if (是) then (true)
  :抛出 ValidateException (FIRST_PAID_RATIO_NOT_CONFIGURED);
  stop
else (否)
  :校验通过;
endif
stop
@enduml
```

## 7. 时序图
```plantuml
@startuml
title 首次实缴比例配置校验时序
participant TradeService as "交易服务"
participant ValidatorChain as "校验链"
participant FirstPaidRatioParamsConfigValidator as "首次实缴比例配置校验器"
participant TradeContext as "交易上下文"

TradeService -> ValidatorChain: execute(context)
activate ValidatorChain

ValidatorChain -> FirstPaidRatioParamsConfigValidator : validate(context)
activate FirstPaidRatioParamsConfigValidator

FirstPaidRatioParamsConfigValidator -> TradeContext : getFundTradeCalendarInfoDTO()
activate TradeContext
TradeContext -->> FirstPaidRatioParamsConfigValidator : fundTradeCalendarInfoDTO
deactivate TradeContext

alt 首次实缴比例未配置或无效
    FirstPaidRatioParamsConfigValidator -->> ValidatorChain : throw ValidateException
    deactivate FirstPaidRatioParamsConfigValidator
    ValidatorChain -->> TradeService: throw ValidateException
    deactivate ValidatorChain
else 校验通过
    FirstPaidRatioParamsConfigValidator -->> ValidatorChain : void
    deactivate FirstPaidRatioParamsConfigValidator
end
@enduml
```

## 8. 异常处理机制
- **主要异常**: `com.howbuy.dtms.order.service.commom.exception.ValidateException`
- **触发场景**: 当从 `TradeContext` 中获取的 `firstPayInRatio` 值为 `null` 或小于等于 `0` 时。
- **处理方式**: 抛出 `ValidateException`，并附带错误枚举 `ExceptionEnum.FIRST_PAID_RATIO_NOT_CONFIGURED`。该异常通常由上层校验链或服务捕获，并转化为统一的错误响应返回给调用方。

## 9. 调用的公共模块或外部依赖
- 无外部服务或数据库依赖，仅依赖 `TradeContext` 中传递的数据。

## 10. 被哪些其他模块调用
- 该校验器作为一个原子校验规则，被注入到Spring容器中，并由交易校验链管理器（实现了责任链模式）根据业务场景动态调用。 