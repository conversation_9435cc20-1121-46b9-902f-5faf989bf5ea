# 储蓄罐基金购买校验器 (`BuyPiggyFundValidator`) 设计文档

## 1. 模块名称
BuyPiggyFundValidator (购买储蓄罐基金校验器)

## 2. 模块作用简述
本模块负责在客户购买储蓄罐基金时，校验客户是否已签署有效的储蓄罐协议，并确保当前购买的基金与协议中约定的基金一致。

## 3. 功能清单
- `validate(TradeContext context)`: 执行储蓄罐基金购买相关的业务校验。

## 4. 关键方法说明
- **方法签名**
  ```java
  public void validate(TradeContext context)
  ```
- **入参**
  | 字段名   | 类型           | 是否必填 | 说明                                           |
  |----------|----------------|----------|------------------------------------------------|
  | `context`  | `TradeContext` | 是       | 交易上下文，包含香港客户号(`hkCustNo`)和基金代码(`fundCode`)等信息。 |

- **出参**
  | 类型   | 含义             |
  |--------|------------------|
  | `void` | 无直接返回值，校验失败时抛出异常。 |

- **异常处理说明**
  - `ValidateException`: 当校验不通过时抛出。
    - `PIGGY_FUND_SIGN_ERROR`: 客户未签署海外储蓄罐协议或协议未生效。
    - `PIGGY_SIGN_FUND_NOT_MATCHED`: 客户签署的储蓄罐协议中约定的基金与当前购买的基金不匹配。

## 5. 关键业务逻辑说明
1.  从 `TradeContext` 中获取当前交易的香港客户号 `hkCustNo`。
2.  调用外部服务 `CounterOrderQueryOuterService.hasSignCxg` 方法，检查该客户是否已签署有效的储蓄罐协议。
3.  **协议签署校验**：如果客户未签署协议，则校验失败，系统抛出 `ValidateException` 并附带错误信息 `【香港客户号{XXXX}未签署海外储蓄罐协议或协议未生效】`，流程终止。
4.  **协议内容校验**：如果客户已签署协议，则继续调用 `CounterOrderQueryOuterService.getHkCustPiggyAgreement` 方法获取客户签署的协议详情。
5.  从协议详情中获取约定的储蓄罐基金代码，并将其与本次交易的基金代码进行比较。
6.  如果两者不一致，则校验失败，系统抛出 `ValidateException` 并附带错误信息 `【香港客户号{XXXX}签署的储蓄罐协议非当前储蓄罐基金】`，流程终止。
7.  如果所有校验均通过，则方法正常结束。

## 6. 流程图
```plantuml
@startuml
title BuyPiggyFundValidator 校验流程
start
:获取交易上下文 (TradeContext);
:获取香港客户号 (hkCustNo) 和基金代码 (fundCode);
:调用外部服务检查客户是否已签署储蓄罐协议;
if (是否已签署?) then (是)
  :调用外部服务获取储蓄罐协议详情;
  :获取协议中的基金代码;
  if (协议基金代码与交易基金代码一致?) then (是)
    :校验通过;
    end
  else (否)
    :抛出"协议基金不匹配"异常;
    stop
  endif
else (否)
  :抛出"未签署协议"异常;
  stop
endif
@enduml
```

## 7. 时序图
```plantuml
@startuml
title BuyPiggyFundValidator 时序图
actor "调用方" as caller
participant "BuyPiggyFundValidator" as validator
participant "CounterOrderQueryOuterService" as service

caller -> validator: validate(context)
activate validator

validator -> service: hasSignCxg(hkCustNo)
activate service
service --> validator: (isSigned)
deactivate service

alt isSigned = false
    validator ->> caller: throws ValidateException (PIGGY_FUND_SIGN_ERROR)
else isSigned = true
    validator -> service: getHkCustPiggyAgreement(hkCustNo)
    activate service
    service --> validator: (agreementDetail)
    deactivate service
    
    alt agreementDetail.fundCode != context.fundCode
         validator ->> caller: throws ValidateException (PIGGY_SIGN_FUND_NOT_MATCHED)
    else
         validator -->> caller: 校验通过
    end
end
deactivate validator
@enduml
```

## 8. 异常处理机制
| 异常场景                                | 触发条件                               | 处理方式                                      |
|-----------------------------------------|----------------------------------------|-----------------------------------------------|
| `PIGGY_FUND_SIGN_ERROR`                 | 客户未签署有效的海外储蓄罐协议。       | 通过 `ExceptionUtils.throwValidateException` 抛出 `ValidateException`。 |
| `PIGGY_SIGN_FUND_NOT_MATCHED`           | 客户购买的基金与协议中约定的基金不符。 | 通过 `ExceptionUtils.throwValidateException` 抛出 `ValidateException`。 |

## 9. 调用的公共模块或外部依赖
- **模块名称**: `CounterOrderQueryOuterService`
- **功能简述**: 提供查询柜台订单相关信息的外部服务接口，包括查询客户储蓄罐协议的签署状态和详细内容。

## 10. 被哪些其他模块调用
- 该校验器作为交易校验链的一部分，被 `TradeValidateManager` 在执行申购类交易时根据规则动态调用。 