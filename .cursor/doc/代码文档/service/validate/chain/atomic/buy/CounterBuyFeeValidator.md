# 柜台购买手续费校验器 (`CounterBuyFeeValidator`) 设计文档

## 1. 模块名称
CounterBuyFeeValidator (柜台购买手续费校验器)

## 2. 模块作用简述
本模块用于校验和计算柜台申购场景下的交易费用。它不仅会实时计算手续费，还会验证上游传入的预估手续费是否与计算结果一致，并对特定币种（如日元）的金额格式进行特殊校验。

## 3. 功能清单
- `validate(TradeContext context)`: 执行手续费相关的校验与计算，并将计算结果更新回交易上下文。

## 4. 关键方法说明
- **方法签名**
  ```java
  public void validate(TradeContext context)
  ```
- **入参**
  | 字段名   | 类型           | 是否必填 | 说明                                           |
  |----------|----------------|----------|------------------------------------------------|
  | `context`  | `TradeContext` | 是       | 交易上下文，包含申购金额、费率、客户、产品等所有计费所需信息。 |

- **出参**
  | 类型   | 含义             |
  |--------|------------------|
  | `void` | 无直接返回值，校验失败时抛出异常，成功则将计算结果 `BuyFeeComputeInfoVO` 更新到 `context` 中。 |

- **异常处理说明**
  - `ValidateException`: 当校验不通过时抛出。
    - `JPY_AMT_SCALE_ERROR`: 当交易币种为日元(JPY)时，传入的申购金额或手续费金额包含了小数位。
    - `FEE_ERROR`: 上游传入的预估手续费与后端服务实时计算出的手续费不一致。

## 5. 关键业务逻辑说明
1.  **构造计费上下文**：从主交易上下文 `TradeContext` 中提取所需字段，组装成手续费计算专用的上下文对象 `BuyFeeComputeContext`。
2.  **日元金额校验**：
    a. 判断当前交易基金的币种是否为日元 (`JPY`)。
    b. 如果是日元，则检查上游传入的申购金额和预估手续费是否包含小数位（即小数位大于0）。
    c. 如果包含小数，则认为金额格式错误，抛出 `ValidateException (JPY_AMT_SCALE_ERROR)`，流程终止。
3.  **手续费计算**：调用 `FeeComputeService.counterBuyFeeCompute` 方法，传入计费上下文，执行精准的费用计算。该服务会综合考虑费率、折扣、客户身份等多种因素，返回一个包含应收手续费、优惠金额等详细信息的 `BuyFeeComputeInfoVO` 对象。
4.  **手续费一致性校验**：
    a. 检查上游调用方是否传入了预估手续费 (`estimateFee`)。
    b. 如果传入了，则将其与 `FeeComputeService` 计算出的实际手续费进行精确比较。
    c. 如果两者不相等，说明存在差异，系统会记录一条Info级别的日志，并抛出 `ValidateException (FEE_ERROR)`，流程终止。
5.  **更新交易上下文**：如果所有校验都通过，将包含精确计算结果的 `feeComputeInfoVO` 对象设置回主 `TradeContext` 中，以供后续的订单创建等流程使用。

## 6. 流程图
```plantuml
@startuml
title CounterBuyFeeValidator 校验流程
start
:获取交易上下文 (TradeContext);
:构建手续费计算上下文 (BuyFeeComputeContext);

if (基金币种是日元?) then (是)
  if (申购金额或手续费有小数位?) then (是)
    :抛出"日元金额格式错误"异常;
    stop
  endif
endif

:调用 FeeComputeService 计算费用;
:获取计算后的手续费详情 (feeComputeInfoVO);

if (上游传入了预估手续费?) then (是)
  if (传入手续费 != 计算后手续费?) then (是)
    :记录手续费不一致日志;
    :抛出"手续费错误"异常;
    stop
  endif
endif

:将计算结果设置回主交易上下文;
:校验通过;
end
@enduml
```

## 7. 时序图
```plantuml
@startuml
title CounterBuyFeeValidator 时序图
actor "调用方" as caller
participant "CounterBuyFeeValidator" as validator
participant "FeeComputeService" as service

caller -> validator: validate(context)
activate validator

alt is JPY currency and amount has decimals
    validator ->> caller: throws ValidateException (JPY_AMT_SCALE_ERROR)
    deactivate validator
end

validator -> service: counterBuyFeeCompute(...)
activate service
service --> validator: (feeComputeInfoVO)
deactivate service

if incoming estimateFee is not null
    alt incoming estimateFee != calculated estimateFee
        validator ->> caller: throws ValidateException (FEE_ERROR)
        deactivate validator
    end
end

validator -> validator: context.setFeeComputeInfoVO(feeComputeInfoVO)
validator -->> caller: 校验通过
deactivate validator
@enduml
```

## 8. 异常处理机制
| 异常场景             | 触发条件                                   | 处理方式                                      |
|----------------------|--------------------------------------------|-----------------------------------------------|
| `JPY_AMT_SCALE_ERROR`  | 基金币种为日元，但金额字段包含小数位。     | 抛出 `ValidateException`。                      |
| `FEE_ERROR`            | 上游传入的预估手续费与后端计算结果不一致。 | 记录日志，并抛出 `ValidateException`。          |

## 9. 调用的公共模块或外部依赖
- **模块名称**: `FeeComputeService`
- **功能简述**: 核心费用计算服务，封装了复杂的计费逻辑，能够根据不同的业务场景和参数计算出准确的交易费用。

## 10. 被哪些其他模块调用
- 该校验器作为交易校验链的一部分，被 `TradeValidateManager` 在执行柜台申购类交易时根据规则动态调用。 