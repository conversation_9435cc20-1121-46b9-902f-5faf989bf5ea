# 接口详细设计文档

## 1. 接口名称
柜台撤单聚合接口

## 2. 接口说明
提供柜台交易场景下的基金订单撤单相关功能，包括撤单校验和撤单执行。此接口是一个聚合接口，包含了撤单前的校验和撤单执行两个步骤，专门为柜台交易场景提供支持。

**业务背景**：
- 柜台交易订单在特定条件下允许撤单，如未支付、未上报、在撤单时间窗口内等
- 撤单操作需要严格的前置校验，确保操作的合法性和安全性
- 支持自行撤销和强制取消两种撤单类型

**使用场景**：
- 柜台工作人员协助客户撤单操作
- 客户通过柜台系统发起撤单请求
- 系统自动撤单处理（如风控要求）

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.cancel.CounterCancelFacade
```

---

## 方法一：`counterCancelValidate`

### 4.1. 方法说明
**柜台撤单校验**：在执行实际的柜台撤单操作前，对撤单的所有前提条件进行全面校验。

### 4.2. 方法签名
```java
Response<Body> counterCancelValidate(CounterCancelValidateRequest request)
```

### 4.3. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK000001 | 客户在香港的唯一标识号 |
| 订单号 | dealNo | String | 是 | 20250726000001 | 交易订单的唯一标识号 |
| 交易码 | txCode | String | 否 | HW0011 | 交易类型编码，默认为HW0011 |
| 申请日期 | appDt | String | 否 | 20250726 | 申请日期，格式：yyyyMMdd |
| 申请时间 | appTm | String | 否 | 211751 | 申请时间，格式：HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 1 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 交易网点编号 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT20250726001 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456789 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone14 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 张三的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 16.0 | 操作系统版本 |

### 4.4. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 操作成功 | 响应描述信息 |
| 数据封装 | data | Body | 否 | {} | 响应数据封装对象 |

---

## 方法二：`counterCancel`

### 4.5. 方法说明
**柜台撤单执行**：执行实际的柜台撤单操作，更新订单状态并发送相关通知。

### 4.6. 方法签名
```java
Response<Body> counterCancel(CounterCancelRequest request)
```

### 4.7. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK000001 | 客户在香港的唯一标识号 |
| 订单号 | dealNo | String | 是 | 20250726000001 | 交易订单的唯一标识号 |
| 撤单类型 | cancelType | String | 是 | 1 | 1-自行撤销；2-强制取消 |
| 撤单原因 | cancelReason | String | 否 | 客户主动撤单 | 撤单原因说明 |
| 撤单资金账号 | cancelCpAcctNo | String | 否 | 6228480000000001 | 撤单时指定的资金账号 |
| 交易码 | txCode | String | 否 | HW0011 | 交易类型编码，默认为HW0011 |
| 申请日期 | appDt | String | 否 | 20250726 | 申请日期，格式：yyyyMMdd |
| 申请时间 | appTm | String | 否 | 211751 | 申请时间，格式：HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 1 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 交易网点编号 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT20250726001 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456789 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone14 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 张三的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 16.0 | 操作系统版本 |

### 4.8. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 操作成功 | 响应描述信息 |
| 数据封装 | data | Body | 否 | {} | 响应数据封装对象 |

## 5. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 操作成功 | 正常处理完成 |
| 9999 | 系统异常 | 系统内部错误 |
| 1001 | 参数校验失败 | 必填参数缺失或格式错误 |
| 2001 | 订单不存在 | 根据客户号和订单号未找到订单 |
| 2002 | 订单状态不允许撤单 | 订单已完成或已撤单 |
| 2003 | 业务类型不支持撤单 | 当前业务类型不在支持撤单的范围内 |
| 2004 | 撤单类型不存在 | 撤单类型参数值无效 |

## 6. 关键业务逻辑说明

### 6.1. 撤单校验逻辑（counterCancelValidate）
1. **参数校验**：验证必填参数的完整性和格式正确性
2. **订单存在性校验**：根据香港客户号和订单号查询订单是否存在
3. **业务类型校验**：验证订单的业务类型是否在柜台支持的撤单范围内
   - 支持的业务类型：认购(SUBS)、申购(PURCHASE)、112A、赎回(REDEEM)、112B、1136
4. **订单状态校验**：检查订单当前状态是否允许撤单操作
5. **关联订单处理**：如果是认缴实缴关联订单，需要同时校验关联订单

### 6.2. 撤单执行逻辑（counterCancel）
1. **撤单类型获取**：根据撤单类型代码获取对应的枚举值
2. **执行校验**：调用撤单校验逻辑确保操作合法性
3. **订单状态更新**：批量更新订单状态为已撤单
4. **消息通知**：发送撤单成功的消息通知相关系统
5. **关联订单处理**：如果存在关联订单，同时处理关联订单的撤单

## 7. 流程图

@startuml
start
:接收撤单请求;
:参数校验;
if (参数校验通过?) then (否)
  :返回参数错误;
  stop
endif
:查询订单信息;
if (订单存在?) then (否)
  :返回订单不存在错误;
  stop
endif
:业务类型校验;
if (业务类型支持撤单?) then (否)
  :返回业务类型不支持错误;
  stop
endif
:订单状态校验;
if (订单状态允许撤单?) then (否)
  :返回订单状态错误;
  stop
endif
if (是否为撤单执行?) then (是)
  :获取撤单类型;
  :更新订单状态;
  :发送消息通知;
endif
:返回成功响应;
stop
@enduml

## 8. 时序图

@startuml
participant "客户端" as Client
participant "CounterCancelFacade" as Facade
participant "CounterCancelValidateService" as ValidateService
participant "CounterCancelService" as CancelService
participant "数据库" as DB
participant "消息队列" as MQ

== 撤单校验流程 ==
Client -> Facade: counterCancelValidate(request)
Facade -> ValidateService: process(request)
ValidateService -> DB: 查询订单信息
DB --> ValidateService: 返回订单数据
ValidateService -> ValidateService: 执行各项校验
ValidateService --> Facade: 校验完成
Facade --> Client: Response<Body>

== 撤单执行流程 ==
Client -> Facade: counterCancel(request)
Facade -> CancelService: process(request)
CancelService -> CancelService: 获取撤单类型
CancelService -> ValidateService: 执行校验
CancelService -> DB: 批量更新订单状态
DB --> CancelService: 更新成功
CancelService -> MQ: 发送撤单通知消息
MQ --> CancelService: 消息发送成功
CancelService --> Facade: 处理完成
Facade --> Client: Response<Body>
@enduml

## 9. 异常处理机制

### 9.1. 主要异常场景及处理方式

| 异常类型 | 异常场景 | 处理方式 | 返回信息 |
|----------|----------|----------|----------|
| ValidateException | 参数校验失败 | 捕获异常，返回具体校验错误信息 | 参数校验失败的详细描述 |
| ValidateException | 订单不存在 | 抛出业务异常 | "订单不存在" |
| ValidateException | 订单状态不允许撤单 | 抛出业务异常 | "订单状态不允许撤单" |
| ValidateException | 业务类型不支持撤单 | 抛出业务异常 | "业务类型不支持撤单" |
| ValidateException | 撤单类型不存在 | 抛出业务异常 | "撤单类型不存在" |
| RuntimeException | 数据库操作异常 | 事务回滚，记录错误日志 | "系统异常，请稍后重试" |
| RuntimeException | 消息发送异常 | 记录错误日志，不影响主流程 | 撤单成功，但通知发送失败 |

### 9.2. 异常处理策略
- **参数校验异常**：在接口入口进行统一校验，及时返回错误信息
- **业务逻辑异常**：通过ValidateException抛出，由全局异常处理器统一处理
- **系统异常**：记录详细错误日志，返回通用错误信息保护系统内部信息
- **事务处理**：确保数据一致性，异常时自动回滚事务

## 10. 调用的公共模块或外部依赖

### 10.1. 内部依赖模块

| 模块名称 | 功能简述 | 调用方式 |
|----------|----------|----------|
| CounterCancelValidateService | 柜台撤单校验服务 | Spring依赖注入 |
| CounterCancelService | 柜台撤单执行服务 | Spring依赖注入 |
| AbstractCounterCancelService | 柜台撤单抽象服务基类 | 继承关系 |
| BaseCancelOrderService | 撤单服务基类 | 继承关系 |
| HwDealOrderRepository | 订单数据访问层 | Spring依赖注入 |
| TradeValidatorHelper | 交易校验助手 | Spring依赖注入 |
| SendMqService | 消息发送服务 | Spring依赖注入 |

### 10.2. 外部依赖

| 依赖名称 | 功能简述 | 使用场景 |
|----------|----------|----------|
| Dubbo | 分布式服务框架 | 提供RPC服务 |
| Spring Framework | 依赖注入容器 | 管理Bean生命周期 |
| MyBatis | 数据持久化框架 | 数据库操作 |
| 消息队列 | 异步消息通信 | 发送撤单通知 |

## 11. 幂等性与安全性说明

### 11.1. 幂等性保证
- **撤单校验接口**：天然幂等，多次调用结果一致，不会产生副作用
- **撤单执行接口**：通过订单状态控制幂等性，已撤单的订单不允许重复撤单
- **数据库更新**：使用乐观锁机制防止并发更新冲突
- **消息发送**：支持消息去重，避免重复发送通知

### 11.2. 安全性措施
- **参数校验**：严格校验所有输入参数，防止恶意输入
- **权限控制**：通过客户号校验确保只能操作自己的订单
- **操作审计**：记录所有撤单操作的详细日志，便于追溯
- **数据加密**：敏感信息在传输和存储过程中进行加密处理
- **访问控制**：限制接口访问频率，防止恶意攻击

### 11.3. 鉴权机制
- **客户身份验证**：通过香港客户号验证客户身份
- **订单归属校验**：确保客户只能撤单自己的订单
- **操作权限校验**：验证当前用户是否有撤单操作权限
- **渠道权限控制**：不同交易渠道具有不同的操作权限

## 12. 备注与风险点

### 12.1. 注意事项
1. **时间窗口限制**：撤单操作需要在规定的时间窗口内进行，超时后不允许撤单
2. **关联订单处理**：认缴实缴关联订单需要同时处理，确保数据一致性
3. **状态同步**：撤单后需要及时同步相关系统的订单状态
4. **资金处理**：撤单时需要考虑资金冻结和解冻的处理

### 12.2. 边界处理
- **并发撤单**：同一订单的并发撤单请求，通过数据库锁机制保证只有一个成功
- **部分成功场景**：批量处理时如果部分订单撤单失败，需要明确返回处理结果
- **网络异常**：网络中断时的重试机制和超时处理
- **数据一致性**：确保订单状态更新和消息发送的最终一致性

### 12.3. 特殊逻辑说明
- **强制取消**：管理员可以强制取消订单，绕过部分业务校验
- **撤单补偿**：撤单失败时的补偿机制和人工干预流程
- **历史数据处理**：对于历史订单的撤单需要特殊处理逻辑
- **监控告警**：关键操作失败时需要及时告警通知相关人员

---

**文档版本**：v1.0
**创建时间**：2025-07-26 21:17:51
**创建人**：hongdong.xie
**审核状态**：待审核
