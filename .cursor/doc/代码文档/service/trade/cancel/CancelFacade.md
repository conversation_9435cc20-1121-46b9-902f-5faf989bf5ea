# 1. 接口名称
撤单接口

# 2. 接口说明
该接口用于处理基金交易订单的撤单操作。用户可以通过此接口取消处于特定状态下的交易订单，例如未支付或未提交的订单。接口支持用户主动发起的"自行撤销"和系统或客服发起的"强制取消"。

# 3. 接口类型
Dubbo

# 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.cancel.CancelFacade.execute(CancelOrderRequest request)
```

# 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **香港客户号** | `hkCustNo` | String | 是 | "HK000001" | 用户的唯一身份标识。 |
| **订单号** | `dealNo` | String | 是 | "20250427000001" | 需要撤销的交易订单号。 |
| **交易密码** | `txPassword` | String | 是 | "******" | 用户的交易密码，用于验证操作权限。 |
| **撤单类型** | `cancelType` | String | 是 | "1" | "1"-自行撤销；"2"-强制取消。 |
| 交易码 | `txCode` | String | 否 | "HW0010" | 标识本次交易的类型，默认为"HW0010"。 |
| 申请日期 | `appDt` | String | 否 | "20250427" | 请求发起的日期，格式: yyyyMMdd。 |
| 申请时间 | `appTm` | String | 否 | "103000" | 请求发起的时间，格式: HHmmss。 |
| 交易渠道 | `tradeChannel` | String | 否 | "11" | 1-柜台; 9-CRM-PC; 11-APP 等。 |
| 网点号 | `outletCode` | String | 否 | "1001" | 交易发生的网点编号。 |
| IP地址 | `ipAddress` | String | 否 | "***********" | 客户端的IP地址。 |
| 外部订单号 | `externalDealNo`| String | 否 | "ext20250427001"| 第三方系统的订单号。 |
| MAC地址 | `macAddress` | String | 否 | "00-E0-4C-68-01-23"| 客户端设备的MAC地址。 |
| 设备序列号 | `deviceSerialNo`| String | 否 | "SN12345678" | 客户端设备的序列号。 |
| 设备型号 | `deviceModel` | String | 否 | "iPhone 15 Pro" | 客户端设备的型号。 |
| 设备名称 | `deviceName` | String | 否 | "My iPhone" | 客户端设备的名称。 |
| 系统版本号 | `systemVersion` | String | 否 | "iOS 17.4" | 客户端操作系统的版本。 |

# 6. 响应参数表

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- |
| **状态码** | `code` | String | "0000" | "0000"表示成功，其他表示失败。 |
| **描述信息**| `description` | String | "撤单成功" | 对状态码的补充说明。 |
| **数据** | `data` | Void | null | 成功时，该字段为null。 |

# 7. 返回码说明

| 返回码 | 说明 | 备注 |
| :--- | :--- | :--- |
| 0000 | 成功 | 操作被成功执行。 |
| B0103 | 订单不存在 | 传入的 `dealNo` 无法找到对应的订单。 |
| B0110 | 该业务类型不支持撤单 | 订单的业务类型（如分红）不允许执行撤单操作。 |
| B0111 | 订单状态不允许撤单 | 订单当前状态（如已成功、已失败）无法被撤销。 |
| E0002 | 交易密码错误 | 交易密码校验失败。 |
| ... | 其他业务/系统异常 | 更多错误码请参考项目统一错误码文档。 |

# 8. 关键业务逻辑说明
撤单的核心业务逻辑是原子性的，包含**"校验-更新-通知"**三个主要阶段，整个过程由 `BaseCancelOrderService` 统一调度。

1.  **数据获取与校验**：
    *   根据 `hkCustNo` 和 `dealNo` 从数据库查询订单主表(`hw_deal_order`)和明细表(`hw_deal_order_dtl`)。若订单不存在，则直接抛出异常。
    *   检查订单的业务类型(`middleBusiCode`)是否在支持撤单的白名单内（目前支持申购、认购、赎回等）。如果不支持，则拒绝操作。
    *   特殊处理"认缴并首次实缴"的订单，确保关联的认缴单也一并处理。

2.  **责任链校验**：
    *   构建一个包含多项检查的责任链，对交易上下文(`TradeContext`)进行严格校验。这是一个关键步骤，确保了操作的合法性和安全性。
    *   **校验链包括**：账户状态校验、订单是否已提交校验、是否为储蓄罐老基金赎回校验、订单是否已申请成功校验、交易密码校验、订单是否已支付校验、是否为电子合同订单校验、是否在可撤单时间窗口内校验、支付方式是否允许撤单校验、储蓄罐赎回订单校验。
    *   任何一个校验环节失败，都会中断流程并抛出 `ValidateException` 异常，返回具体的错误信息。

3.  **订单状态更新**：
    *   所有校验通过后，调用 `OrderUpdateService` 服务准备更新数据。
    *   根据请求中的 `cancelType`，将订单状态(`order_status`)和申请状态(`app_status`)分别更新为"自行撤销"(05) 或"强制撤销"(06)。
    *   将上报状态(`submit_status`)更新为"不需上报"。
    *   通过批量更新接口 `hwDealOrderRepository.batchUpdateDealOrder` 将变更持久化到数据库。

4.  **发送消息通知**：
    *   更新成功后，向消息队列发送订单更新消息，以通知其他依赖系统。
    *   向CRM系统发送撤单通知。
    *   如果订单使用了"储蓄罐"支付，则会触发相应的退款通知。

# 9. 流程图 (PlantUML)
```plantuml
@startuml
title 撤单接口处理流程

start
:接收 CancelOrderRequest;

:根据 dealNo 和 hkCustNo 查询订单;
if (订单是否存在?) then (yes)
  :查询订单明细;
  if (业务类型是否支持撤单?) then (yes)
    :构建包含10个步骤的校验责任链
    (账户/订单状态/交易密码等);
    if (责任链校验全部通过?) then (yes)
      :构建订单更新对象 (OrderUpdateBO)
      设置订单状态为"已撤单";
      :通过 Repository 批量更新订单和明细表;
      if (更新成功?) then (yes)
        :发送订单更新MQ消息;
        :发送CRM通知消息;
        :发送储蓄罐退款消息(如果需要);
        :返回成功响应;
      else (no)
        :记录错误日志;
        :返回系统异常;
      endif
    else (no)
      :抛出 ValidateException;
      :返回具体业务错误信息;
    endif
  else (no)
    :抛出 ValidateException;
    :返回"业务不支持撤单"错误;
  endif
else (no)
  :抛出 ValidateException;
  :返回"订单不存在"错误;
endif

stop
@enduml
```

# 10. 时序图 (PlantUML)
```plantuml
@startuml
title 撤单接口调用时序

actor Client as C
participant CancelFacadeImpl as F
participant CancelOrderService as S
participant TradeValidatorHelper as V
participant HwDealOrderRepository as R
participant OrderUpdateService as U
participant SendMqService as Mq
participant SendMessageService as Msg

C -> F : execute(request)
F -> S : process(request)
S -> R : selectByHkCustNoAndDealNo(...)
R --> S : HwDealOrder
S -> V : doValidate(context, validatorChain)
V --> S :校验通过
S -> U : cancelOrder(...)
U --> S : OrderUpdateBO
S -> R : batchUpdateDealOrder(...)
R --> S : 更新成功
S -> Mq : sendOrderUpdateMessage(...)
S -> Msg : sendCancelOrderCrmNotify(...)
S --> F : 返回处理结果
F --> C : Response.ok()

@enduml
```

# 11. 异常处理机制
接口的异常处理主要依赖于自定义的 `ValidateException` 和全局异常处理机制。
- **业务校验异常 (`ValidateException`)**: 在业务逻辑校验（如订单状态不符、交易密码错误）失败时主动抛出。每个校验失败点都有明确的错误码(`ExceptionEnum`)和错误信息，直接返回给调用方。
- **其他异常**: 如数据库连接异常、空指针等运行时异常，由框架的全局异常处理器捕获，并统一返回系统级错误码（如A0001），避免内部堆栈信息泄露。

# 12. 调用的公共模块或外部依赖

| 模块/服务 | 功能简述 |
| :--- | :--- |
| **dtms-order-dao** | 数据访问层，通过 `HwDealOrderRepository` 和 `HwDealOrderDtlRepository` 与数据库交互。 |
| **TradeValidatorHelper** | 交易校验器帮助类，用于执行责任链模式的复杂校验。 |
| **OrderUpdateService** | 订单更新服务，封装了更新订单状态的业务逻辑。 |
| **SendMqService** | 消息队列发送服务，用于发布订单状态变更的领域事件。 |
| **SendMessageService** | 消息发送服务，用于发送CRM通知和邮件通知。 |

# 13. 幂等性与安全性说明
- **幂等性**: 接口具备幂等性。核心逻辑会校验订单的当前状态，只有处于可撤销状态的订单才能被处理。如果一个已撤销的订单被重复请求撤单，校验环节会失败并返回"订单状态不允许撤单"的错误，不会对数据造成二次修改。
- **安全性**:
  - **身份验证**: 依赖Dubbo框架的调用来源验证。
  - **操作授权**: 通过校验**交易密码**来确保操作者是订单所有者本人。
  - **参数校验**: `CancelOrderRequest` 中的所有必填字段都通过 `@MyValidation` 注解进行非空和格式校验。
  - **防刷限流**: 依赖上游网关或统一的限流组件进行防护。

# 14. 备注与风险点
- 该接口涉及交易状态的变更，必须在事务(`@Transactional`)内执行，以保证数据的一致性。
- 消息发送为异步操作，需要关注消息的可靠性投递和消费端的幂等性处理，防止通知丢失或重复处理。
- 撤单校验规则(`validatorChain`)未来可能随业务发展而调整，新增或修改校验逻辑时，需要注意对现有流程的影响。

---
*Generated by AI Assistant at 2025-01-16 15:30:00* 