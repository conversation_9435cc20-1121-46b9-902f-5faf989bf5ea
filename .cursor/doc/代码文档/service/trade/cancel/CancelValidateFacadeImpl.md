# 1. 接口名称
撤单校验接口

# 2. 接口说明
该接口用于校验基金交易订单是否可以撤单。在用户发起撤单操作之前，系统需要通过此接口对订单进行全面的校验，确保订单满足撤单的各项条件。接口会检查账户状态、订单状态、支付状态、时间限制等多个维度，只有通过所有校验的订单才允许进行撤单操作。

**业务背景**：
- 基金交易订单在特定条件下允许撤单，如未支付、未上报、在撤单时间窗口内等
- 撤单校验是撤单流程的前置步骤，确保操作的合法性和安全性
- 支持电子成单和柜台成单两种订单类型的校验

**使用场景**：
- 用户在交易系统中发起撤单操作前的预校验
- 客服系统协助用户撤单前的合规性检查
- 批量撤单操作的前置校验

# 3. 接口类型
Dubbo

# 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.cancel.CancelValidateFacade.execute(CancelOrderValidateRequest request)
```

# 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK000001 | 客户在香港的唯一标识号 |
| 订单号 | dealNo | String | 是 | 20250427000001 | 交易订单的唯一标识号 |
| 交易码 | txCode | String | 是 | HW0011 | 交易类型编码，默认为HW0011（撤单校验） |

# 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 订单可以撤单 | 响应描述信息 |
| 数据封装 | data | Void | 否 | null | 数据内容，校验接口无返回数据 |

# 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 订单通过撤单校验，可以进行撤单操作 |
| C021001 | 客户不存在 | 根据香港客户号未找到对应客户信息 |
| C021002 | 客户状态异常 | 客户账户状态不允许进行交易操作 |
| C021033 | 不在开放期内不允许撤单 | 订单不在可撤单的时间窗口内 |
| C021034 | 跨天不可撤单 | 订单创建时间超过当天，不允许撤单 |
| C021035 | 交易订单不存在 | 根据订单号未找到对应的交易订单 |
| C021037 | 订单状态错误 | 订单当前状态不支持撤单操作 |
| C021044 | 订单状态不支持该操作 | 订单已提交或已完成，不允许撤单 |
| C021045 | 支付状态不支持该操作 | 订单已支付，不允许撤单 |
| C021046 | 成单方式不支持该操作 | 非电子成单订单不支持撤单 |

# 8. 关键业务逻辑说明

**核心处理流程**：

1. **参数校验**：
   - 验证香港客户号(hkCustNo)和订单号(dealNo)的非空性和格式正确性
   - 检查交易码(txCode)是否为有效的撤单校验交易码

2. **订单信息查询**：
   - 根据香港客户号和订单号查询交易订单信息
   - 构建交易上下文(TradeContext)，包含客户信息、订单信息、产品信息等

3. **责任链校验**：
   系统按照预定义的校验链顺序执行以下校验：
   
   - **账户状态校验(ACCOUNT_STATUS)**：检查客户账户是否正常，状态异常则不允许撤单
   - **订单未上报校验(ORDER_UN_SUBMIT)**：确保订单尚未上报给基金公司
   - **柜台在途撤单校验(COUNTER_IN_TRANSIT_CANCEL)**：检查是否存在柜台正在处理的撤单申请
   - **储蓄罐老基金编码校验(ORDER_REDEEM_PIGGY_OLD_FUND_CODE)**：针对储蓄罐赎回订单的特殊校验
   - **订单申请成功校验(ORDER_APPLY_SUCCESS)**：确保订单处于已申请成功状态
   - **订单未支付校验(ORDER_UN_PAY)**：只有未支付的订单才允许撤单
   - **电子成单校验(ELECTRONIC_ORDER)**：验证订单是否为电子成单方式
   - **撤单时间校验(CANCEL_ORDER_TIME)**：检查是否在允许撤单的时间窗口内
   - **撤单支付方式校验(CANCEL_ORDER_PAY_METHOD)**：验证支付方式是否支持撤单
   - **储蓄罐赎回校验(CANCEL_ORDER_CXG_REDEEM)**：储蓄罐相关订单的特殊校验规则

4. **校验结果处理**：
   - 所有校验通过：返回成功响应，表示订单可以撤单
   - 任一校验失败：抛出ValidateException异常，包含具体的错误码和错误描述

**判断分支**：
- 如果客户不存在或状态异常，直接返回相应错误
- 如果订单不存在或状态不符合撤单条件，返回订单状态错误
- 如果订单已支付或已上报，不允许撤单
- 如果超出撤单时间窗口（通常为当天），不允许撤单
- 如果是储蓄罐相关订单，需要额外检查储蓄罐协议状态

# 9. 流程图 (PlantUML)

```plantuml
@startuml
title 撤单校验接口流程图

start

:接收撤单校验请求;
:参数校验(hkCustNo, dealNo);

if (参数校验通过?) then (是)
  :查询交易订单信息;
  if (订单存在?) then (是)
    :构建交易上下文;
    :执行责任链校验;
    
    :账户状态校验;
    if (账户状态正常?) then (否)
      :抛出账户状态异常;
      stop
    endif
    
    :订单未上报校验;
    if (订单未上报?) then (否)
      :抛出订单状态错误;
      stop
    endif
    
    :柜台在途撤单校验;
    if (无在途撤单?) then (否)
      :抛出柜台在途撤单异常;
      stop
    endif
    
    :储蓄罐老基金编码校验;
    :订单申请成功校验;
    :订单未支付校验;
    if (订单未支付?) then (否)
      :抛出支付状态错误;
      stop
    endif
    
    :电子成单校验;
    if (电子成单?) then (否)
      :抛出成单方式错误;
      stop
    endif
    
    :撤单时间校验;
    if (在撤单时间窗口内?) then (否)
      :抛出撤单时间错误;
      stop
    endif
    
    :撤单支付方式校验;
    :储蓄罐赎回校验;
    
    if (所有校验通过?) then (是)
      :返回成功响应;
    else (否)
      :抛出校验异常;
      stop
    endif
  else (否)
    :抛出订单不存在异常;
    stop
  endif
else (否)
  :抛出参数校验异常;
  stop
endif

stop
@enduml
```

# 10. 时序图 (PlantUML)

```plantuml
@startuml
title 撤单校验接口调用时序

actor Client as C
participant CancelValidateFacadeImpl as F
participant CancelOrderValidateService as S
participant HwDealOrderRepository as R
participant TradeValidatorHelper as V
participant "校验器链" as VC

C -> F : execute(request)
activate F

F -> S : process(request)
activate S

S -> R : selectByHkCustNoAndDealNo(hkCustNo, dealNo)
activate R
R --> S : HwDealOrder
deactivate R

S -> S : buildTradeContext(order)
S -> S : buildValidatorChain()

S -> V : doValidate(context, validatorChain)
activate V

V -> VC : 账户状态校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 订单未上报校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 柜台在途撤单校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 储蓄罐老基金编码校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 订单申请成功校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 订单未支付校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 电子成单校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 撤单时间校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 撤单支付方式校验
activate VC
VC --> V : 校验通过
deactivate VC

V -> VC : 储蓄罐赎回校验
activate VC
VC --> V : 校验通过
deactivate VC

V --> S : 所有校验通过
deactivate V

S --> F : 校验完成
deactivate S

F --> C : Response.ok()
deactivate F

@enduml
```

# 11. 异常处理机制

**主要异常场景及处理方式**：

1. **参数校验异常**：
   - **场景**：请求参数为空或格式不正确
   - **处理**：抛出参数校验异常，返回具体的参数错误信息
   - **异常类型**：ValidateException

2. **业务校验异常**：
   - **场景**：订单状态、账户状态、时间限制等业务规则校验失败
   - **处理**：抛出ValidateException，包含具体的业务错误码和描述
   - **异常传播**：异常会被全局异常处理器捕获，转换为标准的Response格式返回

3. **数据访问异常**：
   - **场景**：数据库查询失败或数据不存在
   - **处理**：记录错误日志，抛出相应的业务异常
   - **容错机制**：对于查询不到数据的情况，明确返回"订单不存在"错误

4. **系统异常**：
   - **场景**：网络异常、服务不可用等系统级错误
   - **处理**：记录详细错误日志，返回通用系统错误信息
   - **监控告警**：触发系统监控告警，便于运维人员及时处理

**异常处理原则**：
- 所有异常都会被统一处理，确保返回格式的一致性
- 敏感信息不会暴露给调用方，只返回业务相关的错误描述
- 详细的错误信息会记录在系统日志中，便于问题排查

# 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| CancelOrderValidateService | 撤单校验业务服务，负责具体的校验逻辑处理 |
| TradeValidatorHelper | 交易校验助手，提供责任链校验能力 |
| HwDealOrderRepository | 交易订单数据访问层，提供订单查询功能 |
| TradeContext | 交易上下文对象，封装校验所需的各种信息 |
| ValidateException | 校验异常类，用于封装校验失败的错误信息 |
| Response | 通用响应封装类，提供统一的返回格式 |
| MyValidation | 参数校验注解，提供请求参数的自动校验 |
| ExceptionEnum | 异常枚举类，定义各种业务异常的错误码和描述 |

**外部系统依赖**：
- **数据库**：MySQL数据库，存储交易订单、客户信息等数据
- **Dubbo框架**：提供分布式服务调用能力
- **Spring框架**：提供依赖注入和事务管理

# 13. 幂等性与安全性说明

**幂等性**：
- **接口特性**：该接口具有天然的幂等性
- **实现方式**：相同的请求参数多次调用，返回结果保持一致
- **业务保证**：校验逻辑基于订单当前状态，不会因重复调用而改变订单数据

**安全性**：
- **参数校验**：对所有输入参数进行严格的格式和业务规则校验
- **权限控制**：通过Dubbo服务调用，依赖上层系统的权限控制
- **数据安全**：只进行数据查询和校验，不涉及敏感数据的修改
- **日志记录**：记录关键操作日志，便于审计和问题追踪

**限流与监控**：
- **调用频率**：建议对单个客户的调用频率进行适当限制
- **性能监控**：监控接口响应时间和成功率
- **异常监控**：对异常情况进行实时监控和告警

# 14. 备注与风险点

**注意事项**：
1. **时间窗口限制**：撤单校验有严格的时间限制，通常只允许当天撤单
2. **状态一致性**：校验时需要确保订单状态的实时性和一致性
3. **储蓄罐订单**：储蓄罐相关订单有特殊的校验规则，需要额外关注
4. **柜台订单**：柜台成单的订单可能有不同的撤单规则

**边界处理**：
1. **并发场景**：多个用户同时对同一订单进行撤单校验时的处理
2. **数据延迟**：考虑数据同步延迟对校验结果的影响
3. **系统维护**：系统维护期间的校验逻辑调整

**特殊逻辑说明**：
1. **责任链模式**：采用责任链模式进行校验，便于扩展和维护
2. **校验顺序**：校验器的执行顺序经过精心设计，优先执行快速校验，减少不必要的资源消耗
3. **异常传播**：校验失败时立即中断后续校验，提高接口响应速度

**风险点**：
1. **数据一致性风险**：订单状态在校验过程中可能发生变化
2. **性能风险**：复杂的校验链可能影响接口响应时间
3. **业务规则变更风险**：撤单规则的变更需要同步更新校验逻辑
