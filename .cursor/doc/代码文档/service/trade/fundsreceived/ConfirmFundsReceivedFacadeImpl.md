
# 接口详细设计文档：ConfirmFundsReceivedFacade

## 1. 接口名称
资金到账匹配确认

## 2. 接口说明
该接口用于处理资金到账的确认和匹配操作。当一笔订单的款项由客户打出并被系统接收后，后台运营人员或相关系统通过调用此接口，将实际到账的资金信息与待支付的订单进行关联和状态更新。接口的核心是确认订单支付成功，并触发后续的通知流程。主要使用场景为后台管理系统（CRM）中的手动资金到账确认功能。

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fundsreceived.ConfirmFundsReceivedFacade.execute(ConfirmFundsReceivedRequest request)
```

## 5. 请求参数表
### ConfirmFundsReceivedRequest
| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 订单号 | `dealNo` | String | 是 | "2024080800001" | 待确认的订单编号。 |
| 香港客户号 | `hkCustNo` | String | 是 | "HK0012345" | 客户在香港系统的唯一标识。 |
| 实际打款金额 | `actualPayAmt` | BigDecimal | 是 | 10000.50 | 客户实际支付的金额。 |
| 实际打款日期 | `actualPayDt` | String | 是 | "20240808" | 客户实际打款的日期（格式：yyyyMMdd）。 |
| 实际打款时间 | `actualPayTm` | String | 是 | "153000" | 客户实际打款的时间（格式：HHmmss）。 |
| 操作人 | `operator` | String | 否 | "admin" | 执行此操作的用户ID或名称。 |
| 币种不一致确认标识 | `currencyFlag` | String | 否 | "true" | 当订单币种与支付币种不一致时，前端二次确认后需上送此标识。 |
| 凭证状态确认标识 | `voucherStatusFlag` | String | 否 | "true" | 当打款凭证状态异常（如未上传、审核不通过）时，前端二次确认后需上送此标识。 |

### BaseRequest (父类)
| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 交易码 | `txCode` | String | 是 | "1122" | 标识当前交易类型的代码。 |
| 申请日期 | `appDt` | String | 是 | "20240808" | 请求发起的日期（格式：yyyyMMdd）。 |
| 申请时间 | `appTm` | String | 是 | "153000" | 请求发起的时间（格式：HHmmss）。 |
| 交易渠道 | `tradeChannel` | String | 是 | "9" | 发起交易的渠道，例如：9-CRM-PC。 |
| 网点号 | `outletCode` | String | 是 | "001" | 交易网点代码。 |
| IP地址 | `ipAddress` | String | 是 | "***********" | 客户端IP地址。 |
| 外部订单号 | `externalDealNo` | String | 否 | "EXT20240808001" | 外部系统传入的唯一订单号。 |
| MAC地址 | `macAddress` | String | 否 | "00-1A-2B-3C-4D-5E" | 客户端MAC地址。 |
| 设备序列号 | `deviceSerialNo` | String | 否 | "SN123456789" | 移动设备的序列号。 |
| 设备型号 | `deviceModel` | String | 否 | "iPhone 15 Pro" | 客户端设备型号。 |
| 设备名称 | `deviceName` | String | 否 | "My-iPhone" | 客户端设备名称。 |
| 系统版本号 | `systemVersion` | String | 否 | "iOS 17.5" | 客户端操作系统版本。 |

## 6. 响应参数表
### Response<ConfirmFundsReceivedResponse>
| 中文名 | 英文名 | 类型 | 字段说明 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | String | 接口处理结果的状态码，详见返回码说明。 |
| 描述信息 | `description` | String | 对处理结果的简要描述。 |
| 数据封装 | `data` | ConfirmFundsReceivedResponse | 具体的响应数据对象。 |

### ConfirmFundsReceivedResponse
| 中文名 | 英文名 | 类型 | 字段说明 |
| :--- | :--- | :--- | :--- |
| 描述 | `description` | String | 成功或二次确认时的详细描述信息。 |
| 二次确认字段 | `field` | String | 当需要二次确认时，该字段指示哪个场景需要确认（如：`currencyFlag`）。 |

## 7. 返回码说明
| 返回码 | 说明 | 备注 |
| :--- | :--- | :--- |
| 0000 | 成功 | 操作成功完成。 |
| 9999 | 系统异常 | 未知的内部错误。 |
| A001 | 需要二次确认 | 当出现特定业务场景（如币种不符、凭证状态异常）时返回，提示前端弹窗让用户确认。 |
| B00040019 | 订单不存在 | 根据传入的订单号和客户号未找到对应的订单记录。 |
| B00040003 | 订单状态错误 | 订单当前状态不是“申请成功”，无法进行到账确认。 |
| B00040004 | 订单已付款或已退款 | 订单的支付状态已经是“付款成功”或“已退款”，无需重复操作。 |
| B00040026 | 实际打款日期格式不正确 | `actualPayDt` 字段格式非 `yyyyMMdd`。 |
| B00040027 | 实际打款时间格式不正确 | `actualPayTm` 字段格式非 `HHmmss`。 |

## 8. 关键业务逻辑说明
1.  **前置校验**：接口首先对请求参数进行基础校验，如打款日期和时间的格式。
2.  **订单获取**：根据 `hkCustNo` 和 `dealNo` 查询 `hw_deal_order` 表，获取订单信息。若订单不存在，则中断并返回错误。
3.  **状态校验**：
    *   校验订单状态（`order_status`）是否为“申请成功”。如果不是，说明订单流程异常，中断并返回错误。
    *   校验支付状态（`pay_status`）是否为“未付款”、“付款中”或“付款失败”。如果订单已支付成功或已退款，则中断并返回错误，防止重复处理。
4.  **二次确认逻辑**：
    *   **凭证校验**：查询关联的打款凭证（`hw_pay_voucher_order`）。如果凭证状态为“未上传”、“待审核”或“审核不通过”，且请求中未包含 `voucherStatusFlag` 确认标识，则接口返回 `A001`，并附带提示信息，要求前端弹窗让用户确认是否继续。
    *   **币种校验**：检查订单币种与实际打款币种是否一致。如果不一致，且请求中未包含 `currencyFlag` 确认标识，同样返回 `A001` 进行二次确认。
5.  **核心处理**：当所有校验通过（或二次确认已由前端确认并上送相应 flag）后，执行以下操作：
    *   更新 `hw_deal_order` 表，将 `pay_status` 更新为“支付成功”（`1`），并记录 `actual_pay_amt`, `actual_pay_dt`, `actual_pay_tm` 等信息。
6.  **后续通知**：
    *   **MQ通知**：发送一条订单更新的MQ消息，通知CRM等其他依赖系统该订单状态已变更。
    *   **消息推送**：调用消息服务，向客户的投顾发送到账确认通知。如果该笔交易非全权委托，则同时向客户本人发送通知。

## 9. 流程图
```plantuml
@startuml
title 资金到账确认流程
start
:接收到账确认请求;
:基础参数校验（金额/日期/时间）;
if (校验失败?) then (是)
  :返回参数错误;
  stop
endif
:根据dealNo和hkCustNo查询订单;
if (订单不存在?) then (是)
  :返回订单不存在错误;
  stop
endif
:校验订单状态是否为“申请成功”;
if (状态不正确?) then (是)
  :返回订单状态错误;
  stop
endif
:校验支付状态是否为“未付款”/“付款中”/“失败”;
if (状态不正确?) then (是)
  :返回订单已支付或已退款错误;
  stop
endif
:查询关联的打款凭证;
if (凭证状态异常 且 未收到前端确认?) then (是)
  :返回"A001"要求二次确认;
  stop
endif
:检查支付币种与订单币种;
if (币种不一致 且 未收到前端确认?) then (是)
  :返回"A001"要求二次确认;
  stop
endif
:更新订单状态为"支付成功"，记录打款信息;
:发送MQ通知CRM等系统;
:发送消息通知投顾和客户;
:返回成功响应;
stop
@enduml
```

## 10. 时序图
```plantuml
@startuml
title 资金到账确认时序图
actor "后台操作员" as user
participant "ConfirmFundsReceivedFacadeImpl" as facade
participant "ConfirmFundsReceivedService" as service
participant "HwDealOrderRepository" as orderRepo
participant "HwPayVoucherOrderRepository" as voucherRepo
participant "SendMqService" as mqService
participant "SendMessageService" as msgService
database "数据库" as db

user -> facade: execute(request)
facade -> service: execute(request)
service -> service: doValidate(request)
service -> orderRepo: selectByHkCustNoAndDealNo(...)
orderRepo -> db: SELECT FROM hw_deal_order
db --> orderRepo: HwDealOrder
orderRepo --> service: HwDealOrder
service -> voucherRepo: listByTradeDealNo(...)
voucherRepo -> db: SELECT FROM hw_pay_voucher_order
db --> voucherRepo: List<HwPayVoucherOrder>
voucherRepo --> service: List<HwPayVoucherOrder>
alt 需要二次确认
    service --> facade: Response(code="A001", data=...)
    facade --> user: 需要二次确认
else 校验通过
    service -> orderRepo: updateByDealNo(...)
    orderRepo -> db: UPDATE hw_deal_order
    db --> orderRepo: 
    orderRepo --> service:
    service -> mqService: sendOrderUpdateMessage(...)
    service -> msgService: sendConfirmFundReceivedNotify(...)
    service --> facade: Response(code="0000")
    facade --> user: 操作成功
end
@enduml
```

## 11. 异常处理机制
- **业务异常**：通过抛出 `ValidateException` 并携带 `ExceptionEnum` 中的特定错误码来处理可预期的业务逻辑错误（如订单状态不正确、订单不存在等）。全局异常处理器会捕获此异常并转换为统一的 `Response` 格式返回给调用方。
- **系统异常**：对于不可预期的运行时异常（如数据库连接失败），由全局异常处理器统一捕获，记录错误日志，并返回统一的系统异常响应（如 code: "9999"）。

## 12. 调用的公共模块或外部依赖
| 模块/服务名称 | 功能简述 |
| :--- | :--- |
| `HwDealOrderRepository` | 订单表(`hw_deal_order`)的数据库操作服务。 |
| `HwPayVoucherOrderRepository` | 打款凭证审核表(`hw_pay_voucher_order`)的数据库操作服务。 |
| `HkCustInfoOuterService` | 调用外部服务获取客户在香港系统的详细信息。 |
| `SendMqService` | 发送MQ消息的公共服务，用于系统间的解耦和异步通知。 |
| `SendMessageService` | 发送短信、Push等消息通知的公共服务。 |

## 13. 幂等性与安全性说明
- **幂等性**：该接口具备幂等性。在业务校验逻辑中，会对订单的支付状态进行检查。如果一个请求被重复提交，在第一次成功处理后，订单的支付状态会变为“支付成功”。后续的重复请求会因为支付状态不满足前置条件（未付款/付款中/失败）而被拒绝，从而保证了业务操作不会被重复执行。
- **安全性**：接口的安全性依赖于Dubbo框架自身的安全机制以及上层网关的统一认证授权。业务代码层面未实现独立的鉴权逻辑。

## 14. 备注与风险点
- **二次确认机制**：接口的二次确认逻辑依赖于调用方（前端）的配合。后端返回 `A001` 码后，需要前端正确解析并弹窗提示，然后在用户确认后重新携带特定标识（如 `currencyFlag`）发起请求。这种方式增强了操作的严谨性，但也增加了前后端的耦合度。
- **事务一致性**：核心的数据库更新操作在一个事务中完成。但后续的MQ发送和消息推送是异步操作，存在分布式事务的最终一致性问题。如果消息发送失败，需要有重试或补偿机制来确保通知的可靠性。 