# 接口详细设计文档

## 1. 接口名称
柜台基金转换聚合接口

## 2. 接口说明
提供柜台交易场景下的基金转换相关功能，包括基金转换校验和基金转换订单创建。此接口是一个聚合接口，包含了转换前的校验和转换执行两个步骤，专门为柜台交易场景提供支持。

**业务背景**：
- 基金转换是指投资者将持有的某只基金份额转换为同一基金管理人管理的另一只基金份额的业务
- 转换操作需要严格的前置校验，确保转出基金和转入基金属于同一基金管理人
- 支持香港客户的基金转换业务，需要验证客户状态、基金状态、转换限制等多维度校验

**使用场景**：
- 柜台工作人员协助香港客户进行基金转换操作
- 客户通过柜台系统发起基金转换请求
- 投资顾问为客户进行资产配置调整时的基金转换操作

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fundtransfer.CounterFundTransferFacade
```

---

## 方法一：`addFundTransferOrder`

### 4.1. 方法说明
**基金转换订单保存接口**：执行基金转换业务逻辑，创建转换订单并返回订单信息。

### 4.2. 方法签名
```java
Response<CounterFundTransferVO> addFundTransferOrder(AddFundTransferRequest request)
```

### 4.3. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 转出基金 | outFundCode | String | 是 | 000001 | 需要转出的基金代码 |
| 转入基金 | inFundCode | String | 是 | 000002 | 需要转入的基金代码 |
| 香港客户号 | hkCustNo | String | 是 | HK123456 | 香港客户的唯一标识 |
| 申请份额 | appVol | BigDecimal | 是 | 1000.00 | 申请转换的基金份额 |
| 基金交易账号 | fundTxAcctNo | String | 是 | TX123456789 | 基金交易账户号码 |
| 交易码 | txCode | String | 否 | HW_ORDER_DEFAULT | 交易类型代码 |
| 申请日期 | appDt | String | 否 | 20250726 | 申请日期，格式yyyyMMdd |
| 申请时间 | appTm | String | 否 | 214603 | 申请时间，格式HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 1 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 交易网点编号 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT123456 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 我的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 15.0 | 操作系统版本 |

### 4.4. 响应参数表

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
| 状态码 | code | String | 200 | 响应状态码 |
| 描述信息 | description | String | 成功 | 响应描述信息 |
| 数据封装 | data | CounterFundTransferVO | - | 业务数据对象 |
| 订单号 | data.dealNo | String | 202507260001 | 生成的订单号 |
| 订单明细号 | data.dealDtlNo | String | 202507260001001 | 订单明细号 |

---

## 方法二：`addFundTransferValidator`

### 5.1. 方法说明
**基金转换校验接口**：在执行实际的基金转换操作前，对转换的所有前提条件进行全面校验。

### 5.2. 方法签名
```java
Response<Body> addFundTransferValidator(AddFundTransferValidatorRequest request)
```

### 5.3. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 转出基金 | outFundCode | String | 是 | 000001 | 需要转出的基金代码 |
| 转入基金 | inFundCode | String | 是 | 000002 | 需要转入的基金代码 |
| 香港客户号 | hkCustNo | String | 是 | HK123456 | 香港客户的唯一标识 |
| 申请份额 | appVol | BigDecimal | 是 | 1000.00 | 申请转换的基金份额 |
| 基金交易账号 | fundTxAcctNo | String | 是 | TX123456789 | 基金交易账户号码 |
| 交易码 | txCode | String | 否 | HW_ORDER_DEFAULT | 交易类型代码 |
| 申请日期 | appDt | String | 否 | 20250726 | 申请日期，格式yyyyMMdd |
| 申请时间 | appTm | String | 否 | 214603 | 申请时间，格式HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 1 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 交易网点编号 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT123456 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 我的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 15.0 | 操作系统版本 |

### 5.4. 响应参数表

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
| 状态码 | code | String | 200 | 响应状态码 |
| 描述信息 | description | String | 成功 | 响应描述信息 |
| 数据封装 | data | Body | {} | 空的业务数据对象 |

## 6. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 200 | 成功 | 操作成功完成 |
| 400 | 参数错误 | 请求参数不合法 |
| 500 | 系统错误 | 服务器内部错误 |
| TRANSFER_FUND_NOT_SAME_MANAGER | 转换基金不属于同一管理人 | 转出基金和转入基金必须属于同一基金管理人 |

## 7. 关键业务逻辑说明

### 7.1. 基金转换订单创建逻辑
1. **参数校验**：验证请求参数的完整性和格式正确性
2. **业务校验**：调用校验服务进行业务规则验证
3. **基金管理人校验**：确保转出基金和转入基金属于同一基金管理人
4. **转出基金校验**：验证转出基金的状态、客户持仓、赎回限制等
5. **转入基金校验**：验证转入基金的状态、申购限制、储蓄罐协议等
6. **订单创建**：创建基金转换订单和订单明细
7. **数据持久化**：保存订单信息到数据库
8. **消息发送**：发送订单创建消息到MQ

### 7.2. 基金转换校验逻辑
1. **参数校验**：验证请求参数的完整性和格式正确性
2. **基金管理人校验**：确保转出基金和转入基金属于同一基金管理人
3. **转出基金校验**：验证转出基金的各项业务规则
4. **转入基金校验**：验证转入基金的各项业务规则
5. **返回校验结果**：如果校验通过则正常返回，否则抛出业务异常

## 8. 流程图

@startuml
start
:接收基金转换请求;
:参数校验;
if (参数校验通过?) then (是)
  :构建交易上下文;
  :基金管理人校验;
  if (同一管理人?) then (是)
    :转出基金校验;
    if (转出基金校验通过?) then (是)
      :转入基金校验;
      if (转入基金校验通过?) then (是)
        if (是订单创建接口?) then (是)
          :创建订单;
          :保存订单到数据库;
          :发送MQ消息;
          :返回订单信息;
        else (否)
          :返回校验成功;
        endif
      else (否)
        :抛出转入基金校验异常;
      endif
    else (否)
      :抛出转出基金校验异常;
    endif
  else (否)
    :抛出管理人不一致异常;
  endif
else (否)
  :抛出参数校验异常;
endif
stop
@enduml

## 9. 时序图

@startuml
participant "客户端" as Client
participant "CounterFundTransferFacade" as Facade
participant "FundTransferService" as Service
participant "FundTransferValidatorService" as ValidatorService
participant "TradeValidatorHelper" as ValidatorHelper
participant "OrderCreateService" as OrderService
participant "数据库" as DB
participant "MQ" as MQ

Client -> Facade: addFundTransferOrder(request)
Facade -> Service: process(request)
Service -> Service: tradeValidate(request)
Service -> Service: buildContext(request)
Service -> ValidatorHelper: doValidate(context)
ValidatorHelper -> ValidatorHelper: 执行各项校验
ValidatorHelper --> Service: 校验结果
Service -> OrderService: createFundTransferOrder(context)
OrderService --> Service: OrderCreateBO
Service -> DB: saveDealOrder(orderCreateBO)
DB --> Service: 保存成功
Service -> MQ: sendOrderCreateMessage()
MQ --> Service: 发送成功
Service --> Facade: CounterFundTransferVO
Facade --> Client: Response<CounterFundTransferVO>

Client -> Facade: addFundTransferValidator(request)
Facade -> ValidatorService: process(request)
ValidatorService -> ValidatorService: doValidate(request)
ValidatorService -> ValidatorService: buildContext(request)
ValidatorService -> ValidatorHelper: doValidate(context)
ValidatorHelper --> ValidatorService: 校验结果
ValidatorService --> Facade: void
Facade --> Client: Response<Body>
@enduml

## 10. 异常处理机制

### 10.1. 主要异常场景
1. **参数校验异常**：请求参数为空、格式不正确、必填参数缺失
2. **基金管理人不一致异常**：转出基金和转入基金不属于同一基金管理人
3. **基金状态异常**：基金暂停申购、暂停赎回、基金清盘等
4. **客户状态异常**：客户账户冻结、风险等级不匹配等
5. **持仓不足异常**：转出基金持仓份额不足
6. **业务规则异常**：不满足转换条件、超出转换限额等

### 10.2. 异常处理方式
- 参数校验异常：通过@MyValidation注解自动校验，抛出ValidateException
- 业务规则异常：通过TradeValidatorHelper统一校验，抛出BusinessException
- 系统异常：记录错误日志，返回统一的系统错误响应
- 所有异常都会被统一的异常处理器捕获并转换为标准的Response格式

## 11. 调用的公共模块或外部依赖

### 11.1. 内部依赖模块
| 模块名称 | 功能简述 |
|----------|----------|
| FundTransferService | 基金转换业务处理服务 |
| FundTransferValidatorService | 基金转换校验服务 |
| TradeValidatorHelper | 交易校验助手，统一处理各种业务校验 |
| OrderCreateService | 订单创建服务，负责生成订单和订单明细 |
| SendMqService | MQ消息发送服务 |
| TradeContextBuilder | 交易上下文构建器 |

### 11.2. 外部依赖服务
| 服务名称 | 功能简述 |
|----------|----------|
| QueryFundInfoOuterService | 查询基金信息的外部服务 |
| FundShareService | 基金份额查询服务 |

### 11.3. 数据访问层
| 组件名称 | 功能简述 |
|----------|----------|
| HwDealOrderRepository | 订单数据访问层 |

## 12. 幂等性与安全性说明

### 12.1. 幂等性
- **基金转换校验接口**：具有幂等性，多次调用相同参数的校验请求返回相同结果
- **基金转换订单创建接口**：非幂等性，每次调用都会创建新的订单
- 建议客户端在调用订单创建接口前先调用校验接口，确保业务规则满足后再创建订单

### 12.2. 安全性
- **参数校验**：所有输入参数都经过严格的格式和业务规则校验
- **权限控制**：通过Dubbo服务调用，需要相应的服务调用权限
- **数据安全**：敏感信息如客户号、基金代码等都经过业务规则验证
- **审计日志**：关键操作都会记录详细的业务日志用于审计

### 12.3. 限流与监控
- 接口调用频率监控，防止恶意调用
- 业务异常监控，及时发现系统问题
- 性能监控，确保接口响应时间在合理范围内

## 13. 备注与风险点

### 13.1. 注意事项
1. **基金管理人校验**：转出基金和转入基金必须属于同一基金管理人，这是基金转换的基本要求
2. **份额精度**：申请份额需要符合基金的最小申购单位要求
3. **交易时间**：基金转换需要在基金的交易时间内进行
4. **储蓄罐基金**：如果转入基金是储蓄罐基金，需要验证客户是否签署储蓄罐协议

### 13.2. 边界处理
1. **最小转换份额**：需要满足基金的最小转换份额要求
2. **最大转换份额**：不能超过客户持有的转出基金份额
3. **转换费率**：系统会根据基金公司的费率规则计算转换费用
4. **交易日历**：需要考虑基金的交易日历，非交易日不能进行转换

### 13.3. 特殊逻辑说明
1. **赎回类型处理**：转出基金按照份额赎回方式处理
2. **净值确认**：转换涉及两只基金的净值确认，可能存在时间差
3. **资金清算**：转换过程中的资金清算由基金公司处理
4. **失败回滚**：如果转换过程中出现异常，需要进行相应的回滚处理

---

**文档版本**：1.0
**创建时间**：2025-07-26 21:46:03
**创建人**：hongdong.xie
**最后更新**：2025-07-26 21:46:03
