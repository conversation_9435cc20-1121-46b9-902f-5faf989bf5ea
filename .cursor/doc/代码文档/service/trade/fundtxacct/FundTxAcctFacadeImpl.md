# 接口详细设计文档

## 1. 接口名称
基金交易账号开通接口

## 2. 接口说明
提供基金交易账号开通功能。此接口用于为香港客户开通基金交易账号，支持全委和非全委两种账号类型的开通操作。

**业务背景**：
- 基金交易账号是客户进行基金交易的前置条件，每个客户针对不同基金产品需要开通对应的交易账号
- 支持全委专户和非全委两种账号类型，满足不同客户的投资需求
- 全委账号与基金代码绑定，非全委账号为通用账号

**使用场景**：
- 客户首次购买基金时需要开通对应的交易账号
- 投资顾问为客户配置新的基金产品时开通交易账号
- 系统自动为客户开通基金交易账号

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fundtxacct.FundTxAcctFacade.addFundTxAcct(AddFundTxAcctRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK123456 | 香港客户的唯一标识 |
| 基金交易账号类型 | fundTxAccType | String | 是 | 0 | 0-非全委 1-全委 |
| 基金代码 | fundCode | String | 否 | F001 | 基金产品代码，全委账号必填 |
| 交易码 | txCode | String | 否 | HW_ORDER_DEFAULT | 交易码，默认值 |
| 申请日期 | appDt | String | 否 | 20250727 | 申请日期 yyyyMMdd |
| 申请时间 | appTm | String | 否 | 210324 | 申请时间 HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 1 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 网点编号 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT123456 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 14.0 | 操作系统版本 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 操作成功 | 响应描述信息 |
| 数据封装 | data | Object | 是 | - | 响应数据对象 |
| 基金交易账号 | data.fundTxAcctNo | String | 是 | 0HK1234561 | 生成的基金交易账号 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 操作成功 |
| C020002 | 参数错误 | 请求参数不合法 |
| C021002 | 客户状态异常 | 客户状态不正常，无法开通账号 |
| C022001 | 基金交易账号已存在 | 该客户已存在相同类型的基金交易账号 |
| C020001 | 系统异常 | 系统内部错误 |

## 8. 关键业务逻辑说明

1. **参数校验**：
   - 校验香港客户号和基金交易账号类型为必填项
   - 全委账号类型时，基金代码为必填项

2. **客户状态校验**：
   - 调用香港账户中心接口查询客户信息
   - 验证客户状态必须为正常状态（0-正常）

3. **账号重复性校验**：
   - 根据客户号、账号类型、基金代码查询是否已存在相同账号
   - 如果已存在则抛出异常

4. **账号生成规则**：
   - 全委账号：账号类型(1) + 客户号 + 基金代码
   - 非全委账号：账号类型(0) + 客户号
   - 设置账号状态为正常（1-正常）

5. **数据保存**：
   - 将生成的基金交易账号信息保存到数据库
   - 返回生成的基金交易账号

## 9. 流程图

```plantuml
@startuml
start
:接收开通基金交易账号请求;
:参数校验;
if (参数校验通过?) then (否)
  :抛出参数错误异常;
  stop
endif
:查询香港客户信息;
if (客户状态正常?) then (否)
  :抛出客户状态异常;
  stop
endif
:查询是否已存在相同账号;
if (账号已存在?) then (是)
  :抛出账号已存在异常;
  stop
endif
:生成基金交易账号;
note right
  全委账号：1+客户号+基金代码
  非全委账号：0+客户号
end note
:保存账号信息到数据库;
:返回基金交易账号;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "调用方" as Client
participant "FundTxAcctFacadeImpl" as Facade
participant "AddFundTxAcctService" as Service
participant "HkCustInfoOuterService" as HkService
participant "FundTxAcctService" as FundService
participant "HwFundTxAcctRepository" as Repository
participant "数据库" as DB

Client -> Facade: addFundTxAcct(request)
Facade -> Service: process(request)
Service -> Service: 参数校验
Service -> HkService: getHkCustInfo(hkCustNo)
HkService --> Service: 返回客户信息
Service -> Service: 校验客户状态
Service -> FundService: selectByFundTxAcctType(hkCustNo, fundTxAccType, fundCode)
FundService -> Repository: selectByHkCustNoAndFundCode(hkCustNo, fundTxAccType, fundCode)
Repository -> DB: 查询基金交易账号
DB --> Repository: 返回查询结果
Repository --> FundService: 返回账号信息
FundService --> Service: 返回账号信息
Service -> Service: 校验账号是否存在
Service -> FundService: createFundTxAcct(hkCustNo, fundTxAccType, fundCode)
FundService --> Service: 返回生成的账号对象
Service -> Repository: save(hwFundTxAcctPO)
Repository -> DB: 保存账号信息
DB --> Repository: 保存成功
Repository --> Service: 保存成功
Service --> Facade: 返回响应对象
Facade --> Client: 返回Response<AddFundTxAcctResponse>
@enduml
```

## 11. 异常处理机制

### 主要异常场景及处理方式

1. **参数校验异常**：
   - **场景**：必填参数为空或格式不正确
   - **处理**：抛出ValidateException，返回参数错误码
   - **异常码**：C020002

2. **客户状态异常**：
   - **场景**：客户状态不为正常状态
   - **处理**：抛出ValidateException，返回客户状态异常码
   - **异常码**：C021002

3. **基金交易账号已存在异常**：
   - **场景**：相同类型的基金交易账号已存在
   - **处理**：抛出ValidateException，返回账号已存在异常码
   - **异常码**：C022001

4. **系统异常**：
   - **场景**：数据库操作失败、外部接口调用失败等
   - **处理**：抛出BusinessException，返回系统异常码
   - **异常码**：C020001

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| AddFundTxAcctService | 基金交易账号开通核心业务逻辑处理 |
| HkCustInfoOuterService | 香港客户信息查询外部服务 |
| FundTxAcctService | 基金交易账号相关业务服务，包括账号生成和查询 |
| HwFundTxAcctRepository | 基金交易账号数据访问层，提供数据库操作 |
| AbstractTradeService | 抽象交易服务基类，提供通用的校验框架 |
| Response | 统一响应对象封装 |
| ExceptionEnum | 异常枚举定义 |
| ValidateException | 校验异常类 |
| BusinessException | 业务异常类 |

## 13. 幂等性与安全性说明

### 幂等性
- **是否幂等**：否
- **说明**：每次调用都会尝试创建新的基金交易账号，不具备幂等性
- **重复调用影响**：如果基金交易账号已存在，会抛出异常，不会重复创建

### 安全性
- **鉴权**：依赖Dubbo框架的服务调用鉴权机制
- **限流**：无特殊限流机制，依赖系统级别的限流策略
- **验签**：无特殊验签要求
- **数据安全**：敏感信息如客户号需要按照数据安全规范处理

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_fund_tx_acct | 基金交易账号表 |

## 15. 备注与风险点

### 注意事项
1. **账号生成规则**：全委账号和非全委账号的生成规则不同，需要严格按照规则执行
2. **客户状态校验**：必须确保客户状态为正常才能开通账号
3. **重复性检查**：开通前必须检查是否已存在相同类型的账号

### 风险点
1. **并发风险**：多个请求同时为同一客户开通相同类型账号时，可能存在并发问题
2. **数据一致性**：账号生成和保存操作需要保证事务一致性
3. **外部依赖风险**：依赖香港账户中心接口，如果接口不可用会影响业务

### 边界处理
1. **全委账号**：必须提供基金代码，与基金代码强绑定
2. **非全委账号**：不需要基金代码，为通用账号
3. **账号唯一性**：同一客户的同一类型账号（全委+基金代码组合）必须唯一

## 11. 异常处理机制

### 主要异常场景及处理方式

1. **参数校验异常**：
   - **场景**：必填参数为空或格式不正确
   - **处理**：抛出ValidateException，返回参数错误码
   - **异常码**：C020002

2. **客户状态异常**：
   - **场景**：客户状态不为正常状态
   - **处理**：抛出ValidateException，返回客户状态异常码
   - **异常码**：C021002

3. **基金交易账号已存在异常**：
   - **场景**：相同类型的基金交易账号已存在
   - **处理**：抛出ValidateException，返回账号已存在异常码
   - **异常码**：C022001

4. **系统异常**：
   - **场景**：数据库操作失败、外部接口调用失败等
   - **处理**：抛出BusinessException，返回系统异常码
   - **异常码**：C020001

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| AddFundTxAcctService | 基金交易账号开通核心业务逻辑处理 |
| HkCustInfoOuterService | 香港客户信息查询外部服务 |
| FundTxAcctService | 基金交易账号相关业务服务，包括账号生成和查询 |
| HwFundTxAcctRepository | 基金交易账号数据访问层，提供数据库操作 |
| AbstractTradeService | 抽象交易服务基类，提供通用的校验框架 |
| Response | 统一响应对象封装 |
| ExceptionEnum | 异常枚举定义 |
| ValidateException | 校验异常类 |
| BusinessException | 业务异常类 |

## 13. 幂等性与安全性说明

### 幂等性
- **是否幂等**：否
- **说明**：每次调用都会尝试创建新的基金交易账号，不具备幂等性
- **重复调用影响**：如果基金交易账号已存在，会抛出异常，不会重复创建

### 安全性
- **鉴权**：依赖Dubbo框架的服务调用鉴权机制
- **限流**：无特殊限流机制，依赖系统级别的限流策略
- **验签**：无特殊验签要求
- **数据安全**：敏感信息如客户号需要按照数据安全规范处理

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_fund_tx_acct | 基金交易账号表 |

## 15. 备注与风险点

### 注意事项
1. **账号生成规则**：全委账号和非全委账号的生成规则不同，需要严格按照规则执行
2. **客户状态校验**：必须确保客户状态为正常才能开通账号
3. **重复性检查**：开通前必须检查是否已存在相同类型的账号

### 风险点
1. **并发风险**：多个请求同时为同一客户开通相同类型账号时，可能存在并发问题
2. **数据一致性**：账号生成和保存操作需要保证事务一致性
3. **外部依赖风险**：依赖香港账户中心接口，如果接口不可用会影响业务

### 边界处理
1. **全委账号**：必须提供基金代码，与基金代码强绑定
2. **非全委账号**：不需要基金代码，为通用账号
3. **账号唯一性**：同一客户的同一类型账号（全委+基金代码组合）必须唯一
