# 接口详细设计文档

## 1. 接口名称
柜台基金交易账号开通校验接口

## 2. 接口说明
提供柜台交易场景下的基金交易账号开通前的校验功能。此接口用于在实际开通基金交易账号前，检查指定的基金交易账号是否已经存在，避免重复开通，为柜台工作人员提供友好的提示信息。

**业务背景**：
- 基金交易账号是客户进行基金交易的前置条件，每个客户针对不同基金产品需要开通对应的交易账号
- 支持全委专户和非全委两种账号类型的校验
- 在批量开通基金交易账号前，需要先校验哪些账号已经存在，避免重复开通导致的业务异常

**使用场景**：
- 柜台工作人员在协助香港客户开通基金交易账号前的预校验
- 批量开通基金交易账号时的重复性检查
- 投资顾问为客户配置新的基金产品时的账号存在性验证

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fundtxacct.CounterFundTxAcctOpenValidateFacade.execute(CounterFundTxAcctOpenRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港客户的唯一标识 |
| 基金交易账号产品信息列表 | counterValidateFundTxAcctInfoList | List&lt;AddFundTxAcctRequest&gt; | 是 | - | 需要校验的基金交易账号信息列表 |

### AddFundTxAcctRequest 子对象字段

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港客户的唯一标识 |
| 基金交易账号类型 | fundTxAccType | String | 是 | 0 | 0-非全委；1-全委 |
| 基金代码 | fundCode | String | 否 | 000001 | 基金代码，全委类型时必填 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
| 响应码 | code | String | 0000 | 响应状态码 |
| 响应消息 | description | String | 操作成功 | 响应消息描述 |
| 响应数据 | data | CounterFundTxAcctOpenResponse | - | 响应数据对象 |
| 已存在的基金交易账号信息列表 | exitFundTxAcctInfoList | List&lt;ExitFundTxAcctInfo&gt; | - | 已存在的基金交易账号信息 |

### ExitFundTxAcctInfo 子对象字段

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
| 基金交易账号类型 | fundTxAcctType | String | 0 | 0-非全委；1-全委 |
| 基金交易账号 | fundTxAcctNo | String | 0HK10086 | 基金交易账号 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 校验操作成功完成 |
| C020002 | 参数错误 | 请求参数不合法或为空 |
| C020001 | 系统异常 | 服务器内部异常 |

## 8. 关键业务逻辑说明

### 核心处理流程
1. **参数校验**：检查请求参数的完整性，验证基金交易账号产品信息列表不能为空
2. **批量分片处理**：将基金交易账号信息列表按200个一批进行分片处理，避免单次处理数据量过大
3. **基金交易账号生成**：根据账号类型和客户信息生成对应的基金交易账号
   - 非全委账号：格式为 "0" + 香港客户号
   - 全委账号：格式为 "1" + 香港客户号 + 基金代码
4. **存在性检查**：查询数据库验证生成的基金交易账号是否已经存在
5. **结果汇总**：将所有已存在的基金交易账号信息汇总返回

### 关键判断分支
- **参数为空判断**：如果基金交易账号产品信息列表为空，抛出参数错误异常
- **账号类型判断**：根据基金交易账号类型（全委/非全委）生成不同格式的账号
- **存在性判断**：如果基金交易账号已存在，将其添加到返回结果中，不抛出异常

## 9. 流程图

```plantuml
@startuml
start
:接收校验请求;
:检查参数有效性;
if (基金交易账号信息列表为空?) then (是)
  :抛出参数错误异常;
  stop
endif
:按200个一批分片处理;
:遍历每个分片;
:根据账号类型生成基金交易账号;
note right
  非全委: 0 + 客户号
  全委: 1 + 客户号 + 基金代码
end note
:查询数据库检查账号是否存在;
if (账号已存在?) then (是)
  :添加到已存在列表;
endif
:处理下一个分片;
:汇总所有已存在的账号信息;
:返回校验结果;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "柜台系统" as Counter
participant "CounterFundTxAcctOpenValidateFacadeImpl" as Facade
participant "CounterFundTxAcctOpenValidateService" as Service
participant "AbstractCounterFundTxAcctOpenService" as AbstractService
participant "FundTxAcctService" as FundService
participant "HwFundTxAcctRepository" as Repository
participant "数据库" as DB

Counter -> Facade: execute(request)
Facade -> Service: process(request)
Service -> Service: 参数校验
Service -> Service: 按200个分片处理
loop 每个分片
  Service -> AbstractService: buildHwFundTxAcctPO(fundTxAcctInfoList)
  AbstractService -> FundService: createFundTxAcct(hkCustNo, fundTxAccType, fundCode)
  FundService --> AbstractService: 返回HwFundTxAcctPO
  AbstractService --> Service: 返回基金交易账号列表
  Service -> AbstractService: checkExitFundTxAcctInfos(hkCustNo, fundTxAcctPOS)
  AbstractService -> FundService: batchSelectByFundTxAcctNo(hkCustNo, fundTxAcctPOS)
  FundService -> Repository: batchSelectByFundTxAcctNo(hkCustNo, fundTxAcctPOS)
  Repository -> DB: 查询基金交易账号
  DB --> Repository: 返回查询结果
  Repository --> FundService: 返回已存在账号
  FundService --> AbstractService: 返回已存在账号
  AbstractService -> AbstractService: 转换为ExitFundTxAcctInfo
  AbstractService --> Service: 返回已存在账号信息
  Service -> Service: 汇总已存在账号信息
end
Service --> Facade: 返回校验结果
Facade --> Counter: 返回Response
@enduml
```

## 11. 异常处理机制

### 主要异常场景
1. **参数异常**：
   - 场景：基金交易账号产品信息列表为空
   - 处理：抛出BusinessException，异常码C020002
   - 影响：请求被拒绝，返回参数错误信息

2. **系统异常**：
   - 场景：数据库连接异常、服务调用超时等
   - 处理：抛出BusinessException，异常码C020001
   - 影响：请求失败，返回系统异常信息

### 异常处理策略
- 所有业务异常统一使用BusinessException包装
- 异常信息通过ExceptionEnum枚举定义，保证一致性
- 不会因为账号已存在而抛出异常，而是正常返回已存在的账号信息

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| CounterFundTxAcctOpenValidateService | 基金交易账号校验核心业务逻辑处理 |
| AbstractCounterFundTxAcctOpenService | 抽象基类，提供公共的账号检查和生成方法 |
| FundTxAcctService | 基金交易账号相关业务服务，包括账号生成和查询 |
| HwFundTxAcctRepository | 基金交易账号数据访问层，提供数据库操作 |
| Response | 统一响应对象封装 |
| ExceptionEnum | 异常枚举定义 |
| BusinessException | 业务异常类 |
| Google Guava Lists | 用于列表分片处理 |

## 13. 幂等性与安全性说明

### 幂等性
- **是否幂等**：是
- **说明**：多次调用相同参数的校验请求，返回结果一致
- **实现方式**：纯查询操作，不修改任何数据状态

### 安全性
- **鉴权**：依赖Dubbo框架的服务调用鉴权机制
- **限流**：无特殊限流机制，依赖系统级别的限流策略
- **验签**：无特殊验签要求
- **数据安全**：敏感信息如客户号需要按照数据安全规范处理

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_fund_tx_acct | 基金交易账号表 |

## 15. 备注与风险点

### 注意事项
1. **批量处理限制**：单次最多支持处理的基金交易账号数量建议控制在合理范围内，当前按200个分批处理
2. **账号生成规则**：基金交易账号的生成规则需要与实际开通逻辑保持一致
3. **数据一致性**：校验结果基于当前数据库状态，在高并发场景下可能存在时间窗口内的数据不一致

### 特殊逻辑说明
1. **分片处理逻辑**：使用Google Guava的Lists.partition方法进行分片，每批200个
2. **账号类型区分**：全委账号需要包含基金代码，非全委账号不需要
3. **结果汇总**：所有分片的校验结果会汇总到同一个响应对象中返回

### 边界处理
1. **空列表处理**：如果没有已存在的账号，返回空的exitFundTxAcctInfoList
2. **重复账号处理**：如果同一批次中有重复的账号信息，会去重处理
3. **异常恢复**：单个分片处理异常不会影响其他分片的处理
