# 接口详细设计文档

## 1. 接口名称
柜台基金交易账号开通接口

## 2. 接口说明
提供柜台交易场景下的基金交易账号开通功能。此接口用于为香港客户批量开通基金交易账号，支持全委和非全委两种账号类型的开通操作。

**业务背景**：
- 基金交易账号是客户进行基金交易的前置条件，每个客户针对不同基金产品需要开通对应的交易账号
- 支持全委专户和非全委两种账号类型，满足不同客户的投资需求
- 柜台场景下需要批量开通多个基金交易账号，提高操作效率

**使用场景**：
- 柜台工作人员协助香港客户开通基金交易账号
- 客户首次购买基金时需要开通对应的交易账号
- 投资顾问为客户配置新的基金产品时开通交易账号

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fundtxacct.CounterFundTxAcctOpenFacade.execute(CounterFundTxAcctOpenRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港客户的唯一标识 |
| 基金交易账号产品信息列表 | counterValidateFundTxAcctInfoList | List&lt;AddFundTxAcctRequest&gt; | 是 | - | 需要开通的基金交易账号信息列表 |
| 交易码 | txCode | String | 是 | TX001 | 交易类型标识码 |
| 申请日期 | appDt | String | 是 | 20250427 | 申请日期，格式：yyyyMMdd |
| 申请时间 | appTm | String | 是 | 143000 | 申请时间，格式：HHmmss |
| 交易渠道 | tradeChannel | String | 是 | 1 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 交易网点编号 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT123456 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 张三的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 14.0 | 操作系统版本 |

### AddFundTxAcctRequest 子对象字段

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港客户的唯一标识 |
| 基金交易账号类型 | fundTxAccType | String | 是 | 0 | 0-非全委；1-全委 |
| 基金代码 | fundCode | String | 否 | 000001 | 基金产品代码 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
| 响应码 | code | String | 200 | 响应状态码 |
| 响应消息 | message | String | 成功 | 响应消息描述 |
| 响应数据 | data | CounterFundTxAcctOpenResponse | - | 响应数据对象 |
| 已存在的基金交易账号信息列表 | exitFundTxAcctInfoList | List&lt;ExitFundTxAcctInfo&gt; | - | 已存在的基金交易账号信息 |

### ExitFundTxAcctInfo 子对象字段

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|--------|--------|------|--------|----------|
| 基金交易账号类型 | fundTxAcctType | String | 0 | 0-非全委；1-全委 |
| 基金交易账号 | fundTxAcctNo | String | 123456789 | 基金交易账号 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 200 | 成功 | 操作成功完成 |
| 400 | 参数错误 | 请求参数不合法 |
| 500 | 系统内部错误 | 服务器内部异常 |

## 8. 关键业务逻辑说明

1. **参数校验**：验证请求参数的完整性和合法性，包括香港客户号、基金交易账号信息列表等必填字段
2. **批量处理**：将基金交易账号信息按200个一批进行分批处理，避免单次处理数据量过大
3. **账号生成**：根据客户号、账号类型和基金代码生成基金交易账号
4. **重复性检查**：检查生成的基金交易账号是否已存在，如存在则返回已存在的账号信息
5. **批量保存**：如无重复账号，则批量保存所有新生成的基金交易账号到数据库
6. **异常处理**：对业务异常进行统一处理和返回

## 9. 流程图

```plantuml
@startuml
start
:接收开通请求;
:校验请求参数;
if (参数校验通过?) then (否)
  :抛出参数异常;
  stop
else (是)
  :获取基金交易账号信息列表;
  :按200个一批分组处理;
  
  partition "批量处理" {
    :生成基金交易账号PO对象;
    :检查账号是否已存在;
    if (存在重复账号?) then (是)
      :收集已存在账号信息;
    else (否)
      :继续处理下一批;
    endif
  }
  
  if (有重复账号?) then (是)
    :返回已存在账号信息;
  else (否)
    :批量保存所有账号;
    :返回成功结果;
  endif
endif
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "柜台系统" as Counter
participant "CounterFundTxAcctOpenFacadeImpl" as Facade
participant "CounterFundTxAcctOpenService" as Service
participant "FundTxAcctService" as FundService
participant "HwFundTxAcctRepository" as Repository
participant "数据库" as DB

Counter -> Facade: execute(request)
Facade -> Service: process(request)

Service -> Service: 参数校验
Service -> Service: 按200个分批处理

loop 每批处理
  Service -> FundService: createFundTxAcct()
  FundService -> Service: 返回HwFundTxAcctPO
  Service -> FundService: batchSelectByFundTxAcctNo()
  FundService -> DB: 查询已存在账号
  DB -> FundService: 返回查询结果
  FundService -> Service: 返回已存在账号列表
end

alt 存在重复账号
  Service -> Facade: 返回已存在账号信息
  Facade -> Counter: 返回Response(exitFundTxAcctInfoList)
else 无重复账号
  Service -> Repository: batchSave(allFundTxAcctPOS)
  Repository -> DB: 批量插入账号
  DB -> Repository: 插入成功
  Repository -> Service: 保存成功
  Service -> Facade: 返回空响应
  Facade -> Counter: 返回Response(success)
end
@enduml
```

## 11. 异常处理机制

### 主要异常场景及处理方式

1. **参数异常**
   - **场景**：请求参数为空或基金交易账号信息列表为空
   - **处理**：抛出BusinessException(ExceptionEnum.PARAMS_ERROR)
   - **返回**：400错误码，提示参数错误

2. **数据库异常**
   - **场景**：批量保存基金交易账号时数据库操作失败
   - **处理**：事务回滚，抛出系统异常
   - **返回**：500错误码，提示系统内部错误

3. **业务逻辑异常**
   - **场景**：基金交易账号生成失败或校验失败
   - **处理**：记录错误日志，返回具体错误信息
   - **返回**：相应业务错误码和错误描述

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| CounterFundTxAcctOpenService | 基金交易账号开通核心业务逻辑处理 |
| FundTxAcctService | 基金交易账号相关业务服务，包括账号生成和查询 |
| HwFundTxAcctRepository | 基金交易账号数据访问层，提供数据库操作 |
| AbstractCounterFundTxAcctOpenService | 抽象基类，提供公共的账号检查和生成方法 |
| Response | 统一响应对象封装 |
| ExceptionEnum | 异常枚举定义 |
| BusinessException | 业务异常类 |

## 13. 幂等性与安全性说明

### 幂等性
- **是否幂等**：否
- **说明**：每次调用都会尝试创建新的基金交易账号，不具备幂等性
- **重复调用影响**：如果基金交易账号已存在，会返回已存在的账号信息，不会重复创建

### 安全性
- **鉴权**：依赖Dubbo框架的服务调用鉴权机制
- **限流**：无特殊限流机制，依赖系统级别的限流策略
- **验签**：无特殊验签要求
- **数据安全**：敏感信息如客户号需要按照数据安全规范处理

## 14. 备注与风险点

### 注意事项
1. **批量处理限制**：单次最多支持处理的基金交易账号数量建议控制在合理范围内，避免超时
2. **事务一致性**：批量保存操作需要保证事务一致性，要么全部成功要么全部失败
3. **重复账号处理**：当存在重复账号时，系统会返回已存在的账号信息，调用方需要根据返回结果进行相应处理

### 边界处理
1. **空列表处理**：当基金交易账号信息列表为空时，抛出参数异常
2. **大批量数据**：采用分批处理机制，每批200个，避免单次处理数据量过大导致性能问题
3. **并发处理**：在高并发场景下，可能存在同时创建相同账号的情况，需要依赖数据库唯一约束保证数据一致性

### 特殊逻辑说明
1. **账号生成规则**：基金交易账号的生成规则由FundTxAcctService统一管理
2. **分批处理逻辑**：使用Google Guava的Lists.partition方法进行分批，每批200个记录
3. **存在性检查**：通过查询数据库确认账号是否已存在，而不是简单的内存检查

---

**文档生成时间**：2025-07-27 20:55:16
**接口实现类**：CounterFundTxAcctOpenFacadeImpl
**作者**：jinqing.rao
