# ChangePayMethodFacadeImpl 接口设计文档

## 1. 接口名称
支付方式修改接口

## 2. 接口说明
该接口用于修改海外基金交易订单的支付方式，支持电汇、支票、海外储蓄罐三种支付方式的切换。主要应用于客户在订单提交后、支付完成前需要变更支付方式的场景。接口会对订单状态、支付方式有效性、资金账号等进行严格校验，确保修改操作的合规性和安全性。

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
- **接口类**: `com.howbuy.dtms.order.client.facade.trade.changeorder.ChangePayMethodFacade`
- **方法签名**: `Response<ChangePayMethodResponse> execute(ChangePayMethodRequest request)`

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 客户在香港的唯一标识 |
| 订单号 | dealNo | String | 是 | D20250424001 | 需要修改支付方式的订单号 |
| 支付方式 | payMethod | String | 是 | 1 | 1-电汇、2-支票、3-海外储蓄罐 |
| 资金账号 | cpAcctNo | String | 否 | 6225887744556633 | 支付方式为1-电汇时必填 |
| 交易渠道 | tradeChannel | String | 否 | 9 | 交易发起渠道 |
| 申请日期 | appDt | String | 否 | 20250424 | 申请日期，格式：YYYYMMDD |
| 申请时间 | appTm | String | 否 | 164118 | 申请时间，格式：HHmmss |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 成功 | 响应描述信息 |
| 数据封装 | data | ChangePayMethodResponse | 否 | {} | 响应数据对象，当前为空对象 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 支付方式修改成功 |
| C020001 | 系统异常 | 系统内部错误 |
| C020002 | 参数错误 | 请求参数校验失败 |
| C021001 | 客户不存在 | 香港客户号不存在 |
| C021002 | 客户状态异常 | 客户账户状态不允许交易 |
| C023001 | 订单不存在 | 指定订单号不存在 |
| C023002 | 订单状态异常 | 订单状态不允许修改支付方式 |
| C024001 | 支付方式无效 | 不支持的支付方式 |
| C024002 | 储蓄罐协议未签约 | 选择储蓄罐支付但客户未签约 |

## 8. 关键业务逻辑说明

1. **参数校验阶段**：
   - 校验必填参数的完整性和格式正确性
   - 验证香港客户号和订单号的有效性
   - 检查支付方式代码的合法性

2. **业务规则校验**：
   - 账户状态校验：确保客户账户状态正常，允许进行交易操作
   - 订单状态校验：验证订单处于可修改状态（未提交、申请成功等状态）
   - 支付方式校验：检查新的支付方式是否被客户支持
   - 资金账号校验：当支付方式为电汇时，验证资金账号的有效性

3. **特殊业务逻辑**：
   - 储蓄罐支付校验：如果选择海外储蓄罐支付，需要验证客户是否已签署储蓄罐协议
   - 支付方式变更限制：某些特定状态的订单不允许修改支付方式

4. **数据更新操作**：
   - 更新订单的支付方式信息
   - 更新相关的资金账号信息（如适用）
   - 记录操作日志和变更历史

5. **消息通知**：
   - 发送订单修改消息到MQ，通知相关系统订单信息变更

## 9. 流程图

```plantuml
@startuml
start
:接收支付方式修改请求;
:参数校验;
if (参数校验通过?) then (否)
  :返回参数错误;
  stop
endif

:查询订单信息;
if (订单存在?) then (否)
  :返回订单不存在错误;
  stop
endif

:构建交易上下文;
:执行业务校验链;
note right
  - 账户状态校验
  - 订单状态校验
  - 支付方式校验
  - 资金账号校验
end note

if (校验通过?) then (否)
  :返回业务校验错误;
  stop
endif

:更新订单支付方式;
:发送订单修改消息;
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "客户端" as Client
participant "ChangePayMethodFacadeImpl" as Facade
participant "ChangePayMethodService" as Service
participant "TradeValidatorHelper" as Validator
participant "OrderUpdateService" as OrderUpdate
participant "HwDealOrderRepository" as OrderRepo
participant "SendMqService" as MQ

Client -> Facade: execute(request)
Facade -> Service: execute(request)

Service -> Service: doValidate(request)
Service -> OrderRepo: selectByHkCustNoAndDealNo()
OrderRepo --> Service: 订单信息

Service -> Validator: doValidate(context, validatorList)
note right
  执行校验链：
  - ACCOUNT_STATUS
  - ORDER_UN_SUBMIT  
  - ORDER_APPLY_SUCCESS
  - CHANGE_PAY_METHOD
  - CP_ACCT_NO
end note
Validator --> Service: 校验结果

Service -> OrderUpdate: changePayMethod(context, payMethod)
OrderUpdate --> Service: OrderUpdateBO

Service -> OrderRepo: updateDealOrder(orderUpdateBO)
OrderRepo --> Service: 更新结果

Service -> MQ: sendOrderUpdateMessage(hwDealOrder)
MQ --> Service: 发送结果

Service --> Facade: Response<ChangePayMethodResponse>
Facade --> Client: 响应结果
@enduml
```

## 11. 异常处理机制

### 11.1 参数校验异常
- 使用`@MyValidation`注解进行参数自动校验
- 必填参数为空时抛出`ValidateException`异常
- 参数格式不正确时返回具体的错误信息

### 11.2 业务校验异常
- 通过校验链模式进行多层业务规则校验
- 每个校验器独立处理特定的业务规则
- 校验失败时抛出`ValidateException`，包含具体的错误码和描述

### 11.3 数据库操作异常
- 数据库连接异常时抛出`DB_ERROR`异常
- 数据更新失败时进行事务回滚
- 并发更新冲突时抛出`CONCURRENT_ERROR`异常

### 11.4 外部服务调用异常
- 调用储蓄罐协议查询服务失败时的降级处理
- 消息队列发送失败时的重试机制
- 超时异常的处理和日志记录

## 12. 调用的公共模块或外部依赖

### 12.1 内部模块
- **TradeContextBuilder**: 构建交易上下文对象
- **TradeValidatorHelper**: 校验链管理器，协调多个校验器的执行
- **OrderUpdateService**: 订单更新业务服务
- **SendMqService**: 消息队列发送服务
- **HwDealOrderRepository**: 订单数据访问层
- **HwDealOrderDtlRepository**: 订单明细数据访问层

### 12.2 外部依赖
- **CounterOrderQueryOuterService**: 中台订单查询服务，用于验证储蓄罐协议签约状态
- **PayMethodEnum**: 支付方式枚举类，定义支持的支付方式
- **ExceptionEnum**: 异常枚举类，定义业务异常码和描述

### 12.3 基础组件
- **AssertUtils**: 断言工具类，用于参数校验
- **DateUtils**: 日期工具类，用于时间格式处理
- **LogUtils**: 日志工具类，用于结构化日志输出

## 13. 幂等性与安全性说明

### 13.1 幂等性
- **接口幂等性**: 相同参数的重复调用产生相同结果，不会造成数据不一致
- **实现方式**: 通过订单状态和支付方式的组合判断，避免重复修改
- **并发控制**: 使用数据库行锁防止并发修改同一订单

### 13.2 安全性
- **参数校验**: 严格的参数格式和业务规则校验
- **权限控制**: 验证客户号与订单的归属关系
- **数据完整性**: 事务保证数据修改的原子性
- **审计日志**: 记录所有修改操作的详细日志

### 13.3 限流与监控
- **接口限流**: 基于客户号的调用频率限制
- **性能监控**: 接口响应时间和成功率监控
- **异常告警**: 异常率超过阈值时的实时告警

## 14. 备注与风险点

### 14.1 注意事项
- 支付方式修改仅在订单特定状态下允许，需要严格控制修改时机
- 储蓄罐支付需要额外的协议签约校验，增加了业务复杂度
- 资金账号校验依赖外部系统，需要考虑服务可用性

### 14.2 风险点
- **数据一致性风险**: 订单更新和消息发送之间的一致性保证
- **外部依赖风险**: 储蓄罐协议查询服务不可用时的处理策略
- **并发修改风险**: 多个操作同时修改同一订单时的数据冲突

### 14.3 边界处理
- **订单状态边界**: 明确定义哪些状态下允许修改支付方式
- **支付方式边界**: 某些特殊产品可能限制特定支付方式
- **时间边界**: 考虑交易时间窗口对支付方式修改的影响
