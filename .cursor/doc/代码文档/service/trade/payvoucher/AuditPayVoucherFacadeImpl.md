# 审核付款凭证接口设计文档

## 1. 接口名称
审核付款凭证接口

## 2. 接口说明
该接口用于审核客户上传的付款凭证，支持对汇款银行卡、汇款币种、汇款金额、备注、凭证文件等各项内容进行审核，并记录审核意见。主要应用于海外基金交易业务中的付款凭证审核流程，确保客户提交的付款信息真实有效。

**业务背景**：在海外基金交易中，客户需要上传付款凭证证明其已完成汇款，运营人员需要对这些凭证进行审核，确认汇款信息的准确性。

**使用场景**：
- 运营人员审核客户上传的付款凭证
- 对凭证中的各项信息进行逐项审核
- 记录审核意见和审核结果
- 更新凭证审核状态

## 3. 接口类型
Dubbo接口

## 4. 接口地址或方法签名
**接口类**：`com.howbuy.dtms.order.client.facade.trade.payvoucher.AuditPayVoucherFacade`
**方法签名**：`Response<AuditPayVoucherResponse> execute(AuditPayVoucherRequest auditPayVoucherRequest)`

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 打款凭证流水号 | voucherNo | String | 是 | "202407220001" | 待审核的凭证流水号 |
| 交易订单号 | tradeOrderNo | String | 否 | "HW202407220001" | 交易下单凭证时必传 |
| 操作人 | operator | String | 是 | "admin" | 审核操作人员 |
| 汇款银行卡审核通过标识 | remitCpAcctNoPassFlag | boolean | 是 | true | 汇款银行卡是否审核通过 |
| 汇款银行卡审核意见 | remitCpAcctNoRemark | String | 否 | "银行卡信息正确" | 汇款银行卡审核意见 |
| 汇款币种审核通过标识 | remitCurrencyPassFlag | boolean | 是 | true | 汇款币种是否审核通过 |
| 汇款币种审核意见 | remitCurrencyRemark | String | 否 | "币种正确" | 汇款币种审核意见 |
| 汇款金额审核通过标识 | remitAmtPassFlag | boolean | 是 | true | 汇款金额是否审核通过 |
| 汇款金额审核意见 | remitAmtRemark | String | 否 | "金额正确" | 汇款金额审核意见 |
| 备注审核通过标识 | remarkPassFlag | boolean | 是 | true | 备注是否审核通过 |
| 备注审核意见 | remarkRemark | String | 否 | "备注信息完整" | 备注审核意见 |
| 实际到账金额 | actualPayAmt | BigDecimal | 否 | 10000.00 | 实际到账金额 |
| 实际到账币种 | actualPayCurrency | String | 否 | "USD" | 实际到账币种 |
| 实际到账日期 | actualPayDt | String | 否 | "20240722" | 实际到账日期(yyyyMMdd) |
| 实际到账时间 | actualPayTm | String | 否 | "143000" | 实际到账时间(HHmmss) |
| 入账流水号 | receiptSerialNo | String | 否 | "RCP202407220001" | 入账流水号 |
| 凭证文件审核意见列表 | fileTypeAuditList | List<AuditPayVoucherFileTypeDTO> | 否 | - | 凭证文件审核意见 |
| 审核状态 | auditStatus | String | 是 | "3" | 审核状态(3-审核通过,4-审核不通过) |
| 重复凭证标识 | duplicateVoucher | String | 否 | "0" | 重复凭证(1-是,0-否) |
| 交易码 | txCode | String | 否 | "HW_ORDER_DEFAULT" | 交易码 |
| 申请日期 | appDt | String | 否 | "20240722" | 申请日期(yyyyMMdd) |
| 申请时间 | appTm | String | 否 | "143000" | 申请时间(HHmmss) |
| 交易渠道 | tradeChannel | String | 否 | "9" | 交易渠道(1-柜台,9-CRM-PC等) |
| 网点号 | outletCode | String | 否 | "001" | 网点号 |
| IP地址 | ipAddress | String | 否 | "***********" | IP地址 |
| 外部订单号 | externalDealNo | String | 否 | "EXT202407220001" | 外部订单号 |
| MAC地址 | macAddress | String | 否 | "00:11:22:33:44:55" | MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | "DEV001" | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | "iPhone12" | 设备型号 |
| 设备名称 | deviceName | String | 否 | "iPhone" | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | "iOS 15.0" | 系统版本号 |

### 5.1 AuditPayVoucherFileTypeDTO 结构

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 材料类型 | fileType | String | 是 | "1" | 材料类型或文件类型ID |
| 材料类型描述 | fileTypeDesc | String | 否 | "银行转账凭证" | 材料类型描述 |
| 材料来源 | fileSource | String | 是 | "0" | 材料来源(0-dtms-order,1-crm-trade) |
| 文件ID | fileId | String | 否 | "FILE001" | CRM资料审核时的文件ID |
| 审核意见 | remark | String | 否 | "文件清晰" | 审核意见 |
| 审核通过标识 | passFlag | boolean | 是 | true | 是否审核通过 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | "0000" | 响应状态码 |
| 描述信息 | description | String | 是 | "成功" | 响应描述信息 |
| 数据封装 | data | AuditPayVoucherResponse | 是 | - | 响应数据 |
| 凭证流水号 | data.voucherNo | String | 是 | "202407220001" | 审核完成的凭证流水号 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 审核操作成功 |
| C020001 | 系统异常 | 系统内部错误 |
| C020002 | 参数错误 | 请求参数不正确 |
| C020003 | 并发异常 | 并发操作冲突 |
| C020996 | 数据库异常 | 数据库操作失败 |
| C023001 | 凭证订单不存在 | 指定的凭证流水号不存在 |

## 8. 关键业务逻辑说明

1. **参数校验**：
   - 验证凭证流水号是否存在
   - 验证操作人是否有效
   - 验证审核状态是否合法

2. **数据查询**：
   - 根据凭证流水号查询凭证订单信息
   - 查询关联的香港客户信息
   - 查询银行卡信息
   - 查询交易订单信息（如果是交易下单凭证）

3. **审核处理**：
   - 更新凭证订单的审核状态
   - 记录各项审核意见
   - 更新实际到账信息
   - 生成审核详情记录

4. **状态流转**：
   - 已上传 → 审核通过/审核不通过
   - 记录审核操作流水
   - 更新审核时间和操作人

5. **外部系统交互**：
   - 调用CRM系统进行资料审核（如果有CRM文件）
   - 发送审核结果消息通知

## 9. 流程图

```plantuml
@startuml
start
:接收审核请求;
:校验请求参数;
if (参数校验通过?) then (否)
  :返回参数错误;
  stop
endif
:查询凭证订单信息;
if (凭证订单存在?) then (否)
  :返回凭证不存在错误;
  stop
endif
:查询客户信息;
:查询银行卡信息;
if (是交易下单凭证?) then (是)
  :查询交易订单信息;
endif
:构建审核上下文;
:执行业务校验;
if (业务校验通过?) then (否)
  :返回业务错误;
  stop
endif
:更新凭证审核状态;
:记录审核意见;
:更新实际到账信息;
:生成审核变更记录;
if (有CRM文件需要审核?) then (是)
  :调用CRM审核接口;
endif
:发送审核结果消息;
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "客户端" as Client
participant "AuditPayVoucherFacadeImpl" as Facade
participant "AuditPayVoucherService" as Service
participant "PayVoucherValidator" as Validator
participant "HwPayVoucherOrderRepository" as Repository
participant "HkCustInfoOuterService" as CustService
participant "HkBankCardInfoOuterService" as BankService
participant "CounterDealOuterService" as CounterService
participant "SendMessageService" as MessageService
participant "MySQL数据库" as DB

Client -> Facade: execute(AuditPayVoucherRequest)
Facade -> Service: execute(request)
Service -> Validator: validate(request)
Validator -> Repository: selectByVouchNo(voucherNo)
Repository -> DB: 查询凭证订单
DB --> Repository: 返回凭证订单信息
Repository --> Validator: 返回凭证订单
Validator -> CustService: getHkCustInfo(hkCustNo)
CustService --> Validator: 返回客户信息
Validator -> BankService: getHkBankCardByCpAcctNo(hkCustNo, cpAcctNo)
BankService --> Validator: 返回银行卡信息
Validator --> Service: 返回验证上下文
Service -> Repository: updatePayVoucherOrder(context)
Repository -> DB: 更新凭证订单状态
DB --> Repository: 更新成功
Service -> Repository: insertAuditRecord(auditRecord)
Repository -> DB: 插入审核记录
DB --> Repository: 插入成功
alt 有CRM文件需要审核
  Service -> CounterService: auditCrmFiles(fileList)
  CounterService --> Service: 返回审核结果
end
Service -> MessageService: sendAuditResultMessage(context)
MessageService --> Service: 发送成功
Service --> Facade: 返回响应结果
Facade --> Client: Response<AuditPayVoucherResponse>
@enduml
```

## 11. 异常处理机制

### 11.1 主要异常场景

1. **参数异常**：
   - 凭证流水号为空或格式错误
   - 操作人为空
   - 审核状态不合法
   - **处理方式**：抛出ValidateException，返回参数错误码

2. **业务异常**：
   - 凭证订单不存在
   - 凭证状态不允许审核
   - 客户信息不存在
   - **处理方式**：抛出BusinessException，返回对应业务错误码

3. **系统异常**：
   - 数据库连接异常
   - 外部服务调用失败
   - **处理方式**：抛出SystemException，返回系统错误码

4. **并发异常**：
   - 多人同时审核同一凭证
   - **处理方式**：使用乐观锁机制，抛出ConcurrentException

### 11.2 异常处理策略

- 所有异常统一由全局异常处理器捕获
- 记录详细的异常日志
- 返回标准化的错误响应
- 敏感信息不暴露给客户端

## 12. 调用的公共模块或外部依赖

### 12.1 内部模块

| 模块名称 | 功能简述 |
|----------|----------|
| AuditPayVoucherService | 审核付款凭证核心业务服务 |
| PayVoucherValidator | 付款凭证业务校验器 |
| HwPayVoucherOrderRepository | 付款凭证订单数据访问层 |
| HwDealOrderRepository | 交易订单数据访问层 |
| SendMessageService | 消息发送服务 |
| CardSnapShotService | 银行卡快照服务 |
| CustSnapShotService | 客户快照服务 |

### 12.2 外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| HkCustInfoOuterService | 香港客户信息查询服务 |
| HkBankCardInfoOuterService | 香港银行卡信息查询服务 |
| CounterDealOuterService | 柜台交易服务 |

## 13. 幂等性与安全性说明

### 13.1 幂等性
- **幂等性**：是
- **实现方式**：基于凭证流水号和审核状态的组合判断，同一凭证在同一状态下重复审核会返回相同结果
- **幂等性保障**：通过数据库唯一约束和业务状态校验确保

### 13.2 安全性
- **鉴权**：通过Dubbo服务注册中心进行服务鉴权
- **限流**：依赖Dubbo框架的限流机制
- **验签**：无特殊验签要求
- **数据安全**：敏感数据（银行卡号、证件号）采用加密存储
- **操作审计**：记录完整的操作日志和审核轨迹

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_pay_voucher_order | 打款凭证信息表 |
| hw_pay_voucher_order_alter_record | 打款凭证变更记录表 |
| hw_pay_voucher_order_file | 打款凭证文件表 |
| hw_deal_order | 交易订单表 |
| hw_fund_tx_acct | 基金交易账户表 |

## 15. 备注与风险点

### 15.1 注意事项
- 审核操作具有不可逆性，需要谨慎操作
- 实际到账信息一旦确认，会影响后续的资金对账
- CRM文件审核依赖外部系统，需要处理超时和失败场景

### 15.2 边界处理
- 凭证状态必须为"已上传"才能进行审核
- 交易下单凭证必须关联有效的交易订单
- 审核意见长度限制在500字符以内

### 15.3 特殊逻辑说明
- 重复凭证检查：系统会检查是否存在相同的汇款信息
- 文件审核：支持dtms-order和crm-trade两种来源的文件审核
- 消息通知：审核完成后会发送消息通知相关系统

### 15.4 风险点
- 外部服务依赖风险：香港客户信息服务、银行卡信息服务不可用
- 数据一致性风险：审核过程中的状态更新需要保证原子性
- 性能风险：大量凭证同时审核可能导致数据库压力

---

**文档生成时间**：2025-07-28 19:10:02
**文档版本**：v1.0
**维护人员**：hongdong.xie
