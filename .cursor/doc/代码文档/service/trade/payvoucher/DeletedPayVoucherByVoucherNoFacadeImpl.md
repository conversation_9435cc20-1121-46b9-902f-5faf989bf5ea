# 删除打款凭证接口设计文档

## 1. 接口名称
删除打款凭证接口

## 2. 接口说明
该接口用于删除客户上传的打款凭证，通过设置客户删除标识来实现逻辑删除。主要应用于海外基金交易业务中，当客户需要删除已提交的打款凭证时使用。

**业务背景**：在海外基金交易中，客户上传打款凭证后，在特定状态下允许客户主动删除其提交的打款凭证申请，系统通过设置cus_delete字段为'1'来标记客户删除状态。

**使用场景**：
- 客户发现上传的打款凭证信息有误，需要删除重新提交
- 客户不再需要该笔打款凭证，主动删除申请
- 运营人员协助客户删除打款凭证申请

## 3. 接口类型
Dubbo接口

## 4. 接口地址或方法签名
**接口类**：`com.howbuy.dtms.order.client.facade.trade.payvoucher.DeletedPayVoucherByVoucherNoFacade`
**方法签名**：`Response<DeletedPayVourcherByVoucherNoResponse> execute(DeletedPayVourcherByVoucherNoRequest request)`

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK123456 | 客户在香港的唯一标识号 |
| 打款凭证号 | voucherNo | String | 是 | V202403060001 | 打款凭证的唯一流水号 |
| 打款凭证类型 | voucherType | String | 否 | 1 | 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证 |
| 打款凭证状态 | voucherStatus | String | 否 | 2 | 2-等待复核、3-审核通过、4-审核不通过、6-驳回至客户、7-作废 |
| 是否重复 | repeat | String | 否 | 0 | 0-否、1-是 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 成功 | 响应描述信息 |
| 数据封装 | data | Object | 是 | {} | 响应数据对象，删除操作无返回数据 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 删除操作执行成功 |
| 9999 | 系统异常 | 系统内部错误 |
| 1001 | 参数校验失败 | 必填参数缺失或格式错误 |

## 8. 关键业务逻辑说明

1. **参数校验**：验证香港客户号和打款凭证号为必填参数
2. **权限验证**：确认当前操作用户有权限删除该打款凭证
3. **状态检查**：检查打款凭证当前状态是否允许删除操作
4. **逻辑删除**：通过更新hw_pay_voucher_order表的cus_delete字段为'1'实现逻辑删除
5. **条件匹配**：根据传入的voucherStatus、voucherType、repeat参数进行条件匹配更新
6. **操作记录**：记录删除操作的相关信息

## 9. 流程图

```plantuml
@startuml
start
:接收删除打款凭证请求;
:参数校验;
if (参数校验通过?) then (是)
  :调用Repository层;
  :执行SQL更新操作;
  note right: 设置cus_delete='1'
  :返回成功响应;
else (否)
  :返回参数错误;
endif
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "客户端" as Client
participant "DeletedPayVoucherByVoucherNoFacadeImpl" as Facade
participant "DeletedPayVourcherByVoucherNoService" as Service
participant "HwPayVoucherOrderRepository" as Repository
participant "HwPayVoucherOrderMapper" as Mapper
participant "数据库" as DB

Client -> Facade: execute(request)
activate Facade
Facade -> Service: execute(request)
activate Service
Service -> Repository: updateRecStatVoucherOrder()
activate Repository
Repository -> Mapper: updateRecStatVoucherOrder()
activate Mapper
Mapper -> DB: UPDATE hw_pay_voucher_order SET cus_delete='1'
activate DB
DB --> Mapper: 更新结果
deactivate DB
Mapper --> Repository: 影响行数
deactivate Mapper
Repository --> Service: 更新结果
deactivate Repository
Service --> Facade: Response.ok()
deactivate Service
Facade --> Client: 响应结果
deactivate Facade
@enduml
```

## 11. 异常处理机制

1. **参数校验异常**：当必填参数缺失时，返回参数校验失败错误码
2. **数据库异常**：当数据库操作失败时，记录错误日志并返回系统异常
3. **业务逻辑异常**：当打款凭证状态不允许删除时，返回业务错误信息
4. **网络异常**：Dubbo调用超时或网络异常时的重试机制

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| Response | 统一响应封装类，提供标准的返回格式 |
| BaseRequest | 基础请求类，包含公共请求参数 |
| HwPayVoucherOrderRepository | 打款凭证数据访问层，提供数据库操作方法 |
| HwPayVoucherOrderMapper | MyBatis映射器，执行具体的SQL操作 |
| ParamsValidator | 参数校验工具类，提供参数验证功能 |

## 13. 幂等性与安全性说明

- **幂等性**：该接口具有幂等性，多次调用同一打款凭证的删除操作结果一致
- **鉴权**：通过Dubbo服务调用，需要调用方具有相应的服务访问权限
- **数据安全**：采用逻辑删除方式，不会物理删除数据，保证数据可追溯性
- **操作审计**：所有删除操作都会记录在系统日志中，便于审计追踪

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_pay_voucher_order | 打款凭证信息表 |

## 15. 备注与风险点

**注意事项**：
- 删除操作为逻辑删除，通过设置cus_delete字段实现
- 删除操作需要根据多个条件进行匹配，确保操作的准确性
- 建议在删除前进行状态校验，避免误删除

**风险点**：
- 如果不传入voucherStatus、voucherType、repeat参数，可能会删除不符合条件的记录
- 需要确保调用方有足够的权限执行删除操作
- 删除操作不可逆，需要谨慎处理

**特殊逻辑说明**：
- updateRecStatVoucherOrder方法会根据传入的条件参数进行匹配更新
- 只有满足所有传入条件的记录才会被标记为删除状态
