# 上传打款凭证接口设计文档

## 1. 接口名称
上传打款凭证接口

## 2. 接口说明
该接口用于客户上传打款凭证，支持三种类型的凭证上传：开户入金确认凭证、交易下单凭证、存入现金账户凭证。主要应用于海外基金交易业务中的付款凭证管理流程，确保客户的汇款信息得到及时记录和处理。

**业务背景**：在海外基金交易中，客户需要通过银行汇款完成资金支付，并上传相应的打款凭证作为付款证明。系统需要记录这些凭证信息，并进行后续的审核和对账处理。

**使用场景**：
- 客户开户后上传入金确认凭证
- 客户交易下单后上传付款凭证
- 客户向现金账户存入资金时上传凭证
- 支持凭证信息的更新和重新提交

## 3. 接口类型
Dubbo接口

## 4. 接口地址或方法签名
**接口类**：`com.howbuy.dtms.order.client.facade.trade.payvoucher.UploadPayVoucherFacade`
**方法签名**：`Response<UploadPayVoucherResponse> execute(UploadPayVoucherRequest uploadPayVoucherRequest)`

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 上传打款凭证流水号 | voucherNo | String | 否 | "123456789" | 若传了，则在原订单基础上更新订单 |
| 打款凭证数据 | data | PayVoucherDataDTO | 是 | - | 打款凭证核心数据对象 |
| 香港客户号 | data.hkCustNo | String | 是 | "HK123456" | 香港客户唯一标识 |
| 汇款资金账号 | data.remitCpAcctNo | String | 是 | "6225880123456789" | 客户汇款使用的银行账号 |
| 汇款币种 | data.remitCurrency | String | 是 | "USD" | 汇款币种代码 |
| 汇款金额 | data.remitAmt | BigDecimal | 是 | 10000.00 | 汇款金额 |
| 上传打款凭证类型 | data.voucherType | String | 是 | "1" | 0-开户入金确认凭证、1-交易下单凭证、2-存入现金账户凭证 |
| 交易订单号 | data.tradeOrderNo | String | 否 | "202407220001" | 类型为1-交易下单凭证时，订单号必传 |
| 是否同意换汇 | data.agreeSwap | String | 否 | "1" | 0-否 1-是 |
| 备注 | data.remark | String | 否 | "汇款备注信息" | 备注信息 |
| 文件来源 | data.fileSource | String | 否 | "1" | 1-DTMS_ORDER 2-CRM |
| 入账流水号 | data.receiptSerialNo | String | 否 | "REC123456" | 入账流水号 |
| 附件列表 | fileList | List<UploadPayVoucherFileDTO> | 否 | - | 打款凭证附件列表 |
| 文件主键 | fileList[].id | Long | 否 | 1001 | 文件主键ID |
| 资料名称 | fileList[].fileName | String | 否 | "汇款凭证.jpg" | 文件名称 |
| 资料相对路径 | fileList[].fileUrl | String | 否 | "/upload/voucher/xxx.jpg" | 文件存储路径 |
| 资料类型 | fileList[].fileType | String | 否 | "image/jpeg" | 文件类型 |
| 操作人 | operator | String | 否 | "admin" | 操作人员标识 |
| 交易码 | txCode | String | 否 | "HW001" | 交易码 |
| 申请日期 | appDt | String | 否 | "20240722" | 申请日期 yyyyMMdd |
| 申请时间 | appTm | String | 否 | "143000" | 申请时间 HHmmss |
| 交易渠道 | tradeChannel | String | 否 | "11" | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | "001" | 网点号 |
| IP地址 | ipAddress | String | 否 | "***********" | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | "EXT123456" | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | "00:11:22:33:44:55" | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | "SN123456" | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | "iPhone 12" | 设备型号 |
| 设备名称 | deviceName | String | 否 | "用户手机" | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | "iOS 15.0" | 操作系统版本 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | "000000" | 响应状态码 |
| 描述信息 | description | String | 是 | "操作成功" | 响应描述信息 |
| 数据封装 | data | UploadPayVoucherResponse | 是 | - | 响应数据对象 |
| 上传打款凭证流水号 | data.voucherNo | String | 是 | "123456789" | 生成的凭证流水号 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 000000 | 操作成功 | 正常响应 |
| C021061 | 客户已开户入金确认，请勿重复提交 | 业务校验失败 |
| C021062 | 基于交易上传打款凭证，订单号必传 | 参数校验失败 |
| C021063 | 汇款银行卡不是下单时的银行卡 | 业务校验失败 |
| C021064 | 订单的支付方式不是电汇，不允许上传 | 业务校验失败 |
| C021065 | 订单是无需付款/退款/付款成功/付款失败，不允许上传 | 业务校验失败 |
| C021066 | 上传打款凭证状态是无需上传/已上传/审核通过，不允许上传 | 业务校验失败 |
| C021067 | 订单状态{申请失败/自行撤单/强制撤单/确认成功/确认失败/部分确认}，不允许上传 | 业务校验失败 |
| C023001 | 查询香港客户信息异常 | 外部服务调用异常 |
| C023002 | 查询香港银行卡信息异常 | 外部服务调用异常 |

## 8. 关键业务逻辑说明

### 8.1 参数校验逻辑
1. **基础参数校验**：验证香港客户号、汇款资金账号、汇款币种、汇款金额、凭证类型等必填字段
2. **文件来源校验**：当文件来源为DTMS_ORDER时，附件列表不能为空
3. **客户状态校验**：香港客户号必须存在且状态为【开户申请成功、正常】
4. **银行卡校验**：验证汇款资金账号对应的银行卡信息是否存在且有效

### 8.2 交易订单校验逻辑（凭证类型为交易下单凭证时）
1. **订单号校验**：交易订单号必传，否则返回错误
2. **重复上传校验**：若凭证流水号为空，检查交易订单是否已关联其他有效凭证
3. **订单存在性校验**：验证交易订单是否存在
4. **订单状态校验**：验证订单支付方式、支付状态、凭证上传状态等是否符合上传条件

### 8.3 数据持久化逻辑
1. **新建凭证**：若凭证流水号为空，创建新的打款凭证记录
2. **更新凭证**：若凭证流水号有值，更新现有凭证记录
3. **文件处理**：保存凭证附件信息到文件表
4. **银行卡信息**：记录银行卡相关信息（加密、摘要、掩码等）

### 8.4 后续处理逻辑
1. **交易订单更新**：若为交易下单凭证，更新订单的打款凭证状态为"已上传"，支付状态为"付款中"
2. **消息通知**：发送MQ消息通知CRM系统更新打款状态
3. **操作记录**：记录凭证操作流水，状态为"申请提交"

## 9. 流程图

```plantuml
@startuml
start
:接收上传打款凭证请求;
:参数基础校验;
if (校验通过?) then (否)
  :返回参数错误;
  stop
endif

:查询香港客户信息;
if (客户存在且状态正常?) then (否)
  :返回客户状态异常;
  stop
endif

:查询银行卡信息;
if (银行卡有效?) then (否)
  :返回银行卡信息异常;
  stop
endif

if (凭证类型为交易下单凭证?) then (是)
  :校验交易订单;
  if (订单校验通过?) then (否)
    :返回订单相关错误;
    stop
  endif
endif

:保存打款凭证信息;
:保存凭证附件;
:记录操作流水;

if (凭证类型为交易下单凭证?) then (是)
  :更新交易订单状态;
  :发送MQ消息通知CRM;
endif

:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "客户端" as Client
participant "UploadPayVoucherFacadeImpl" as Facade
participant "UploadPayVoucherService" as Service
participant "HkCustInfoOuterService" as CustService
participant "HkBankCardInfoOuterService" as BankService
participant "HwPayVoucherOrderRepository" as VoucherRepo
participant "HwDealOrderRepository" as OrderRepo
participant "SendMqService" as MqService

Client -> Facade: execute(request)
Facade -> Service: execute(request)

Service -> Service: 参数校验
Service -> CustService: 查询香港客户信息
CustService -> Service: 返回客户信息

Service -> BankService: 查询银行卡信息
BankService -> Service: 返回银行卡信息

alt 凭证类型为交易下单凭证
  Service -> OrderRepo: 查询交易订单
  OrderRepo -> Service: 返回订单信息
  Service -> Service: 校验订单状态
end

Service -> VoucherRepo: 保存打款凭证
VoucherRepo -> Service: 返回凭证流水号

alt 凭证类型为交易下单凭证
  Service -> OrderRepo: 更新订单状态
  Service -> MqService: 发送订单更新消息
end

Service -> Facade: 返回处理结果
Facade -> Client: 返回响应
@enduml
```

## 11. 异常处理机制

### 11.1 参数校验异常
- **ValidateException**：参数为空、格式错误等基础校验异常
- **处理方式**：直接返回错误码和错误信息，不进行后续处理

### 11.2 业务校验异常
- **客户状态异常**：客户不存在或状态不正常
- **银行卡异常**：银行卡信息不存在或无效
- **订单状态异常**：交易订单状态不符合上传条件
- **重复上传异常**：订单已关联有效的打款凭证
- **处理方式**：记录日志，返回具体的业务错误码和描述

### 11.3 外部服务异常
- **香港客户信息查询异常**：调用客户信息服务失败
- **银行卡信息查询异常**：调用银行卡信息服务失败
- **处理方式**：记录异常日志，返回外部服务调用异常错误码

### 11.4 数据库操作异常
- **数据保存异常**：打款凭证信息保存失败
- **数据更新异常**：交易订单状态更新失败
- **处理方式**：事务回滚，记录异常日志，返回系统异常错误码

## 12. 调用的公共模块或外部依赖

### 12.1 外部服务依赖
- **HkCustInfoOuterService**：香港客户信息查询服务
- **HkBankCardInfoOuterService**：香港银行卡信息查询服务
- **SendMqService**：消息队列发送服务

### 12.2 数据访问层依赖
- **HwPayVoucherOrderRepository**：打款凭证订单数据访问
- **HwPayVoucherOrderFileRepository**：打款凭证文件数据访问
- **HwDealOrderRepository**：交易订单数据访问
- **HwPayVoucherOrderOperateRepository**：打款凭证操作流水数据访问

### 12.3 校验器依赖
- **PayVoucherValidator**：打款凭证业务校验器
- **ParamsValidator**：参数校验工具

### 12.4 工具类依赖
- **BeanUtils**：对象属性复制工具
- **StringUtils**：字符串工具类
- **CollectionUtils**：集合工具类

## 13. 幂等性与安全性说明

### 13.1 幂等性设计
- **凭证流水号机制**：通过voucherNo参数实现幂等性，相同流水号的请求会更新现有记录而非创建新记录
- **重复上传校验**：对于交易下单凭证，系统会校验订单是否已关联有效凭证，防止重复上传
- **操作记录**：每次操作都会记录操作流水，便于追踪和审计

### 13.2 安全性措施
- **参数校验**：严格的参数校验，防止恶意数据注入
- **客户权限校验**：验证客户状态和权限，确保只有有效客户可以操作
- **银行卡信息加密**：银行卡号等敏感信息进行加密存储
- **操作审计**：记录操作人、操作时间等信息，便于审计追踪

### 13.3 数据一致性
- **事务控制**：使用数据库事务确保数据一致性
- **状态同步**：通过MQ消息确保与外部系统的状态同步
- **乐观锁**：使用版本号或时间戳防止并发更新冲突

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_pay_voucher_order | 打款凭证信息表 |
| hw_pay_voucher_order_file | 打款凭证订单材料表 |
| hw_pay_voucher_order_operate | 打款凭证操作流水表 |
| hw_deal_order | 海外交易订单主表 |
| hw_fund_tx_acct | 非全委基金交易账号表 |

## 15. 备注与风险点

### 15.1 注意事项
- **文件上传限制**：当文件来源为DTMS_ORDER时，必须提供附件列表
- **凭证类型限制**：不同凭证类型有不同的校验规则，需要严格按照业务规则执行
- **订单状态依赖**：交易下单凭证的上传依赖于订单的当前状态，需要确保状态流转的正确性

### 15.2 边界处理
- **大文件处理**：需要考虑大文件上传的性能和存储问题
- **并发处理**：多个用户同时操作同一订单时的并发控制
- **网络异常**：外部服务调用失败时的重试和降级机制

### 15.3 特殊逻辑说明
- **换汇逻辑**：agreeSwap字段用于标识客户是否同意换汇，影响后续的资金处理流程
- **文件来源区分**：不同文件来源（DTMS_ORDER、CRM）有不同的处理逻辑
- **消息通知**：只有交易下单凭证才会触发MQ消息通知CRM系统

### 15.4 性能考虑
- **批量操作**：文件信息的批量插入优化
- **索引优化**：基于查询频率优化数据库索引
- **缓存策略**：客户信息和银行卡信息的缓存策略

---

**文档生成时间**：2025-07-28 20:11:01
**文档版本**：v1.0
**维护人员**：hongdong.xie
