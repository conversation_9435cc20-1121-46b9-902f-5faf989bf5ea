# 撤销打款凭证接口设计文档

## 1. 接口名称
撤销打款凭证接口

## 2. 接口说明
该接口用于撤销客户已上传的打款凭证，支持客户在特定状态下主动撤销其提交的打款凭证申请。主要应用于海外基金交易业务中，当客户需要取消已提交的打款凭证时使用。

**业务背景**：在海外基金交易中，客户上传打款凭证后，在审核流程的特定阶段（等待复核、驳回至初审）允许客户主动撤销申请，以便重新提交或取消操作。

**使用场景**：
- 客户发现上传的打款凭证信息有误，需要撤销重新提交
- 客户不再需要该笔打款凭证，主动取消申请
- 运营人员协助客户撤销打款凭证申请

## 3. 接口类型
Dubbo接口

## 4. 接口地址或方法签名
**接口类**：`com.howbuy.dtms.order.client.facade.trade.payvoucher.CancelPayVoucherFacade`
**方法签名**：`Response<String> execute(CancelPayVoucherRequest request)`

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 打款凭证流水号 | voucherNo | String | 是 | 202407250001 | 上传打款凭证时生成的唯一流水号 |
| 香港客户号 | hkCustNo | String | 是 | HK000001 | 客户在香港的唯一标识号 |
| 操作人 | operator | String | 是 | admin | 执行撤销操作的操作员工号或客户标识 |
| 交易码 | txCode | String | 否 | HW0011 | 交易类型编码，默认为HW0011 |
| 申请日期 | appDt | String | 是 | 20250728 | 申请日期，格式：yyyyMMdd |
| 申请时间 | appTm | String | 是 | 192758 | 申请时间，格式：HHmmss |
| 交易渠道 | tradeChannel | String | 是 | 11 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 是 | 001 | 网点编号 |
| IP地址 | ipAddress | String | 是 | ************* | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT20250728001 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456789 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone14 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 客户手机 | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 16.0 | 操作系统版本 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 000000 | 响应状态码，000000表示成功 |
| 描述信息 | description | String | 是 | 操作成功 | 响应描述信息 |
| 数据封装 | data | String | 否 | success | 返回的业务数据，撤销成功时返回成功标识 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 000000 | 操作成功 | 撤销打款凭证成功 |
| C021057 | 当前流水号不存在 | 打款凭证流水号在系统中不存在 |
| C021068 | 非当前客户申请不允许再作废 | 打款凭证不属于当前客户，无权限撤销 |
| C021069 | 订单状态{XXX}，不允许作废 | 打款凭证当前状态不允许撤销操作 |
| C020001 | 参数校验失败 | 必填参数为空或格式不正确 |

## 8. 关键业务逻辑说明

1. **参数校验**：验证必填参数voucherNo、hkCustNo是否为空
2. **凭证存在性校验**：根据voucherNo查询打款凭证记录，验证凭证是否存在
3. **权限校验**：验证当前客户是否有权限撤销该打款凭证（凭证所属客户与请求客户一致）
4. **状态校验**：验证打款凭证当前状态是否允许撤销（仅"等待复核"和"驳回至初审"状态允许撤销）
5. **数据更新**：更新打款凭证状态为"已撤销"，记录撤销操作信息
6. **快照记录**：保存客户基本信息和银行卡信息快照
7. **变更记录**：在变更记录表中记录本次撤销操作的详细信息

## 9. 流程图（PlantUML）

```plantuml
@startuml
title 撤销打款凭证流程图

start
:接收撤销请求;
:参数基础校验;
if (必填参数是否完整?) then (否)
  :返回参数校验失败;
  stop
endif

:根据voucherNo查询打款凭证;
if (打款凭证是否存在?) then (否)
  :返回"当前流水号不存在";
  stop
endif

:校验客户权限;
if (是否为凭证所属客户?) then (否)
  :返回"非当前客户申请不允许再作废";
  stop
endif

:校验凭证状态;
if (状态是否为"等待复核"或"驳回至初审"?) then (否)
  :返回"订单状态不允许作废";
  stop
endif

:查询客户信息和银行卡信息;
:构建更新参数;
:更新打款凭证状态为已撤销;
:插入客户和银行卡快照;
:记录变更操作;
:返回撤销成功;
stop
@enduml
```

## 10. 时序图（PlantUML）

```plantuml
@startuml
title 撤销打款凭证时序图

actor Client as C
participant CancelPayVoucherFacadeImpl as F
participant CancelPayVoucherService as S
participant HwPayVoucherOrderRepository as R
participant HkCustInfoOuterService as CS
participant HkBankCardInfoOuterService as BS
participant CustSnapShotService as CSS
participant CardSnapShotService as CAS

C -> F : execute(request)
F -> S : execute(request)
S -> S : validate(request)
S -> R : selectByVouchNo(voucherNo)
R --> S : HwPayVoucherOrder
S -> CS : getHkCustInfo(hkCustNo)
CS --> S : HkCustInfoDTO
S -> BS : getHkBankCardByCpAcctNo(...)
BS --> S : HkBankCardInfoDTO
S -> S : buildUpdateBo(context)
S -> R : updateVoucherOrder(operateBO)
R --> S : 更新成功
S -> CSS : insertCustSnapshot(...)
S -> CAS : insertCardSnapshot(...)
S --> F : Response.ok()
F --> C : 撤销成功响应

@enduml
```

## 11. 异常处理机制

- **参数校验异常**：对必填参数进行非空校验，参数不合法时抛出ValidateException
- **业务校验异常**：
  - 打款凭证不存在时抛出VOUCHER_ORDER_NO_NOT_EXISTS异常
  - 非当前客户操作时抛出VOUCHER_ORDER_CANCEL_NOT_CURRENT_CUST异常
  - 状态不匹配时抛出VOUCHER_ORDER_CANCEL_STATUS_NOT_MATCH异常
- **系统异常**：数据库操作失败、外部服务调用失败等系统级异常统一处理

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| HwPayVoucherOrderRepository | 打款凭证数据访问层，提供凭证查询和更新功能 |
| HkCustInfoOuterService | 香港客户信息外部服务，获取客户基本信息 |
| HkBankCardInfoOuterService | 香港银行卡信息外部服务，获取银行卡信息 |
| CustSnapShotService | 客户快照服务，保存客户信息快照 |
| CardSnapShotService | 银行卡快照服务，保存银行卡信息快照 |
| ParamsValidator | 参数校验工具类，提供通用参数校验功能 |

## 13. 幂等性与安全性说明

- **幂等性**：接口具备幂等性。对同一个打款凭证重复执行撤销操作，系统会进行状态校验，已撤销的凭证不会重复处理
- **安全性**：
  - **身份验证**：依赖Dubbo框架的调用来源验证
  - **操作授权**：通过校验客户号确保只有凭证所属客户才能执行撤销操作
  - **参数校验**：所有必填字段都进行非空和格式校验
  - **状态控制**：严格控制可撤销的凭证状态，防止非法操作

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_pay_voucher_order | 打款凭证信息表 |
| hw_pay_voucher_order_alter_record | 打款凭证变更记录表 |
| hw_cust_snapshot | 客户信息快照表 |
| hw_card_snapshot | 银行卡信息快照表 |

## 15. 备注与风险点

- **状态控制**：严格控制可撤销的凭证状态，只有"等待复核"和"驳回至初审"状态的凭证才允许撤销
- **权限控制**：必须确保只有凭证所属客户才能执行撤销操作，防止越权操作
- **数据一致性**：撤销操作涉及多个表的数据更新，需要保证事务的一致性
- **审计追踪**：所有撤销操作都会记录在变更记录表中，便于后续审计和问题排查

---
*Generated by AI Assistant at 2025-07-28 19:27:58*
