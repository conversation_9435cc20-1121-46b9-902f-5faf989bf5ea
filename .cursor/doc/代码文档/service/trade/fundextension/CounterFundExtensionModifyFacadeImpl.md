# 接口详细设计文档

## 1. 接口名称
柜台基金展期修改接口

## 2. 接口说明
提供柜台交易场景下的基金展期修改功能。此接口用于处理香港客户的基金展期修改业务，支持本金展期、本金+收益展期、收益展期、到期赎回等多种展期选项。

**业务背景**：
- 基金展期是指投资者对持有的基金份额进行展期操作，可以选择不同的展期方式
- 展期修改允许客户在原有展期基础上进行调整，包括展期选项、展期控制类型和展期控制数等
- 柜台场景下需要严格的业务校验和风控管理，确保展期修改操作的合法性

**使用场景**：
- 柜台工作人员协助香港客户进行基金展期修改操作
- 客户通过柜台系统发起基金展期修改申请
- 投资顾问为客户进行投资策略调整时的展期修改操作

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fundextension.CounterFundExtensionModifyFacade.addCounterFundExtensionModifyOrder(FundExtensionModifyRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港客户的唯一标识号 |
| 基金编码 | fundCode | String | 是 | 000001 | 基金的唯一标识编码 |
| 基金交易账号 | fundTxAcctNo | String | 是 | 123456789 | 客户的基金交易账号 |
| 展期选项 | extOption | String | 是 | 1 | 1-本金展期,2-本金+收益展期,3-收益展期,4-到期赎回 |
| 展期控制类型 | extControlType | String | 是 | 1 | 展期控制类型:1-月 |
| 展期控制数 | extControlNum | String | 否 | 6 | 展期控制数量 |
| 份额明细号 | volDtlNo | String | 是 | VOL202412160001 | 份额明细的唯一标识号 |
| 交易码 | txCode | String | 否 | HW0000 | 交易码，默认HW0000 |
| 申请日期 | appDt | String | 否 | 20241216 | 申请日期，格式yyyyMMdd |
| 申请时间 | appTm | String | 否 | 143000 | 申请时间，格式HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 1 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 交易网点编号 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT202412160001 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456789 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 张三的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 15.0 | 操作系统版本号 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 操作成功 | 响应描述信息 |
| 数据封装 | data | Object | 是 | - | 响应数据对象 |
| 订单号 | data.dealNo | String | 是 | D202412160001 | 生成的订单号 |
| 订单明细号 | data.dealDtlNo | String | 是 | DD202412160001 | 生成的订单明细号 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 展期修改订单创建成功 |
| 9999 | 系统异常 | 系统内部错误 |
| 1001 | 参数校验失败 | 必填参数缺失或格式错误 |
| 2001 | 客户信息不存在 | 香港客户号不存在 |
| 2002 | 基金信息不存在 | 基金编码不存在或已停用 |
| 2003 | 份额明细不存在 | 份额明细号不存在 |
| 3001 | 业务校验失败 | 不满足展期修改业务规则 |

## 8. 关键业务逻辑说明

1. **参数校验**：对请求参数进行基础校验，包括必填字段检查、格式校验等
2. **客户校验**：验证香港客户号的有效性和状态
3. **基金校验**：验证基金编码的有效性、基金状态、是否支持展期等
4. **份额校验**：验证份额明细号的有效性，确认份额归属和状态
5. **展期规则校验**：根据展期选项和控制类型进行业务规则校验
6. **订单创建**：通过FundExtensionService.process()方法创建展期修改订单
7. **订单保存**：将订单信息保存到数据库，生成订单号和订单明细号
8. **响应返回**：返回创建成功的订单信息

## 9. 流程图

```plantuml
@startuml
start
:接收展期修改请求;
:参数基础校验;
if (参数校验通过?) then (否)
  :返回参数错误;
  stop
endif
:构建交易上下文;
:客户信息校验;
if (客户校验通过?) then (否)
  :返回客户错误;
  stop
endif
:基金信息校验;
if (基金校验通过?) then (否)
  :返回基金错误;
  stop
endif
:份额明细校验;
if (份额校验通过?) then (否)
  :返回份额错误;
  stop
endif
:展期业务规则校验;
if (业务规则校验通过?) then (否)
  :返回业务规则错误;
  stop
endif
:创建展期修改订单;
:保存订单到数据库;
:生成订单号和明细号;
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "柜台系统" as Counter
participant "CounterFundExtensionModifyFacade" as Facade
participant "FundExtensionService" as Service
participant "TradeContextBuilder" as Builder
participant "OrderCreateService" as OrderService
participant "HwDealOrderRepository" as Repository
participant "数据库" as DB

Counter -> Facade: addCounterFundExtensionModifyOrder(request)
Facade -> Service: process(request)
Service -> Service: tradeValidate(request)
Service -> Builder: buildTradeContext(request)
Builder --> Service: TradeContext
Service -> OrderService: createCounterFundExtensionOrder(context)
OrderService --> Service: OrderCreateBO
Service -> Repository: saveDealOrder(orderCreateBO)
Repository -> DB: 保存订单数据
DB --> Repository: 保存成功
Repository --> Service: 保存结果
Service --> Facade: FundExtensionModifyVO
Facade --> Counter: Response<FundExtensionModifyVO>
@enduml
```

## 11. 异常处理机制

1. **参数校验异常**：通过@MyValidation注解进行参数校验，校验失败时抛出参数异常
2. **业务校验异常**：在业务逻辑处理过程中，如客户状态、基金状态等校验失败时抛出业务异常
3. **系统异常**：数据库操作、网络通信等系统级异常统一处理
4. **事务回滚**：订单创建过程中如发生异常，自动回滚事务确保数据一致性

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| FundExtensionService | 基金展期业务服务，处理展期相关业务逻辑 |
| TradeContextBuilder | 交易上下文构建器，构建交易执行上下文 |
| OrderCreateService | 订单创建服务，负责订单和订单明细的创建 |
| HwDealOrderRepository | 订单数据访问层，负责订单数据的持久化 |
| MyValidation | 参数校验框架，提供统一的参数校验功能 |

## 13. 幂等性与安全性说明

- **幂等性**：接口不具备幂等性，重复调用会创建多个订单
- **鉴权**：通过Dubbo框架进行服务间调用鉴权
- **限流**：依赖Dubbo框架的限流机制
- **验签**：请求参数通过@MyValidation注解进行格式验证
- **数据安全**：敏感信息如客户号、账号等需要加密传输

## 14. 备注与风险点

1. **并发控制**：多个展期修改请求可能存在并发问题，需要通过数据库锁机制控制
2. **数据一致性**：订单创建过程涉及多表操作，需要确保事务一致性
3. **业务规则变更**：展期规则可能随业务发展而变化，需要保持代码的可扩展性
4. **外部依赖**：依赖基金信息、客户信息等外部数据，需要考虑外部服务不可用的情况
5. **审计日志**：重要的展期修改操作需要记录完整的审计日志

---

**文档生成时间**：2025-07-27 20:52:11  
**文档版本**：v1.0  
**维护人员**：hongdong.xie
