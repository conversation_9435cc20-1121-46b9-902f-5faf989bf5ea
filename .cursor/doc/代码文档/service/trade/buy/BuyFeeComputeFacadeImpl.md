# 接口详细设计文档

## 1. 接口名称
基金申购费用计算

## 2. 接口说明
用于计算客户在进行基金申购操作时所需支付的各项费用，包括手续费、折扣金额及实际支付金额等。该接口在用户输入购买金额后调用，实时返回费用详情，以便用户确认。

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.buy.BuyFeeComputeFacade.execute(FeeComputeRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 香港客户号 | hkCustNo | String | 是 | `HK123456` | 客户在香港的唯一标识 |
| 基金代码 | fundCode | String | 是 | `HB0001` | 待申购的基金代码 |
| 买入金额 | buyAmt | BigDecimal | 是 | `10000.00` | 客户希望申购的金额 |
| 申请日期 | appDt | String | 是 | `20250428` | 交易申请的日期，格式 YYYYMMDD |
| 申请时间 | appTm | String | 是 | `143000` | 交易申请的时间，格式 HHMMSS |
| 交易渠道 | tradeChannel | String | 是 | `11` | 1-柜台, 9-CRM-PC, 11-APP等 |
| 网点号 | outletCode | String | 是 | `101` | 交易发生的网点代码 |
| IP地址 | ipAddress | String | 是 | `***********` | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | `2025042812345` | 外部系统传入的订单号 |
| MAC地址 | macAddress | String | 否 | `00-E0-4C-68-02-B4` | 客户端MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | `SN12345678` | 客户端设备序列号 |
| 设备型号 | deviceModel | String | 否 | `iPhone 15 Pro` | 客户端设备型号 |
| 设备名称 | deviceName | String | 否 | `My iPhone` | 客户端设备名称 |
| 系统版本号 | systemVersion | String | 否 | `iOS 17.4.1` | 客户端操作系统版本 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 字段说明 |
|---|---|---|---|
| 实际支付金额 | actualPayAmt | BigDecimal | 用户最终需要支付的总金额（本金+手续费） |
| 手续费费率 | feeRate | BigDecimal | 当前交易适用的手续费率 |
| 预估手续费 | estimateFee | BigDecimal | 根据折扣计算后的预估手续费 |
| 原始手续费 | originalFee | BigDecimal | 未经任何折扣的原始手续费 |
| 是否大于预约金额 | isLargerPrebookAmt | String | 是否超过了客户的预约购买额度 (0-否, 1-是) |
| 折扣是否生效 | validDiscountRate | String | 当前使用的折扣是否有效 (0-否, 1-是) |
| 实际折扣率 | actualDiscountRate | BigDecimal | 最终生效的折扣率 |
| 折扣类型 | discountType | String | 使用的折扣活动类型 |
| 折扣金额 | discountAmt | BigDecimal | 通过折扣减少的金额 |
| 预约折扣率 | prebookDiscountRate | BigDecimal | 客户预约时享受的折扣率 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|---|---|---|
| 0000 | 成功 | |
| A0001 | 系统错误 | 请联系技术支持 |
| B0001 | 参数校验失败 | 请求参数不符合要求 |
| C0001 | 外部服务调用异常 | 依赖的外部服务出现问题 |
| D0001 | 数据不存在 | 例如基金信息、客户信息不存在 |

*注：具体错误码请参考项目统一的错误码规范。*

## 8. 关键业务逻辑说明
1.  **参数校验**：对入参进行非空和格式校验。
2.  **构建上下文**：根据 `hkCustNo` 查询客户信息，根据 `fundCode` 查询基金基本信息（如IPO结束日期），并判断当前交易是认购还是申购，组装成 `BuyFeeComputeContext` 上下文对象。
3.  **查询预约信息**：使用客户号、基金代码、业务类型等信息，调用 `hkPrebookOuterService` 查询客户的预约详情 `HkPreBookDetailDTO`。
4.  **费用计算**：将上下文对象和预约信息传入核心的 `feeComputeService.buyFeeCompute` 方法，进行详细的费用计算。该方法会综合考虑基金费率、平台折扣、客户预约折扣等多种因素，计算出最终的各项费用。
5.  **返回结果**：将计算得到的 `BuyFeeComputeInfoVO` 对象包装后返回。

## 9. 流程图
```plantuml
@startuml
start
:接收 FeeComputeRequest 请求;
:参数校验;
if (校验失败) then (yes)
    :返回参数错误;
    stop
endif
:调用 HkCustInfoOuterService 获取客户信息;
:调用 QueryFundInfoOuterService 获取基金信息;
:构建 BuyFeeComputeContext 上下文;
:调用 HkPrebookOuterService 获取预约详情;
:调用 FeeComputeService.buyFeeCompute 计算费用;
:封装 BuyFeeComputeInfoVO 响应;
:返回结果;
end
@enduml
```

## 10. 时序图
```plantuml
@startuml
actor Client
participant BuyFeeComputeFacadeImpl as Facade
participant BuyFeeComputeService as Service
participant HkCustInfoOuterService as CustService
participant QueryFundInfoOuterService as FundService
participant HkPrebookOuterService as PrebookService
participant FeeComputeService as CoreFeeService

Client -> Facade: execute(request)
Facade -> Service: process(request)
Service -> Service: 基础参数校验
Service -> Service: buildContext(request)
Service -> CustService: getHkCustInfo(hkCustNo)
CustService --> Service: HkCustInfoDTO
Service -> FundService: queryFundBasicInfo(fundCode)
FundService --> Service: FundBasicInfoDTO
Service --> Service: BuyFeeComputeContext
Service -> PrebookService: getHkPreBookDetail(...)
PrebookService --> Service: HkPreBookDetailDTO
Service -> CoreFeeService: buyFeeCompute(context, prebook)
CoreFeeService --> Service: BuyFeeComputeInfoVO
Service --> Facade: BuyFeeComputeInfoVO
Facade --> Client: Response<BuyFeeComputeInfoVO>
@enduml
```

## 11. 异常处理机制
- **参数校验异常**：通过 `ParamsValidator` 抛出，由全局异常处理器捕获，返回参数错误码。
- **业务异常**：在业务流程中（如客户不存在、基金不存在等），会抛出自定义的业务异常，返回对应的业务错误码。
- **外部服务调用异常**：Dubbo 调用其他服务（如客户中心、产品中心）的超时或异常，会被捕获并转化为系统异常，返回系统错误码。
- **未知异常**：所有未被捕获的异常由全局异常处理器统一处理，记录错误日志，并返回统一的系统错误提示。

## 12. 调用的公共模块或外部依赖
| 模块/服务 | 功能简述 |
|---|---|
| `HkCustInfoOuterService` | 香港客户中心服务，用于查询客户基本信息。 |
| `QueryFundInfoOuterService` | 产品中心服务，用于查询基金的基本信息。 |
| `HkPrebookOuterService` | 预约中心服务，用于查询客户的基金购买预约记录。 |
| `FeeComputeService` | 核心费用计算服务，封装了申购、认购等不同场景下的复杂费用计算逻辑。 |

## 13. 幂等性与安全性说明
- **幂等性**：该接口为查询接口，不涉及数据状态的变更，天然幂等。
- **安全性**：
    - **鉴权**：依赖Dubbo框架和网关层进行服务间的调用鉴权。
    - **数据安全**：关键信息如客户号在传输和日志中应进行脱敏处理。

## 14. 备注与风险点
- 费用计算依赖于产品中心提供的基金费率和活动中心提供的折扣信息，需确保这些外部数据的准确性和实时性。
- 计算逻辑较为复杂，需要覆盖认购/申购、不同渠道、有无预约等多种场景的测试用例。 