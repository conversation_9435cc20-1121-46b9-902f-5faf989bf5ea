# CRM基金是否可购买接口

## 1. 接口名称
CRM基金是否可购买校验接口

## 2. 接口说明
### 接口用途
该接口用于校验指定基金是否可以被香港客户购买，主要服务于CRM系统的基金交易前置校验功能。

### 业务背景
在基金交易系统中，客户购买基金前需要进行多项校验，包括基金状态、客户资格、交易时间等。该接口作为购买前置校验的核心组件，确保只有符合条件的基金才能被客户购买。

### 使用场景
- CRM系统基金购买页面的实时校验
- 基金交易订单创建前的预校验
- 客户咨询基金购买资格时的查询
- 批量基金购买资格检查

## 3. 接口类型
Dubbo RPC接口

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.buy.CrmFundCanBuyFacade.execute(CanBuyRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK123456 | 香港客户的唯一标识号 |
| 基金代码 | fundCode | String | 是 | 000001 | 基金的唯一标识代码 |
| 交易码 | txCode | String | 否 | HW_ORDER_CAN_BUY | 交易类型代码，默认为可购买校验 |
| 申请日期 | appDt | String | 是 | 20250725 | 申请日期，格式yyyyMMdd |
| 申请时间 | appTm | String | 是 | 144734 | 申请时间，格式HHmmss |
| 交易渠道 | tradeChannel | String | 是 | 9 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 网点编号 |
| IP地址 | ipAddress | String | 是 | *********** | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT123456 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 张三的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 14.0 | 操作系统版本 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 000000 | 响应状态码，000000表示成功 |
| 描述信息 | description | String | 是 | 处理成功 | 响应描述信息 |
| 数据封装 | data | FundCanBuyResponse | 否 | {} | 响应数据对象，当前为空对象 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 000000 | 处理成功 | 基金可以购买 |
| 999999 | 系统异常 | 系统内部错误 |
| 100001 | 参数错误 | 必填参数缺失或格式错误 |
| 200001 | 客户不存在 | 香港客户号不存在 |
| 200002 | 基金不存在 | 基金代码不存在 |
| 200003 | 基金不可购买 | 基金状态不允许购买 |

## 8. 关键业务逻辑说明

1. **参数校验阶段**：
   - 校验必填参数hkCustNo（香港客户号）和fundCode（基金代码）
   - 校验基础交易参数appDt、appTm、tradeChannel、ipAddress

2. **交易上下文构建**：
   - 根据请求参数构建交易上下文TradeContext
   - 设置业务类型为购买（BUY）
   - 初始化购买金额为0（仅校验，不涉及实际金额）

3. **基金交易账号处理**：
   - 查询客户的默认非全委基金交易账号
   - 如果账号不存在，自动创建非全委类型的基金交易账号

4. **分次Call产品校验逻辑**：
   - 判断基金是否支持分次Call功能
   - 如果支持，执行分次Call专用校验链
   - 如果不支持，执行标准校验链

5. **标准校验链执行**：
   - 产品年龄校验（PRODUCT_AGE）
   - 产品渠道校验（PRODUCT_CHANNEL）
   - 产品业务校验（PRODUCT_BUSINESS）
   - 开放日校验（OPEN_DT）
   - 产品净值状态校验（PRODUCT_NAV_STATUS）
   - 当期期数和金额校验（CURRENT_PERIOD_NUMBER_AND_AMOUNT）
   - 基金费率配置校验（FUND_FEE_RATE_CONFIG）

## 9. 流程图

```plantuml
@startuml
start
:接收CanBuyRequest请求;
:校验必填参数hkCustNo、fundCode;
:校验基础交易参数;
:构建交易上下文TradeContext;
:查询客户基金交易账号;
if (账号是否存在?) then (否)
  :创建非全委基金交易账号;
else (是)
  :使用现有账号;
endif
:设置购买金额为0;
if (基金是否支持分次Call?) then (是)
  :执行分次Call校验逻辑;
  :校验首付比例参数;
  :校验后续缴款参数;
else (否)
  :执行标准校验链;
  :产品年龄校验;
  :产品渠道校验;
  :产品业务校验;
  :开放日校验;
  :产品净值状态校验;
  :当期期数和金额校验;
  :基金费率配置校验;
endif
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "CRM系统" as CRM
participant "CrmFundCanBuyFacadeImpl" as Facade
participant "ParamsValidator" as Validator
participant "CrmCanBuyService" as Service
participant "TradeContextBuilder" as Builder
participant "HwFundTxAcctRepository" as Repo
participant "TradeValidatorHelper" as Helper

CRM -> Facade: execute(CanBuyRequest)
Facade -> Validator: validate(request, "hkCustNo", "fundCode")
Validator --> Facade: 校验通过
Facade -> Service: process(request)
Service -> Validator: validate(request, 基础参数)
Service -> Builder: buildTradeContext(request)
Builder --> Service: TradeContext
Service -> Repo: getNotFullFundTxAcct(hkCustNo)
alt 账号不存在
  Service -> Service: createFundTxAcct()
end
Service -> Helper: buildChain(校验器列表)
Helper --> Service: 校验链
Service -> Helper: doValidate(context, 校验链)
Helper --> Service: 校验完成
Service --> Facade: 处理完成
Facade --> CRM: Response.ok()
@enduml
```

## 11. 异常处理机制

### 主要异常场景及处理方式

1. **参数校验异常**：
   - 异常类型：ValidateException
   - 触发条件：必填参数缺失或格式错误
   - 处理方式：抛出参数错误异常，返回错误码100001

2. **客户信息异常**：
   - 异常类型：BusinessException
   - 触发条件：香港客户号不存在或状态异常
   - 处理方式：抛出业务异常，返回相应错误码

3. **基金信息异常**：
   - 异常类型：BusinessException
   - 触发条件：基金代码不存在或基金状态不可购买
   - 处理方式：抛出业务异常，返回相应错误码

4. **校验链异常**：
   - 异常类型：ValidateException
   - 触发条件：任一校验器校验失败
   - 处理方式：中断校验链执行，抛出具体校验失败异常

5. **系统异常**：
   - 异常类型：RuntimeException
   - 触发条件：数据库连接失败、网络异常等
   - 处理方式：记录错误日志，返回系统异常错误码999999

## 12. 调用的公共模块或外部依赖

### 公共模块

| 模块名称 | 功能简述 |
|----------|----------|
| ParamsValidator | 参数校验工具类，提供统一的参数非空校验功能 |
| CrmCanBuyService | CRM购买校验业务服务，核心业务逻辑处理 |
| TradeValidatorHelper | 交易校验链帮助类，管理和执行校验链 |
| TradeContextBuilder | 交易上下文构建器，构建交易校验所需上下文 |
| HwFundTxAcctRepository | 基金交易账号数据访问层，查询和操作基金账号 |
| FundTxAcctService | 基金交易账号业务服务，提供账号创建等功能 |

### 外部依赖

| 依赖名称 | 功能简述 |
|----------|----------|
| Dubbo | RPC框架，提供服务注册和远程调用能力 |
| Spring Framework | 依赖注入和AOP支持 |
| Lombok | 代码生成工具，简化getter/setter等代码 |
| Apache Commons | 通用工具类库，提供字符串、集合等操作 |

## 13. 幂等性与安全性说明

### 幂等性
- **接口幂等性**：是
- **说明**：该接口为查询类接口，多次调用相同参数返回相同结果，具备天然幂等性
- **实现方式**：无状态设计，不修改任何数据，仅进行校验和查询操作

### 安全性
- **鉴权机制**：依赖Dubbo框架的服务鉴权机制
- **参数验证**：严格的参数校验，防止SQL注入和XSS攻击
- **访问控制**：仅允许内部系统调用，不对外部开放
- **数据脱敏**：敏感信息在日志中进行脱敏处理
- **限流机制**：依赖Dubbo的限流配置，防止接口被恶意调用

## 14. 备注与风险点

### 注意事项
1. **交易时间限制**：接口校验会考虑基金的交易时间窗口，非交易时间可能返回不可购买
2. **客户状态依赖**：客户账户状态、风险等级等会影响校验结果
3. **基金状态变化**：基金的开放状态、净值状态等可能实时变化，需要获取最新状态
4. **分次Call逻辑**：对于支持分次Call的基金，校验逻辑更加复杂，需要特别关注

### 边界处理
1. **空值处理**：对于非必填参数，系统会进行空值兼容处理
2. **并发处理**：接口支持高并发调用，但需要注意数据库连接池配置
3. **超时处理**：建议设置合理的调用超时时间，避免长时间等待

### 特殊逻辑说明
1. **账号自动创建**：当客户没有基金交易账号时，系统会自动创建非全委类型账号
2. **校验链设计**：采用责任链模式，可以灵活配置校验规则
3. **上下文传递**：通过TradeContext在各个校验器之间传递数据，保证数据一致性

### 风险点
1. **性能风险**：校验链较长，可能影响接口响应时间
2. **数据一致性**：多个校验器可能访问不同数据源，需要保证数据一致性
3. **配置依赖**：校验规则依赖配置表，配置错误可能导致校验异常
