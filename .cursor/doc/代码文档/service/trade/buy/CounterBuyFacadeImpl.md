# 接口详细设计文档

## 1. 接口名称
柜台买入聚合接口

## 2. 接口说明
提供柜台交易场景下的基金买入相关功能，包括普通买入和储蓄罐买入。此接口是一个聚合接口，包含了交易前的校验和交易执行两个步骤，并分别对普通买入和储蓄罐买入两种场景提供了支持。

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.buy.CounterBuyFacade
```

---

## 方法一：`counterBuyValidate`

### 4.1. 方法说明
**柜台买入校验**：在执行实际的柜台买入操作前，对交易的所有前提条件进行全面校验。

### 4.2. 方法签名
```java
Response<Body> counterBuyValidate(CounterBuyValidateRequest request)
```

### 4.3. 请求参数
(参数详情请参考 `CounterBuyValidateRequest` 类，核心参数如下)
| 中文名 | 英文名 | 类型 | 是否必填 | 字段说明 |
|---|---|---|---|---|
| 香港客户号 | hkCustNo | String | 是 | 客户唯一标识 |
| 基金代码 | fundCode | String | 是 | |
| 基金交易账号 | fundTxAcctNo | String | 是 | |
| 支付方式 | payMethod | String | 是 | `1`-电汇, `2`-支票, `3`-海外储蓄罐 |
| 买入金额 | buyAmt | BigDecimal | 是 | |
| 预估手续费 | estimateFee | BigDecimal | 是 | |
| 中台业务码 | businessCode | String | 是 | 如 `1122` (申购) |
| ... | ... | ... | ... | (其他基础参数) |

### 4.4. 响应参数
- `Response<Body>`: 校验成功时，返回成功的响应码，`data`部分为空。校验失败时，抛出业务异常。

### 4.5. 关键业务逻辑
1.  **构建上下文**：基于请求参数，构建包含客户、产品、交易方向等信息的 `TradeContext`。
2.  **构建校验链**：通过 `TradeValidatorHelper` 动态构建一个包含20个校验项的校验责任链。
3.  **执行校验**：依次执行校验链中的各项校验，包括：
    -   账户状态、风评、投资者资质
    -   产品状态、渠道、业务限制
    -   支付方式、交易工作日、柜台是否关闭
    -   交易额度、差额、手续费
    -   预约交易有效性
4.  任何一项校验失败，则中断流程并抛出异常。全部通过则方法正常返回。

---

## 方法二：`counterBuy`

### 4.1. 方法说明
**柜台买入执行**：执行实际的柜台买入操作，创建订单并发送消息。此操作应在 `counterBuyValidate` 成功后调用。

### 4.2. 方法签名
```java
Response<CounterBuyVO> counterBuy(CounterBuyRequest request)
```

### 4.3. 请求参数
(参数与 `counterBuyValidate` 基本一致，增加了展期相关字段)
| 中文名 | 英文名 | 类型 | 是否必填 | 字段说明 |
|---|---|---|---|---|
| ... | ... | ... | ... | (同 `counterBuyValidate` 请求参数) |
| 展期选项 | extOption | String | 否 | `1`-本金展期, `2`-本金+收益展期... |
| 展期控制数 | extControlNum | String | 否 | |

### 4.4. 响应参数
| 中文名 | 英文名 | 类型 | 字段说明 |
|---|---|---|---|
| 订单号 | dealNo | String | 新创建的订单号 |
| 订单明细号 | dealDtlNo | String | 新创建的订单明细号 |

### 4.5. 关键业务逻辑
1.  **执行校验**：首先调用内部的 `doValidate` 方法，执行与 `counterBuyValidate` 完全相同的校验流程。
2.  **创建订单**：校验通过后，调用 `OrderCreateService.createOrder` 服务，根据上下文信息组装 `OrderCreateBO` 对象。
3.  **保存订单**：调用 `HwDealOrderRepository.saveDealOrder` 将订单和订单明细数据持久化到数据库。
4.  **发送消息**：调用 `OrderMessageService.buyOrderMessage` 发送订单创建成功的MQ消息，以供下游系统消费。
5.  **返回结果**：将新生成的订单号和明细号封装到 `CounterBuyVO` 中返回。

---

## 方法三：`counterPiggyBuyValidate`

### 4.1. 方法说明
**柜台储蓄罐买入校验**：`counterBuyValidate` 的一个特定场景变种，专门用于校验使用储蓄罐作为支付方式的买入交易。

### 4.2. 方法签名
```java
Response<Body> counterPiggyBuyValidate(CounterPiggyBuyValidateRequest request)
```

### 4.3. 请求参数
(与 `counterBuyValidate` 的参数完全相同)

### 4.4. 响应参数
(同 `counterBuyValidate` 响应参数)

### 4.5. 关键业务逻辑
- 与 `counterBuyValidate` 的逻辑基本一致，但在校验链中增加了一项针对储蓄罐买入的专属校验 `TradeValidatorEnum.BUY_PIGGY_FUND`。

---

## 方法四：`counterNewPiggyBuy`

### 4.1. 方法说明
**柜台储蓄罐买入执行**：`counterBuy` 的一个特定场景变种，专门用于执行使用储蓄罐作为支付方式的买入交易。

### 4.2. 方法签名
```java
Response<CounterBuyVO> counterNewPiggyBuy(CounterNewPiggyBuyRequest request)
```

### 4.3. 请求参数
(与 `counterBuy` 的参数基本相同，但不包含展期字段)

### 4.4. 响应参数
(同 `counterBuy` 响应参数)

### 4.5. 关键业务逻辑
1.  **执行校验**：调用内部 `doValidate` 方法，执行与 `counterPiggyBuyValidate` 完全相同的校验流程（包含储蓄罐专属校验）。
2.  **创建订单、保存订单、发送消息**：后续逻辑与 `counterBuy` 方法完全相同。
3.  **返回结果**：返回新生成的订单号和明细号。 