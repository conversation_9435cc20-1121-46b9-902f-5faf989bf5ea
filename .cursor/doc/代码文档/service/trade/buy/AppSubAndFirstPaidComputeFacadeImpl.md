# 接口详细设计文档

## 1. 接口名称
申购认缴与实缴费用计算

## 2. 接口说明
针对支持分次缴纳（认缴/实缴模式）的基金产品，计算其在申购时的手续费。该接口可以根据认缴金额和本次实缴金额，确定适用的费率类型（按认缴总额计算或按本次实缴金额计算），并返回详细的费用信息。

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.buy.AppSubAndFirstPaidComputeFacade.execute(AppSubAndFirstPaidComputeRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|---|---|---|---|---|---|
| 香港客户号 | hkCustNo | String | 是 | `HK123456` | 客户的香港唯一标识 |
| 基金代码 | fundCode | String | 是 | `HB0002` | 目标基金的代码 |
| 认缴金额 | subAmt | BigDecimal | 是 | `100000.00` | 客户承诺的总投资金额 |
| 实缴金额 | paidAmt | BigDecimal | 是 | `20000.00` | 本次实际支付的金额 |
| 申请日期 | appDt | String | 是 | `20250510` | 格式 YYYYMMDD |
| 申请时间 | appTm | String | 是 | `102030` | 格式 HHMMSS |
| 交易渠道 | tradeChannel | String | 是 | `11` | 标识交易来源，如APP、PC等 |
| 网点号 | outletCode | String | 是 | `101` | |
| IP地址 | ipAddress | String | 是 | `************` | |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 字段说明 |
|---|---|---|---|
| 实际支付金额 | actualPayAmt | BigDecimal | 本次实缴金额 + 计算出的手续费 |
| 手续费费率 | feeRate | BigDecimal | 最终采用的手续费率 |
| 手续费类型 | feeRateType | String | `1`-按认缴金额计算, `2`-按实缴金额计算 |
| 预估手续费 | estimateFee | BigDecimal | 折扣后的预估手续费 |
| 原始手续费 | originalFee | BigDecimal | 折扣前的原始手续费 |
| 是否大于预约金额 | isLargerPrebookAmt | String | `0`-否, `1`-是 |
| 折扣是否生效 | validDiscountRate | String | `0`-否, `1`-是 |
| 实际折扣率 | actualDiscountRate | BigDecimal | 最终生效的折扣率 |
| 折扣类型 | discountType | String | 使用的折扣活动类型 |
| 折扣金额 | discountAmt | BigDecimal | 折扣掉的金额 |
| 预约折扣率 | prebookDiscountRate | BigDecimal | 客户预约时享受的折扣率 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|---|---|---|
| 0000 | 成功 | |
| A0001 | 系统错误 | |
| B0001 | 参数校验失败 | |
| B0002 | 预估手续费小于0 | 业务规则不允许 |
| B0003 | 认缴首次费率错误 | 未找到或配置错误 |
| C0001 | 外部服务调用异常 | |
| D0001 | 数据不存在 | |

## 8. 关键业务逻辑说明
1.  **参数校验与上下文构建**：
    -   对请求进行基础校验。
    -   调用外部服务获取客户信息(`HkCustInfoOuterService`)和基金信息(`QueryFundInfoOuterService`)，构建`BuyFeeComputeContext`。
2.  **查询预约信息**：调用`HkPrebookOuterService`查询客户的预约记录。
3.  **获取费用计算规则**：调用`FeeComputeService.getSubAndFirstPaidFundFeeRateByAppAmt`方法，根据认缴金额和实缴金额，确定本次应采用哪种费率规则（按认缴金额算费率，还是按实缴金额算费率）。
4.  **确定计费基础金额**：
    -   如果费率类型是`SUBSCRIBE_PAY_TYPE`（按认缴），则将认缴金额(`subAmt`)设置为计费基础。
    -   否则，将实缴金额(`paidAmt`)设置为计费基础。
5.  **核心费用计算**：调用`FeeComputeService.subAndFirstPaidBuyFeeCompute`方法，传入实缴金额、上下文、上一步获取的费率规则和预约信息，计算出手续费详情。
6.  **结果校验与封装**：
    -   校验计算出的预估手续费是否大于等于0，否则抛出异常。
    -   将`FeeComputeService`返回的`BuyFeeComputeInfoVO`转换为`AppSubAndFirstPaidComputeInfoVO`，并补充`feeRateType`字段后返回。

## 9. 流程图
```plantuml
@startuml
start
:接收 AppSubAndFirstPaidComputeRequest;
:参数校验;
if (校验失败) then (yes)
  :返回参数错误;
  stop
endif
:构建计算上下文 (客户信息、基金信息);
:查询客户预约记录;
:获取认缴/实缴费率规则;
if (按认缴金额计算费率?) then (yes)
  :设置认缴金额为计费基础;
else (no)
  :设置实缴金额为计费基础;
endif
:调用核心服务计算手续费;
:校验预估手续费是否 < 0;
if (是) then (yes)
  :抛出异常;
  stop
endif
:封装响应结果 (补充费率类型);
:返回 AppSubAndFirstPaidComputeInfoVO;
end
@enduml
```

## 10. 时序图
```plantuml
@startuml
actor Client
participant Facade as AppSubAndFirstPaidComputeFacadeImpl
participant Service as AppSubAndFirstPaidFeeComputeService
participant CoreFee as FeeComputeService
participant Prebook as HkPrebookOuterService
participant Fund as QueryFundInfoOuterService
participant Cust as HkCustInfoOuterService

Client -> Facade: execute(request)
Facade -> Service: process(request)
Service -> Cust: getHkCustInfo(...)
Cust --> Service: HkCustInfoDTO
Service -> Fund: queryFundBasicInfo(...)
Fund --> Service: FundBasicInfoDTO
Service -> Prebook: getHkPreBookDetail(...)
Prebook --> Service: HkPreBookDetailDTO
Service -> CoreFee: getSubAndFirstPaidFundFeeRateByAppAmt(...)
CoreFee --> Service: FundFeeRateDTO (费率规则)
Service -> CoreFee: subAndFirstPaidBuyFeeCompute(...)
CoreFee --> Service: BuyFeeComputeInfoVO
Service -> Service: 封装为 AppSubAndFirstPaidComputeInfoVO
Service --> Facade: AppSubAndFirstPaidComputeInfoVO
Facade --> Client: Response
@enduml
```

## 11. 异常处理机制
- **参数校验**：`ParamsValidator`进行基础校验，不通过则抛出`ValidateException`。
- **业务断言**：使用`Assert.notNull`对关键业务数据（如费率类型）进行检查，失败会引发`IllegalArgumentException`。
- **业务校验**：对计算结果（如手续费不能为负）进行检查，不满足条件则抛出`ValidateException`。
- **外部依赖**：对外部服务的调用异常依赖统一的异常处理机制进行捕获和转换。

## 12. 调用的公共模块或外部依赖
| 模块/服务 | 功能简述 |
|---|---|
| `HkCustInfoOuterService` | 查询客户基本信息。 |
| `QueryFundInfoOuterService` | 查询基金产品信息，包括费率等。 |
| `HkPrebookOuterService` | 查询客户的交易预约记录。 |
| `FeeComputeService` | 核心费用计算服务，提供了获取费率规则和执行计算的底层能力。 |

## 13. 幂等性与安全性说明
- **幂等性**：该接口是查询计算类接口，不修改任何数据状态，天然幂等。
- **安全性**：同`BuyFeeComputeFacade`，依赖服务调用链上的鉴权机制，并注意敏感数据处理。

## 14. 备注与风险点
- 接口的核心在于`getSubAndFirstPaidFundFeeRateByAppAmt`方法返回的费率规则，该规则的正确性直接决定了后续计算的准确性。
- 依赖产品中心对认缴/实缴模式基金的费率配置必须准确无误。 