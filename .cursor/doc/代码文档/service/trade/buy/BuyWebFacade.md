# 接口详细设计文档

## 1. 接口名称
APP 基金认申购下单接口

## 2. 接口说明
提供给移动端（APP）进行基金认购或申购的下单功能。用户通过此接口提交购买基金的请求，系统后台处理并创建交易订单。

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.buy.BuyWebFacade.execute(BuyWebRequest request)
```

## 5. 请求参数表 (`BuyWebRequest`)

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|:---|:---|:---|:---:|:---|:---|
| 香港客户号 | `hkCustNo` | String | 是 | "HK10086" | 客户在香港的唯一标识 |
| 基金代码 | `fundCode` | String | 是 | "000001" | 交易的基金代码 |
| 资金账号 | `cpAcctNo` | String | 否 | "987654321" | 支付方式为1-电汇时必填 |
| 交易密码 | `txPassword` | String | 是 | "********" | 用户的交易密码，通常是加密传输 |
| 支付方式 | `payMethod` | String | 是 | "1" | 支付方式: 1-电汇、2-支票、3-海外储蓄罐 |
| 申请金额 | `buyAmt` | BigDecimal | 是 | 100000.00 | 用户申请购买的金额 |
| 预估手续费 | `estimateFee` | BigDecimal | 是 | 100.00 | 前端计算的预估交易手续费 |
| 是否同意换汇 | `isAgreeCurrencyExchange` | String | 否 | "1" | 0-否 1-是。当支付币种与产品币种不一致时，是否同意系统进行换汇 |
| 预约单号 | `prebookDealNo` | String | 否 | "PB20240726001" | 如果此交易由预约单转化而来，则需要上送此单号 |
| 订单号 | `dealNo` | String | 是 | "DEAL20240726001" | 本次交易的唯一订单号，用于幂等性控制 |
| 外部订单号 | `externalDealNo` | String | 否 | "EXT20240407001" | 外部系统传入的订单号，用于跟踪 |
| 交易渠道 | `tradeChannel` | String | 是 | "11" | 标识交易来源渠道, 如: 11-APP |
| 申请日期 | `appDt` | String | 是 | "20240726" | 客户端的申请日期，格式: YYYYMMDD |
| 申请时间 | `appTm` | String | 是 | "155849" | 客户端的申请时间，格式: HHmmss |

## 6. 响应参数表 (`Response<BuyWebVO>`)
**通用响应参数**
| 英文名 | 类型 | 示例值 | 字段说明 |
|:---|:---|:---|:---|
| `code` | String | "0000" | 状态码，`0000` 表示成功，其他表示失败 |
| `description`| String | "成功" | 响应的描述信息 |
| `data` | BuyWebVO| `{...}` | 业务数据体 |

**`BuyWebVO` 结构**
| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
|:---|:---|:---|:---|:---|
| 订单号 | `dealNo` | String | "202404070001" | 系统生成的唯一交易订单号 |
| 订单状态 | `orderStatus` | String | "01" | 订单的当前状态 |
| 订单状态描述 | `orderStatusDesc` | String | "申请成功" | 订单状态的中文描述 |

## 7. 返回码说明
| 返回码 | 说明 | 备注 |
|:---|:---|:---|
| 0000 | 成功 | 交易已成功受理 |
| B001 | 参数校验失败 | 请求参数不符合要求，例如必填项为空 |
| B020 | 交易重复提交 | 订单号重复，触发幂等性校验 |
| C001 | 系统异常 | 后台服务发生未知错误 |

## 8. 关键业务逻辑说明
1.  **参数校验**：对 `BuyWebRequest` 中的必填字段和格式进行基础校验。
2.  **构建交易上下文**：使用 `TradeContextBuilder` 将请求参数转换为内部统一的交易上下文 `TradeContext`，用于后续业务逻辑的传递。
3.  **执行交易校验链**：通过 `TradeValidatorHelper` 执行一系列的业务规则校验，例如客户状态、交易资格、基金状态、交易限额、首次交易标识等。
4.  **创建订单**：调用 `OrderCreateService` 创建订单主表 (`HwDealOrderPO`) 和订单明细表 (`HwDealOrderDtlPO`) 的实体对象。
5.  **保存订单**：通过 `HwDealOrderRepository` 将订单实体和明细实体持久化到数据库，此操作在事务中完成。
6.  **发送MQ消息**：调用 `OrderMessageService` 发送订单创建成功的MQ消息，用于触发后续的记账、通知等异步流程。
7.  **组装返回结果**：将生成的订单号 (`dealNo`) 等信息封装到 `BuyWebVO` 中，并包装成统一的 `Response` 对象返回给调用方。

## 9. 流程图
```plantuml
@startuml
start
:接收 BuyWebRequest 请求;
:参数校验;
if (校验通过?) then (是)
    :构建 TradeContext;
    :执行业务规则校验链
    (客户/产品/风控);
    if (校验通过?) then (是)
        :创建订单实体;
        :保存订单至数据库;
        :发送MQ通知消息;
        :返回成功响应 (dealNo);
    else (否)
        :记录错误日志;
        :返回失败响应 (错误码, 错误信息);
    endif
else (否)
    :返回参数错误响应;
endif
stop
@enduml
```

## 10. 时序图
```plantuml
@startuml
actor "客户端(APP)" as Client
participant "BuyWebFacadeImpl" as Facade
participant "BuyWebService" as Service
participant "TradeValidatorHelper" as Validator
participant "OrderCreateService" as Creator
participant "HwDealOrderRepository" as Repo
participant "Database" as DB
participant "OrderMessageService" as Messenger
participant "MQ" as MqBroker

Client -> Facade : execute(BuyWebRequest)
activate Facade
Facade -> Service : process(request)
activate Service
Service -> Validator : tradeValidate(request)
activate Validator
Validator --> Service : 返回校验后的TradeContext
deactivate Validator
Service -> Creator : createOrder(context)
activate Creator
Creator --> Service : 返回OrderCreateBO
deactivate Creator
Service -> Repo : saveDealOrder(orderCreateBO)
activate Repo
Repo -> DB : INSERT INTO hw_deal_order...
Repo -> DB : INSERT INTO hw_deal_order_dtl...
deactivate Repo
Service -> Messenger : buyOrderMessage(context, bo)
activate Messenger
Messenger -> MqBroker : 发送订单消息
deactivate Messenger
Service --> Facade : 返回BuyWebVO
deactivate Service
Facade --> Client : 返回Response<BuyWebVO>
deactivate Facade
@enduml
```

## 11. 异常处理机制
- **业务异常**：在业务校验环节（如客户不满足交易条件、产品已关闭等），会抛出自定义的 `BusinessException`，由全局异常处理器捕获并转换为对应的错误码和错误信息返回给前端。
- **系统异常**：在处理过程中发生的非预期异常（如数据库连接失败、空指针等），会由全局异常处理器捕获，记录详细错误日志，并向前端返回统一的"系统异常"错误码，避免暴露内部实现细节。

## 12. 调用的公共模块或外部依赖
| 模块/依赖 | 功能简述 |
|:---|:---|
| `dtms-order-service` | **核心服务层** |
| `BuyWebService` | 编排和组织认申购交易的核心服务 |
| `TradeValidatorHelper`| 执行交易链式校验，包括客户、产品、风控等 |
| `OrderCreateService` | 负责根据交易上下文创建订单实体 |
| `HwDealOrderRepository` | 负责订单数据的数据库持久化操作 |
| `OrderMessageService` | 负责发送订单相关的MQ消息 |
| `MQ Middleware` | 用于系统间业务流程的异步解耦 |
| `Database` | 存储订单、客户、产品等核心数据 |


## 13. 幂等性与安全性说明
- **幂等性**：接口通过请求参数中的 `dealNo` (订单号) 字段来保证幂等性。在创建订单前，会检查该 `dealNo` 是否已经存在，如果存在则直接返回错误或已存在的订单信息，防止重复创建订单。
- **安全性**：
    - **鉴权**：依赖于上游网关或Dubbo框架的Filter进行统一的身份认证和会话管理。
    - **验签**：关键请求可能需要进行数字签名验证，以防止请求被篡改。
    - **防刷**：可接入API网关层的限流、防刷策略，对用户或IP进行访问频率控制。

## 14. 备注与风险点
- **事务管理**：订单创建（包括主表和明细表）的核心操作由Spring的 `@Transactional` 注解保证在同一个数据库事务中，确保数据的一致性。
- **依赖服务**：接口处理依赖多个内部服务，需要考虑对下游服务（如MQ）的熔断和降级处理，保证在下游服务异常时主流程的稳定性。
- **数据一致性**：通过MQ发送消息的异步流程，需要有可靠消息机制（如事务消息或本地消息表+定时扫描）来保证主业务和后续异步操作的最终数据一致性。 