# CRM基金是否可购买接口

## 1. 接口名称
CRM基金是否可购买校验接口

## 2. 接口说明
### 接口用途
该接口用于校验指定基金是否可以被香港客户购买，主要服务于CRM系统的基金交易前置校验功能。

### 业务背景
在基金交易系统中，客户购买基金前需要进行多项校验，包括基金状态、客户资格、交易时间等。该接口作为购买前置校验的核心组件，确保只有符合条件的基金才能被客户购买。

### 使用场景
- CRM系统基金购买页面的实时校验
- 基金交易订单创建前的预校验
- 客户咨询基金购买资格时的查询
- 批量基金购买资格检查

## 3. 接口类型
Dubbo RPC接口

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.buy.CrmFundCanBuyFacade.execute(CanBuyRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名（字段名） | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|------------------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK123456789 | 香港客户的唯一标识号 |
| 基金代码 | fundCode | String | 是 | 000001 | 基金的唯一标识代码 |
| 交易码 | txCode | String | 否 | HW_ORDER_CAN_BUY | 交易类型编码，默认为可买入校验 |
| 申请日期 | appDt | String | 否 | 20250124 | 申请日期，格式：yyyyMMdd |
| 申请时间 | appTm | String | 否 | 143000 | 申请时间，格式：HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 9 | 交易渠道：1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 001 | 交易网点编号 |
| IP地址 | ipAddress | String | 否 | ************* | 客户端IP地址 |
| 外部订单号 | externalDealNo | String | 否 | EXT20250124001 | 外部系统订单号 |
| MAC地址 | macAddress | String | 否 | 00:11:22:33:44:55 | 客户端MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456789 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | 张三的iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | iOS 15.0 | 操作系统版本 |

## 6. 响应参数表

| 中文名 | 英文名（字段名） | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|------------------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 200 | 响应状态码，200表示成功 |
| 描述信息 | description | String | 是 | 操作成功 | 响应描述信息 |
| 数据封装 | data | FundCanBuyResponse | 否 | {} | 业务数据封装对象 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 200 | 操作成功 | 基金可以购买 |
| 400 | 参数错误 | 必填参数缺失或格式错误 |
| 500 | 系统内部错误 | 服务器内部异常 |
| 1001 | 客户不存在 | 香港客户号不存在 |
| 1002 | 基金不存在 | 基金代码不存在 |
| 1003 | 基金暂停申购 | 基金当前状态不允许申购 |
| 1004 | 客户资格不符 | 客户不符合该基金的购买条件 |

## 8. 关键业务逻辑说明

该接口的核心处理逻辑包含以下几个关键步骤：

1. **参数校验阶段**：
   - 使用ParamsValidator对请求参数进行校验
   - 重点校验hkCustNo（香港客户号）和fundCode（基金代码）的必填性和格式正确性
   - 校验失败直接抛出参数异常

2. **业务处理阶段**：
   - 调用CrmCanBuyService.process()方法执行核心业务逻辑
   - 该方法内部会进行多维度的购买资格校验
   - 包括客户状态校验、基金状态校验、交易时间校验等

3. **响应构建阶段**：
   - 业务处理成功后，直接返回Response.ok()
   - 异常情况由全局异常处理器统一处理并返回相应错误码

## 9. 流程图

```plantuml
@startuml
start
:接收CanBuyRequest请求;
:参数校验\n- 校验hkCustNo必填\n- 校验fundCode必填;
if (参数校验通过?) then (否)
  :抛出参数异常;
  stop
else (是)
  :调用CrmCanBuyService.process();
  :执行业务逻辑\n- 客户资格校验\n- 基金状态校验\n- 交易时间校验;
  if (业务校验通过?) then (否)
    :抛出业务异常;
    stop
  else (是)
    :返回Response.ok();
    stop
  endif
endif
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "CRM前端" as Frontend
participant "Dubbo网关" as Gateway
participant "CrmFundCanBuyFacadeImpl" as Controller
participant "ParamsValidator" as Validator
participant "CrmCanBuyService" as Service
participant "数据库" as DB
participant "Redis缓存" as Cache

Frontend -> Gateway: 调用基金可买入校验接口
Gateway -> Controller: execute(CanBuyRequest)
Controller -> Validator: validate(request, "hkCustNo", "fundCode")
Validator --> Controller: 校验结果

alt 参数校验失败
    Controller --> Gateway: 抛出参数异常
    Gateway --> Frontend: 返回参数错误
else 参数校验成功
    Controller -> Service: process(canBuyRequest)
    Service -> Cache: 查询客户信息缓存
    Cache --> Service: 客户信息
    
    alt 缓存未命中
        Service -> DB: 查询客户信息
        DB --> Service: 客户信息
        Service -> Cache: 更新缓存
    end
    
    Service -> Cache: 查询基金信息缓存
    Cache --> Service: 基金信息
    
    alt 缓存未命中
        Service -> DB: 查询基金信息
        DB --> Service: 基金信息
        Service -> Cache: 更新缓存
    end
    
    Service -> Service: 执行购买资格校验逻辑
    Service --> Controller: 校验结果
    Controller --> Gateway: Response.ok()
    Gateway --> Frontend: 返回成功响应
end
@enduml
```

## 11. 异常处理机制

### 主要异常场景及处理方式

1. **参数校验异常**
   - 异常类型：参数验证失败
   - 处理方式：ParamsValidator抛出验证异常，由全局异常处理器捕获并返回400错误码
   - 返回信息：具体的参数错误描述

2. **业务逻辑异常**
   - 异常类型：客户不存在、基金不存在、购买资格不符等
   - 处理方式：Service层抛出业务异常，返回对应的业务错误码
   - 返回信息：具体的业务错误描述

3. **系统异常**
   - 异常类型：数据库连接异常、网络异常、服务不可用等
   - 处理方式：全局异常处理器捕获并返回500错误码
   - 返回信息：系统内部错误，不暴露具体异常信息

4. **超时异常**
   - 异常类型：Dubbo调用超时
   - 处理方式：Dubbo框架自动重试，超过重试次数后返回超时异常
   - 返回信息：服务调用超时

## 12. 调用的公共模块或外部依赖

### 内部模块依赖

| 模块名称 | 功能简述 |
|----------|----------|
| ParamsValidator | 参数校验工具类，提供统一的参数验证功能 |
| CrmCanBuyService | CRM购买资格校验服务，核心业务逻辑处理 |
| BaseRequest | 基础请求类，包含通用的请求参数 |
| Response | 通用响应封装类，标准化接口返回格式 |

### 外部依赖

| 依赖名称 | 功能简述 |
|----------|----------|
| Dubbo | RPC框架，提供服务注册发现和远程调用能力 |
| Spring Framework | 依赖注入和AOP支持 |
| Lombok | 代码生成工具，简化实体类编写 |
| MyValidation | 自定义参数验证注解框架 |

## 13. 幂等性与安全性说明

### 幂等性
- **是否幂等**：是
- **幂等说明**：该接口为查询类接口，多次调用相同参数返回相同结果，具备天然幂等性
- **实现方式**：无需特殊处理，查询操作本身不会改变系统状态

### 安全性

#### 鉴权机制
- **认证方式**：基于Dubbo的服务间认证
- **授权控制**：通过Dubbo的访问控制列表限制调用方
- **权限校验**：在Service层进行客户权限校验

#### 限流控制
- **限流策略**：基于客户号的令牌桶限流
- **限流阈值**：每个客户每秒最多10次调用
- **超限处理**：返回限流异常，提示调用频率过高

#### 数据安全
- **敏感数据**：客户号、设备信息等
- **加密传输**：Dubbo通信使用TLS加密
- **日志脱敏**：敏感字段在日志中进行脱敏处理

#### 验签机制
- **签名算法**：HMAC-SHA256
- **签名字段**：关键业务参数参与签名计算
- **防重放**：基于时间戳的防重放攻击机制

## 14. 备注与风险点

### 注意事项

1. **参数校验**
   - hkCustNo和fundCode为必填参数，调用方必须确保传入有效值
   - 日期时间格式必须严格按照yyyyMMdd和HHmmss格式传入

2. **性能考虑**
   - 该接口为高频调用接口，建议调用方实现适当的缓存机制
   - 避免短时间内对同一客户和基金进行重复校验

3. **业务逻辑**
   - 校验结果可能因基金状态变化而实时变化
   - 建议在实际购买前再次进行校验确认

### 边界处理

1. **空值处理**
   - 非必填参数允许为空，系统会使用默认值
   - 必填参数为空时会抛出参数校验异常

2. **特殊字符处理**
   - 客户号和基金代码不允许包含特殊字符
   - 设备信息字段允许包含中文和特殊字符

### 特殊逻辑说明

1. **交易时间校验**
   - 系统会校验当前时间是否在基金的交易时间范围内
   - 节假日和非交易日会返回相应的错误提示

2. **客户资格校验**
   - 不同基金可能有不同的客户资格要求
   - 系统会根据基金类型和客户等级进行动态校验

### 风险点

1. **数据一致性风险**
   - 基金状态和客户信息可能存在缓存延迟
   - 建议关键业务场景使用实时查询

2. **性能风险**
   - 高并发场景下可能出现数据库连接池耗尽
   - 建议配置合适的连接池参数和超时时间

3. **业务风险**
   - 校验通过不代表最终购买一定成功
   - 实际购买时可能因市场变化导致失败