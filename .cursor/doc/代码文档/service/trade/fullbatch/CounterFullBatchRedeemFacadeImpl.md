# 接口详细设计文档

## 1. 接口名称
柜台全委专户批量赎回接口

## 2. 接口说明
提供柜台交易场景下的全委专户批量赎回功能。此接口用于处理香港客户的全委专户基金批量赎回业务，支持多只基金同时赎回操作。

**业务背景**：
- 全委专户是指客户将资金委托给专业投资机构进行投资管理的业务模式
- 批量赎回功能允许客户一次性对多只基金发起赎回申请，提高操作效率
- 柜台场景下需要严格的业务校验和风控管理

**使用场景**：
- 柜台工作人员协助香港客户进行全委专户基金批量赎回
- 客户通过柜台系统发起多只基金的赎回申请
- 投资顾问为客户进行资产配置调整时的批量赎回操作

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fullbatch.CounterFullBatchRedeemFacade.execute(CounterFullBatchRedeemRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港地区客户的唯一标识号 |
| 全委批量赎回列表 | fullBatchRedeemInfoRequestList | List<FullBatchRedeemInfoRequest> | 是 | - | 批量赎回的基金明细列表 |
| 交易渠道 | tradeChannel | String | 是 | 1 | 交易渠道：1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 申请日期 | appDt | String | 是 | 20250726 | 申请日期，格式：YYYYMMDD |
| 申请时间 | appTm | String | 是 | 213035 | 申请时间，格式：HHmmss |

### 5.1 FullBatchRedeemInfoRequest 明细参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 外部订单号 | externalDealNo | String | 是 | EXT202507260001 | 外部系统生成的唯一订单号 |
| 基金交易账号 | fundTxAcctNo | String | 是 | 100001 | 客户在基金公司的交易账号 |
| 基金代码 | fundCode | String | 是 | 000001 | 基金产品的唯一标识代码 |
| 赎回方式 | redeemMethod | String | 是 | 1 | 赎回方式：1-金额赎回；2-份额赎回 |
| 申请金额 | appAmt | BigDecimal | 否 | 10000.00 | 赎回申请金额（金额赎回时必填） |
| 申请份额 | appVol | BigDecimal | 否 | 10000.00 | 赎回申请份额（份额赎回时必填） |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 成功 | 响应描述信息 |
| 响应数据 | data | CounterFullBatchRedeemResponse | 否 | - | 响应数据对象（当前为空对象） |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 批量赎回申请提交成功 |
| 9999 | 系统异常 | 系统内部错误 |
| 1001 | 参数校验失败 | 请求参数不符合要求 |
| 2001 | 客户状态异常 | 客户账户状态不允许交易 |
| 2002 | 基金交易账户状态异常 | 基金交易账户被冻结或注销 |
| 3001 | 基金产品状态异常 | 基金暂停赎回或清盘 |
| 3002 | 可用份额不足 | 客户持有份额不足以支持赎回 |
| 4001 | 赎回金额限制 | 赎回金额不符合最低赎回限制 |

## 8. 关键业务逻辑说明

1. **参数校验阶段**：
   - 验证香港客户号格式和有效性
   - 校验批量赎回列表不能为空
   - 验证每个赎回项的必填字段完整性
   - 校验赎回方式与申请金额/份额的匹配性

2. **业务校验阶段**：
   - 客户账户状态校验：确认客户账户正常且允许交易
   - 基金交易账户状态校验：验证基金交易账户状态正常
   - 产品业务校验：检查基金产品是否支持当前渠道和业务类型
   - 基金净值状态校验：确认基金净值正常，可进行赎回
   - 赎回方式校验：验证赎回方式的合法性
   - 可用份额校验：检查客户持有份额是否足够赎回
   - 最低持有份额校验：确保赎回后剩余份额符合最低持有要求
   - 交易限额校验：验证赎回金额是否在允许范围内

3. **订单创建阶段**：
   - 批量创建赎回订单记录
   - 生成内部订单号
   - 设置订单初始状态
   - 保存订单到数据库

4. **异步消息发送**：
   - 使用线程池异步发送订单消息
   - 触发后续的订单处理流程
   - 确保消息发送的可靠性

## 9. 流程图

```plantuml
@startuml
start
:接收批量赎回请求;
:参数基础校验;
if (校验通过?) then (否)
  :返回参数错误;
  stop
endif
:执行业务校验链;
note right
  - 客户状态校验
  - 基金交易账户校验
  - 产品业务校验
  - 基金净值状态校验
  - 赎回方式校验
  - 可用份额校验
  - 最低持有份额校验
  - 交易限额校验
end note
if (业务校验通过?) then (否)
  :返回业务错误;
  stop
endif
:批量创建订单;
:保存订单到数据库;
:异步发送订单消息;
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "柜台系统" as Counter
participant "CounterFullBatchRedeemFacade" as Facade
participant "CounterFullBatchRedeemService" as Service
participant "校验器链" as Validator
participant "订单服务" as OrderService
participant "消息队列" as MQ
participant "数据库" as DB

Counter -> Facade: execute(request)
Facade -> Service: execute(request)
Service -> Service: validate(request)
Service -> Validator: 执行校验链
Validator -> Validator: 客户状态校验
Validator -> Validator: 基金交易账户校验
Validator -> Validator: 产品业务校验
Validator -> Validator: 其他业务校验
Validator --> Service: 校验结果
Service -> Service: batchCreateAndSaveDeal()
Service -> DB: 批量保存订单
DB --> Service: 保存成功
Service -> OrderService: 异步发送订单消息
OrderService -> MQ: 发送消息
Service --> Facade: 返回响应
Facade --> Counter: 返回结果
@enduml
```

## 11. 异常处理机制

1. **参数校验异常**：
   - 捕获参数验证失败异常
   - 返回具体的参数错误信息
   - 记录错误日志便于排查

2. **业务校验异常**：
   - 各校验器抛出的业务异常
   - 统一异常处理机制
   - 返回业务错误码和描述

3. **数据库异常**：
   - 订单保存失败的处理
   - 事务回滚机制
   - 异常信息记录和告警

4. **消息发送异常**：
   - 异步消息发送失败处理
   - 重试机制和补偿策略
   - 失败消息的记录和监控

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| CounterFullBatchRedeemService | 柜台全委专户批量赎回核心业务服务 |
| AbstractCounterFullBatchRedeemService | 抽象批量赎回服务基类，提供通用校验逻辑 |
| TradeValidatorHelper | 交易校验助手，执行各种业务校验 |
| OrderMessageService | 订单消息服务，负责发送订单相关消息 |
| BatchTradeContext | 批量交易上下文，保存批量交易的相关信息 |
| ThreadPoolTaskExecutor | 线程池执行器，用于异步处理 |

## 13. 幂等性与安全性说明

**幂等性**：
- 通过外部订单号（externalDealNo）保证幂等性
- 重复提交相同外部订单号的请求不会重复创建订单
- 数据库层面对外部订单号建立唯一约束

**安全性**：
- 客户身份验证：验证香港客户号的合法性
- 权限校验：确保客户有权限进行相应基金的赎回操作
- 数据加密：敏感数据传输过程中进行加密处理
- 审计日志：记录所有交易操作的详细日志

**限流**：
- 接口层面的限流控制
- 防止恶意批量请求
- 保护系统稳定性

## 14. 备注与风险点

**注意事项**：
1. 批量赎回操作涉及多只基金，需要确保所有基金的校验都通过才能执行
2. 异步消息发送失败不影响订单创建，但需要监控消息发送状态
3. 赎回方式（金额赎回/份额赎回）需要与对应的申请金额/申请份额字段匹配

**边界处理**：
1. 批量赎回列表为空时的处理
2. 单个基金校验失败对整个批次的影响
3. 部分订单创建成功，部分失败的处理策略

**特殊逻辑说明**：
1. 全委专户业务的特殊校验规则
2. 香港客户的特殊业务要求
3. 批量操作的事务一致性保证

**风险点**：
1. 大批量操作可能导致系统性能问题
2. 异步消息处理失败可能导致订单状态不一致
3. 并发操作可能导致份额计算错误
