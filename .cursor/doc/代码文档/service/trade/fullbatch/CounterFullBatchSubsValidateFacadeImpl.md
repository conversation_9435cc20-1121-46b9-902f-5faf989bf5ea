# 接口详细设计文档

## 1. 接口名称
柜台全委专户认申购校验接口

## 2. 接口说明
提供柜台交易场景下的全委专户认申购校验功能。此接口用于在执行实际批量认申购操作前，对所有前置条件进行全面校验，确保交易的合法性和安全性。

**业务背景**：
- 全委专户是指客户将资金委托给专业投资机构进行投资管理的业务模式
- 批量认申购校验是批量认申购流程的第一步，用于提前发现和拦截不符合条件的交易
- 柜台场景下需要严格的业务校验和风控管理，包括账户状态、产品状态、交易限制等多维度校验

**使用场景**：
- 柜台工作人员在协助香港客户进行全委专户基金批量认申购前的预校验
- 客户通过柜台系统发起多只基金认申购申请的前置校验
- 投资顾问为客户进行资产配置调整时的批量认申购可行性验证

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fullbatch.CounterFullBatchSubsValidateFacade.execute(CounterFullBatchSubsValidateRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港地区客户的唯一标识号 |
| 基金交易账号 | fundTxAcctNo | String | 是 | 100001 | 客户的基金交易账户号码 |
| 全委批量认申购列表 | fullBatchSubsList | List&lt;FullBatchSubsInfoRequest&gt; | 是 | 见下表 | 批量认申购的基金明细列表 |
| 交易渠道 | tradeChannel | String | 是 | 1 | 交易发起的渠道标识 |
| 申请日期 | appDt | String | 是 | 20250414 | 申请日期，格式：YYYYMMDD |
| 申请时间 | appTm | String | 是 | 192116 | 申请时间，格式：HHmmss |

### FullBatchSubsInfoRequest 明细参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 外部订单号 | externalDealNo | String | 否 | EXT20250414001 | 外部系统的订单号 |
| 基金代码 | fundCode | String | 是 | 000001 | 基金的唯一标识代码 |
| 中台业务码 | midBusinessCode | String | 是 | SUB001 | 中台系统的业务类型代码 |
| 净申请金额 | netAppAmt | BigDecimal | 是 | 10000.00 | 扣除费用后的实际申请金额 |
| 预估费用 | estimateFee | BigDecimal | 是 | 100.00 | 预估的交易手续费 |
| 申请金额 | appAmt | String | 否 | 10100.00 | 总申请金额（含费用） |
| 展期选项 | extensionOption | String | 否 | 1 | 产品展期选择 |
| 展期控制数 | extensionCount | String | 否 | 3 | 展期次数控制 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码 |
| 描述信息 | description | String | 是 | 成功 | 响应描述信息 |
| 响应数据 | data | CounterFullBatchSubsValidateResponse | 否 | {} | 校验响应数据（当前为空对象） |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 校验通过，可以进行后续交易 |
| 1001 | 参数校验失败 | 请求参数不符合要求 |
| 2001 | 账户状态异常 | 客户账户状态不允许交易 |
| 2002 | 基金交易账户状态异常 | 基金交易账户被冻结或注销 |
| 2003 | 风险等级不匹配 | 客户风险等级与产品不匹配 |
| 2004 | 衍生品经验不足 | 客户缺乏衍生品投资经验 |
| 2005 | 投资者资格不符 | 不符合投资者适当性要求 |
| 2006 | 产品年龄限制 | 客户年龄不符合产品要求 |
| 2007 | 产品渠道限制 | 当前渠道不支持该产品 |
| 2008 | 产品业务限制 | 产品不支持当前业务类型 |
| 2009 | 支付方式限制 | 支付方式不被允许 |
| 2010 | 开户日期限制 | 开户日期不符合支付方式要求 |
| 2011 | 柜台已关闭 | 当前时间柜台不可交易 |
| 2012 | 产品净值状态异常 | 产品净值状态不允许交易 |
| 2013 | 当期期数金额限制 | 超出当期允许的期数或金额 |
| 2014 | 当期单笔限制 | 超出当期单笔交易限制 |
| 2015 | 买入交易限制 | 违反买入交易相关限制 |
| 2016 | 交易差额限制 | 交易差额不符合要求 |
| 2017 | 柜台买入费用异常 | 柜台买入费用计算或校验异常 |
| 9999 | 系统异常 | 系统内部错误 |

## 8. 关键业务逻辑说明

### 核心校验流程：
1. **参数基础校验**：验证请求参数的完整性和格式正确性
2. **账户状态校验**：检查客户账户和基金交易账户的状态是否正常
3. **适当性校验**：验证客户的风险等级、衍生品经验、投资者资格等是否符合产品要求
4. **产品校验**：检查产品的年龄限制、渠道限制、业务类型限制等
5. **交易限制校验**：验证支付方式、开户日期、柜台状态、净值状态等交易前置条件
6. **额度校验**：检查当期期数金额限制、单笔交易限制、买入交易限制等
7. **费用校验**：验证交易差额和柜台买入费用的合理性

### 批量处理逻辑：
- 对批量认申购列表中的每个基金项目分别进行校验
- 任何一个基金项目校验失败，整个批量校验失败
- 校验过程中会构建批量交易上下文，包含所有必要的校验信息

### 校验链模式：
- 使用责任链模式构建校验链，包含多个校验器
- 每个校验器负责特定的业务规则校验
- 校验器按顺序执行，遇到失败立即终止并返回错误信息

## 9. 流程图

```plantuml
@startuml
start
:接收校验请求;
:参数基础校验;
if (参数校验通过?) then (否)
  :返回参数错误;
  stop
endif
:构建批量交易上下文;
:遍历认申购列表;
while (还有基金项目?) is (是)
  :获取基金项目信息;
  :构建校验链;
  :执行账户状态校验;
  if (账户状态正常?) then (否)
    :返回账户状态错误;
    stop
  endif
  :执行适当性校验;
  if (适当性校验通过?) then (否)
    :返回适当性错误;
    stop
  endif
  :执行产品校验;
  if (产品校验通过?) then (否)
    :返回产品限制错误;
    stop
  endif
  :执行交易限制校验;
  if (交易限制校验通过?) then (否)
    :返回交易限制错误;
    stop
  endif
  :执行额度校验;
  if (额度校验通过?) then (否)
    :返回额度限制错误;
    stop
  endif
  :执行费用校验;
  if (费用校验通过?) then (否)
    :返回费用错误;
    stop
  endif
endwhile (否)
:所有校验通过;
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "调用方" as Caller
participant "CounterFullBatchSubsValidateFacadeImpl" as Facade
participant "CounterFullBatchSubsValidateService" as Service
participant "TradeValidatorHelper" as Validator
participant "AbstractCounterFullBatchSubsService" as AbstractService

Caller -> Facade: execute(request)
activate Facade
Facade -> Service: execute(request)
activate Service
Service -> AbstractService: validate(request)
activate AbstractService
AbstractService -> AbstractService: doValidate(request)
AbstractService -> AbstractService: doBatchValidate(request, hkCustNo, fundTxAcctNo, fullBatchSubsList)
loop 遍历认申购列表
  AbstractService -> Service: doValidateChain(context)
  activate Service
  Service -> Validator: buildChain(validatorList)
  activate Validator
  Validator --> Service: validatorList
  deactivate Validator
  Service -> Validator: doValidate(context, validatorList)
  activate Validator
  loop 执行校验链
    Validator -> Validator: 执行单个校验器
    alt 校验失败
      Validator --> Service: 抛出业务异常
    end
  end
  Validator --> Service: 校验通过
  deactivate Validator
  Service --> AbstractService: 校验完成
  deactivate Service
end
AbstractService --> Service: 批量校验完成
deactivate AbstractService
Service --> Facade: 校验成功
deactivate Service
Facade -> Facade: Response.ok()
Facade --> Caller: Response<CounterFullBatchSubsValidateResponse>
deactivate Facade
@enduml
```

## 11. 异常处理机制

### 主要异常场景：
1. **参数校验异常**：使用@MyValidation注解进行参数校验，校验失败抛出参数异常
2. **业务校验异常**：各个校验器在校验失败时抛出具体的业务异常
3. **系统异常**：数据库连接异常、网络异常等系统级异常

### 异常处理方式：
- **参数异常**：直接返回参数错误码和错误信息
- **业务异常**：捕获业务异常，转换为对应的错误码和用户友好的错误信息
- **系统异常**：记录详细错误日志，返回通用系统错误码

### 日志记录：
- 接口调用开始和结束时记录关键信息
- 校验失败时记录详细的失败原因和上下文信息
- 系统异常时记录完整的异常堆栈信息

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| CounterFullBatchSubsValidateService | 柜台全委专户认申购校验核心业务服务 |
| TradeValidatorHelper | 交易校验助手，提供统一的业务校验能力 |
| AbstractCounterFullBatchSubsService | 抽象的全委专户认申购服务基类 |
| BatchTradeContext | 批量交易上下文对象 |
| @MyValidation | 参数校验注解，提供统一的参数校验能力 |
| Response | 统一响应对象封装 |

## 13. 幂等性与安全性说明

### 幂等性：
- **幂等接口**：校验接口本身是幂等的，多次调用结果一致
- **无副作用**：校验过程不会修改任何业务数据
- **可重复调用**：支持重复校验，不会产生数据不一致问题

### 安全性：
- **鉴权机制**：依赖Dubbo框架的服务鉴权
- **参数校验**：使用@MyValidation注解进行参数合法性校验
- **业务校验**：多层次业务规则校验，确保交易合规性
- **数据安全**：敏感信息传输加密，数据库访问权限控制

### 限流：
- **接口限流**：可配置基于IP或客户的访问频率限制
- **资源保护**：校验过程中对数据库和缓存的访问进行合理控制

## 14. 备注与风险点

### 注意事项：
1. **批量校验性能**：大批量数据校验时需要注意处理时间和资源消耗
2. **校验规则一致性**：确保校验规则与实际交易规则保持一致
3. **错误信息准确性**：校验失败时提供准确、用户友好的错误信息

### 风险点：
1. **校验遗漏**：可能存在某些边界情况的校验遗漏
2. **性能风险**：大量并发校验请求可能影响系统性能
3. **数据一致性**：校验时点与实际交易时点之间的数据变化风险

### 特殊逻辑说明：
- 校验接口返回的Response对象中data字段为空对象，主要通过code和description字段传递校验结果
- 校验失败时会抛出异常，由框架统一处理并转换为错误响应
- 批量校验采用"快速失败"策略，任何一个项目校验失败即终止整个批量校验

---
**文档生成时间**：2025-07-26 21:43:08  
**接口版本**：1.0.0  
**维护人员**：shaoyang.li
