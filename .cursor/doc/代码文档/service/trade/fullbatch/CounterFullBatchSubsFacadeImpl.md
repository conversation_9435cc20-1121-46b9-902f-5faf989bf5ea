# 接口详细设计文档

## 1. 接口名称
柜台全委专户认申购接口

## 2. 接口说明
提供柜台交易场景下的全委专户认申购功能。此接口用于处理香港客户的全委专户基金批量认申购业务，支持多只基金同时认申购操作。

**业务背景**：
- 全委专户是指客户将资金委托给专业投资机构进行投资管理的业务模式
- 批量认申购功能允许客户一次性对多只基金发起认申购申请，提高操作效率
- 柜台场景下需要严格的业务校验和风控管理

**使用场景**：
- 柜台工作人员协助香港客户进行全委专户基金批量认申购
- 客户通过柜台系统发起多只基金的认申购申请
- 投资顾问为客户进行资产配置时的批量认申购操作

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fullbatch.CounterFullBatchSubsFacade.execute(CounterFullBatchSubsRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港地区客户的唯一标识号 |
| 基金交易账号 | fundTxAcctNo | String | 是 | 100001 | 客户的基金交易账户号码 |
| 全委批量认申购列表 | fullBatchSubsList | List&lt;FullBatchSubsInfoRequest&gt; | 是 | 见下表 | 批量认申购的基金明细列表 |
| 交易渠道 | tradeChannel | String | 是 | 1 | 交易发起的渠道标识 |
| 申请日期 | appDt | String | 是 | 20250414 | 申请日期，格式：YYYYMMDD |
| 申请时间 | appTm | String | 是 | 192116 | 申请时间，格式：HHmmss |

### FullBatchSubsInfoRequest 明细参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 外部订单号 | externalDealNo | String | 否 | EXT20250414001 | 外部系统生成的订单号 |
| 基金代码 | fundCode | String | 是 | 000001 | 基金的唯一标识代码 |
| 中台业务码 | midBusinessCode | String | 是 | 1122 | 中台系统的业务类型编码 |
| 净申请金额 | netAppAmt | BigDecimal | 是 | 10000.00 | 扣除费用后的实际申请金额 |
| 预估费用 | estimateFee | BigDecimal | 是 | 150.00 | 预估的手续费金额 |
| 申请金额 | appAmt | String | 否 | 10150.00 | 总申请金额（含费用） |
| 展期选项 | extensionOption | String | 否 | 1 | 展期选择标识 |
| 展期控制数 | extensionCount | String | 否 | 3 | 展期次数控制 |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 接口调用结果状态码 |
| 描述信息 | description | String | 是 | 成功 | 接口调用结果描述 |
| 数据封装 | data | CounterFullBatchSubsResponse | 否 | null | 响应数据对象（当前为空对象） |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 接口调用成功 |
| 1001 | 参数校验失败 | 请求参数不符合要求 |
| 1002 | 业务校验失败 | 业务规则校验不通过 |
| 9999 | 系统异常 | 系统内部错误 |

## 8. 关键业务逻辑说明

1. **参数校验阶段**：
   - 校验香港客户号、基金交易账号、交易渠道等必填参数
   - 校验申请日期和时间格式的正确性
   - 校验批量认申购列表不为空且每个明细的必填字段完整

2. **业务校验阶段**：
   - 账户状态校验：验证客户账户和基金交易账户状态正常
   - 风险等级校验：验证客户风险等级与产品匹配
   - 产品状态校验：验证基金产品可正常交易
   - 交易限制校验：验证交易金额、次数等限制条件
   - 费用计算校验：验证预估费用的准确性

3. **订单创建阶段**：
   - 批量创建交易订单记录
   - 生成内部订单号
   - 保存订单详细信息到数据库

4. **异步处理阶段**：
   - 异步发送订单消息到MQ
   - 触发后续的订单处理流程

## 9. 流程图

```plantuml
@startuml
start
:接收认申购请求;
:参数校验;
if (参数校验通过?) then (是)
  :执行业务校验;
  if (业务校验通过?) then (是)
    :批量创建订单;
    :保存订单到数据库;
    :异步发送订单消息;
    :返回成功响应;
  else (否)
    :返回业务校验失败;
  endif
else (否)
  :返回参数校验失败;
endif
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "调用方" as Client
participant "CounterFullBatchSubsFacadeImpl" as Facade
participant "CounterFullBatchSubsService" as Service
participant "TradeValidatorHelper" as Validator
participant "OrderMessageService" as MsgService
participant "数据库" as DB
participant "MQ" as MQ

Client -> Facade: execute(request)
Facade -> Service: execute(request)
Service -> Service: validate(request)
Service -> Validator: doValidate(context, validatorList)
Validator -> DB: 查询账户、产品信息
DB --> Validator: 返回查询结果
Validator --> Service: 校验结果
Service -> Service: batchCreateAndSaveDeal(context)
Service -> DB: 批量保存订单
DB --> Service: 保存成功
Service -> MsgService: buyOrderMessage(context, orderBO)
MsgService -> MQ: 发送订单消息
MQ --> MsgService: 发送成功
Service --> Facade: 处理完成
Facade --> Client: Response.ok()
@enduml
```

## 11. 异常处理机制

### 主要异常场景及处理方式：

1. **参数校验异常**：
   - 场景：必填参数为空、格式不正确
   - 处理：抛出参数校验异常，返回具体错误信息

2. **业务校验异常**：
   - 场景：账户状态异常、产品不可交易、风险等级不匹配等
   - 处理：抛出业务异常，返回具体的业务错误码和描述

3. **数据库异常**：
   - 场景：数据库连接失败、SQL执行异常
   - 处理：事务回滚，记录错误日志，返回系统异常

4. **MQ发送异常**：
   - 场景：消息队列服务不可用
   - 处理：记录错误日志，不影响主流程（异步处理）

## 12. 调用的公共模块或外部依赖

| 模块名称 | 功能简述 |
|----------|----------|
| CounterFullBatchSubsService | 柜台全委专户认申购核心业务服务 |
| TradeValidatorHelper | 交易校验助手，提供统一的业务校验能力 |
| OrderMessageService | 订单消息服务，负责发送订单相关MQ消息 |
| AbstractCounterFullBatchSubsService | 抽象的全委专户认申购服务基类 |
| BatchTradeContext | 批量交易上下文对象 |
| ThreadPoolTaskExecutor | 线程池执行器，用于异步处理 |

## 13. 幂等性与安全性说明

### 幂等性：
- **非幂等接口**：每次调用都会创建新的订单记录
- **重复调用风险**：可能产生重复订单
- **建议**：调用方应通过外部订单号等机制控制重复提交

### 安全性：
- **鉴权机制**：依赖Dubbo框架的服务鉴权
- **参数校验**：使用@MyValidation注解进行参数合法性校验
- **业务校验**：多层次业务规则校验，确保交易合规性
- **数据安全**：敏感信息传输加密，数据库访问权限控制

### 限流：
- **接口限流**：可配置基于IP或客户的访问频率限制
- **资源保护**：使用线程池控制并发处理能力

## 14. 备注与风险点

### 注意事项：
1. **批量处理性能**：大批量数据处理时需要注意内存使用和处理时间
2. **事务一致性**：确保批量订单创建的事务完整性
3. **异步消息可靠性**：MQ消息发送失败时的补偿机制

### 边界处理：
1. **空列表处理**：当fullBatchSubsList为空时，直接返回成功
2. **部分失败处理**：批量校验中部分记录失败时的处理策略
3. **并发控制**：同一客户并发提交时的处理机制

### 特殊逻辑说明：
1. **费用计算**：预估费用与实际费用可能存在差异，需要后续对账处理
2. **展期逻辑**：展期选项和展期控制数的业务含义需要结合具体产品规则
3. **异步处理**：订单创建成功后的异步处理不影响接口响应，但需要监控处理结果

### 性能优化建议：
1. **批量操作**：数据库操作尽量使用批量插入提高性能
2. **缓存使用**：频繁查询的基础数据可考虑缓存
3. **异步解耦**：非核心流程异步处理，提高接口响应速度

---

**文档生成时间**：2025-07-26 21:39:47
**接口实现类**：CounterFullBatchSubsFacadeImpl
**作者**：hongdong.xie
