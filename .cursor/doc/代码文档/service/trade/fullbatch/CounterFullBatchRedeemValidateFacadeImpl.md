# 接口详细设计文档

## 1. 接口名称
柜台全委专户批量赎回校验接口

## 2. 接口说明
提供柜台交易场景下的全委专户批量赎回校验功能。此接口用于在执行实际批量赎回操作前，对所有前置条件进行全面校验，确保交易的合法性和安全性。

**业务背景**：
- 全委专户是指客户将资金委托给专业投资机构进行投资管理的业务模式
- 批量赎回校验是批量赎回流程的第一步，用于提前发现和拦截不符合条件的交易
- 柜台场景下需要严格的业务校验和风控管理，包括账户状态、产品状态、交易限制等多维度校验

**使用场景**：
- 柜台工作人员在协助香港客户进行全委专户基金批量赎回前的预校验
- 客户通过柜台系统发起多只基金赎回申请的前置校验
- 投资顾问为客户进行资产配置调整时的批量赎回可行性验证

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.fullbatch.CounterFullBatchRedeemValidateFacade.execute(CounterFullBatchRedeemValidateRequest request)
```

## 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 香港客户号 | hkCustNo | String | 是 | HK10086 | 香港地区客户的唯一标识号 |
| 全委批量赎回列表 | fullBatchRedeemInfoRequestList | List<FullBatchRedeemInfoRequest> | 是 | - | 批量赎回的基金明细列表 |
| 交易渠道 | tradeChannel | String | 是 | 1 | 交易渠道：1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 申请日期 | appDt | String | 是 | 20250726 | 申请日期，格式：YYYYMMDD |
| 申请时间 | appTm | String | 是 | 213456 | 申请时间，格式：HHmmss |

### 5.1 FullBatchRedeemInfoRequest 明细参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 外部订单号 | externalDealNo | String | 是 | EXT202507260001 | 外部系统生成的唯一订单号 |
| 基金交易账号 | fundTxAcctNo | String | 是 | 100001 | 客户在基金公司的交易账号 |
| 基金代码 | fundCode | String | 是 | 000001 | 基金产品的唯一标识代码 |
| 赎回方式 | redeemMethod | String | 是 | 1 | 赎回方式：1-金额赎回；2-份额赎回 |
| 申请金额 | appAmt | BigDecimal | 否 | 10000.00 | 赎回申请金额（金额赎回时必填） |
| 申请份额 | appVol | BigDecimal | 否 | 10000.00 | 赎回申请份额（份额赎回时必填） |

## 6. 响应参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 成功 | 响应描述信息 |
| 响应数据 | data | CounterFullBatchRedeemValidateResponse | 否 | - | 响应数据对象（当前为空对象） |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 校验通过，可以进行批量赎回操作 |
| 1001 | 参数错误 | 请求参数不符合要求 |
| 1002 | 客户状态异常 | 客户账户状态不允许交易 |
| 1003 | 基金交易账户状态异常 | 基金交易账户被冻结或注销 |
| 1004 | 产品渠道不匹配 | 产品不支持当前交易渠道 |
| 1005 | 产品业务类型不匹配 | 产品不支持当前业务类型 |
| 1006 | 开户日期校验失败 | 开户日期不符合交易要求 |
| 1007 | 柜台已关闭 | 当前时间柜台不允许交易 |
| 1008 | 产品净值状态异常 | 产品净值状态不允许赎回 |
| 1009 | 赎回方式不支持 | 产品不支持当前赎回方式 |
| 1010 | 赎回方向校验失败 | 赎回方向配置错误 |
| 1011 | 可用份额不足 | 客户可用份额不足以支持赎回 |
| 1012 | 最低持有份额校验失败 | 赎回后剩余份额低于最低持有要求 |
| 1013 | 赎回交易限制 | 超出赎回交易限制 |
| 9999 | 系统异常 | 系统内部错误 |

## 8. 关键业务逻辑说明

### 8.1 校验流程概述
1. **参数基础校验**：验证请求参数的完整性和格式正确性
2. **批量数量限制校验**：检查批量赎回列表数量是否超过系统配置的最大值
3. **并发校验处理**：使用线程池对每个基金的赎回信息进行并发校验，提高处理效率
4. **校验链执行**：对每个基金执行完整的校验链，包括账户、产品、交易等多维度校验
5. **结果汇总**：收集所有校验结果，如有任何一个基金校验失败，则整体校验失败

### 8.2 详细校验规则
**账户状态校验（ACCOUNT_STATUS）**：
- 验证香港客户号的有效性
- 检查客户账户状态是否允许交易
- 确认客户风险等级是否匹配

**基金交易账户状态校验（FUND_TX_ACCT_STAT）**：
- 验证基金交易账号的有效性
- 检查账户状态是否正常（非冻结、非注销）
- 确认账户与客户的关联关系

**产品渠道校验（PRODUCT_CHANNEL）**：
- 验证产品是否支持当前交易渠道
- 检查渠道权限配置

**产品业务校验（PRODUCT_BUSINESS）**：
- 验证产品是否支持批量赎回业务
- 检查业务类型配置

**开户日期校验（OPEN_DT）**：
- 验证开户日期是否满足交易要求
- 检查是否在冷静期内

**柜台关闭校验（COUNTER_CLOSED）**：
- 验证当前时间是否在柜台交易时间内
- 检查是否为交易日

**产品净值状态校验（PRODUCT_NAV_STATUS）**：
- 验证产品净值状态是否正常
- 检查是否可以进行赎回操作

**赎回方式校验（REDEMPTION_METHOD）**：
- 验证产品是否支持指定的赎回方式
- 检查赎回方式配置

**赎回方向校验（REDEMPTION_DIRECTION）**：
- 验证赎回方向配置的正确性

**可用份额校验（AVAILABLE_SHARE）**：
- 验证客户可用份额是否足够
- 检查份额冻结情况

**最低持有份额校验（SELL_MIN_HOLD）**：
- 验证赎回后剩余份额是否满足最低持有要求

**赎回交易限制校验（SELL_TRADE_LIMIT）**：
- 验证是否超出单笔或单日赎回限制
- 检查频次限制

## 9. 流程图

```plantuml
@startuml
start
:接收批量赎回校验请求;
:参数基础校验;
if (参数校验通过?) then (否)
  :返回参数错误;
  stop
endif
:检查批量数量限制;
if (数量在限制范围内?) then (否)
  :返回数量超限错误;
  stop
endif
:生成批次号;
:创建线程池任务列表;
fork
  :基金1校验;
  :构建交易上下文;
  :执行校验链;
  if (校验通过?) then (是)
    :记录成功结果;
  else (否)
    :记录失败结果;
  endif
fork again
  :基金2校验;
  :构建交易上下文;
  :执行校验链;
  if (校验通过?) then (是)
    :记录成功结果;
  else (否)
    :记录失败结果;
  endif
fork again
  :基金N校验;
  :构建交易上下文;
  :执行校验链;
  if (校验通过?) then (是)
    :记录成功结果;
  else (否)
    :记录失败结果;
  endif
end fork
:等待所有校验完成;
:汇总校验结果;
if (所有基金校验通过?) then (是)
  :返回校验成功;
else (否)
  :返回校验失败及错误信息;
endif
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "客户端" as Client
participant "CounterFullBatchRedeemValidateFacadeImpl" as Facade
participant "CounterFullBatchRedeemValidateService" as Service
participant "AbstractCounterFullBatchRedeemService" as AbstractService
participant "TradeValidatorHelper" as Validator
participant "线程池" as ThreadPool
participant "数据库" as DB

Client -> Facade: execute(request)
activate Facade
Facade -> Service: execute(request)
activate Service
Service -> Service: 记录开始日志
Service -> AbstractService: validate(request)
activate AbstractService
AbstractService -> AbstractService: doBatchValidate()
AbstractService -> AbstractService: 参数校验
AbstractService -> AbstractService: 数量限制校验
AbstractService -> AbstractService: 生成批次号
loop 每个基金
  AbstractService -> ThreadPool: 提交校验任务
  activate ThreadPool
  ThreadPool -> AbstractService: doValidate()
  AbstractService -> AbstractService: 构建交易上下文
  AbstractService -> AbstractService: doValidateChain()
  AbstractService -> Validator: buildChain()
  activate Validator
  Validator -> AbstractService: 返回校验链
  deactivate Validator
  AbstractService -> Validator: doValidate()
  activate Validator
  Validator -> DB: 查询账户信息
  activate DB
  DB -> Validator: 返回账户信息
  deactivate DB
  Validator -> DB: 查询产品信息
  activate DB
  DB -> Validator: 返回产品信息
  deactivate DB
  Validator -> DB: 查询份额信息
  activate DB
  DB -> Validator: 返回份额信息
  deactivate DB
  Validator -> AbstractService: 校验结果
  deactivate Validator
  ThreadPool -> AbstractService: 返回校验结果
  deactivate ThreadPool
end
AbstractService -> AbstractService: 等待所有任务完成
AbstractService -> AbstractService: 汇总校验结果
AbstractService -> Service: 返回校验结果
deactivate AbstractService
Service -> Service: 构建响应对象
Service -> Service: 记录完成日志
Service -> Facade: 返回响应
deactivate Service
Facade -> Client: 返回校验结果
deactivate Facade
@enduml
```

## 11. 异常处理机制

### 11.1 主要异常场景
1. **参数校验异常**：
   - 必填参数缺失
   - 参数格式错误
   - 参数值超出范围
   - 处理方式：直接返回参数错误，不进行后续处理

2. **业务校验异常**：
   - 账户状态异常
   - 产品状态异常
   - 交易限制异常
   - 处理方式：记录具体错误信息，返回对应的业务错误码

3. **并发处理异常**：
   - 线程池任务执行异常
   - 超时异常
   - 处理方式：捕获异常并记录错误日志，将异常信息包装到校验结果中

4. **系统异常**：
   - 数据库连接异常
   - 网络异常
   - 其他运行时异常
   - 处理方式：记录详细错误日志，返回系统异常错误码

### 11.2 异常处理策略
- **快速失败**：任何一个基金校验失败，整体校验即失败
- **异常隔离**：单个基金校验异常不影响其他基金的校验
- **详细日志**：记录每个校验步骤的详细日志，便于问题排查
- **友好提示**：向用户返回友好的错误提示信息

## 12. 调用的公共模块或外部依赖

### 12.1 公共模块
| 模块名称 | 功能简述 |
|----------|----------|
| TradeValidatorHelper | 交易校验助手，提供校验链构建和执行功能 |
| TradeContextBuilder | 交易上下文构建器，用于构建交易上下文对象 |
| SequenceService | 序列号服务，用于生成批次号等唯一标识 |
| NacosParamRefreshScope | Nacos参数刷新作用域，提供动态配置参数 |
| BatchTradePoolExecutor | 批量交易线程池执行器，用于并发处理 |

### 12.2 外部依赖
| 依赖名称 | 功能简述 |
|----------|----------|
| Dubbo | 分布式服务框架，提供RPC调用能力 |
| Spring | 依赖注入和AOP框架 |
| Lombok | 代码生成工具，简化实体类编写 |
| SLF4J | 日志门面，提供统一的日志接口 |

### 12.3 数据库依赖
- **客户信息表**：查询客户账户状态和基本信息
- **基金产品表**：查询基金产品信息和配置
- **基金交易账户表**：查询基金交易账户状态
- **客户持仓表**：查询客户基金持仓和可用份额
- **交易配置表**：查询交易限制和业务规则配置

## 13. 幂等性与安全性说明

### 13.1 幂等性
- **接口幂等性**：校验接口本身是幂等的，多次调用相同参数返回相同结果
- **无状态设计**：校验过程不会修改任何业务数据，只进行读取和校验
- **批次号生成**：每次调用都会生成新的批次号，用于日志追踪，不影响幂等性

### 13.2 安全性
- **参数校验**：对所有输入参数进行严格校验，防止恶意输入
- **权限控制**：通过Dubbo框架进行服务调用权限控制
- **数据脱敏**：日志中敏感信息进行脱敏处理
- **异常处理**：不向外暴露系统内部异常信息

### 13.3 限流与监控
- **并发控制**：通过线程池控制并发处理数量，防止系统过载
- **数量限制**：限制批量处理的最大数量，防止大批量请求影响系统性能
- **监控告警**：对关键指标进行监控，如处理时间、成功率、异常率等

## 14. 备注与风险点

### 14.1 注意事项
1. **批量数量限制**：系统配置的批量最大数量需要根据实际性能测试结果进行调整
2. **线程池配置**：批量交易线程池的核心线程数和最大线程数需要根据系统负载进行优化
3. **超时设置**：校验操作的超时时间需要考虑数据库查询和网络延迟
4. **内存使用**：大批量校验时需要注意内存使用情况，避免内存溢出

### 14.2 边界处理
1. **空列表处理**：当批量赎回列表为空时，直接返回参数错误
2. **重复基金代码**：同一批次中包含重复基金代码时的处理策略
3. **部分成功场景**：当部分基金校验成功、部分失败时的处理逻辑

### 14.3 特殊逻辑说明
1. **赎回方式校验**：金额赎回时appAmt必填，份额赎回时appVol必填
2. **交易时间校验**：需要考虑不同时区和节假日的影响
3. **产品状态校验**：需要实时查询产品最新状态，避免使用缓存数据

### 14.4 性能优化建议
1. **数据库查询优化**：对频繁查询的表建立合适的索引
2. **缓存策略**：对变化频率较低的配置数据可以考虑缓存
3. **异步处理**：充分利用并发处理能力，提高整体处理效率
4. **连接池优化**：合理配置数据库连接池参数

---

**文档版本**：1.0
**创建时间**：2025-07-26 21:34:56
**创建人**：hongdong.xie
**最后更新**：2025-07-26 21:34:56
