# 1. 接口名称
基金赎回接口

# 2. 接口类型
Dubbo 接口

# 3. 接口的请求参数和响应参数表

## 请求参数 (SellWebRequest)

| 中文名 | 英文名 | 类型 | 是否必填 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- |
| 香港客户号 | hkCustNo | String | 是 | 客户在香港的唯一标识 |
| 基金代码 | fundCode | String | 是 | 交易的基金代码 |
| 资金账号 | cpAcctNo | String | 否 | 赎回方向为1-电汇时必填 |
| 交易密码 | txPassword | String | 是 | 用户的交易密码 |
| 赎回方式 | redeemMethod | String | 是 | 1-按份额赎回；2-按金额赎回 |
| 赎回方向 | redeemDirection | String | 是 | 1-回银行卡\|电汇、2-留账好买香港账户、3-回海外储蓄罐、4-基金转投 |
| 申请金额 | appAmt | BigDecimal | 否 | 按金额赎回时必填 |
| 申请份额 | appVol | BigDecimal | 否 | 按份额赎回时必填 |
| 预约单号 | prebookDealNo | String | 否 | 如果是预约交易，则需要提供预约单号 |
| 订单号 | dealNo | String | 是 | 本次交易的唯一订单号 |

## 响应参数 (Response<SellWebVO>)

| 英文名 | 类型 | 字段说明 |
| :--- | :--- | :--- |
| code | String | 状态码，`0000` 表示成功 |
| description | String | 描述信息 |
| data | SellWebVO | 业务数据 |

## SellWebVO 结构

| 中文名 | 英文名 | 类型 | 字段说明 |
| :--- | :--- | :--- | :--- |
| 订单号 | dealNo | String | 创建成功的订单号 |

# 4. 关键业务逻辑说明

1.  **构建交易上下文**: 根据 `SellWebRequest` 请求参数，构建包含交易所有信息的 `TradeContext` 对象。
2.  **执行交易校验链**: 按照预设顺序执行一系列校验，确保交易的合规性和有效性。校验项包括：
    - 账户状态校验 (`ACCOUNT_STATUS`)
    - 交易密码校验 (`TRADE_PASSWORD`)
    - 资金账号校验 (`CP_ACCT_NO`)
    - 基金账户状态校验 (`FUND_TX_ACCT_STAT`)
    - 预约单号/订单号有效性校验 (`PREBOOK_DEAL_NO_VALID`, `DEAL_NO_VALID`)
    - 外部订单号校验 (`EXTERNAL_ORDER_NO`)
    - 产品渠道和业务校验 (`PRODUCT_CHANNEL`, `PRODUCT_BUSINESS`)
    - 开放日校验 (`OPEN_DT`)
    - 产品净值状态校验 (`PRODUCT_NAV_STATUS`)
    - 赎回方式和方向校验 (`REDEMPTION_METHOD`, `REDEMPTION_DIRECTION`)
    - 可用份额校验 (`AVAILABLE_SHARE`)
    - 赎回后最低持有份额校验 (`SELL_MIN_HOLD`)
    - 交易限制校验 (`SELL_TRADE_LIMIT`)
3.  **创建订单**: 校验通过后，调用 `OrderCreateService` 创建订单及订单明细 (`OrderCreateBO`)。
4.  **保存订单**: 调用 `HwDealOrderRepository` 将新创建的订单数据持久化到数据库。
5.  **发送消息**: 调用 `OrderMessageService` 发送订单创建成功的消息到消息队列，以供下游系统处理。
6.  **返回结果**: 将生成的订单号 `dealNo` 封装到 `SellWebVO` 中并返回给调用方。

# 5. 关键流程图

```plantuml
@startuml
title 基金赎回业务流程图
start
:接收 SellWebRequest 请求;
:构建 TradeContext;
:执行交易校验链;
if (校验是否通过?) then (是)
  :调用 OrderCreateService 创建订单;
  :调用 HwDealOrderRepository 保存订单;
  :调用 OrderMessageService 发送MQ消息;
  :构建 SellWebVO 响应;
  :返回成功响应;
else (否)
  :抛出交易异常;
  :返回失败响应;
endif
stop
@enduml
```

# 6. 时序图

```plantuml
@startuml
title 基金赎回接口调用时序图
actor User as "调用方"
participant FundSellWebFacadeImpl as "Facade"
participant SellWebService as "Service"
participant TradeValidatorHelper as "校验器"
participant OrderCreateService as "订单创建服务"
participant HwDealOrderRepository as "订单仓库"
participant OrderMessageService as "消息服务"

User -> FundSellWebFacadeImpl: execute(SellWebRequest)
FundSellWebFacadeImpl -> SellWebService: process(SellWebRequest)
SellWebService -> TradeValidatorHelper: buildChain(...)
SellWebService -> TradeValidatorHelper: doValidate(...)
TradeValidatorHelper --> SellWebService: 校验结果
alt 校验通过
    SellWebService -> OrderCreateService: createOrder(context)
    OrderCreateService --> SellWebService: OrderCreateBO
    SellWebService -> HwDealOrderRepository: saveDealOrder(OrderCreateBO)
    HwDealOrderRepository --> SellWebService: success
    SellWebService -> OrderMessageService: sellOrderMessage(...)
    OrderMessageService --> SellWebService: success
    SellWebService --> FundSellWebFacadeImpl: SellWebVO
    FundSellWebFacadeImpl --> User: Response<SellWebVO>
else 校验失败
    TradeValidatorHelper --> SellWebService: 抛出异常
    SellWebService --> FundSellWebFacadeImpl: 异常
    FundSellWebFacadeImpl --> User: 异常响应
end
@enduml
```

# 7. 调用的公共模块

- **TradeValidatorHelper**: 交易校验器帮助类，用于构建和执行校验链，确保业务规则的一致性。
- **OrderCreateService**: 订单创建服务，负责根据交易上下文组装订单数据，是订单创建的核心业务逻辑模块。
- **HwDealOrderRepository**: 订单仓库类，负责订单数据的数据库持久化操作（增删改查）。
- **OrderMessageService**: 订单消息服务，负责将订单相关的业务事件发送到消息队列，实现系统间的解耦和异步通信。
- **TradeContextBuilder**: 交易上下文构建器，用于从原始请求构建标准化的内部交易上下文对象。 