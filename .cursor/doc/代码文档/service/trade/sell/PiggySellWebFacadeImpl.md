# 储蓄罐赎回接口

## 1. 接口名称
储蓄罐赎回接口

## 2. 接口说明
本接口用于处理用户的储蓄罐产品赎回请求。用户可以通过指定赎回份额或金额，将资金从储蓄罐中赎回至银行卡、好买香港账户，或进行转投。

## 3. 接口类型
Dubbo 接口

## 4. 接口地址或方法签名
```java
com.howbuy.dtms.order.client.facade.trade.sell.PiggySellWebFacade.execute(PiggySellWebRequest request)
```

## 5. 请求参数表 (`PiggySellWebRequest`)
| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **香港客户号** | `hkCustNo` | String | 是 | `HK12345` | 客户在香港的唯一标识 |
| **基金代码** | `fundCode` | String | 是 | `HB0001` | 需要赎回的储蓄罐关联的基金代码 |
| **资金账号** | `cpAcctNo` | String | 否 | `ACCT001` | 赎回方向为1-电汇时必填 |
| **交易密码** | `txPassword` | String | 是 | `******` | 用户的交易密码（通常是加密后的） |
| **赎回方式** | `redeemMethod` | String | 是 | `1` | `1`-按份额赎回；`2`-按金额赎回 |
| **赎回方向** | `redeemDirection` | String | 是 | `2` | `1`-回银行卡\|电汇、`2`-留账好买香港账户、`3`-回海外储蓄罐、`4`-基金转投 |
| **申请金额** | `appAmt` | BigDecimal | 否 | `10000.00` | 按金额赎回时填写 |
| **申请份额** | `appVol` | BigDecimal | 否 | `10000.00` | 按份额赎回时填写 |
| **预约单号** | `prebookDealNo` | String | 否 | `PB20240817` | 如果是预约赎回，则填写此字段 |
| **订单号** | `dealNo` | String | 是 | `DEAL20240817` | 调用方生成的唯一订单号，用于幂等性校验 |
| **外部订单号**| `externalDealNo`| String | 否 | `EXT20240817`| 外部系统的订单号 |
| **交易渠道** | `tradeChannel`| String | 是 | `11` | 标识交易来源，如 `11`-APP |
| **IP地址** | `ipAddress`| String | 是 | `***********`| 客户端IP地址 |

*注：部分通用参数（如 `txCode`, `appDt` 等）继承自 `BaseRequest`，此处未一一列出。*

## 6. 响应参数表 (`Response<PiggySellWebResponse>`)

### 响应体结构
| 英文名 | 类型 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- |
| `code` | String | `"0000"` | 状态码，`0000` 表示成功 |
| `description` | String | `"成功"` | 响应的描述信息 |
| `data` | `PiggySellWebResponse` | | 业务数据体 |

### `PiggySellWebResponse` 结构
| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- |
| **订单号** | `dealNo` | String | `DEAL20240817` | 系统内生成的唯一交易订单号 |

## 7. 返回码说明
| 返回码 | 说明 |
| :--- | :--- |
| `0000` | 交易成功 |
| `Bxxxx` | 业务类错误，如余额不足、交易密码错误等 |
| `Axxxx` | 系统类错误，如数据库异常、依赖服务不可用等 |

## 8. 关键业务逻辑说明
1.  **参数校验与上下文构建**：接口接收到请求后，首先会将 `PiggySellWebRequest` 请求对象转换成内部的 `TradeContext` 业务上下文对象，该对象贯穿整个交易处理流程。
2.  **业务校验链**：系统通过一个可配置的校验链（`TradeValidatorHelper`）对交易的合法性进行全面检查。主要校验项包括：
    -   账户状态是否正常
    -   交易密码是否正确
    -   产品是否支持赎回
    -   交易时间是否在开放日内
    -   赎回金额/份额是否满足最低持有和交易限制
    -   可用份额是否充足
    -   订单号（`dealNo`）的唯一性，用于幂等控制
3.  **订单创建**：所有校验通过后，调用 `OrderCreateService` 创建订单（`hw_deal_order`）和订单明细（`hw_deal_order_dtl`）。
4.  **数据持久化**：调用 `HwDealOrderRepository` 将生成的订单信息保存到数据库中。
5.  **消息通知**：订单成功入库后，调用 `OrderMessageService` 发送一条MQ消息，通知下游系统（如清算、通知中心等）进行后续处理。
6.  **返回结果**：将生成的订单号封装到 `PiggySellWebResponse` 中并返回给调用方。

## 9. 流程图

```plantuml
@startuml
title 储蓄罐赎回流程图

start
:接收 PiggySellWebRequest 请求;
:构建 TradeContext 上下文;

group 业务校验 (TradeValidatorHelper)
    :校验账户状态;
    :校验交易密码;
    :校验产品状态与交易限制;
    :校验可用份额;
    :校验订单号唯一性 (幂等);
end group

if (校验通过?) then (是)
    :创建订单 (OrderCreateService);
    :保存订单至数据库 (HwDealOrderRepository);
    :发送订单MQ消息 (OrderMessageService);
    :构建 PiggySellWebResponse;
    :返回成功响应;
else (否)
    :抛出业务异常;
    :返回失败响应;
endif

stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
title 储蓄罐赎回时序图

actor Client as "调用方"
participant PiggySellWebFacadeImpl as "Facade"
participant PiggySellWebService as "Service"
participant TradeValidatorHelper as "校验器"
participant OrderCreateService as "订单创建服务"
participant HwDealOrderRepository as "数据仓库"
participant OrderMessageService as "消息服务"

Client -> PiggySellWebFacadeImpl : execute(request)
PiggySellWebFacadeImpl -> PiggySellWebService : piggyProcess(request)

PiggySellWebService -> PiggySellWebService : buildContext(request)
PiggySellWebService -> TradeValidatorHelper : doValidate(context, validatorChain)
TradeValidatorHelper --> PiggySellWebService : 校验结果

alt 校验成功
    PiggySellWebService -> OrderCreateService : createOrder(context)
    OrderCreateService --> PiggySellWebService : orderCreateBO

    PiggySellWebService -> HwDealOrderRepository : saveDealOrder(orderCreateBO)
    HwDealOrderRepository --> PiggySellWebService : success

    PiggySellWebService -> OrderMessageService : sellOrderMessage(context, bo)
    OrderMessageService --> PiggySellWebService : success

    PiggySellWebService --> PiggySellWebFacadeImpl : response
    PiggySellWebFacadeImpl --> Client : Response.ok(response)
else 校验失败
    TradeValidatorHelper --> PiggySellWebService : throw BusinessException
    PiggySellWebService --> PiggySellWebFacadeImpl : throw BusinessException
    PiggySellWebFacadeImpl --> Client : Response.fail(exception)
end

@enduml
```

## 11. 异常处理机制
- **业务异常**：在业务校验环节，任何不满足条件的情况（如密码错误、余额不足）都会通过 `AssertUtils` 抛出自定义的业务异常（`BusinessException`）。这些异常会被全局的Dubbo异常过滤器捕获，并转换成统一的错误码和错误信息返回给调用方。
- **系统异常**：数据库连接失败、MQ发送失败等系统级问题会抛出系统异常，同样由全局异常处理机制捕获，返回系统错误的响应码。

## 12. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
| :--- | :--- |
| `PiggySellWebService` | 储蓄罐赎回的核心业务逻辑处理服务。 |
| `TradeValidatorHelper` | 负责执行一系列业务规则校验的服务。 |
| `OrderCreateService` | 负责根据交易上下文组装订单和订单明细实体。 |
| `HwDealOrderRepository` | 负责将订单数据持久化到数据库。 |
| `OrderMessageService` | 负责在订单创建成功后，向消息队列发送通知。 |

## 13. 幂等性与安全性说明
- **幂等性**：接口通过 `dealNo`（订单号）字段保证幂等性。在业务校验环节会检查该 `dealNo` 是否已经处理过，如果存在，则拒绝重复请求，防止重复下单。调用方必须保证每次请求的 `dealNo` 是唯一的。
- **安全性**：
    - **认证**：接口通过 `txPassword`（交易密码）对用户身份进行验证。
    - **传输**：Dubbo框架本身支持配置安全传输协议。
    - **防刷**：网关层或接口实现中可根据需要增加限流、IP黑名单等防刷机制。

## 14. 备注与风险点
- **事务一致性**：订单创建（写数据库）和消息发送（写MQ）两个步骤需要保证事务一致性。当前实现是先写库后发消息，如果MQ发送失败，需要有重试或补偿机制（如定时任务扫描未发送消息的订单）来确保数据最终一致性。
- **依赖稳定性**：接口处理流程依赖多个内部服务，任何一个服务的延迟或不可用都可能导致交易失败。需要对核心依赖进行熔断、降级等高可用配置。 