# 1. 接口名称
CRM查询基金是否可赎回

# 2. 接口说明
该接口用于CRM系统查询客户持有的特定基金是否满足赎回（卖出）的条件。接口会进行一系列业务规则的校验，例如产品渠道、业务限制、开放日、产品状态以及可用份额等。

# 3. 接口类型
Dubbo 接口

# 4. 接口地址或方法签名
- **接口类:** `com.howbuy.dtms.order.client.facade.trade.sell.CrmFundCanSellFacade`
- **方法签名:** `Response<FundCanSellResponse> execute(CanSellRequest canSellRequest)`

# 5. 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 香港客户号 | `hkCustNo` | String | 是 | `HK000001` | 客户在香港的唯一标识 |
| 基金代码 | `fundCode` | String | 是 | `HB00123` | 需要查询的基金产品的代码 |
| 基金交易账号 | `fundTxAcctNo` | String | 否 | `T0000001` | 客户的基金交易账号。如果为空，系统将默认使用非全委交易账户 |
| 交易渠道 | `tradeChannel` | String | 是 | `CRM` | 标识交易请求来源渠道 |
| IP地址 | `ipAddress` | String | 是 | `***********` | 客户端IP地址 |
| 应用日期 | `appDt` | String | 是 | `20250425` | 请求发起的应用日期 |
| 应用时间 | `appTm` | String | 是 | `110500` | 请求发起的应用时间 |

# 6. 响应参数表

`FundCanSellResponse` 对象当前为空，不包含任何字段。接口的成功与否通过外层的 `Response` 对象状态判断。

| 中文名 | 英文名 | 类型 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- |
| - | - | - | - | - |

# 7. 返回码说明

| 返回码 | 说明 | 备注 |
| :--- | :--- | :--- |
| `0000` | 成功 | 表示接口调用成功，所有校验通过。 |
| 其他业务错误码 | 失败 | 具体错误信息见 `message` 字段，可能包含产品不可售、非开放日、份额不足等。 |

# 8. 关键业务逻辑说明
1.  **参数校验**: 对请求中的 `hkCustNo` 和 `fundCode` 进行非空校验。同时对 `appDt`, `appTm`, `tradeChannel`, `ipAddress` 等基础参数进行校验。
2.  **上下文构建**: 根据请求参数构建 `TradeContext`，用于在整个校验流程中传递数据。业务类型设置为 `SELL`（赎回），业务代码为 `REDEEM`。
3.  **交易账号处理**:
    - 如果请求中 `fundTxAcctNo` 为空，系统会自动查询该客户的非全权委托基金交易账号（`FundTxAcctTypeEnum.NON_FULL`）。
    - 如果不存在，则会为客户创建一个新的非全权委托基金交易账号。
4.  **校验链执行**:
    - 构建一个校验链，依次执行以下业务规则校验：
        - **产品渠道校验** (`PRODUCT_CHANNEL`): 验证该产品是否在当前渠道可售。
        - **产品业务校验** (`PRODUCT_BUSINESS`): 验证该产品是否支持赎回业务。
        - **开放日校验** (`OPEN_DT`): 检查当天是否为基金的赎回开放日。
        - **产品净值状态校验** (`PRODUCT_NAV_STATUS`): 检查产品的净值状态是否正常，允许交易。
        - **可用份额校验** (`BALANCE_AVAILABLE_SHARE`): 检查客户在该基金的持仓中是否有足够的可用份额进行赎回。
5.  **结果返回**: 如果所有校验均通过，则返回成功的 `Response` 对象。若任一校验失败，则抛出业务异常，由Dubbo框架统一处理并返回相应的错误信息。

# 9. 流程图
```plantuml
@startuml
title CRM查询基金是否可赎回流程

start
:接收 CanSellRequest 请求;

:基础参数校验;
if (校验失败?) then (yes)
  :返回错误信息;
  stop
endif

:构建交易上下文(TradeContext);

if (fundTxAcctNo 是否为空?) then (yes)
  :查询客户名下非全委交易账号;
  if (账号不存在?) then (yes)
    :创建新的非全权委托交易账号;
  endif
  :设置交易账号到上下文;
endif

:构建校验链 (Validator Chain);
note right
- 产品渠道校验
- 产品业务校验
- 开放日校验
- 产品净值状态校验
- 可用份额校验
end note

:执行校验链;

if (任一校验失败?) then (yes)
  :抛出业务异常;
  :返回错误响应;
  stop
endif

:返回成功响应 (Response.ok());

stop

@enduml
```

# 10. 时序图
```plantuml
@startuml
title CRM查询基金是否可赎回时序图

actor CRM系统 as crm
participant CrmFundCanSellFacadeImpl as facade
participant CrmCanSellService as service
participant TradeValidatorHelper as validator
participant HwFundTxAcctRepository as repo
participant FundTxAcctService as txAcctService

crm -> facade: execute(canSellRequest)
activate facade

facade -> service: process(canSellRequest)
activate service

service -> service: 参数校验与上下文构建

alt request.getFundTxAcctNo() is blank
    service -> repo: getNotFullFundTxAcct(hkCustNo)
    activate repo
    repo --> service: HwFundTxAcctPO or null
    deactivate repo

    alt HwFundTxAcctPO is null
        service -> txAcctService: createFundTxAcct(...)
        activate txAcctService
        txAcctService --> service: new HwFundTxAcctPO
        deactivate txAcctService
    end
end

service -> validator: buildChain(...)
activate validator
validator --> service: validatorList
deactivate validator

service -> validator: doValidate(context, validatorList)
activate validator
note right of validator: 依次执行各业务校验规则
validator --> service: (校验通过)
deactivate validator

service --> facade: (void)
deactivate service

facade -> facade: Response.ok()
facade --> crm: Response<FundCanSellResponse>
deactivate facade

@enduml
```

# 11. 异常处理机制
- **参数校验异常**: 如果 `ParamsValidator` 校验失败，会抛出 `BusinessException`，其中包含具体的字段校验错误信息。
- **业务规则异常**: 在校验链执行过程中，任何一个校验器失败都会抛出 `BusinessException`，错误码和错误信息由具体的校验器定义（如 `E_000_1001`: 非本渠道代销产品, `E_002_1001`: 非交易日或未到交易时间等）。
- **数据库异常**: 如果在查询或创建交易账号时发生数据库错误，会抛出数据库相关异常。
- **未知异常**: 其他未捕获的运行时异常会被统一的异常处理器捕获，返回系统错误信息。

# 12. 调用的公共模块或外部依赖
| 模块名称 | 功能简述 |
| :--- | :--- |
| `dtms-common` | 提供了公共的枚举，如 `BusinessCodeEnum`, `BusinessTypeEnum` 等。 |
| `dtms-order-dao` | 数据访问层，用于操作 `hw_fund_tx_acct` 表。 |
| `FundTxAcctService` | 基金交易账号服务，用于创建新的交易账号。 |
| `TradeValidatorHelper` | 交易校验链辅助类，用于构建和执行一系列的业务规则校验。 |
| `TradeContextBuilder` | 交易上下文构建器，用于创建和初始化 `TradeContext`。 |

# 13. 幂等性与安全性说明
- **幂等性**: 该接口为查询接口，不涉及状态变更，天然具备幂等性。
- **鉴权**: 依赖于Dubbo框架层级的安全机制，如IP白名单、服务鉴权等。
- **限流**: 可通过Dubbo的限流配置或Sentinel等外部组件对接口进行限流。
- **验签**: 无。

# 14. 业务处理涉及到的表名
| 表名 | 表注释 |
| :--- | :--- |
| `hw_fund_tx_acct` | 基金交易账号表 |
| `...` | 其他在校验链中可能涉及的表，如产品信息表、交易日历表、客户持仓表等（具体依赖于校验器的实现） |

# 15. 备注与风险点
- 该接口的响应 `FundCanSellResponse` 当前是空实现，如果未来需要返回更多关于可赎回的详细信息（如最大可赎回份额、预计到账日期等），需要对该响应对象进行扩展。
- 校验逻辑依赖于 `TradeValidatorHelper` 中构建的校验链，如果校验规则发生变化，需要修改 `CrmCanSellService` 中 `buildChain` 的部分。