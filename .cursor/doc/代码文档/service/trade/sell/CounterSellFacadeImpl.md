# 柜台销售接口

## 1. 接口名称
柜台销售接口

## 2. 接口说明
该接口提供柜台交易场景下的基金卖出相关功能，包括普通卖出和储蓄罐卖出，并为每种交易提供前置校验功能。

## 3. 接口类型
Dubbo

## 4. 接口地址或方法签名

### 4.1 柜台卖出校验
- **接口方法:** `com.howbuy.dtms.order.client.facade.trade.sell.CounterSellFacade.counterSellValidate(CounterSellValidateRequest request)`

### 4.2 柜台卖出
- **接口方法:** `com.howbuy.dtms.order.client.facade.trade.sell.CounterSellFacade.counterSell(CounterSellRequest request)`

### 4.3 柜台储蓄罐卖出校验
- **接口方法:** `com.howbuy.dtms.order.client.facade.trade.sell.CounterSellFacade.counterPiggySellValidate(CounterPiggySellValidateRequest request)`

### 4.4 柜台储蓄罐卖出
- **接口方法:** `com.howbuy.dtms.order.client.facade.trade.sell.CounterSellFacade.counterNewPiggySell(CounterNewPiggySellRequest request)`

---

## 5. `counterSellValidate` - 柜台卖出校验

### 5.1 请求参数表

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 香港客户号 | `hkCustNo` | String | 是 | "HK20240001" | 客户在香港的唯一标识 |
| 基金代码 | `fundCode` | String | 是 | "HB0001" | 待卖出的基金代码 |
| 基金交易账号 | `fundTxAcctNo` | String | 是 | "A0000001" | 客户的基金交易账号 |
| 赎回方式 | `redeemMethod` | String | 是 | "1" | 1-按份额赎回, 2-按金额赎回 |
| 赎回方向 | `redeemDirection` | String | 是 | "1" | 1-回银行卡/电汇, 2-留存好买香港账户, 3-回海外储蓄罐, 4-基金转投 |
| 申请金额 | `appAmt` | BigDecimal | 否 | 10000.00 | `redeemMethod`为2时必填 |
| 申请份额 | `appVol` | BigDecimal | 否 | 10000.00 | `redeemMethod`为1时必填 |
| 资金账号 | `cpAcctNo` | String | 否 | "6222..." | 赎回方向为1-电汇时必填 |
| 预约单号 | `prebookDealNo` | String | 否 | "P20241108001" | 预约交易的唯一编号 |
| 交易渠道 | `tradeChannel` | String | 是 | "11" | 1-柜台, 9-CRM-PC, 11-APP |
| ... | ... | ... | ... | ... | (继承自`BaseRequest`的公共字段) |

### 5.2 响应参数表

| 中文名 | 英文名 | 类型 | 字段说明 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | String | `0000`表示成功，其他表示失败 |
| 描述信息 | `description` | String | 对返回结果的描述 |
| 数据封装 | `data` | Body | 成功时为空Body对象 |

### 5.3 返回码说明

| 返回码 | 说明 | 备注 |
| :--- | :--- | :--- |
| `0000` | 成功 | 校验通过 |
| `9999` | 系统异常 |  |
| `O0001`| 参数校验失败 |  |

### 5.4 关键业务逻辑说明
1.  执行参数的非空和格式校验。
2.  校验客户状态、基金状态、交易账户状态是否正常。
3.  根据`redeemMethod`（赎回方式），校验`appAmt`（金额）或`appVol`（份额）是否在合法范围内，并校验客户持有份额是否足够。
4.  校验交易时间是否在允许的交易时段内。
5.  如果`redeemDirection`为电汇，校验`cpAcctNo`（资金账号）的有效性。

### 5.5 流程图
```plantuml
@startuml
title counterSellValidate - 柜台卖出校验流程
start
:接收 CounterSellValidateRequest 请求;
:参数校验;
if (校验失败?) then (是)
  :返回失败响应;
  stop
endif
:调用 CounterSellValidateService.process() 方法;
:执行核心业务校验
- 校验客户状态
- 校验基金状态
- 校验持有份额
- 校验交易限制;
if (业务校验失败?) then (是)
  :抛出业务异常;
  :全局异常处理器捕获异常;
  :返回失败响应;
  stop
endif
:返回成功响应 (Response.ok());
stop
@enduml
```

---

## 6. `counterSell` - 柜台卖出

### 6.1 请求参数表
(同 `counterSellValidate` 5.1)

### 6.2 响应参数表

| 中文名 | 英文名 | 类型 | 字段说明 |
| :--- | :--- | :--- | :--- |
| 状态码 | `code` | String | `0000`表示成功，其他表示失败 |
| 描述信息 | `description` | String | 对返回结果的描述 |
| 数据封装 | `data` | CounterSellVO | 包含订单号和订单明细号 |
| 订单号 | `data.dealNo` | String | 生成的唯一订单号 |
| 订单明细号 | `data.dealDtlNo` | String | 生成的唯一订单明细号 |

### 6.3 返回码说明
(同 `counterSellValidate` 5.3)

### 6.4 关键业务逻辑说明
1.  调用 `counterSellService.process()` 处理卖出请求。
2.  内部首先执行与 `counterSellValidate` 相同的校验逻辑。
3.  校验通过后，创建订单记录，状态为处理中。
4.  扣减客户持有的基金份额。
5.  记录交易流水。
6.  将订单信息推送到消息队列，由下游系统继续处理。
7.  返回订单号和订单明细号。

### 6.5 流程图
```plantuml
@startuml
title counterSell - 柜台卖出流程
start
:接收 CounterSellRequest 请求;
:调用 CounterSellService.process() 方法;
:执行前置校验(同counterSellValidate);
if (校验失败?) then (是)
  :抛出业务异常并返回;
  stop
endif
:创建订单 (Order);
:保存订单到数据库;
:扣减用户基金份额;
:记录交易流水;
:发送订单消息到MQ;
:构建 CounterSellVO 响应;
:返回成功响应;
stop
@enduml
```

### 6.6 时序图
```plantuml
@startuml
title counterSell - 时序图
actor Client
participant CounterSellFacadeImpl as Facade
participant CounterSellService as Service
participant OrderDao as DAO
participant "MessageQueue" as MQ

Client -> Facade: counterSell(request)
Facade -> Service: process(request)
Service -> Service: 业务校验()
Service -> DAO: insertOrder(order)
DAO --> Service: 订单信息
Service -> DAO: decreaseShare(cust, fund, vol)
Service -> MQ: sendOrderMessage(order)
Service --> Facade: CounterSellVO
Facade --> Client: Response<CounterSellVO>
@enduml
```

---

## 7. `counterPiggySellValidate` & `counterNewPiggySell`

这两个接口的功能与 `counterSellValidate` 和 `counterSell` 类似，但针对的是“储蓄罐”这一特定业务场景。

-   **`counterPiggySellValidate`**：储蓄罐卖出前置校验，逻辑与 `counterSellValidate` 基本一致，但可能包含针对储蓄罐产品的特殊校验规则。
-   **`counterNewPiggySell`**：执行储蓄罐卖出操作，逻辑与 `counterSell` 基本一致，但操作的资产为储蓄罐份额。

其请求/响应参数、流程图、时序图结构与普通卖出接口类似，此处不再赘述。

---

## 8. 异常处理机制

| 异常场景 | 处理方式 |
| :--- | :--- |
| 请求参数校验失败 | `MyValidation` 注解配合全局AOP进行校验，返回通用失败响应码 `O0001`。 |
| 业务逻辑校验失败 | 在Service层通过断言或直接抛出自定义业务异常（如 `BusinessException`），由全局异常处理器统一捕获，并返回具体的错误码和错误信息。 |
| 数据库操作异常 | 事务回滚，记录错误日志，返回通用系统异常响应码 `9999`。 |
| Dubbo调用超时或失败 | Dubbo框架自带重试机制（根据配置），若最终失败，记录异常日志，返回系统异常响应。 |

## 9. 调用的公共模块或外部依赖

| 模块/依赖名称 | 功能简述 |
| :--- | :--- |
| `dtms-order-dao` | 提供数据库访问能力，用于订单、持仓等数据的增删改查。 |
| `com.howbuy.commons:commons-validator` | 提供统一的参数校验框架。 |
| `org.apache.dubbo` | RPC框架，用于服务暴露和调用。 |
| `Redis` (通过缓存服务) | 用于缓存常用数据（如基金信息、客户信息）和分布式锁。 |
| `RocketMQ` (或其他MQ) | 用于发送订单处理消息，实现系统解耦和异步处理。 |

## 10. 幂等性与安全性说明

-   **幂等性**：
    -   查询/校验类接口 (`counterSellValidate`, `counterPiggySellValidate`) 天然幂等。
    -   交易类接口 (`counterSell`, `counterNewPiggySell`) 通过在 `BaseRequest` 中引入唯一的 `externalDealNo` (外部订单号) 字段，并在Service层结合分布式锁实现。首次请求时，将 `externalDealNo` 存入Redis并设置过期时间，后续相同 `externalDealNo` 的请求将被拦截，从而保证接口的幂等性。
-   **安全性**：
    -   **鉴权**：依赖Dubbo服务端的IP白名单或Token验证机制（具体看框架集成）。
    -   **限流**：可通过网关层（如Sentinel）或在Dubbo Provider端配置限流策略，防止恶意请求。
    -   **数据校验**：对所有外部输入进行严格的格式和业务规则校验。

## 11. 业务处理涉及到的表名

| 表名 | 表注释 |
| :--- | :--- |
| `t_order` | 订单主表 |
| `t_order_detail` | 订单明细表 |
| `t_cust_fund_share` | 客户基金份额表 |
| `t_trade_journal` | 交易流水表 |

## 12. 备注与风险点

-   **风险点**：扣减份额与创建订单需要保证在同一事务中，确保数据一致性。
-   **注意事项**：`redeemMethod` (赎回方式) 和 `redeemDirection` (赎回方向) 两个字段的组合会衍生出不同的业务逻辑分支，需要特别关注。
-   **分布式事务**：如果后续流程（如调用支付、清算系统）涉及跨库或跨服务操作，需要引入分布式事务解决方案（如Seata）来保证数据最终一致性。