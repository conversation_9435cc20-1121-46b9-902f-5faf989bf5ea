# 批量储蓄罐交易申请买入接口设计文档

## 1. 接口名称
批量储蓄罐交易申请买入接口

## 2. 接口说明
本接口用于批量处理储蓄罐交易申请买入业务，支持通过Excel导入、可用余额、客户控管表、退款控管表、自动赎回等多种来源的批量买入申请。该接口适用于海外储蓄罐业务场景，支持香港客户的基金申购操作。

**业务背景：**
- 支持海外储蓄罐业务的批量交易申请处理
- 适用于申购业务类型（中台业务码为申购）
- 支持多种申请来源的统一处理
- 提供完整的参数校验、费率计算、数据持久化功能

**使用场景：**
- 运营人员通过Excel批量导入客户储蓄罐交易申请
- 系统自动处理客户可用余额的储蓄罐申购
- 控管表数据的批量下发处理
- 退款和自动赎回场景的批量申购处理

## 3. 接口类型
Dubbo接口

## 4. 接口地址或方法签名

### Dubbo接口方法签名
```java
Response<BatchPiggyTradeAppBuyResponse> execute(BatchPiggyTradeAppBuyRequest request);
```

### 接口类全路径
- 接口定义：`com.howbuy.dtms.order.client.facade.trade.piggytradeapp.BatchPiggyTradeAppBuyFacade`
- 接口实现：`com.howbuy.dtms.order.service.provider.dubbo.trade.piggytradeapp.BatchPiggyTradeAppBuyFacadeImpl`

## 5. 请求参数表

### 5.1 基础请求参数（继承BaseRequest）

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 交易代码 | txCode | String | 是 | HW_ORDER_PIGGY_BUY | 交易代码，继承自BaseRequest |
| 操作员 | operator | String | 否 | admin | 操作员信息 |
| 渠道号 | channelNo | String | 否 | WEB | 渠道号 |

### 5.2 业务请求参数

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 储蓄罐申请买入列表 | piggyTradeAppBuyList | List<PiggyTradeAppBuyItem> | 是 | 见下表 | 批量买入申请列表，最大200条 |

### 5.3 PiggyTradeAppBuyItem参数

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 导入申请ID | importAppId | String | 是 | APP001 | 唯一标识申请记录 |
| 储蓄罐申请来源 | piggyAppSource | String | 是 | 0 | 0-excel, 1-可用余额, 2-客户控管表, 3-退款控管表, 4-自动赎回 |
| 香港客户号 | hkCustNo | String | 是 | HK001 | 香港客户唯一标识 |
| 基金交易账号 | fundTxAcctNo | String | 是 | FTA001 | 基金交易账户号 |
| 申请金额 | appAmt | BigDecimal | 是 | 10000.00 | 申请买入金额 |
| 关联订单号 | relationalDealNo | String | 否 | 123456 | 关联的订单号 |

## 6. 响应参数表

### 6.1 Response封装结构

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 成功 | 响应描述信息 |
| 数据封装 | data | BatchPiggyTradeAppBuyResponse | 是 | {} | 响应数据对象 |

### 6.2 BatchPiggyTradeAppBuyResponse参数

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| - | - | - | - | - | 当前版本无返回字段，仅返回成功状态 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 批量处理成功 |
| 1001 | 参数错误 | 请求参数校验失败 |
| 1002 | 系统错误 | 申请ID已存在等业务错误 |
| 1003 | 账户不存在 | 客户号不存在 |
| 9999 | 系统异常 | 系统内部异常 |

## 8. 关键业务逻辑说明

### 8.1 参数校验逻辑
1. 校验储蓄罐申请买入列表不能为空且不超过200条
2. 校验每个申请项的必填字段：导入申请ID、申请来源、客户号、交易账号、申请金额
3. 校验储蓄罐申请来源枚举值的有效性
4. 校验导入申请ID的唯一性，已存在且非失败状态的记录不允许重复提交

### 8.2 前置查询逻辑
1. 查询当前可购买的储蓄罐基金产品信息
2. 批量查询客户信息，验证客户号的有效性
3. 查询基金费率信息，用于后续费用计算
4. 获取预计上报日期

### 8.3 费用计算逻辑
1. 根据客户类型和申请金额匹配对应的费率
2. 获取客户折扣率，默认为1.0（无折扣）
3. 根据基金的费用计算方式（内扣法/外扣法）计算手续费和实际买入金额
4. 外扣法：买入金额 = 申请金额 / (1 + 费率 × 折扣率)，手续费 = 申请金额 - 买入金额
5. 内扣法：买入金额 = 申请金额，手续费 = 买入金额 × 费率 × 折扣率

### 8.4 数据处理逻辑
1. 构建储蓄罐交易申请导入记录，设置基本信息、客户信息、基金信息
2. 根据申请来源确定支付方式
3. 在事务中处理数据：先删除生成失败的记录，再批量插入新记录
4. 确保数据一致性和完整性

## 9. 流程图

```plantuml
@startuml
start
:接收批量买入请求;
:参数校验;
if (参数校验通过?) then (否)
  :抛出参数错误异常;
  stop
else (是)
endif
:前置查询;
note right
  - 查询可购买基金信息
  - 批量查询客户信息  
  - 查询费率信息
  - 获取预计上报日期
end note
:构建导入记录;
note right
  - 设置基本信息
  - 计算费用和金额
  - 确定支付方式
end note
:数据事务处理;
note right
  - 删除失败记录
  - 批量插入新记录
end note
:返回成功响应;
stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
participant "调用方" as Client
participant "BatchPiggyTradeAppBuyFacadeImpl" as Facade
participant "BatchPiggyTradeAppBuyService" as Service
participant "HwPiggyTradeAppImportRepository" as Repository
participant "QueryFundInfoOuterService" as FundService
participant "QueryCustInfoOuterService" as CustService

Client -> Facade: execute(request)
Facade -> Service: process(request)

Service -> Service: validateParams(request)
note right: 参数校验

Service -> FundService: querySupportBuyPiggyBuyFundList()
FundService --> Service: 基金信息

Service -> CustService: batchQueryHkCustInfo()
CustService --> Service: 客户信息

Service -> Service: buildImportRecords()
note right: 构建导入记录\n计算费用

Service -> Repository: queryByImportAppIds()
Repository --> Service: 已存在记录

Service -> Repository: batchProcessRecords()
note right: 事务处理\n删除失败记录\n插入新记录
Repository --> Service: 处理结果

Service --> Facade: BatchPiggyTradeAppBuyResponse
Facade --> Client: Response<BatchPiggyTradeAppBuyResponse>
@enduml
```

## 11. 异常处理机制

### 11.1 主要异常场景
1. **参数校验异常**：请求参数为空、格式错误、超出限制等
2. **业务校验异常**：申请ID重复、客户不存在、基金产品不可购买等
3. **数据处理异常**：数据库操作失败、事务回滚等
4. **外部服务异常**：查询客户信息、基金信息等外部服务调用失败

### 11.2 异常处理方式
- 使用ValidateException处理业务校验异常
- 使用@Transactional确保数据一致性
- 记录详细的错误日志便于问题排查
- 返回标准化的错误响应给调用方

## 12. 调用的公共模块或外部依赖

### 12.1 公共模块
| 模块名称 | 功能简述 |
|----------|----------|
| AssertUtils | 参数校验工具类 |
| DateUtils | 日期处理工具类 |
| TradeUtils | 交易相关工具类 |
| Constants | 系统常量定义 |

### 12.2 外部依赖
| 模块名称 | 功能简述 |
|----------|----------|
| QueryFundInfoOuterService | 查询基金信息外部服务 |
| QueryCustInfoOuterService | 查询客户信息外部服务 |
| HwPiggyTradeAppImportRepository | 储蓄罐交易申请数据访问层 |

## 13. 幂等性与安全性说明

### 13.1 幂等性
- **幂等性保证**：通过导入申请ID的唯一性校验确保幂等性
- **重复提交处理**：已存在且非失败状态的记录不允许重复提交
- **失败重试机制**：生成失败的记录允许重新提交，系统会先删除失败记录再插入新记录

### 13.2 安全性
- **参数校验**：严格的参数格式和业务规则校验
- **数据校验**：客户信息、基金信息的有效性校验
- **事务控制**：使用数据库事务确保数据一致性
- **日志记录**：完整的操作日志记录便于审计

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_piggy_trade_app_import | 海外储蓄罐导入申请表 |

## 15. 备注与风险点

### 15.1 注意事项
1. 批量处理最大支持200条记录，超出限制会抛出异常
2. 费用计算精度根据币种确定：日元保留0位小数，其他币种保留2位小数
3. 申请来源枚举值必须严格按照系统定义使用
4. 客户信息和基金信息必须在系统中存在且有效

### 15.2 边界处理
- 空列表处理：申请列表为空时抛出参数错误异常
- 重复ID处理：相同导入申请ID的记录根据生成状态决定是否允许提交
- 精度处理：金额计算采用向下取整方式确保精度一致性

### 15.3 特殊逻辑说明
- 支付方式根据申请来源自动确定：Excel和可用余额为海外储蓄罐支付
- 折扣率查询失败时默认使用1.0（无折扣）
- 客户名称优先使用中文名，中文名为空时使用英文名

---

**文档生成时间**：2025-07-29 08:26:03
**文档版本**：v1.0
**维护人员**：shaoyang.li
