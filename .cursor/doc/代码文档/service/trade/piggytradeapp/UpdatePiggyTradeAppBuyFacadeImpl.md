# 修改储蓄罐交易申请买入接口设计文档

## 1. 接口名称
修改储蓄罐交易申请买入接口

## 2. 接口说明
本接口用于修改储蓄罐交易申请买入信息，包括买入金额、折扣率、手续费等关键交易参数。该接口适用于通过Excel导入或可用余额方式创建的储蓄罐交易申请，在未生成正式订单前的修改操作。

**业务背景：**
- 支持海外储蓄罐业务的交易申请修改
- 适用于申购业务类型（中台业务码为申购）
- 仅允许修改来源为Excel或可用余额的申请记录
- 仅允许修改状态为"未生成"的申请记录

**使用场景：**
- 运营人员在后台管理系统中修改客户储蓄罐交易申请
- 修正客户录入错误的交易金额或费率信息
- 根据实际业务需求调整交易参数

## 3. 接口类型
Dubbo接口

## 4. 接口地址或方法签名

### Dubbo接口方法签名
```java
Response<UpdatePiggyTradeAppBuyResponse> execute(UpdatePiggyTradeAppBuyRequest request);
```

### 接口类全路径
- 接口定义：`com.howbuy.dtms.order.client.facade.trade.piggytradeapp.UpdatePiggyTradeAppBuyFacade`
- 接口实现：`com.howbuy.dtms.order.service.provider.dubbo.trade.piggytradeapp.UpdatePiggyTradeAppBuyFacadeImpl`

## 5. 请求参数表

### 5.1 基础请求参数（继承BaseRequest）

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 交易码 | txCode | String | 否 | HW_ORDER_DEFAULT | 默认交易码 |
| 申请日期 | appDt | String | 否 | 20250728 | 格式：yyyyMMdd |
| 申请时间 | appTm | String | 否 | 161530 | 格式：HHmmss |
| 交易渠道 | tradeChannel | String | 否 | 9 | 1-柜台；2-网站；3-电话；4-Wap；9-CRM-PC；10-CRM移动端；11-APP |
| 网点号 | outletCode | String | 否 | 8888 | 所属网点 |
| IP地址 | ipAddress | String | 否 | *********** | 客户端IP |
| 外部订单号 | externalDealNo | String | 否 | EXT20250728001 | 外部系统订单号 |
| 并发控制锁 | currentKey | String | 否 | lock_123 | 并发控制锁key |
| MAC地址 | macAddress | String | 否 | 00:1B:44:11:3A:B7 | 设备MAC地址 |
| 设备序列号 | deviceSerialNo | String | 否 | SN123456789 | 设备序列号 |
| 设备型号 | deviceModel | String | 否 | iPhone 12 | 设备型号 |
| 设备名称 | deviceName | String | 否 | iPhone | 设备名称 |
| 系统版本号 | systemVersion | String | 否 | 14.7.1 | 操作系统版本 |

### 5.2 业务请求参数

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 导入申请ID | importAppId | String | 是 | APP001 | 储蓄罐交易申请唯一标识 |
| 买入金额 | buyAmt | String | 是 | 10000.00 | 客户实际买入金额，必须大于0 |
| 折扣率 | discountRate | String | 是 | 0.8 | 费率折扣比例，0-1之间，如0.8表示8折 |
| 手续费 | fee | String | 是 | 50.00 | 交易手续费金额，必须大于等于0 |
| 操作人 | operator | String | 是 | admin | 执行修改操作的人员 |
| 备注 | remark | String | 是 | 调整买入金额 | 修改备注说明 |

## 6. 响应参数表

### 6.1 统一响应结构

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| 状态码 | code | String | 是 | 0000 | 响应状态码，0000表示成功 |
| 描述信息 | description | String | 是 | 成功 | 响应描述信息 |
| 数据封装 | data | Object | 是 | {} | 业务数据对象 |

### 6.2 业务响应参数

| 中文名 | 英文名 | 类型 | 是否必填 | 示例值 | 字段说明 |
|--------|--------|------|----------|--------|----------|
| - | - | - | - | - | 当前接口只需返回成功状态，无需额外数据字段 |

## 7. 返回码说明

| 返回码 | 说明 | 备注 |
|--------|------|------|
| 0000 | 成功 | 操作成功完成 |
| C020001 | 系统异常 | 系统内部错误 |
| C020002 | 参数错误 | 请求参数校验失败 |
| C020996 | 数据库异常 | 数据库操作异常 |
| C021001 | 客户不存在 | 客户信息查询失败 |
| C021023 | 手续费不正确 | 计算手续费与请求不一致 |
| C021051 | 申请金额低于下限 | 申请金额低于最低限制 |
| C021052 | 申请金额超过上限 | 申请金额超过最高限制 |
| C023001 | 查询香港客户信息异常 | 外部接口调用失败 |

## 8. 关键业务逻辑说明

### 8.1 整体处理流程
接口采用标准的参数校验 → 数据查询 → 业务校验 → 业务处理 → 数据更新的流程模式。

### 8.2 详细处理步骤

#### 8.2.1 参数校验
1. **非空校验**：校验所有必填参数不为空
2. **格式校验**：
   - 买入金额必须为有效数字且大于0
   - 折扣率必须为有效数字且在0-1之间
   - 手续费必须为有效数字且大于等于0

#### 8.2.2 数据查询与验证
1. **查询储蓄罐交易申请记录**：根据importAppId查询对应的申请记录
2. **业务规则校验**：
   - 校验中台业务码必须为申购（防止修改赎回记录）
   - 校验储蓄罐申请来源必须为Excel或可用余额（防止修改控管表下发数据）
   - 校验生成状态必须为"未生成"（已生成的记录不允许修改）

#### 8.2.3 客户信息验证
1. **查询客户信息**：通过香港客户号查询客户基本信息
2. **客户状态校验**：确保客户信息存在且状态正常

#### 8.2.4 基金信息查询
1. **查询基金基本信息**：获取基金币种、费率计算方式等信息
2. **查询基金费率**：根据客户类型、基金代码、买入金额查询适用的费率

#### 8.2.5 费用计算与验证
1. **计算手续费**：根据基金费率计算模式（内扣法/外扣法）计算实际手续费
2. **验证手续费**：确保请求中的手续费与计算结果一致
3. **计算申请金额**：根据买入金额和手续费计算最终申请金额

#### 8.2.6 数据更新
1. **更新申请记录**：更新买入金额、手续费、折扣率、申请金额等字段
2. **更新操作信息**：记录操作人、备注、更新时间等审计信息
3. **并发控制**：通过版本号机制确保并发安全

## 9. 流程图

```plantuml
@startuml
title 修改储蓄罐交易申请买入流程

start
:接收修改请求;

:参数校验;
if (参数合法?) then (是)
  :查询储蓄罐交易申请记录;
  if (记录存在?) then (是)
    :业务规则校验;
    if (校验通过?) then (是)
      :查询客户信息;
      if (客户存在?) then (是)
        :查询基金信息和费率;
        :计算手续费和申请金额;
        if (手续费一致?) then (是)
          :更新储蓄罐交易申请记录;
          if (更新成功?) then (是)
            :返回成功响应;
          else (否)
            :记录错误日志;
            :抛出数据库异常;
          endif
        else (否)
          :记录错误日志;
          :抛出手续费不一致异常;
        endif
      else (否)
        :抛出客户不存在异常;
      endif
    else (否)
      :抛出业务规则校验失败异常;
    endif
  else (否)
    :抛出记录不存在异常;
  endif
else (否)
  :抛出参数错误异常;
endif

stop
@enduml
```

## 10. 时序图

```plantuml
@startuml
title 修改储蓄罐交易申请买入时序图

participant 前端系统
participant 网关层
participant UpdatePiggyTradeAppBuyFacade
participant UpatePiggyTradeAppBuyService
participant HwPiggyTradeAppImportRepository
participant HkCustInfoOuterService
participant QueryFundInfoOuterService
participant 数据库

前端系统 -> 网关层: 发送修改请求
网关层 -> UpdatePiggyTradeAppBuyFacade: execute(request)
UpdatePiggyTradeAppBuyFacade -> UpatePiggyTradeAppBuyService: process(request)
UpatePiggyTradeAppBuyService -> UpatePiggyTradeAppBuyService: 参数校验

UpatePiggyTradeAppBuyService -> HwPiggyTradeAppImportRepository: queryByImportAppId(importAppId)
HwPiggyTradeAppImportRepository --> UpatePiggyTradeAppBuyService: 返回申请记录

UpatePiggyTradeAppBuyService -> UpatePiggyTradeAppBuyService: 业务规则校验

UpatePiggyTradeAppBuyService -> HkCustInfoOuterService: getHkCustInfo(hkCustNo)
HkCustInfoOuterService --> UpatePiggyTradeAppBuyService: 返回客户信息

UpatePiggyTradeAppBuyService -> QueryFundInfoOuterService: queryFundBasicInfo(productCode)
QueryFundInfoOuterService --> UpatePiggyTradeAppBuyService: 返回基金信息

UpatePiggyTradeAppBuyService -> QueryFundInfoOuterService: queryFundFeeRateByAppAmt(request)
QueryFundInfoOuterService --> UpatePiggyTradeAppBuyService: 返回费率信息

UpatePiggyTradeAppBuyService -> UpatePiggyTradeAppBuyService: 计算费用和验证

UpatePiggyTradeAppBuyService -> HwPiggyTradeAppImportRepository: updateByImportAppId(piggyTradeAppImport)
HwPiggyTradeAppImportRepository --> UpatePiggyTradeAppBuyService: 返回更新结果

UpatePiggyTradeAppBuyService --> UpdatePiggyTradeAppBuyFacade: 返回处理结果
UpdatePiggyTradeAppBuyFacade --> 网关层: 返回响应
网关层 --> 前端系统: 返回响应
@enduml
```

## 11. 异常处理机制

### 11.1 异常分类
1. **参数校验异常**：参数格式错误、必填项缺失等
2. **业务规则异常**：业务逻辑校验失败、数据状态异常等
3. **数据访问异常**：数据库操作失败、数据不存在等
4. **外部服务异常**：调用外部服务失败

### 11.2 异常处理策略
1. **统一异常处理**：所有异常统一包装为ValidateException或运行时异常
2. **错误日志记录**：关键异常记录详细错误日志
3. **告警机制**：重要业务异常触发告警通知
4. **事务回滚**：数据库操作异常时自动回滚

### 11.3 主要异常场景
- 参数格式错误：返回C020002参数错误
- 记录不存在：返回对应业务异常码
- 业务规则校验失败：返回具体业务异常码
- 计算结果不一致：记录告警日志并返回C021023手续费不正确
- 数据库更新失败：返回C020996数据库异常

## 12. 调用的公共模块或外部依赖

### 12.1 内部公共模块
| 模块名称 | 功能简述 |
|----------|----------|
| AssertUtils | 参数断言工具类，用于参数校验 |
| AlertLogUtil | 告警日志工具，用于记录异常信息 |
| TradeUtils | 交易工具类，提供币种判断等功能 |
| Constants | 系统常量定义 |

### 12.2 外部依赖服务
| 服务名称 | 功能简述 |
|----------|----------|
| HkCustInfoOuterService | 查询香港客户信息服务 |
| QueryFundInfoOuterService | 查询基金基本信息和费率服务 |

### 12.3 数据访问层
| 模块名称 | 功能简述 |
|----------|----------|
| HwPiggyTradeAppImportRepository | 储蓄罐交易申请记录数据访问 |

## 13. 幂等性与安全性说明

### 13.1 幂等性
- **幂等支持**：是
- **实现机制**：通过数据库记录的版本号机制确保并发安全
- **幂等条件**：相同的importAppId和相同的参数多次调用结果一致

### 13.2 安全性
- **鉴权机制**：通过Dubbo服务调用，依赖调用方系统的身份认证
- **权限控制**：操作人字段记录实际执行人员，可用于审计追踪
- **数据校验**：所有输入参数均经过严格格式和业务规则校验
- **金额校验**：计算手续费与请求手续费必须一致，防止金额篡改

### 13.3 限流与防护
- **限流策略**：依赖Dubbo框架的限流机制
- **并发控制**：通过版本号实现乐观锁，避免并发修改冲突

## 14. 业务处理涉及到的表名

| 表名 | 表注释 |
|------|--------|
| hw_piggy_trade_app_import | 海外储蓄罐导入申请表 |

## 15. 备注与风险点

### 15.1 注意事项
1. **修改限制**：只能修改状态为"未生成"的申请记录
2. **来源限制**：只能修改来源为Excel或可用余额的申请记录
3. **业务类型限制**：只能修改中台业务码为申购的记录
4. **手续费校验**：请求的手续费必须与系统计算结果完全一致

### 15.2 边界处理
1. **日元币种处理**：日元金额不允许有小数位，系统会自动截断
2. **金额精度处理**：根据基金币种类型确定小数位精度
3. **并发处理**：通过版本号机制处理并发修改问题

### 15.3 特殊逻辑说明
1. **费率计算模式**：
   - 外扣法：手续费=净申购金额×交易费率×折扣率，申请金额=净申购金额+手续费
   - 内扣法：手续费=净申购金额×交易费率×折扣率，净申购金额=申请金额
2. **折扣率范围**：折扣率必须在0-1之间，0表示免费，1表示无折扣
3. **操作审计**：每次修改都会记录操作人和修改时间，便于追踪变更历史

### 15.4 风险提示
1. **数据一致性风险**：修改后的金额可能影响后续的订单生成和资金结算
2. **并发修改风险**：多个用户同时修改同一记录可能导致数据不一致
3. **外部依赖风险**：依赖客户信息服务和基金信息服务的可用性